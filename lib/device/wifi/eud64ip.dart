//
//  eud64ip.dart
//  EltakoConnect
//
//  Created by <PERSON> on 23.05.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:async';

import 'package:eltako_connect/connection/wifi/i_wifi_connection.dart';
import 'package:eltako_connect/connection/wifi/wifi_demo_connection.dart';
import 'package:eltako_connect/device/i_br62ip_device.dart';
import 'package:eltako_connect/enumeration/authorization_type.dart';
import 'package:eltako_connect/enumeration/button_channel_configuration_logic.dart';
import 'package:eltako_connect/enumeration/button_type.dart';
import 'package:eltako_connect/enumeration/connection_type.dart';
import 'package:eltako_connect/enumeration/device_type.dart';
import 'package:eltako_connect/enumeration/device_type_category.dart';
import 'package:eltako_connect/enumeration/dim_curve.dart';
import 'package:eltako_connect/enumeration/edge_mode.dart';
import 'package:eltako_connect/enumeration/enocean_repeater_level.dart';
import 'package:eltako_connect/enumeration/enocean_repeater_mode.dart';
import 'package:eltako_connect/enumeration/http_method.dart';
import 'package:eltako_connect/enumeration/login_type.dart';
import 'package:eltako_connect/enumeration/push_button_state.dart';
import 'package:eltako_connect/enumeration/sensor_channel_position.dart';
import 'package:eltako_connect/enumeration/sensor_connection_type.dart';
import 'package:eltako_connect/enumeration/sensor_orientation.dart';
import 'package:eltako_connect/enumeration/sensor_type.dart';
import 'package:eltako_connect/enumeration/service_position.dart';
import 'package:eltako_connect/extension/uuid_extension.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations_extension.dart';
import 'package:eltako_connect/model/authorization_info.dart';
import 'package:eltako_connect/model/authorization_validity_info.dart';
import 'package:eltako_connect/model/device_info.dart';
import 'package:eltako_connect/model/firmware_update/firmware_version/plain_firmware_version.dart';
import 'package:eltako_connect/model/sensor/sensor/button_config.dart';
import 'package:eltako_connect/model/sensor/sensor/enocean_button.dart';
import 'package:eltako_connect/model/sensor/sensor/wired_button.dart';
import 'package:eltako_connect/model/sensor/sensor_config_data.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_central_down_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_central_up_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_directional_down_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_directional_up_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_universal_logic_configuration.dart';
import 'package:eltako_connect/model/service_category.dart';
import 'package:eltako_connect/model/sub_device/sub_device/dimmer_channel_config_data.dart';
import 'package:eltako_connect/model/sub_device/sub_device/dimmer_config_data.dart';
import 'package:eltako_connect/model/wifi_connection_info.dart';
import 'package:eltako_connect/service/automation_service/wifi_automation_service.dart';
import 'package:eltako_connect/service/config_service/config_data.dart';
import 'package:eltako_connect/service/config_service/wifi_config_service.dart';
import 'package:eltako_connect/service/dimmer_service/wifi_dimmer_service.dart';
import 'package:eltako_connect/service/enocean_config_service/enocean_config_data.dart';
import 'package:eltako_connect/service/firmware_config_service/firmware_config_data.dart';
import 'package:eltako_connect/service/firmware_config_service/wifi_firmware_config_service.dart';
import 'package:eltako_connect/service/firmware_update_service/br62ip_firmware_update_service.dart';
import 'package:eltako_connect/service/firmware_update_service/firmware_update_data.dart';
import 'package:eltako_connect/service/matter_sub_sensor_service/wifi_matter_sub_sensor_service.dart';
import 'package:eltako_connect/service/name_service/wifi_name_service.dart';
import 'package:eltako_connect/service/reset_service/br62ip_reset_service.dart';
import 'package:eltako_connect/service/sensor_service/br64ip_sensor_service.dart';
import 'package:eltako_connect/service/sub_device_service/wifi_sub_device_service.dart';
import 'package:eltako_connect/service/sub_sensor_service/br64_ip_sub_sensor_service.dart';
import 'package:eltako_connect/service/system_service/br62ip_system_data.dart';
import 'package:eltako_connect/service/system_service/br62ip_system_service.dart';
import 'package:eltako_connect/storage/secure_storage/section/device_login_storage.dart';
import 'package:eltako_connect/storage/secure_storage/secure_storage.dart';
import 'package:get_it/get_it.dart';

/// Class for EUD64-IP devices
class Eud64ip extends IBr62ipDevice {
  // region [STATIC FUNCTIONS]

  /// Demo data for EUD-IP devices
  static ConfigData createDemoData({required DeviceType deviceType, required String identifier}) => ConfigData(
    deviceInfo: DeviceInfo(
      deviceType: deviceType,
      name: deviceType.demoName,
      anonymousName: 'EUD64IP_DEMO_${UuidExtension.randomUuid().short.toUpperCase()}',
      identifier: identifier,
      connectionType: ConnectionType.wifiDemo,
      model: deviceType.name,
      description: deviceType.localizedDescription,
      hardwareVersion: '1.0.0',
      softwareVersion: '1.0.0',
    ),
    serviceData: [
      DimmerConfigData(
        name: deviceType.name,
        edgeMode: EdgeMode.auto,
        channels: [
          DimmerChannelConfigData(channel: 1, dimCurve: DimCurve.linear, minBrightness: 1, maxBrightness: 100),
        ],
      ),
      if (IBr62ipDevice.enableFirmwareConfigService)
        FirmwareConfigData(updateServerUrl: 'update.eltako.lan', autoUpdateEnabled: true, updateCheckInterval: 12),
      FirmwareUpdateData(availableVersions: [PlainFirmwareVersion(version: '1.12.0')]),
      Br62ipSystemData(
        name: deviceType.demoName,
        model: deviceType.name,
        productId: '8077e5f1-5e9c-44de-83c7-fdd7f2b8e090',
        version: '1.0.0',
        serialNumber: UuidExtension.randomUuid().toString(),
      ),
      EnOceanConfigData(repeaterMode: EnOceanRepeaterMode.on, repeaterLevel: EnOceanRepeaterLevel.one, baseId: 1),
      SensorConfigData(
        supportsEnOcean: true,
        pairedSensors: [
          WiredButton(
            name: l10n.wiredButton,
            buttonType: ButtonType.button2Way,
            productId: SensorType.wiredInput2WayProductId,
            id: UuidExtension.randomUuid().toString(),
            channelCount: 3,
            channels: [
              ButtonConfig(
                position: SensorChannelPosition.left,
                category: DeviceTypeCategory.dimmer,
                configuration: ButtonDimmerDirectionalDownLogicConfiguration(),
                configurationLogic: ButtonChannelConfigurationLogic.directionalDown,
                state: PushButtonState.notPressed,
              ),
              ButtonConfig(
                position: SensorChannelPosition.right,
                category: DeviceTypeCategory.dimmer,
                configuration: ButtonDimmerDirectionalUpLogicConfiguration(),
                configurationLogic: ButtonChannelConfigurationLogic.directionalUp,
                state: PushButtonState.notPressed,
              ),
            ],
          ),
          EnOceanButton(
            name: 'FE64F101',
            buttonType: ButtonType.button1Way,
            productId: SensorType.button1WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F101', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 1,
            channels: [
              ButtonConfig(
                position: SensorChannelPosition.single,
                category: DeviceTypeCategory.dimmer,
                configuration: ButtonDimmerUniversalLogicConfiguration(),
                configurationLogic: ButtonChannelConfigurationLogic.universal,
                state: PushButtonState.notPressed,
              ),
            ],
          ),
          EnOceanButton(
            name: 'FE64F201',
            buttonType: ButtonType.button2Way,
            productId: SensorType.button2WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F201', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 2,
            channels: [
              ButtonConfig(
                position: SensorChannelPosition.top,
                category: DeviceTypeCategory.dimmer,
                configuration: ButtonDimmerDirectionalUpLogicConfiguration(),
                configurationLogic: ButtonChannelConfigurationLogic.directionalUp,
                state: PushButtonState.notPressed,
              ),
              ButtonConfig(
                position: SensorChannelPosition.bottom,
                category: DeviceTypeCategory.dimmer,
                configuration: ButtonDimmerDirectionalDownLogicConfiguration(),
                configurationLogic: ButtonChannelConfigurationLogic.directionalDown,
                state: PushButtonState.notPressed,
              ),
            ],
          ),
          EnOceanButton(
            name: 'FE64F401',
            buttonType: ButtonType.button4Way,
            productId: SensorType.button4WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F401', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 4,
            channels: [
              ButtonConfig(
                position: SensorChannelPosition.topLeft,
                category: DeviceTypeCategory.dimmer,
                configuration: ButtonDimmerDirectionalUpLogicConfiguration(),
                configurationLogic: ButtonChannelConfigurationLogic.directionalUp,
                state: PushButtonState.notPressed,
              ),
              ButtonConfig(
                position: SensorChannelPosition.bottomLeft,
                category: DeviceTypeCategory.dimmer,
                configuration: ButtonDimmerDirectionalUpLogicConfiguration(),
                configurationLogic: ButtonChannelConfigurationLogic.directionalDown,
                state: PushButtonState.notPressed,
              ),
              ButtonConfig(
                position: SensorChannelPosition.topRight,
                category: DeviceTypeCategory.dimmer,
                configuration: ButtonDimmerCentralUpLogicConfiguration(),
                configurationLogic: ButtonChannelConfigurationLogic.centralUp,
                state: PushButtonState.notPressed,
              ),
              ButtonConfig(
                position: SensorChannelPosition.bottomRight,
                category: DeviceTypeCategory.dimmer,
                configuration: ButtonDimmerCentralDownLogicConfiguration(),
                configurationLogic: ButtonChannelConfigurationLogic.centralDown,
                state: PushButtonState.notPressed,
              ),
            ],
          ),
        ],
        unpairedSensors: [
          EnOceanButton(
            name: 'FE64F102',
            buttonType: ButtonType.button1Way,
            productId: SensorType.button1WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F102', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 1,
            channels: [ButtonConfig.unset(position: SensorChannelPosition.single, category: DeviceTypeCategory.dimmer)],
          ),
          EnOceanButton(
            name: 'FE64F202',
            buttonType: ButtonType.button2Way,
            productId: SensorType.button2WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F202', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 2,
            channels: [
              ButtonConfig.unset(position: SensorChannelPosition.top, category: DeviceTypeCategory.dimmer),
              ButtonConfig.unset(position: SensorChannelPosition.bottom, category: DeviceTypeCategory.dimmer),
            ],
          ),
          EnOceanButton(
            name: 'FE64F402',
            buttonType: ButtonType.button4Way,
            productId: SensorType.button4WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F402', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 4,
            channels: [
              ButtonConfig.unset(position: SensorChannelPosition.topLeft, category: DeviceTypeCategory.dimmer),
              ButtonConfig.unset(position: SensorChannelPosition.bottomLeft, category: DeviceTypeCategory.dimmer),
              ButtonConfig.unset(position: SensorChannelPosition.topRight, category: DeviceTypeCategory.dimmer),
              ButtonConfig.unset(position: SensorChannelPosition.bottomRight, category: DeviceTypeCategory.dimmer),
            ],
          ),
        ],
      ),
    ],
  );

  // endregion

  // region [PROPERTIES]

  @override
  late final AuthorizationInfo authorizationInfo;

  @override
  late final List<ServiceCategory> serviceCategories;

  @override
  String get baseURL {
    final api = (connection as IWifiConnection).connectionInfo;
    return 'https://${api.host}:443/api/v0';
  }

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  Eud64ip({required super.name, required super.connection, required String model, super.identifier})
    : super(
        imagePath: DeviceType.fromName(model).imagePath,
        icon: Assets.icons.wifi.circle,
        model: model,
        description: DeviceType.fromName(model).localizedDescription,
      ) {
    deviceInfo.deviceType = DeviceType.fromName(model);
    authorizationInfo = AuthorizationInfo(
      logInType: connection.type == ConnectionType.wifi ? LoginType.eltakoQrCode : LoginType.none,
      authorizationType: connection.type == ConnectionType.wifi ? AuthorizationType.apiKey : AuthorizationType.none,
      createURL: () => '$baseURL/login',
      method: HttpMethod.post,
      extractAuthorizationHeaders: extractAuthorizationHeaders,
      validityInfo: AuthorizationValidityInfo.notExpiring(),
    );
    serviceCategories = [
      ServiceCategory(
        key: 'service.category.menu_bar',
        position: ServicePosition.menuBar,
        services: [WifiConfigService(this), Br62ipFirmwareUpdateService(this), Br62ipResetService(this)],
      ),
      ServiceCategory(
        key: 'service.category.dimmer',
        position: ServicePosition.main,
        services: [WifiDimmerService(this)],
      ),
      ServiceCategory(
        key: 'service.category.configuration',
        position: ServicePosition.main,
        services: [
          // WifiEnOceanConfigService(this),
          Br64ipSensorService(
            this,
            supportedSensorConnectionTypes: [
              SensorConnectionType.wired,
              SensorConnectionType.enOcean,
              SensorConnectionType.matter,
            ],
          ),
        ],
      ),
      ServiceCategory(
        key: 'service.category.device',
        position: ServicePosition.main,
        services: [
          WifiNameService(this),
          if (IBr62ipDevice.enableFirmwareConfigService) WifiFirmwareConfigService(this),
          if (showHiddenServices) ...[
            WifiAutomationService(this),
            WifiMatterSubSensorService(this),
            WifiSubDeviceService(this),
            Br64ipSubSensorService(
              this,
              supportedSensorConnectionTypes: [
                SensorConnectionType.wired,
                SensorConnectionType.enOcean,
                SensorConnectionType.matter,
              ],
            ),
            Br62ipSystemService(this),
          ],
        ],
      ),
      ServiceCategory(
        key: 'service.category.hidden',
        position: ServicePosition.hidden,
        services: [
          if (!showHiddenServices) ...[
            Br62ipSystemService(this),
            Br64ipSubSensorService(
              this,
              supportedSensorConnectionTypes: [
                SensorConnectionType.wired,
                SensorConnectionType.enOcean,
                SensorConnectionType.matter,
              ],
            ),
            WifiAutomationService(this),
            WifiMatterSubSensorService(this),
            WifiSubDeviceService(this),
          ],
        ],
      ),
    ];
  }

  /// Create a demo EUD64-IPM device
  factory Eud64ip.demoEud64npnipm({String? name, String ipAddress = '*************', String version = '01.00'}) =>
      Eud64ip._demo(type: DeviceType.eud64npnipm, name: name, ipAddress: ipAddress, version: version);

  // endregion

  // region [PRIVATE FUNCTIONS]

  /// Create a demo EUD64 device
  factory Eud64ip._demo({required DeviceType type, required String ipAddress, required String version, String? name}) {
    // Create demo name
    final demoName = name ?? type.demoName;

    // Delete old device logins
    unawaited(GetIt.I.get<SecureStorage>().deleteDeviceLogins(ipAddress));

    // Create and return demo device
    final demoData = Eud64ip.createDemoData(deviceType: type, identifier: ipAddress);
    return Eud64ip(
      name: demoName,
      connection: WifiDemoConnection(config: demoData, connectionInfo: WifiConnectionInfo.empty(ipAddress: ipAddress)),
      identifier: ipAddress,
      model: type.name,
    )..setDemoData(demoData, name: name, identifier: ipAddress, version: version);
  }

  // endregion
}
