//
//  esb64ip.dart
//  EltakoConnect
//
//  Created by <PERSON> on 05.08.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:async';

import 'package:eltako_connect/connection/wifi/i_wifi_connection.dart';
import 'package:eltako_connect/connection/wifi/wifi_demo_connection.dart';
import 'package:eltako_connect/device/i_br62ip_device.dart';
import 'package:eltako_connect/enumeration/authorization_type.dart';
import 'package:eltako_connect/enumeration/button_type.dart';
import 'package:eltako_connect/enumeration/connection_type.dart';
import 'package:eltako_connect/enumeration/device_type.dart';
import 'package:eltako_connect/enumeration/device_type_category.dart';
import 'package:eltako_connect/enumeration/enocean_repeater_level.dart';
import 'package:eltako_connect/enumeration/enocean_repeater_mode.dart';
import 'package:eltako_connect/enumeration/http_method.dart';
import 'package:eltako_connect/enumeration/login_type.dart';
import 'package:eltako_connect/enumeration/runtime_mode.dart';
import 'package:eltako_connect/enumeration/sensor_channel_position.dart';
import 'package:eltako_connect/enumeration/sensor_connection_type.dart';
import 'package:eltako_connect/enumeration/sensor_orientation.dart';
import 'package:eltako_connect/enumeration/sensor_type.dart';
import 'package:eltako_connect/enumeration/service_position.dart';
import 'package:eltako_connect/env/environment.dart';
import 'package:eltako_connect/extension/uuid_extension.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/model/authorization_info.dart';
import 'package:eltako_connect/model/authorization_validity_info.dart';
import 'package:eltako_connect/model/device_info.dart';
import 'package:eltako_connect/model/firmware_update/firmware_version/plain_firmware_version.dart';
import 'package:eltako_connect/model/sensor/sensor/button_config.dart';
import 'package:eltako_connect/model/sensor/sensor/enocean_button.dart';
import 'package:eltako_connect/model/sensor/sensor/wired_button.dart';
import 'package:eltako_connect/model/sensor/sensor_config_data.dart';
import 'package:eltako_connect/model/service_category.dart';
import 'package:eltako_connect/model/sub_device/sub_device/shader_config_data.dart';
import 'package:eltako_connect/model/wifi_connection_info.dart';
import 'package:eltako_connect/service/automation_service/wifi_automation_service.dart';
import 'package:eltako_connect/service/config_service/config_data.dart';
import 'package:eltako_connect/service/config_service/wifi_config_service.dart';
import 'package:eltako_connect/service/enocean_config_service/enocean_config_data.dart';
import 'package:eltako_connect/service/enocean_config_service/wifi_enocean_config_service.dart';
import 'package:eltako_connect/service/firmware_config_service/firmware_config_data.dart';
import 'package:eltako_connect/service/firmware_config_service/wifi_firmware_config_service.dart';
import 'package:eltako_connect/service/firmware_update_service/br62ip_firmware_update_service.dart';
import 'package:eltako_connect/service/firmware_update_service/firmware_update_data.dart';
import 'package:eltako_connect/service/matter_sub_sensor_service/wifi_matter_sub_sensor_service.dart';
import 'package:eltako_connect/service/name_service/wifi_name_service.dart';
import 'package:eltako_connect/service/reset_service/br62ip_reset_service.dart';
import 'package:eltako_connect/service/sensor_service/br64ip_sensor_service.dart';
import 'package:eltako_connect/service/shader_service/wifi_shader_service.dart';
import 'package:eltako_connect/service/sub_device_service/wifi_sub_device_service.dart';
import 'package:eltako_connect/service/sub_sensor_service/br64_ip_sub_sensor_service.dart';
import 'package:eltako_connect/service/system_service/br62ip_system_data.dart';
import 'package:eltako_connect/service/system_service/br62ip_system_service.dart';
import 'package:eltako_connect/storage/secure_storage/section/device_login_storage.dart';
import 'package:eltako_connect/storage/secure_storage/secure_storage.dart';
import 'package:get_it/get_it.dart';

/// Class for ESB64-IP devices
class Esb64ip extends IBr62ipDevice {
  // region [STATIC FUNCTIONS]

  /// Demo data for ESB-IP devices
  static ConfigData createDemoData({required DeviceType deviceType, required String identifier}) => ConfigData(
    deviceInfo: DeviceInfo(
      deviceType: deviceType,
      name: deviceType.demoName,
      anonymousName: 'ESB64IP_DEMO_${UuidExtension.randomUuid().short.toUpperCase()}',
      identifier: identifier,
      connectionType: ConnectionType.wifiDemo,
      model: deviceType.name,
      description: deviceType.localizedDescription,
      hardwareVersion: '1.0.0',
      softwareVersion: '1.0.0',
    ),
    serviceData: [
      if (IBr62ipDevice.enableFirmwareConfigService)
        FirmwareConfigData(updateServerUrl: 'update.eltako.lan', autoUpdateEnabled: true, updateCheckInterval: 12),
      FirmwareUpdateData(availableVersions: [PlainFirmwareVersion(version: '1.12.0')]),
      ShaderConfigData(name: deviceType.name, runtimeMode: RuntimeMode.auto, runtime: 16000, runtimeOffset: 0),
      Br62ipSystemData(
        name: deviceType.demoName,
        model: deviceType.name,
        productId: '0b898240-e820-4a06-879a-fccf35260a35',
        version: '1.0.0',
        serialNumber: UuidExtension.randomUuid().toString(),
      ),
      EnOceanConfigData(repeaterMode: EnOceanRepeaterMode.on, repeaterLevel: EnOceanRepeaterLevel.one, baseId: 1),
      SensorConfigData(
        supportsEnOcean: true,
        pairedSensors: [
          WiredButton(
            name: 'Wired input demo',
            buttonType: ButtonType.button2Way,
            productId: SensorType.wiredInput2WayProductId,
            id: UuidExtension.randomUuid().toString(),
            channelCount: 3,
            channels: [
              ButtonConfig.unset(position: SensorChannelPosition.left, category: DeviceTypeCategory.shutter),
              ButtonConfig.unset(position: SensorChannelPosition.right, category: DeviceTypeCategory.shutter),
            ],
          ),
          EnOceanButton(
            name: 'F1T55 demo',
            buttonType: ButtonType.button1Way,
            productId: SensorType.button1WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F101', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 1,
            channels: [
              ButtonConfig.unset(position: SensorChannelPosition.single, category: DeviceTypeCategory.shutter),
            ],
          ),
          EnOceanButton(
            name: 'F2T55 demo',
            buttonType: ButtonType.button2Way,
            productId: SensorType.button2WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F201', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 2,
            channels: [
              ButtonConfig.unset(position: SensorChannelPosition.top, category: DeviceTypeCategory.shutter),
              ButtonConfig.unset(position: SensorChannelPosition.bottom, category: DeviceTypeCategory.shutter),
            ],
          ),
          EnOceanButton(
            name: 'F4T55 demo',
            buttonType: ButtonType.button4Way,
            productId: SensorType.button4WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F401', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 4,
            channels: [
              ButtonConfig.unset(position: SensorChannelPosition.topLeft, category: DeviceTypeCategory.shutter),
              ButtonConfig.unset(position: SensorChannelPosition.bottomLeft, category: DeviceTypeCategory.shutter),
              ButtonConfig.unset(position: SensorChannelPosition.topRight, category: DeviceTypeCategory.shutter),
              ButtonConfig.unset(position: SensorChannelPosition.bottomRight, category: DeviceTypeCategory.shutter),
            ],
          ),
        ],
        unpairedSensors: [
          EnOceanButton(
            name: 'F1T55 demo (unset)',
            buttonType: ButtonType.button1Way,
            productId: SensorType.button1WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F102', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 1,
            channels: [
              ButtonConfig.unset(position: SensorChannelPosition.single, category: DeviceTypeCategory.shutter),
            ],
          ),
          EnOceanButton(
            name: 'F2T55 demo (unset)',
            buttonType: ButtonType.button2Way,
            productId: SensorType.button2WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F202', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 2,
            channels: [
              ButtonConfig.unset(position: SensorChannelPosition.top, category: DeviceTypeCategory.shutter),
              ButtonConfig.unset(position: SensorChannelPosition.bottom, category: DeviceTypeCategory.shutter),
            ],
          ),
          EnOceanButton(
            name: 'F4T55 demo (unset)',
            buttonType: ButtonType.button4Way,
            productId: SensorType.button4WayProductId,
            id: UuidExtension.randomUuid().toString(),
            eurid: int.parse('FE64F402', radix: 16),
            orientation: SensorOrientation.clockwise0,
            lastSeen: DateTime.now(),
            signalStrength: -50,
            channelCount: 4,
            channels: [
              ButtonConfig.unset(position: SensorChannelPosition.topLeft, category: DeviceTypeCategory.shutter),
              ButtonConfig.unset(position: SensorChannelPosition.bottomLeft, category: DeviceTypeCategory.shutter),
              ButtonConfig.unset(position: SensorChannelPosition.topRight, category: DeviceTypeCategory.shutter),
              ButtonConfig.unset(position: SensorChannelPosition.bottomRight, category: DeviceTypeCategory.shutter),
            ],
          ),
        ],
      ),
    ],
  );

  // endregion

  // region [PROPERTIES]

  @override
  late final AuthorizationInfo authorizationInfo;

  @override
  late final List<ServiceCategory> serviceCategories;

  @override
  String get baseURL {
    final api = (connection as IWifiConnection).connectionInfo;
    return 'https://${api.host}:443/api/v0';
  }

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  Esb64ip({required super.name, required super.connection, required String model, super.identifier})
    : super(
        imagePath: DeviceType.fromName(model).imagePath,
        icon: Assets.icons.wifi.circle,
        model: model,
        description: DeviceType.fromName(model).localizedDescription,
      ) {
    deviceInfo.deviceType = DeviceType.fromName(model);
    authorizationInfo = AuthorizationInfo(
      logInType: connection.type == ConnectionType.wifi ? LoginType.eltakoQrCode : LoginType.none,
      authorizationType: connection.type == ConnectionType.wifi ? AuthorizationType.apiKey : AuthorizationType.none,
      createURL: () => '$baseURL/login',
      method: HttpMethod.post,
      extractAuthorizationHeaders: extractAuthorizationHeaders,
      validityInfo: AuthorizationValidityInfo.notExpiring(),
    );
    serviceCategories = [
      ServiceCategory(
        key: 'service.category.menu_bar',
        position: ServicePosition.menuBar,
        services: [WifiConfigService(this), Br62ipFirmwareUpdateService(this), Br62ipResetService(this)],
      ),
      ServiceCategory(key: 'service.category.shader', position: ServicePosition.main, services: []),
      ServiceCategory(
        key: 'service.category.configuration',
        position: ServicePosition.main,
        services: [
          WifiShaderService(this, supportsLcdMode: !Environment.current.isProduction),
          WifiEnOceanConfigService(this),
          Br64ipSensorService(
            this,
            supportedSensorConnectionTypes: [
              SensorConnectionType.wired,
              SensorConnectionType.enOcean,
              SensorConnectionType.matter,
            ],
          ),
        ],
      ),
      ServiceCategory(
        key: 'service.category.device',
        position: ServicePosition.main,
        services: [
          WifiNameService(this),
          if (IBr62ipDevice.enableFirmwareConfigService) WifiFirmwareConfigService(this),
          if (showHiddenServices) ...[
            WifiAutomationService(this),
            WifiMatterSubSensorService(this),
            WifiSubDeviceService(this),
            Br64ipSubSensorService(
              this,
              supportedSensorConnectionTypes: [
                SensorConnectionType.wired,
                SensorConnectionType.enOcean,
                SensorConnectionType.matter,
              ],
            ),
            Br62ipSystemService(this),
          ],
        ],
      ),
      ServiceCategory(
        key: 'service.category.hidden',
        position: ServicePosition.hidden,
        services: [
          if (!showHiddenServices) ...[
            Br62ipSystemService(this),
            Br64ipSubSensorService(
              this,
              supportedSensorConnectionTypes: [
                SensorConnectionType.wired,
                SensorConnectionType.enOcean,
                SensorConnectionType.matter,
              ],
            ),
            WifiAutomationService(this),
            WifiMatterSubSensorService(this),
            WifiSubDeviceService(this),
          ],
        ],
      ),
    ];
  }

  /// Create a demo ESB64-IPM device
  factory Esb64ip.demoEsb64npipm({String? name, String ipAddress = '*************', String version = '01.00'}) =>
      Esb64ip._demo(type: DeviceType.esb64npipm, name: name, ipAddress: ipAddress, version: version);

  // endregion

  // region [PRIVATE FUNCTIONS]

  /// Create a demo ESB62 device
  factory Esb64ip._demo({required DeviceType type, required String ipAddress, required String version, String? name}) {
    // Create demo name
    final demoName = name ?? type.demoName;

    // Delete old device logins
    unawaited(GetIt.I.get<SecureStorage>().deleteDeviceLogins(ipAddress));

    // Create and return demo device
    final demoData = Esb64ip.createDemoData(deviceType: type, identifier: ipAddress);
    return Esb64ip(
      name: demoName,
      connection: WifiDemoConnection(config: demoData, connectionInfo: WifiConnectionInfo.empty(ipAddress: ipAddress)),
      identifier: ipAddress,
      model: type.name,
    )..setDemoData(demoData, name: name, identifier: ipAddress, version: version);
  }

  // endregion
}
