//
//  file_sharing_manager.dart
//  EltakoConnect
//
//  Created by <PERSON> on 10.04.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:io';

import 'package:eltako_connect/l10n/app_localizations_extension.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:share_plus/share_plus.dart';

/// Manager for sharing files
class FileSharingManager {
  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  const FileSharingManager();

  /// Share logging database
  Future<VoidResult> shareLoggingDatabase() async {
    try {
      // Find log file
      final logFile = File(await LogDatabase.path);
      if (!logFile.existsSync()) throw FileSystemException('Logfile not found', logFile.path);

      final params = ShareParams(
        files: [XFile.fromData(logFile.readAsBytesSync(), path: logFile.path)],
        subject: 'Eltako Log Database',
      );

      await SharePlus.instance.share(params);
    } catch (e) {
      final message = 'Sharing logging database failed: $e';
      error(message, category: LogCategory.file);
      return VoidResult.error(message: message);
    }

    /// Sharing done
    return const VoidResult.success();
  }

  /// Share config file
  Future<DataResult<ShareResult>> shareConfigFile({required File file, required String fileName}) async {
    try {
      final fileExtension = file.path.split('.').last;

      // 'subject' is only used when sharing to email
      final params = ShareParams(
        files: [XFile.fromData(file.readAsBytesSync(), mimeType: 'application/yaml')],
        fileNameOverrides: ['$fileName.$fileExtension'],
        subject: l10n.shareConfiguration(fileName),
      );

      final shareFilesResult = await SharePlus.instance.share(params);

      return DataResult.success(data: shareFilesResult);
    } catch (e) {
      final message = 'Error while reading exporting config file: $e';
      error(message, category: LogCategory.file);
      return DataResult.error(message: message);
    }
  }

  // endregion
}
