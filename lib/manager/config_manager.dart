//
//  config_manager.dart
//  EltakoConnect
//
//  Created by <PERSON> on 30.01.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:io';

import 'package:eltako_connect/enumeration/localized_error.dart';
import 'package:eltako_connect/extension/yaml_map_extension.dart';
import 'package:eltako_connect/model/config_info.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/service/config_service/config_data.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:path_provider/path_provider.dart';
import 'package:yaml/yaml.dart';

/// Manager for device configurations
class ConfigManager {
  // region [CONSTANTS]

  /// Config file directory
  static const String _directory = 'config';

  /// Config file extension
  static const String _extension = 'elc';

  // endregion

  // region [COMPUTED PROPERTIES]

  /// Get config directory path
  Future<DataResult<String>> get path async {
    try {
      // Get application directory
      return DataResult.success(data: '${(await getApplicationDocumentsDirectory()).path}/$_directory');
    } catch (e) {
      const message = 'Application documents directory not available on this platform';
      error('$message: $e');
      return const DataResult.error(message: message, error: LocalizedError.notSupported);
    }
  }

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Get stored config files
  Future<DataResult<List<ConfigInfo>>> getInfoList() async {
    try {
      // Get directory path
      final pathResult = await this.path;
      final path = pathResult.data;
      if (path == null) return DataResult.error(message: pathResult.message, error: pathResult.error);

      // Get directory
      final dir = Directory(path);
      if (!dir.existsSync()) {
        // No configs found
        return const DataResult.success(message: 'Found no config files', data: []);
      }

      // Get config files
      final files = await dir.list().toList();
      final configFiles = files.where((file) => file.path.endsWith(_extension)).toList();

      // Get config file information
      final configs = <ConfigInfo>[];
      for (final file in configFiles) {
        // Read config
        final yaml = loadYaml(await File(file.path).readAsString()) as YamlMap;
        final config = ConfigData.fromMap(yaml.toMap());
        final info = ConfigInfo.fromConfigData(config, fileId: file.path.split('/').last.split('.').first);

        // Add config info
        configs.add(info);
      }

      // Return config file information
      return DataResult.success(message: 'Found ${configs.length} config files', data: configs);
    } catch (e) {
      const message = 'Could not get stored configs';
      error('$message: $e');
      return const DataResult.error(message: message, error: LocalizedError.notReadable);
    }
  }

  /// Get all config files
  Future<DataResult<List<ConfigData>>> getConfigs() async {
    try {
      // Get directory path
      final pathResult = await this.path;
      final path = pathResult.data;
      if (path == null) return DataResult.error(message: pathResult.message, error: pathResult.error);

      // Get directory
      final dir = Directory(path);
      if (!dir.existsSync()) throw Exception('Found no config files');

      // Get config files
      final files = await dir.list().toList();
      final configFiles = files.where((file) => file.path.endsWith(_extension)).toList();

      // Parse config files
      final configs = <ConfigData>[];
      for (final file in configFiles) {
        final content = await File(file.path).readAsString();
        final config = ConfigData.fromYaml(content);
        if (config != null) configs.add(config);
      }

      // Return config file information
      return DataResult.success(message: 'Found ${configs.length} config files', data: configs);
    } catch (e) {
      const message = 'Could not get stored configs';
      error('$message: $e');
      return const DataResult.error(message: message, error: LocalizedError.notReadable);
    }
  }

  /// Get config file for given [fileId]
  Future<DataResult<File>> getConfigFile(String fileId) async {
    try {
      // Get directory path
      final pathResult = await this.path;
      final path = pathResult.data;
      if (path == null) return DataResult.error(message: pathResult.message, error: pathResult.error);

      // Get file
      final file = File('$path/$fileId.$_extension');
      if (!file.existsSync()) throw Exception('Config file not found');

      return DataResult.success(data: file);
    } catch (e) {
      const message = 'Could not get config file';
      error('$message: $e');
      return const DataResult.error(message: message, error: LocalizedError.notReadable);
    }
  }

  /// Read config file content for given [fileId]
  Future<DataResult<String>> _readConfigContent(String fileId) async {
    final fileResult = await getConfigFile(fileId);
    final file = fileResult.data;
    if (file == null) {
      return DataResult.error(message: fileResult.message, error: fileResult.error);
    }
    return DataResult.success(data: await file.readAsString());
  }

  /// Get config data for given [fileId]
  Future<DataResult<ConfigData>> load(String fileId) async {
    try {
      // Read config from file
      final contentResult = await _readConfigContent(fileId);
      final content = contentResult.data;

      if (content == null) {
        return DataResult.error(message: contentResult.message, error: contentResult.error);
      }

      final config = ConfigData.fromYaml(content);
      if (config == null) throw Exception('Could not parse config data from file');
      return DataResult.success(data: config);
    } catch (e) {
      const message = 'Could not get config data';
      error('$message: $e');
      return const DataResult.error(message: message, error: LocalizedError.notReadable);
    }
  }

  /// Get config content as YAML string for given [fileId]
  Future<DataResult<String>> getAsYaml(String fileId) async {
    try {
      // Read config from file
      final contentResult = await _readConfigContent(fileId);
      final content = contentResult.data;

      if (content == null) {
        return DataResult.error(message: contentResult.message, error: contentResult.error);
      }

      return DataResult.success(data: content);
    } catch (e) {
      const message = 'Could not get config content';
      error('$message: $e');
      return const DataResult.error(message: message, error: LocalizedError.notReadable);
    }
  }

  /// Export config [data] to file and return file ID (optionally override given [fileId])
  Future<DataResult<String>> save(ConfigData data, {String? fileId}) async {
    // Write config to file
    try {
      // Get directory path
      final pathResult = await this.path;
      final path = pathResult.data;
      if (path == null) return DataResult.error(message: pathResult.message, error: pathResult.error);

      // Create file
      final id = fileId ?? DateTime.now().millisecondsSinceEpoch.toString();
      final file = File('$path/$id.$_extension');
      await file.create(recursive: true);

      // Create and write YAML
      await file.writeAsString(data.toConfigYaml());
      return DataResult.success(message: 'Config written! File ID: $fileId', data: fileId);
    } catch (e) {
      const message = 'Could not export config to file';
      error('$message: $e');
      return const DataResult.error(message: message, error: LocalizedError.notReadable);
    }
  }

  /// Rename config file with given [fileId] to [name]
  Future<VoidResult> rename(String fileId, {required String name}) async {
    // Get config
    final configResult = await load(fileId);
    final config = configResult.data;
    if (config == null) return VoidResult.error(message: configResult.message, error: configResult.error);

    // Rename config
    config
      ..name = name
      ..lastUpdate = DateTime.now();

    // Save config
    return save(config, fileId: fileId);
  }

  /// Delete config file with given [fileId]
  Future<VoidResult> delete(String fileId) async {
    try {
      // Get directory path
      final pathResult = await this.path;
      final path = pathResult.data;
      if (path == null) return VoidResult.error(message: pathResult.message, error: pathResult.error);

      // Get file
      final file = File('$path/$fileId.$_extension');
      if (!file.existsSync()) throw Exception('Config file not found');

      // Delete file
      await file.delete();
      return VoidResult.success(message: 'Config file $fileId deleted');
    } catch (e) {
      const message = 'Could not delete config file';
      warning('$message: $e');
      return const VoidResult.error(message: message, error: LocalizedError.notReadable);
    }
  }

  /// Delete all config files
  Future<VoidResult> reset() async {
    // Get configs
    final configsResult = await getInfoList();
    final configs = configsResult.data;
    if (configs == null) return VoidResult.error(message: configsResult.message, error: configsResult.error);

    // Delete configs
    for (final config in configs) {
      final result = await delete(config.fileId);
      if (!result.success) return result;
    }

    // Return result
    return const VoidResult.success(message: 'All config files deleted');
  }

  // endregion
}
