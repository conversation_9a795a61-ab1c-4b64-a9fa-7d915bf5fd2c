//
//  eltako_router.dart
//  EltakoConnect
//
//  Created by <PERSON> on 30.01.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:async';

import 'package:eltako_connect/enumeration/service_key.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/manager/device_manager.dart';
import 'package:eltako_connect/model/config_info.dart';
import 'package:eltako_connect/release_notes/release_notes.dart';
import 'package:eltako_connect/release_notes/release_notes_view.dart';
import 'package:eltako_connect/view/configurations_view/configurations_view.dart';
import 'package:eltako_connect/view/configurations_view/configurations_view_model.dart';
import 'package:eltako_connect/view/configurations_view/widgets/configuration_detail.dart';
import 'package:eltako_connect/view/demo_devices_view/demo_devices_view.dart';
import 'package:eltako_connect/view/demo_devices_view/demo_devices_view_model.dart';
import 'package:elta<PERSON>_connect/view/device_detail_view.dart';
import 'package:eltako_connect/view/discovery_view/discovery_view.dart';
import 'package:eltako_connect/view/discovery_view/discovery_view_model.dart';
import 'package:eltako_connect/view/service/channel_service/i_channel_service_view_model.dart';
import 'package:eltako_connect/view/service/network_service/i_network_service_view_model.dart';
import 'package:eltako_connect/view/settings/settings_view.dart';
import 'package:eltako_connect/view/settings/settings_view_model.dart';
import 'package:eltako_connect/view/settings/views/imprint_view.dart';
import 'package:eltako_connect/view/whats_new_view/whats_new_view.dart';
import 'package:eltako_connect/view/whats_new_view/whats_new_view_model.dart';
import 'package:eltako_connect/widget/channel/channel_edit_view.dart';
import 'package:eltako_connect/widget/network/network_edit_view.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

/// Eltako router
class EltakoRouter {
  // region Route names
  /// Discovery route name
  static const String discoveryRouteName = 'discovery';

  /// Settings route name
  static const String settingsRouteName = 'settings';

  /// Release notes route name
  static const String releaseNotesRouteName = 'release_notes';

  /// Release note route name
  static const String releaseNoteRouteName = 'release_note';

  /// Device route name
  static const String deviceRouteName = 'device';

  /// Service route name
  static const String serviceRouteName = 'service';

  /// Channel edit route name
  static const String channelEditRouteName = 'channel_edit';

  /// Network edit route name
  static const String networkEditRouteName = 'network_edit';

  /// Imprint route name
  static String imprintRouteName = 'imprint';

  /// Demo devices route name
  static String demoDevicesRouteName = 'demo_devices';

  /// Device route name
  static const String demoDeviceRouteName = 'demo_device';

  /// Service route name
  static const String demoServiceRouteName = 'demo_service';

  /// Channel edit route name
  static const String demoChannelEditRouteName = 'demo_channel_edit';

  /// Network edit route name
  static const String demoNetworkEditRouteName = 'demo_network_edit';

  /// Configurations route name
  static String configurationsRouteName = 'configurations';

  /// Configurations route name
  static String configurationEditRouteName = 'configuration_edit';

  /// Provisioning route name
  static String provisioningRouteName = 'provisioning';

  static final _configurationsViewModel = ConfigurationsViewModel();

  // endregion

  // region Router
  /// The route configuration.
  static GoRouter router = GoRouter(
    errorBuilder: (context, state) {
      error('Error: ${state.error}');
      return Scaffold(body: Center(child: Text(AppLocalizations.of(context).error)));
    },
    initialLocation: '/',
    routes: [
      GoRoute(
        name: discoveryRouteName, // Optional, add name to your routes. Allows you navigate by name instead of path
        path: '/',
        builder: (context, state) => DiscoveryView(viewModel: DiscoveryViewModel(GetIt.instance<DeviceManager>())),
        routes: [
          // GoRoute(
          //   name: provisioningRouteName,
          //   path: provisioningRouteName,
          //   builder: (context, state) => ProvisioningView(viewModel: ProvisioningViewModel()),
          // ),
          GoRoute(
            name: settingsRouteName,
            path: settingsRouteName,
            builder: (context, state) => SettingsView(viewModel: SettingsViewModel()),
            routes: [
              GoRoute(
                name: configurationsRouteName,
                path: configurationsRouteName,
                builder: (context, state) => ConfigurationsView(viewModel: _configurationsViewModel),
                routes: [
                  GoRoute(
                    name: configurationEditRouteName,
                    path: configurationEditRouteName,
                    builder: (context, state) {
                      final config = state.extra as ConfigInfo?;
                      if (config == null) return const SizedBox();
                      return ConfigurationDetail(config: config, viewModel: _configurationsViewModel);
                    },
                  ),
                ],
              ),
              GoRoute(
                name: releaseNotesRouteName,
                path: releaseNotesRouteName,
                builder: (context, state) => const ReleaseNotesView(),
                routes: [
                  GoRoute(
                    name: releaseNoteRouteName,
                    path: '$releaseNoteRouteName/:version',
                    builder: (context, state) {
                      final version = state.pathParameters['version'];
                      if (version == null) return const SizedBox();
                      final whatsNewVersion =
                          ReleaseNotes.updates.where((element) => element.releaseName == version).firstOrNull;
                      return WhatsNewView(
                        allowBack: true,
                        viewModel: WhatsNewViewModel(whatsNewVersion: whatsNewVersion),
                      );
                    },
                  ),
                ],
              ),
              GoRoute(name: imprintRouteName, path: imprintRouteName, builder: (context, state) => const ImprintView()),
              GoRoute(
                name: demoDevicesRouteName,
                path: demoDevicesRouteName,
                builder: (context, state) => DemoDevicesView(viewModel: DemoDevicesViewModel()),
                routes: [
                  GoRoute(
                    name: demoDeviceRouteName,
                    path: EltakoRouter.deviceRouteName,
                    builder: (context, state) {
                      final manager = GetIt.instance<DeviceManager>();
                      if (manager.connectedDevice == null) {
                        return const SizedBox();
                      }
                      return DeviceDetailView(manager.connectedDevice!);
                    },
                    onExit: (context, state) {
                      unawaited(GetIt.instance<DeviceManager>().disconnect());
                      return true;
                    },
                    routes: [
                      GoRoute(
                        name: demoServiceRouteName,
                        path: '$serviceRouteName/:key',
                        builder: (context, state) {
                          final key = state.pathParameters['key'];
                          if (key == null) return const SizedBox();
                          final service =
                              GetIt.instance<DeviceManager>().connectedDevice!.services
                                  .where((element) => element.key == ServiceKey.fromKey(key))
                                  .firstOrNull;
                          if (service == null) return const SizedBox();
                          return service.getWidget();
                        },
                        routes: [
                          GoRoute(
                            name: demoChannelEditRouteName,
                            path: '$demoChannelEditRouteName/:channel',
                            builder: (context, state) {
                              final viewModel = state.extra as IChannelServiceViewModel?;
                              if (viewModel == null) {
                                error('viewModel is null');
                                context.replaceNamed('discovery');

                                throw const FormatException('viewModel is null');
                              }
                              final channel =
                                  viewModel.channels
                                      .where((element) => element.number == int.parse(state.pathParameters['channel']!))
                                      .firstOrNull;
                              if (channel == null) {
                                error('channel is null');
                                context.replaceNamed(demoDeviceRouteName);

                                throw const FormatException('channel is null');
                              }

                              return ChannelEditView(
                                channel: channel,
                                supportedOperationModes: viewModel.supportedOperationModes,
                                onSave: (channel) async {
                                  await viewModel.setChannel(channel, context);
                                },
                              );
                            },
                          ),
                          GoRoute(
                            name: demoNetworkEditRouteName,
                            path: '$demoNetworkEditRouteName/:stationKey',
                            builder: (context, state) {
                              final viewModel = state.extra as INetworkServiceViewModel?;
                              if (viewModel == null) {
                                error('viewModel is null');
                                context.replaceNamed(demoDeviceRouteName);

                                throw const FormatException('viewModel is null');
                              }
                              final station =
                                  viewModel.stations
                                      .where((element) => element.type.key == state.pathParameters['stationKey'])
                                      .firstOrNull;
                              if (station == null) {
                                error('Station is null');
                                context.replaceNamed(deviceRouteName);
                                throw const FormatException('Station is null');
                              }
                              return NetworkEditView(viewModel: viewModel, stationKey: station.type.key);
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          GoRoute(
            name: deviceRouteName,
            path: deviceRouteName,
            builder: (context, state) {
              final manager = GetIt.instance<DeviceManager>();
              if (manager.connectedDevice == null) {
                return const SizedBox();
              }
              return DeviceDetailView(manager.connectedDevice!);
            },
            onExit: (context, state) {
              unawaited(GetIt.instance<DeviceManager>().disconnect());
              return true;
            },
            routes: [
              GoRoute(
                name: serviceRouteName,
                path: '$serviceRouteName/:key',
                builder: (context, state) {
                  final key = state.pathParameters['key'];
                  if (key == null) return const SizedBox();
                  final manager = GetIt.instance<DeviceManager>();
                  if (manager.connectedDevice == null) {
                    return const SizedBox();
                  }
                  final service =
                      manager.connectedDevice!.services
                          .where((element) => element.key == ServiceKey.fromKey(key))
                          .firstOrNull;
                  if (service == null) return const SizedBox();
                  return service.getWidget();
                },
                routes: [
                  GoRoute(
                    name: channelEditRouteName,
                    path: '$channelEditRouteName/:channel',
                    builder: (context, state) {
                      final viewModel = state.extra as IChannelServiceViewModel?;
                      if (viewModel == null) {
                        error('viewModel is null');
                        context.replaceNamed('discovery');

                        throw const FormatException('viewModel is null');
                      }
                      final channel =
                          viewModel.channels
                              .where((element) => element.number == int.parse(state.pathParameters['channel']!))
                              .firstOrNull;
                      if (channel == null) {
                        error('channel is null');
                        context.replaceNamed(deviceRouteName);

                        throw const FormatException('channel is null');
                      }

                      return ChannelEditView(
                        channel: channel,
                        supportedOperationModes: viewModel.supportedOperationModes,
                        onSave: (channel) async => viewModel.setChannel(channel, context),
                      );
                    },
                  ),
                  GoRoute(
                    name: networkEditRouteName,
                    path: '$networkEditRouteName/:stationKey',
                    builder: (context, state) {
                      final viewModel = state.extra as INetworkServiceViewModel?;
                      if (viewModel == null) {
                        error('viewModel is null');
                        context.replaceNamed(deviceRouteName);

                        throw const FormatException('viewModel is null');
                      }
                      final station =
                          viewModel.stations
                              .where((element) => element.type.key == state.pathParameters['stationKey'])
                              .firstOrNull;
                      if (station == null) {
                        error('Station is null');
                        context.replaceNamed(deviceRouteName);
                        throw const FormatException('Station is null');
                      }
                      return NetworkEditView(viewModel: viewModel, stationKey: station.type.key);
                    },
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    ],
  );

  // endregion
}
