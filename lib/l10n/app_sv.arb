{"appName": "ELTAKO Connect", "discoveryHint": "Aktivera Bluetooth på enheten för att ansluta", "devicesFound": "{count, plural, =0 {inga enheter hittades} one {1 enhet hittades} other {{count} enheter hittades}}", "@devicesFound": {"description": "Specifies how many device are found in the discovery overview", "type": "text"}, "discoveryDemodeviceName": "{count, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "discoverySu12Description": "2-ka<PERSON><PERSON><PERSON> kop<PERSON><PERSON>r Bluetooth", "discoveryImprint": "Impressum", "discoveryLegalnotice": "Dataskydd", "generalSave": "Spara", "generalCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detailsHeaderHardwareversion": "Maskinvaruversion", "detailsHeaderSoftwareversion": "Programvaruversion", "detailsHeaderConnected": "Ans<PERSON>en", "detailsHeaderDisconnected": "Frånkopplad", "detailsTimersectionHeader": "Program", "detailsTimersectionTimercount": "av 60 använda program", "detailsConfigurationsectionHeader": "Konfiguration", "detailsConfigurationPin": "PIN-kod för enheten", "detailsConfigurationChannelsDescription": "Kanal 1: {channel1} | Kanal 2:  {channel2}", "settingsCentralHeader": "Centralt På/Av", "detailsConfigurationCentralDescription": "G<PERSON><PERSON> endast om kanalen är inställd på AUTO", "detailsConfigurationDevicedisplaylock": "Lås display", "timerOverviewHeader": "Program", "timerOverviewTimersectionTimerinactivecount": "inaktiv", "timerDetailsListsectionDays1": "<PERSON><PERSON><PERSON><PERSON>", "timerDetailsListsectionDays2": "Tisdag", "timerDetailsListsectionDays3": "Onsdag", "timerDetailsListsectionDays4": "Torsdag", "timerDetailsListsectionDays5": "Fred<PERSON>", "timerDetailsListsectionDays6": "<PERSON><PERSON><PERSON><PERSON>", "timerDetailsListsectionDays7": "S<PERSON><PERSON>g", "timerDetailsHeader": "Program", "timerDetailsSunrise": "Soluppgång", "generalToggleOff": "Av", "generalToggleOn": "På", "timerDetailsImpuls": "<PERSON><PERSON><PERSON>", "generalTextTime": "Tid", "generalTextAstro": "Astro", "generalTextAuto": "Auto", "timerDetailsOffset": "Tidsförskjutning", "timerDetailsPlausibility": "Aktivera <PERSON>kontroll", "timerDetailsPlausibilityDescription": "Om AV är inställd på en tidigare tidpunkt än PÅ ignoreras båda programmen, t.ex. om du slår PÅ vid soluppgången och AV klockan 6:00. Det finns också tillfällen där kontrollen inte önskas, t.ex. slå PÅ vid solnedgång och AV vid 1:00 på natten.", "generalDone": "<PERSON><PERSON>", "generalDelete": "<PERSON><PERSON><PERSON>", "timerDetailsImpulsDescription": "<PERSON><PERSON> impulstidens konfiguration", "settingsNameHeader": "<PERSON><PERSON><PERSON> namn", "settingsNameDescription": "Namnet används för identifiering av enheten .", "settingsFactoryresetHeader": "Fabriksåterställning", "settingsFactoryresetDescription": "Vilket innehåll ska återställas?", "settingsFactoryresetResetbluetooth": "Återställa Bluetooth-inställningar", "settingsFactoryresetResettime": "Återställer tidsinställningar", "settingsFactoryresetResetall": "Fabriksåterställning", "settingsDeletetimerHeader": "Radera program", "settingsDeletetimerDescription": "Ska verkligen alla program raderas?", "settingsDeletetimerAllchannels": "<PERSON>a kanaler", "settingsImpulseHeader": "<PERSON><PERSON><PERSON><PERSON>", "settingsImpulseDescription": "<PERSON><PERSON><PERSON><PERSON> ställer in längden på pulsen.", "generalTextRandommode": "Slumpvalsläge", "settingsChannelsTimeoffsetHeader": "Förskjutning av solståndstiden", "settingsChannelsTimeoffsetDescription": "Sommar: {summerOffset}min | Vinter: {winterOffset}min", "settingsLocationHeader": "Plats", "settingsLocationDescription": "St<PERSON>ll in din plats för att använda astrofunktioner.", "settingsLanguageHeader": "Språk för <PERSON>", "settingsLanguageSetlanguageautomatically": "St<PERSON>ll in språket automatiskt", "settingsLanguageDescription": "<PERSON><PERSON><PERSON><PERSON> spr<PERSON>k för {deviceType}", "settingsLanguageGerman": "Tyska", "settingsLanguageFrench": "Franska", "settingsLanguageEnglish": "Engelska", "settingsLanguageItalian": "Italienska", "settingsLanguageSpanish": "Spanska", "settingsDatetimeHeader": "Datum och tid", "settingsDatetimeSettimeautomatically": "Tillämpa systemtid", "settingsDatetimeSettimezoneautomatically": "Ställ in tidszonen automatiskt", "generalTextTimezone": "T<PERSON><PERSON>", "settingsDatetime24Hformat": "24-timmarsformat", "settingsDatetimeSetsummerwintertimeautomatically": "Automatisk sommar-vintertid", "settingsDatetimeWinter": "Vinter", "settingsDatetimeSummer": "<PERSON><PERSON><PERSON>", "settingsPasskeyHeader": "Aktuell PIN-kod för enheten", "settingsPasskeyDescription": "Ange PIN-koden för enheten", "timerDetailsActiveprogram": "Programmet är aktivt", "timerDetailsActivedays": "Aktiva dagar", "timerDetailsSuccessdialogHeader": "Framgångsrik", "timerDetailsSuccessdialogDescription": "Programmet har lagts till", "settingsRandommodeDescription": "Slumpvalsläget fungerar endast för tidsbaserade program, inte för puls- eller astrobaserade program (soluppgång eller solnedgång). Tiderna förskjuts med upp till 15 minuter.", "settingsSolsticeHeader": "Solstånd tidsförskjutning.", "settingsSolsticeDescription": "Tiden ger en tidsförskjutning av solnedgången. Soluppgången inverteras motsvarande.", "settingsSolsticeHint": "Exempel:\nPå vintern förskjuts tiden till 30 minuter före solnedgången, och till 30 minuter före soluppgången.", "generalTextMinutesShort": "min", "settingsPinDescription": "PIN-koden krävs för an<PERSON>.", "settingsPinHeader": "PIN-kod för en ny enhet", "settingsPinNewpinDescription": "Ange en ny PIN-kod", "settingsPinNewpinRepeat": "Upprepa PIN-koden", "detailsProductinfo": "Produktinformation", "settingsDatetimeSettimeautodescription": "Välj önskad tid", "minutes": "{count, plural, one {Minut} other {Minuter}}", "hours": "{count, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON>}}", "seconds": "{count, plural, one {Sekund} other {Sekunder}}", "generalTextChannel": "{count, plural, one {<PERSON>nal} other {<PERSON><PERSON><PERSON>}}", "generalLabelChannel": "Kanal {number}", "generalTextDate": "Datum", "settingsDatetime24HformatDescription": "Välj önskat format", "settingsDatetimeSetsummerwintertime": "Sommar-/<PERSON><PERSON><PERSON><PERSON>", "settingsDatetime24HformatValue24": "24h", "settingsDatetime24HformatValue12": "AM/PM", "detailsEdittimer": "Redigera program", "settingsPinOldpinRepeat": "Upprepa den aktuella PIN-koden", "settingsPinCheckpin": "PIN-koden kontrolleras", "detailsDevice": "{count, plural, one {enhet} other {enheter}}", "detailsDisconnect": "<PERSON><PERSON><PERSON>", "settingsCentralDescription": "Ingången A1 styr Centralt På/Av.\nCentralt På/Av gäller endast för kanal som är inställd på centralt På/Av.", "settingsCentralHint": "Exempel:\nKanal 1 = Centralt På/Av\nKanal 2 = Av\nA1 = Centralt På -> Endast C1 växlar till På, C2 förblir Av.", "settingsCentralToggleheader": "Central styringång", "settingsCentralActivechannelsdescription": "Aktuella kanaler med inställningen Centralt På/Av:", "settingsSolsticeSign": "Tecken", "settingsDatetimeTimezoneDescription": "Centraleuropeisk tid", "generalButtonContinue": "Fortsätt", "settingsPinConfirmationDescription": "PIN-koden har <PERSON>", "settingsPinFailDescription": "PIN-kodsändringen misslyckades", "settingsPinFailHeader": "<PERSON><PERSON><PERSON><PERSON>", "settingsPinFailShort": "PIN-koden måste vara exakt 6 siffror lång.", "settingsPinFailWrong": "PIN-koden är felaktig", "settingsPinFailMatch": "PIN-koderna stämmer inte överens", "discoveryLostconnectionHeader": "Förbindelsen har brutits", "discoveryLostconnectionDescription": "Förbindelsen med enheten har brutits.", "settingsChannelConfigCentralDescription": "Uppför sig som AUTO och styrs även av de trådbundna centralstyringångarna", "settingsChannelConfigOnDescription": "Kopplar om kanalen till permanent PÅ och ignorerar programmen", "settingsChannelConfigOffDescription": "Kopplar om kanalen till permanent AV och ignorerar programmen", "settingsChannelConfigAutoDescription": "Kopplar enligt tids- och astroprogrammen", "bluetoothPermissionDescription": "Bluetooth krävs för att konfigurera enheterna.", "timerListitemOn": "Slå på", "timerListitemOff": "Stänga av", "timerListitemUnknown": "Okä<PERSON>", "timerDetailsAstroHint": "P<PERSON>sen måste anges i inställningarna för att astroprogrammen ska fungera korrekt.", "timerDetailsTrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timerDetailsSunset": "Solnedgång", "settingsLocationCoordinates": "Koordinater", "settingsLocationLatitude": "Latitud", "settingsLocationLongitude": "<PERSON><PERSON><PERSON>", "timerOverviewEmptyday": "Inga program används för nä<PERSON>rande för {day}", "timerOverviewProgramloaded": "<PERSON>n laddas", "timerOverviewProgramchanged": "<PERSON>t har <PERSON>", "settingsDatetimeProcessing": "<PERSON><PERSON> och tid ä<PERSON>s", "deviceNameEmpty": "Inmatningen får inte vara tom.", "deviceNameHint": "Inmatningen får inte innehålla mer än {count} tecken.", "deviceNameChanged": "En<PERSON>tens namn ändras", "deviceNameChangedSuccessfully": "Enhetens namn har ä<PERSON>.", "deviceNameChangedFailed": "Ett fel har inträffat.", "settingsPinConfirm": "Bekräfta", "deviceShowInstructions": "1. Aktivera enhetens Bluetooth med SET\n2. <PERSON>ck på knappen högst upp för att starta sökningen.", "deviceNameNew": "Ange ett nytt enhetsnamn", "settingsLanguageRetrieved": "Språket hämtas", "detailsProgramsShow": "Visa program", "generalTextProcessing": "Vänta lite", "generalTextRetrieving": "hämtas.", "settingsLocationPermission": "Tillåt ELTAKO Connect att få tillgång till den här enhetens plats", "timerOverviewChannelloaded": "<PERSON><PERSON><PERSON>", "generalTextRandommodeChanged": "Slumpvalsläget har ä<PERSON>", "detailsConfigurationsectionChanged": "Konfiguration<PERSON> ä<PERSON>", "settingsSettimeFunctions": "Tidsfunktioner ändras", "imprintContact": "Kontakt", "imprintPhone": "Telefon", "imprintMail": "E-post", "imprintRegistrycourt": "<PERSON><PERSON><PERSON><PERSON> myndi<PERSON>", "imprintRegistrynumber": "Registreringsnummer", "imprintCeo": "Verkställande direktör", "imprintTaxnumber": "Organisationsnummer", "settingsLocationCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON> plats", "generalTextReset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "discoverySearchStart": "<PERSON><PERSON>", "discoverySearchStop": "Stoppa sökningen", "settingsImpulsSaved": "Pulskopplingstiden lagras", "settingsCentralNochannel": "Det finns inga kanaler med inställningen Centralt På/Av.", "settingsFactoryresetBluetoothConfirmationDescription": "Bluetooth-anslutningen har å<PERSON>ällts.", "settingsFactoryresetBluetoothFailDescription": "Återställning av Bluetooth-anslutningar misslyckades.", "imprintPublisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "discoveryDeviceConnecting": "Anslutning upprättas", "discoveryDeviceRestarting": "Omstart...", "generalTextConfigurationsaved": "Kanalkonfigurationen har sparats.\n", "timerOverviewChannelssaved": "Spara kanaler", "timerOverviewSaved": "Timer sparad\n", "timerSectionList": "<PERSON><PERSON>\n", "timerSectionDayview": "<PERSON><PERSON><PERSON>", "generalTextChannelInstructions": "Kanalinställningar", "generalTextPublisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsDeletetimerDialog": "Vill du verkligen radera alla program?", "settingsFactoryresetResetbluetoothDialog": "Vill du verkligen återställa alla Bluetooth-inställningar?", "settingsCentralTogglecentral": "Centralt\nPå/Av", "generalTextConfirmation": "{serviceName} har <PERSON><PERSON><PERSON>.", "generalTextFailed": "{serviceName} ändringen misslyckades.", "settingsChannelConfirmationDescription": "<PERSON><PERSON><PERSON> har <PERSON>.", "timerDetailsSaveHeader": "Spara programmet", "timerDetailsDeleteHeader": "Radera programmet", "timerDetailsSaveDescription": "Programmet har sparats.", "timerDetailsDeleteDescription": "Programmet raderades.", "timerDetailsAlertweekdays": "Programmet kan inte sparas eftersom inga veckodagar har valts.", "generalTextOk": "OK", "settingsDatetimeChangesuccesfull": "<PERSON><PERSON> och tid har <PERSON>.", "discoveryConnectionFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> misslyckades", "discoveryDeviceResetrequired": "Ingen anslutning kunde upprättas med enheten. F<PERSON>r att lösa problemet raderar du enheten i Bluetooth-inställningarna. Om problemet kvarstår, kontakta vår tekniska support.", "generalTextSearch": "<PERSON><PERSON><PERSON> enheter", "generalTextOr": "eller", "settingsFactoryresetProgramsConfirmationDescription": "Alla program har raderats.", "generalTextManualentry": "<PERSON><PERSON> in<PERSON>ning", "settingsLocationSaved": "<PERSON><PERSON><PERSON> sparad", "settingsLocationAutosearch": "Sök plats automatiskt", "imprintPhoneNumber": "+49 711 / 9435 0000", "imprintMailAddress": "<EMAIL>", "settingsFactoryresetResetallDialog": "Vill du verkligen göra en fabriksåterställning?", "settingsFactoryresetFactoryConfirmationDescription": "<PERSON><PERSON>ten har blivit fabriksåterställd.", "settingsFactoryresetFactoryFailDescription": "Återställningen av enheten misslyckades.", "imprintPhoneNumberIos": "+49711/94350000", "mfzFunctionA2Title": "Tillslagsfördröjning 2-stegs (A2)", "mfzFunctionA2TitleShort": "Tillslagsfördröjning 2-stegs (A2)", "mfzFunctionA2Description": "När styrspänningen ansluts startar tidräkningen t1 mellan 0 och 60 sekunder. Efter nedräknigen sluts kontakten 1-2 och tidräkningen t2 mellan 0 och 60 sekunder börjar. I slutet av denna tid sluts kontakten 3-4. Vid avbrott påbörjas tidsförloppet på nytt med t1.", "mfzFunctionRvTitle": "Frånslagsfördr<PERSON>jning (RV)", "mfzFunctionRvTitleShort": "RV | Frånslagsfördröjning", "mfzFunctionRvDescription": "<PERSON><PERSON>r styrspänningen ansluts sluter kontakten 15-18. \n<PERSON><PERSON>ts styrspänningen så påbörjas tidräkningen, efter vilken kontakten åter bryter. Ka<PERSON>ä<PERSON> under tidräkningen.", "mfzFunctionTiTitle": "<PERSON><PERSON><PERSON>g<PERSON><PERSON><PERSON>d gång vid start (TI)", "mfzFunctionTiTitleShort": "TI | Paus-gångtid gång vid start", "mfzFunctionTiDescription": "<PERSON><PERSON><PERSON> forsätter så länge styrspänningen är på. De båda tiderna kan ställas in separat. När styrspänningen ansluts sluter kontakten 15-18 o<PERSON><PERSON><PERSON><PERSON>.", "mfzFunctionAvTitle": "Tillslagsfördröjning (AV)", "mfzFunctionAvTitleShort": "AV | Tillslagsfördröjning", "mfzFunctionAvDescription": "<PERSON><PERSON><PERSON> styrspänningen ansluts påbörjas tidräkningen, efter nedräkningen sluter kontakten 15-18. Efter avbrott av styrspänningen börjar tidsförloppet på nytt.", "mfzFunctionAvPlusTitle": "Tillslagsfördröjning summering (AV+)", "mfzFunctionAvPlusTitleShort": "AV+ | Tillslagsfördröjning summering", "mfzFunctionAvPlusDescription": "Fungerar som AV. Men vid brytning av styrspänningen sparas den aktuella tiden.", "mfzFunctionAwTitle": "Puls vid frånslag (AW)", "mfzFunctionAwTitleShort": "AW | puls vid frånslag", "mfzFunctionAwDescription": "<PERSON><PERSON><PERSON> styrspänningen bryts växlar kontakten och sluter 15-18, bryter igen när den inställda tiden har gått ut. Om styrspänningen läggs på under pulstiden återgår kontakten omedelbart till viloläget och den återstående tiden nollställs.", "mfzFunctionIfTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> (IF)", "mfzFunctionIfTitleShort": "IF | Pulsfilter", "mfzFunctionIfDescription": "<PERSON><PERSON><PERSON> styrspänningen ansluts sluter kontakten 15-18 under den inställda tiden. Pulser under p<PERSON><PERSON><PERSON><PERSON><PERSON> tid<PERSON><PERSON>.", "mfzFunctionEwTitle": "<PERSON><PERSON><PERSON> vid tillslag (EW)", "mfzFunctionEwTitleShort": "EW | Puls vid tillslag", "mfzFunctionEwDescription": "<PERSON><PERSON><PERSON> styrspänningen ansluts sluter kontakten 15-18 under den inställda tiden. Om styrspänningen försvinner under pulstiden återgår kontakten omedelbart till viloläget och den återstående tiden nollställs.", "mfzFunctionEawTitle": "P<PERSON>s vid till- och frånslag (EAW)", "mfzFunctionEawTitleShort": "EAW | Puls vid till- och frånslag", "mfzFunctionEawDescription": "<PERSON><PERSON><PERSON> styrspänningen ansluts sluter kontakten 15-18 under den inställda tiden (t1). <PERSON><PERSON><PERSON> styrspänningen så sluter den igen inställd tid (t2).", "mfzFunctionTpTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> som börjar med paus (TP)", "mfzFunctionTpTitleShort": "TP | <PERSON><PERSON>-g<PERSON><PERSON><PERSON>d som börjar med paus", "mfzFunctionTpDescription": "Funktionsbeskrivningar som TI, men när styrspänningen ansluts förblir kontakten i läge 15-16.", "mfzFunctionIaTitle": "Impulsstyrd tillslagsfördröjning (IA)", "mfzFunctionIaTitleShort": "IA | Impulsstyrd tillslagsfördröjning", "mfzFunctionIaDescription": "<PERSON><PERSON>r styrspänningen ansluts bö<PERSON><PERSON> tidräkningen t1, när den har löpt ut sluter kontakten 15-18 och förblir där tiden t2  (t.ex. för automatiska dörröppnare). Om t1 är inställd på den kortaste tiden 0,1 s fungerar IA som en pulsfilter under t2 tiden.", "mfzFunctionArvTitle": "Till- och frånslagsfördröjning (ARV)", "mfzFunctionArvTitleShort": "ARV | Till- och frånslagsfördröjning", "mfzFunctionArvDescription": "<PERSON><PERSON><PERSON> styrspänningen ansluts bö<PERSON><PERSON> tid<PERSON>, nä<PERSON> den har löpt ut sluter kontakten 15-18. <PERSON><PERSON><PERSON> styrspänningen så påbörjas en ny nedräkning, efter vilken kontakten återgår till viloläget. Om styrspänningen bryts under nedräkning så påbörjas en ny tidräkning.", "mfzFunctionArvPlusTitle": "Till- och frånslagsfördröjning, summering (ARV+)", "mfzFunctionArvPlusTitleShort": "ARV+ | Till- och frånslagsfördröjning, summering", "mfzFunctionArvPlusDescription": "<PERSON><PERSON><PERSON> som ARV, men om styrspänningen bryts så sparas den redan förflutna tiden.", "mfzFunctionEsTitle": "<PERSON><PERSON><PERSON><PERSON>relä (ES)", "mfzFunctionEsTitleShort": "ES | Impulsrelä", "mfzFunctionEsDescription": "Vid styrpulser längre än 50 ms växlar kontakten till och från.", "mfzFunctionEsvTitle": "Impulsrelä med frånslagsfördröjning och frånslagsvarning (ESV)", "mfzFunctionEsvTitleShort": "ESV | Impulsrelä med frånslagsfördröjning och frånslagsvarning", "mfzFunctionEsvDescription": "Fungerar som SRV. Dessutom med förvarning för frånslag: Ca 30 sekunder före tidens utgång blinkar belysningen tre gånger med minskande tidsintervall.", "mfzFunctionErTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ER)", "mfzFunctionErTitleShort": "ER | Arbetsströmrelä", "mfzFunctionErDescription": "<PERSON><PERSON> länge styrspänningen är ansluten växlar reläkontakten från 15-16 till 15-18.", "mfzFunctionSrvTitle": "Impulsrelä med frånslagsfördröjning (SRV)", "mfzFunctionSrvTitleShort": "SRV | Impulsrelä med frånslagsfördröjning", "mfzFunctionSrvDescription": "Vid styrimpulser längre än 50 ms växlar reläkontakten till och från. När reläkontakten har slutit 15-18 b<PERSON><PERSON><PERSON> tidräkningen automatiskt. Efter tidräkningen återgår reläet till viloläget.", "detailsFunctionsHeader": "<PERSON><PERSON><PERSON>", "mfzFunctionTimeHeader": "Tid (t{index})", "mfzFunctionOnDescription": "permanent <PERSON><PERSON>", "mfzFunctionOffDescription": "permanent Av", "mfzFunctionMultiplier": "<PERSON><PERSON><PERSON>", "discoveryMfz12Description": "Multifunktion tidrelä Bluetooth", "mfzFunctionOnTitle": "Permanent PÅ", "mfzFunctionOnTitleShort": "Permanent PÅ", "mfzFunctionOffTitle": "Permanent AV", "mfzFunctionOffTitleShort": "Permanent AV", "mfzMultiplierSecondsFloatingpoint": "0.1 sekunder", "mfzMultiplierMinutesFloatingpoint": "0.1 minuter", "mfzMultiplierHoursFloatingpoint": "0.1 timmar", "mfzOverviewFunctionsloaded": "<PERSON><PERSON><PERSON> laddas", "mfzOverviewSaved": "Funktion sparad", "settingsBluetoothHeader": "Bluetooth", "bluetoothChangedSuccessfully": "Bluetooth-inställningen har ändrats.", "settingsBluetoothInformation": "Observera: <PERSON><PERSON> den här inställningen är aktiverad är enheten permanent synlig för alla via Bluetooth! Det rekommenderas att använda PIN-kod vid denna funktion.", "settingsBluetoothContinuousconnection": "Permanent synlighet", "settingsBluetoothContinuousconnectionDescription": "Genom aktivering av permanent synlighet förblir Bluetooth aktivt på apparat ({deviceType}) och behöver inte aktiveras manuellt innan en anslutning upprättas.", "settingsBluetoothTimeout": "Timeout fö<PERSON>", "settingsBluetoothPinlimit": "PIN-kodsgräns", "settingsBluetoothTimeoutDescription": "Anslutning<PERSON> bryts efter {timeout} minuters inaktivitet.", "settingsBluetoothPinlimitDescription": "Av säkerhetsskäl har du högst {attempts} försök att ange PIN-koden. Bluetooth avaktiveras sedan och måste återaktiveras manuellt för en ny anslutning.", "settingsBluetoothPinAttempts": "f<PERSON>rsök", "settingsResetfunctionHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsResetfunctionDialog": "Vill du verkligen återställa alla funktioner?", "settingsFactoryresetFunctionsConfirmationDescription": "Alla funktioner har <PERSON>ä<PERSON>.", "mfzFunctionTime": "Tid (t)", "discoveryConnectionFailedInfo": "Ingen Bluetooth-anslutning", "detailsConfigurationDevicedisplaylockDialogtext": "Omedelbart efter att enhetens display har låsts inaktiveras Bluetooth och måste återaktiveras manuellt för att upprätta en ny anslutning.", "detailsConfigurationDevicedisplaylockDialogquestion": "Är du säker på att du vill låsa displayen?", "settingsDemodevices": "Visa demoenheter", "generalTextSettings": "Inställningar", "discoveryWifi": "WiFi", "settingsInformations": "Information", "detailsConfigurationDimmingbehavior": "Funktionsinställningar", "detailsConfigurationSwitchbehavior": "Funktionsinställningar", "detailsConfigurationBrightness": "Ljusstyrka", "detailsConfigurationMinimum": "<PERSON><PERSON> niv<PERSON>", "detailsConfigurationMaximum": "<PERSON>. niv<PERSON>", "detailsConfigurationSwitchesGr": "<PERSON><PERSON> funktion (GR)", "detailsConfigurationSwitchesGs": "Auto funktion (GS)", "detailsConfigurationSwitchesCloserer": "<PERSON><PERSON><PERSON><PERSON> (ER)", "detailsConfigurationSwitchesClosererDescription": "Av -> <PERSON><PERSON><PERSON> (På) -> <PERSON><PERSON><PERSON><PERSON> (Av)", "detailsConfigurationSwitchesOpenerer": "<PERSON><PERSON><PERSON><PERSON> (ER-Inverterad)", "detailsConfigurationSwitchesOpenererDescription": "På -> <PERSON><PERSON><PERSON> (Av) -> <PERSON><PERSON><PERSON><PERSON> (På)", "detailsConfigurationSwitchesSwitch": "Strömbrytare", "detailsConfigurationSwitchesSwitchDescription": "Med varje tryckning växlar ljuset på och av.", "detailsConfigurationSwitchesImpulsswitch": "Impulsbrytare", "detailsConfigurationSwitchesImpulsswitchDescription": "En kort tryckning på knappen används för att tända eller släcka lampan.", "detailsConfigurationSwitchesClosererDescription2": "<PERSON><PERSON><PERSON> ned knappen. När du släpper den stannar motorn.", "detailsConfigurationSwitchesImpulsswitchDescription2": "Tryck kort på knappen för att starta motorn, tryck på den igen för att stoppa den.", "detailsConfigurationWifiloginScan": "Skanna QR-kod", "detailsConfigurationWifiloginScannotvalid": "Den skannade koden är inte giltig", "detailsConfigurationWifiloginDescription": "<PERSON><PERSON> kod", "detailsConfigurationWifiloginPassword": "L<PERSON>senord", "discoveryEsbipDescription": "Aktor för jalus<PERSON> och markiser IP", "discoveryEsripDescription": "Impulsrelä IP", "discoveryEudipDescription": "Universaldimmer IP", "generalTextLoad": "<PERSON><PERSON><PERSON>", "wifiBasicautomationsNotFound": "Ingen automatisering hittades.", "wifiCodeInvalid": "Ogiltig kod", "wifiCodeValid": "Gil<PERSON>g kod", "wifiAuthorizationLogin": "<PERSON><PERSON><PERSON>", "wifiAuthorizationLoginFailed": "Inloggning misslyckades", "wifiAuthorizationSerialnumber": "Serienummer", "wifiAuthorizationProductiondate": "Tillverkningsdatum", "wifiAuthorizationProofofpossession": "PoP", "generalTextWifipassword": "Lösenord för <PERSON>", "generalTextUsername": "Användarnamn", "generalTextEnter": "ELLER ANGE MANUELLT", "wifiAuthorizationScan": "Skanna ELTAKO-koden.", "detailsConfigurationDevicesNofunctionshinttext": "Den här enheten har för nä<PERSON>rande inte stöd för nå<PERSON> andra inställningar", "settingsUsedemodelay": "Använd demofördr<PERSON>jning", "settingsImpulsLoad": "Pulskopplingstiden laddas", "settingsBluetoothLoad": "Bluetooth-inställningen håller på att laddas.", "detailsConfigurationsectionLoad": "<PERSON>n<PERSON><PERSON><PERSON><PERSON> laddas", "generalTextLogin": "Logga in\n", "generalTextAuthentication": "Autentisera", "wifiAuthorizationScanDescription": "Titta efter ELTAKO-koden på enheten eller det medföljande infobladet och skanna den i kamerarutan ovan.", "wifiAuthorizationScanShort": "Skanna ELTAKO-koden", "detailsConfigurationEdgemode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detailsConfigurationEdgemodeLeadingedge": "Framkantsdimring", "generalTextNetwork": "Nätverk", "wifiAuthenticationSuccessful": "Autentisering lyckad", "detailsConfigurationsectionSavechange": "Konfigurationen ändrad", "discoveryWifiAdddevice": "Lägg till WiFi-enhet", "wifiAuthenticationDelay": "<PERSON>ta kan ta upp till 1 minut", "generalTextRetry": "Försök igen", "wifiAuthenticationCredentials": "Vänligen ange inloggningsuppgifterna för ditt WiFi", "wifiAuthenticationSsid": "SSID", "wifiAuthenticationDelaylong": "Det kan ta upp till 1 minut tills enheten är klar\noch visas i appen", "wifiAuthenticationCredentialsShort": "<PERSON><PERSON>-uppgi<PERSON>", "wifiAuthenticationTeachin": "<PERSON><PERSON><PERSON> in enheten i WiFi", "wifiAuthenticationEstablish": "Upprätta anslutning till enheten", "wifiAuthenticationEstablishLong": "Enheten ansluter till WiFi {ssid}", "wifiAuthenticationFailed": "Anslutningen misslyckades. Bryt strömmen till enheten i några sekunder och anslut den sedan igen.", "wifiAuthenticationReset": "Återställ autentisering", "wifiAuthenticationResetHint": "All autentiseringsdata raderas", "wifiAuthenticationInvaliddata": "Autentiseringsdatan är ogiltig", "wifiAuthenticationReauthenticate": "Autentisera igen", "wifiAddhkdeviceHeader": "<PERSON><PERSON><PERSON> till en enhet", "wifiAddhkdeviceDescription": "Ans<PERSON> din nya ELTAKO-enhet till ditt Wi-Fi via Apple Home-appen.", "wifiAddhkdeviceStep1": "Öppna appen Apple Home.", "wifiAddhkdeviceStep2": "Klicka på plusknappen i appens övre högra hörn och välj **Lägg till en enhet**.", "wifiAddhkdeviceStep3": "Följ instruktionerna i appen.", "wifiAddhkdeviceStep4": "Nu kan enheten konfigureras i ELTAKO Connect-appen.", "detailsConfigurationRuntime": "Gångtid", "detailsConfigurationRuntimeMode": "Driftläge", "generalTextManually": "<PERSON><PERSON>", "detailsConfigurationRuntimeAutoDescription": "Solskyddsaktorn ställer in gångtiden automatiskt (rekommenderas).\nVid driftsättning kör solskyddet max ut och max in en gång utan stopp, på så sätt ställs gångtiden in automatiskt.", "detailsConfigurationRuntimeManuallyDescription": "Solskyddsaktorns gångtid ställs in manuellt med hjälp av reglaget nedan. Kontrollera efter inställning att gångtiden stämmer med installationen.", "detailsConfigurationRuntimeDemoDescription": "LCD displayläget är endast tillgängligt via REST API", "generalTextDemomodeActive": "Demoläget aktivt", "detailsConfigurationRuntimeDuration": "Tid", "detailsConfigurationSwitchesGs4": "Gruppomkopplare med lamellvändfunktion (GS4)", "detailsConfigurationSwitchesGs4Description": "Gruppomkopplare med lamellvändfunktion för styrning av persienner", "screenshotSu12": "Utebelysning", "screenshotS2U12": "Utebelysning", "screenshotMfz12": "Pump", "screenshotEsr62": "<PERSON><PERSON>", "screenshotEud62": "Taklampa", "screenshotEsb62": "<PERSON><PERSON><PERSON> balkong", "detailsConfigurationEdgemodeLeadingedgeDescription": "LC1-LC3 är komfortlägen med olika dimmkurvor för dimbara 230 V LED-lampor, som på grund av sin konstruktion inte kan dimras tillräckligt långt med AUTO och därför måste tvingas till framkantsdimring.", "detailsConfigurationEdgemodeAutoDescription": "AUTO gör det möjligt att dimra alla typer av lampor.", "detailsConfigurationEdgemodeTrailingedge": "Bakkant", "detailsConfigurationEdgemodeTrailingedgeDescription": "LC4-LC6 är komfortlägen med olika dimmkurvor för dimbara 230 V LED-lampor.", "updateHeader": "Uppdatering programvara", "updateTitleStepSearch": "<PERSON><PERSON><PERSON> efter upp<PERSON><PERSON><PERSON>", "updateTitleStepFound": "En uppdatering har hittats", "updateTitleStepDownload": "<PERSON><PERSON><PERSON><PERSON> uppdatering", "updateTitleStepInstall": "Installation av uppdatering", "updateTitleStepSuccess": "Uppdateringen lyckades", "updateTitleStepUptodate": "<PERSON><PERSON> upp<PERSON>", "updateTitleStepFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> misslyckades", "updateButtonSearch": "<PERSON><PERSON><PERSON> efter uppdatering", "updateButtonInstall": "Installera uppdatering", "updateCurrentversion": "Aktuell version", "updateNewversion": "Ny uppdatering av programvaran finns tillgänglig", "updateHintPower": "Uppdateringen startar först när enhetens utgång inte är aktiv. Enheten får inte kopplas bort från strömförsörjningen och appen måste vara igång under uppdateringen!", "updateButton": "Uppdatera", "updateHintCompatibility": "En uppdatering rekommenderas, annars begränsas vissa funktioner i appen.", "generalTextDetails": "<PERSON><PERSON><PERSON>", "updateMessageStepMetadata": "Laddar ner information om uppdateringen", "updateMessageStepPrepare": "Uppdateringen förbereds", "updateTitleStepUpdatesuccessful": "Uppdateringen kontrolleras", "updateTextStepFailed": "<PERSON><PERSON><PERSON> gick fel vid uppdateringen, f<PERSON><PERSON><PERSON><PERSON> igen om några minuter eller vänta tills enheten uppdaterar automatiskt (internetanslutning krävs).", "configurationsNotavailable": "Det finns inga tillgängliga konfigurationer ännu.", "configurationsAddHint": "<PERSON><PERSON><PERSON> nya konfigurationer genom att ansluta till en Bluetooth-enhet och spara en konfiguration.", "@configurationsAddHint": {"description": "Now available for all devices not only Bluetooth", "type": "text"}, "configurationsEdit": "Redigera konfiguration", "generalTextName": "<PERSON><PERSON>", "configurationsDelete": "Radera konfiguration", "configurationsDeleteHint": "Ska konfigurationen: {configName} verkligen raderas?", "configurationsSave": "Spara konfiguration", "configurationsSaveHint": "<PERSON><PERSON>r kan du spara konfigurationen på din smartphone, eller ladda en sparad konfiguration.", "configurationsImport": "Importera konfiguration", "configurationsImportHint": "Skall konfigurationen {configName} verkligen flyttas?", "generalTextConfigurations": "{count, plural, one {Konfiguration} other {Konfigurationer}}", "configurationsStepPrepare": "Konfigurationen förbereds", "configurationsStepName": "Ange ett namn för konfigurationen", "configurationsStepSaving": "Konfigurationen sparas", "configurationsStepSavedsuccessfully": "Konfiguration<PERSON> har sparats", "configurationsStepSavingfailed": "Det gick inte att spara konfigurationen", "configurationsStepChoose": "Välj en konfiguration", "configurationsStepImporting": "Konfigurationen importeras", "configurationsStepImportedsuccessfully": "Konfigurationen har importerats", "configurationsStepImportingfailed": "Importen av konfigurationen misslyckades", "discoveryAssuDescription": "Bluetooth kopplingsur astro plug-in utomhusbruk", "settingsDatetimeDevicetime": "Aktuell tid", "settingsDatetimeLoading": "Tidsinställning<PERSON><PERSON> lad<PERSON>", "discoveryEud12Description": "Dimmer Bluetooth", "generalTextOffdelay": "Frånslagsfördröjning", "generalTextRemainingbrightness": "Grundljusstyrka", "generalTextSwitchonvalue": "Startvärde", "motionsensorTitleNoremainingbrightness": "Grundljusfunktion av", "motionsensorTitleAlwaysremainingbrightness": "Grundljusfunktion på", "motionsensorTitleRemainingbrightnesswithprogram": "Grundljusfunktion i program och ZEA", "motionsensorTitleRemainingbrightnesswithprogramandzea": "Grundljus via ZE och ZA", "motionsensorTitleNoremainingbrightnessauto": "<PERSON>get grundljus (halvautomatisk)", "generalTextMotionsensor": "Rörelsedetektor", "generalTextLightclock": "Ljusväckarklocka", "generalTextSnoozeclock": "Snooze-funktion", "generalDescriptionLightclock": "<PERSON>id till<PERSON> ({mode}) tänds ljuset efter ca 1 sekund med lägsta ljusstyrkan och dimras långsamt upp utan att den senast sparade ljusstyrkan ändras.", "generalDescriptionSnoozeclock": "Vid frånkoppling ({mode}) dimras belysningen ned från det aktuella ljusvärdet till den lägsta ljusstyrkan och släcker sedan. Belysningen kan släckas när som helst under neddimringen med ett kort tryck på knappen. Ett långt tryck under neddimringen dimrar upp och avslutar snooze-funktionen.", "generalTextImmediately": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextPercentage": "Procent", "generalTextSwitchoffprewarning": "Frånslagsvarning", "generalDescriptionSwitchoffprewarning": "Långsam dimring till lägsta ljusstyrka", "generalDescriptionOffdelay": "Enheten slås på när styrspänningen läggs på. Om styrspänningen bryts påbörjas tidräkningen och därefter stängs enheten av. Apparaten kan kopplas till igen under tidräkningen.", "generalDescriptionBrightness": "Ljusstyrkan med vilken lampan tänds.", "generalDescriptionRemainingbrightness": "Dimvärdet i procent som det dimrats till när rörelsedetektorn kopplat från.", "generalDescriptionRuntime": "Ljuslarmsfunktionens gångtid från lägsta ljusstyrka till högsta ljusstyrka.", "generalTextUniversalbutton": "Universaltryckknapp", "generalTextDirectionalbutton": "Riktningstryckknapp", "eud12DescriptionAuto": "Automatisk detektering UT/RT (med riktningstrykknappsdiod RTD)", "eud12DescriptionRt": "med riktningstryckknappsdiod RTD", "generalTextProgram": "Program", "eud12MotionsensorOff": "Med rörelsedetektorn inställd på Off", "eud12ClockmodeTitleProgramze": "Program och central på", "eud12ClockmodeTitleProgramza": "Program och central av", "eud12ClockmodeTitleProgrambuttonon": "Program och UT/RT på", "eud12ClockmodeTitleProgrambuttonoff": "Program och UT/RT av", "eud12TiImpulseTitle": "<PERSON><PERSON><PERSON><PERSON> (t1)", "eud12TiImpulseHeader": "Dimmervärde pulstid på", "eud12TiImpulseDescription": "Det dimmervärde i procent som lampan dimras till vid pulstiden ON.", "eud12TiOffTitle": "Pulstid av (t2)", "eud12TiOffHeader": "Dimmervärde pulstid av", "eud12TiOffDescription": "Det dimmervärde i procent som lampan dimras till vid pulstiden OFF.", "generalTextButtonpermanentlight": "Konstantljusfunktion", "generalDescriptionButtonpermanentlight": "Inställning konstantljus med tryckknapp från 0 till 10 timmar i steg om 0,5 timmar. Aktivering genom att trycka på knappen längre än 1 sekund (blinkar 1 gång), Avaktivering genom knapptryckning längre än 2 sekunder.", "generalTextNobuttonpermanentlight": "Ingen TSP", "generalTextBasicsettings": "Grundfunktioner", "generalTextInputswitch": "<PERSON><PERSON> try<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (A1)", "generalTextOperationmode": "Driftläge", "generalTextDimvalue": "Tillkopplinginställningar", "eud12TitleUsememory": "Minnesfunktion på", "eud12DescriptionUsememory": "Minnesfunktionen motsvarar det senast inställda dimmervärdet. Om minnesfunktionen stängs av startar dimmern med maxvärdet.", "generalTextStartup": "Ljusstyrka vid tillkoppling", "generalDescriptionSwitchonvalue": "Det inställbara startvärdet  är det som garanterar att lampan tänds.", "generalTitleSwitchontime": "Tillkopplingstid", "generalDescriptionSwitchontime": "<PERSON><PERSON> tillkopplingstiden dimrar lampan från startvärdet till minnesvärdet.", "generalDescriptionStartup": "Vissa LED-lampor kräver ett högre startvärde för att kunna tändas ordentligt. Lampan tänds med tillkopplingsvärdet och dimras sedan till minnesvärdet efter tillkopplingstiden.", "eud12ClockmodeSubtitleProgramze": "<PERSON>rt klick på Central på", "eud12ClockmodeSubtitleProgramza": "Kort klick på central av", "eud12ClockmodeSubtitleProgrambuttonon": "Dubbelklicka på universal-/riktnings-tryckknappen På", "eud12ClockmodeSubtitleProgrambuttonoff": "Dubbelklicka på universal-/riktnings-tryckknappen Av", "eud12FunctionStairlighttimeswitchTitleShort": "TLZ | Trappautomat", "eud12FunctionMinTitleShort": "MIN", "eud12FunctionMmxTitleShort": "MMX", "eud12FunctionTiDescription": "Timer med justerbar till- och frånslagstid från 0,5 sekunder till 9,9 minuter. Ljusstyrkan kan ställas in från minimal ljusstyrka till maximal ljusstyrka.", "eud12FunctionAutoDescription": "Universaldimmer med inställning för rörelsedetektor, ljuslarm och snooze-funktion", "eud12FunctionErDescription": "Arbetsströmrelä, ljusstyrkan kan stä<PERSON> in från min-ljusstyrka till max-ljusstyrka.", "eud12FunctionEsvDescription": "Universaldimmer med ställbar frånkopplingstid från 1-120 minuter. Frånkopplingsvarning i slutet genom neddimring valbar och justerbar från 1-3 minuter. Båda centralingångarna är aktiva.", "eud12FunctionTlzDescription": "Inställning konstantljus med tryckknapp från 0 till 10 timmar i steg om 0,5 timmar. Aktivering genom att trycka på knappen längre än 1 sekund (blinkar 1 gång), Avaktivering genom knapptryckning längre än 2 sekunder. ", "eud12FunctionMinDescription": "Universal dimmer, tänder med lägsta ljusstyrkan när styrspänning ansluts. <PERSON><PERSON><PERSON> dimras till maximal ljusstyrka med den inställda dimringstiden 1-120 minuter. <PERSON>är styrspänningen tas bort slocknar ljuset direkt, även under dimringstiden. Båda centralingångarna är aktiva.", "eud12FunctionMmxDescription": "Universaldimmer, tänder med lägsta  ljusstyrkan när styrspänning ansluts. Med den inställda dimringstiden 1-120 minuter dimras ljuset till maximal ljusstyrka. När styrspänningen tas bort dimrar dimmern ned till den lägsta inställda  ljusstyrkan. Därefter kopplar den från. Båda centralingångarna är aktiva.", "motionsensorSubtitleNoremainingbrightness": "Med rörelsedetektorn inställd på Off", "motionsensorSubtitleAlwaysremainingbrightness": "Med rörelsedetektorn inställd på Off", "motionsensorSubtitleRemainingbrightnesswithprogram": "Kopplingsprogram aktiverat och avaktiverat med rörelsedetektorn av", "motionsensorSubtitleRemainingbrightnesswithprogramandzea": "Centralt/på aktiverar rörelsedetektor, Centralt/av avaktiverar rörelsedetektor samt kopplingsprogram", "motionsensorSubtitleNoremainingbrightnessauto": "Rörelsedetektorn stänger bara av", "detailsDimsectionHeader": "<PERSON><PERSON><PERSON>", "generalTextFast": "Snabbt", "generalTextSlow": "Långsamt", "eud12TextDimspeed": "<PERSON><PERSON>ringshas<PERSON>ghet", "eud12TextSwitchonspeed": "Tillkopplingshastighet", "eud12TextSwitchoffspeed": "Avstängningshastighet", "eud12DescriptionDimspeed": "Di<PERSON>ringshastigheten är den som dimmern använder, vid dimring från aktuellt värde till målvärdet.", "eud12DescriptionSwitchonspeed": "Tillkopplingshastighet är den dimmern använder för att kopplas in helt.", "eud12DescriptionSwitchoffspeed": "Avstängningshastighet är den dimmern använder för att stänga av helt.", "settingsFactoryresetResetdimHeader": "Återställ dimringsinställningar", "settingsFactoryresetResetdimDescription": "Skall verkligen alla dimringsinställningar återställas?", "settingsFactoryresetResetdimConfirmationDescription": "Dimmerinställningarna har å<PERSON>tällts", "eud12TextSwitchonoffspeed": "Hastighet på/av", "eud12DescriptionSwitchonoffspeed": "Till-/från-kopplingshastighet är den dimmern använder för att koppla till eller från helt.", "timerDetailsDimtoval": "På med dimmervärde i %", "timerDetailsDimtovalDescription": "Dimmern kopplas alltid in med det fasta dimmervärdet i %.", "timerDetailsDimtovalSubtitle": "Slå på med {brightness}%", "timerDetailsDimtomem": "På med minnesvärde", "timerDetailsDimtomemSubtitle": "Tillkoppling med minnesvärde", "timerDetailsMotionsensorwithremainingbrightness": "Återstående ljusstyrka (rörelsedetektor) På", "timerDetailsMotionsensornoremainingbrightness": "Återstående ljusstyrka (rörelsedetektor) Av", "settingsRandommodeHint": "När slumpvalsläge är aktiverat förskjuts alla kopplingstider på denna kanal slumpmässigt med upp till 15 minuter. På- och AV-tiderna förskjuts.", "runtimeOffsetDescription": "Tilläggsdrifttid efter att körtiden har löpt ut. Detta kan användas för att säkerställa att ändläget nås.", "loadingTextDimvalue": "Dimmervärde är laddat", "discoveryEudipmDescription": "Universaldimmer IP Matter", "generalTextOffset": "Tilläggsdrifttid", "eud12DimvalueTestText": "<PERSON>cka l<PERSON>ty<PERSON>", "eud12DimvalueTestDescription": "Den för tillfället inställda dimhastigheten beaktas under testningen.", "eud12DimvalueLoadText": "Ladda l<PERSON>", "settingsDatetimeNotime": "Datum- och tidsinställningarna måste läsas av via apparatens display.", "generalMatterText": "Matter", "generalMatterMessage": "<PERSON><PERSON><PERSON> in din Matter-enhet med hjälp av app t.ex. Google Home, Amazon Alexa eller Samsung SmartThings.", "generalMatterOpengooglehome": "Öppna Google Home", "generalMatterOpenamazonalexa": "Öppna Amazon Alexa", "generalMatterOpensmartthings": "Öppna SmartThings", "generalLabelProgram": "Program {number}", "generalTextDone": "<PERSON><PERSON>", "settingsRandommodeDescriptionShort": "Med slumpmässigt läge aktiverat förskjuts alla program på den här kanalen slumpmässigt med upp till 15 minuter. På-tider kopplar i förväg, av-tider fördröjs.", "all": "<PERSON>a", "discoveryBluetooth": "Bluetooth", "success": "Framgång", "error": "<PERSON><PERSON>", "timeProgramAdd": "Lägg till tidsprogram", "noConnection": "Ingen anslutning", "timeProgramOnlyActive": "Konfigurerade program", "timeProgramAll": "Alla program", "active": "Aktiv", "inactive": "Inaktiv", "timeProgramSaved": "Program {number} sparat", "deviceLanguageSaved": "Enhetens språk sparat", "generalTextTimeShort": "{time} klocka", "programDeleteHint": "Skall program {index} verkligen raderas?", "milliseconds": "{count, plural, one {Millisekund} other {Millisekunder}}", "millisecondsWithValue": "{count, plural, one {{count} Millisekund} other {{count} Millisekunder}}", "secondsWithValue": "{count, plural, one {{count} Sekund} other {{count} Se<PERSON>nder}}", "minutesWithValue": "{count, plural, one {{count} Minut} other {{count} Minuter}}", "hoursWithValue": "{count, plural, one {{count} <PERSON><PERSON>} other {{count} <PERSON><PERSON>}}", "settingsPinFailEmpty": "PIN-koden får inte vara tom", "detailsConfigurationWifiloginScanNoMatch": "Skannad kod stämmer inte överens med enheten", "wifiAuthorizationPopIsEmpty": "PoP kan inte vara tom", "wifiAuthenticationCredentialsHint": "Eftersom appen inte har tillgång till ditt privata Wi-Fi-lösenord går det inte att kontrollera att inmatningen är korrekt. Om ingen anslutning upprättas kontrollerar du lösenordet och anger det igen.", "generalMatterOpenApplehome": "Öppna Apple Home", "timeProgramNoActive": "Inga konfigurerade program", "timeProgramNoEmpty": "Inga fria programplatser tillgängliga", "nameOfConfiguration": "Konfigurationsnamn", "currentDevice": "Aktuell enhet", "export": "Exportera", "import": "Importera", "savedConfigurations": "<PERSON><PERSON> konfigu<PERSON>er", "importableServicesLabel": "Följande inställningar kan importeras:", "notImportableServicesLabel": "Inkompatibla inställningar", "deviceCategoryMeterGateway": "Energimätar Gateway", "deviceCategory2ChannelTimeSwitch": "Kopplingsur 2-kanal Bluetooth", "devicategoryOutdoorTimeSwitchBluetooth": "<PERSON><PERSON><PERSON><PERSON> för <PERSON> Bluetooth", "settingsModbusHeader": "Modbus", "settingsModbusDescription": "<PERSON><PERSON>, paritet och timeout för att konfigurera överföringshastighet, feldetektering och väntetid.", "settingsModbusRTU": "Modbus RTU", "settingsModbusBaudrate": "Baudrate", "settingsModbusParity": "Pa<PERSON>t", "settingsModbusTimeout": "Modbus tidsgräns", "locationServiceDisabled": "<PERSON><PERSON><PERSON> är inaktiverad", "locationPermissionDenied": "Ge platstjänster tillstånd att använda din aktuella position.", "locationPermissionDeniedPermanently": "Platstjänster är permanent nekade, tillåt platstjänster i enhetens inställningar att använda din aktuella position.", "lastSync": "Senaste synkroniseringen", "dhcpActive": "DHCP aktiv", "ipAddress": "IP", "subnetMask": "Subnätmask", "standardGateway": "Standard-gateway", "dns": "DNS", "alternateDNS": "Alternativ DNS", "errorNoNetworksFound": "Inget wifi-nätverk hittades", "availableNetworks": "Tillgängliga nätverk", "enableWifiInterface": "Aktivera WiFi-gränssnitt", "enableLANInterface": "Aktivera LAN-gränssnitt", "hintDontDisableAllInterfaces": "Se till att inte alla gränssnitt är inaktiverade. Det senast aktiverade gränssnittet har prioritet.", "ssid": "SSID", "searchNetworks": "Sök WiFi-nätverk", "errorNoNetworkEnabled": "Minst en anslutning måste vara aktiv", "errorActiveNetworkInvalid": "Alla aktiva stationer är inte giltiga", "invalidNetworkConfiguration": "Ogiltig nätverkskonfiguration", "generalDefault": "Standard", "mqttHeader": "MQTT", "mqttConnected": "Ansluten till MQTT-broker", "mqttDisconnected": "Ingen anslutning till MQTT-brokern", "mqttBrokerURI": "URI broker", "mqttBrokerURIHint": "URI för MQTT-broker", "mqttPort": "Port", "mqttPortHint": "MQTT-port", "mqttClientId": "Klient-ID", "mqttClientIdHint": "MQTT-klient-ID", "mqttUsername": "Användarnamn", "mqttUsernameHint": "MQTT användarnamn", "mqttPassword": "L<PERSON>senord", "mqttPasswordHint": "MQTT-lösenord", "mqttCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mqttCertificateHint": "MQTT-certifikat", "mqttTopic": "Ämne", "mqttTopicHint": "MQTT-ämne", "electricityMeter": "Energimätare", "electricityMeterCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "electricityMeterHistory": "Historia", "electricityMeterReading": "Mätaravläsning", "connectivity": "Anslutningsmöjligheter", "electricMeter": "{count, plural, one {Energimätare} other {Energimätare}}", "discoveryZGW16Description": "Modbus-Energimätare-MQTT-Gateway", "bluetoothConnectionLost": "Bluetooth-anslutningen förlorad", "bluetoothConnectionLostDescription": "Bluetooth-anslutningen till enheten har förlorats. Kontrollera anslutningen till enheten.", "openBluetoothSettings": "Öppna Bluetooth-inställningar", "password": "L<PERSON>senord", "setInitialPassword": "Ange <PERSON>t lösenord", "initialPasswordMinimumLength": "Lösenordet måste innehålla minst {length} tecken", "repeatPassword": "Upprepa lösenord", "passwordsDoNotMatch": "Lösenorden stämmer inte överens", "savePassword": "<PERSON><PERSON>", "savePasswordHint": "Lösenordet sparas för framtida anslutningar på din enhet.", "retrieveNtpServer": "Hämta tid från NTP-server", "retrieveNtpServerFailed": "Anslutningen till NTP-servern kunde inte upprättas.", "retrieveNtpServerSuccess": "Anslutningen till NTP-servern lyckades.", "settingsPasswordNewPasswordDescription": "Ange n<PERSON>t lösenord", "settingsPasswordConfirmationDescription": "Lösenordsbytet lyckades", "dhcpRangeStart": "Start av DHCP-intervall", "dhcpRangeEnd": "DHCP-intervallets slut", "forwardOnMQTT": "Vidarebefordra till MQTT", "showAll": "Visa alla", "hide": "<PERSON><PERSON><PERSON><PERSON>", "changeToAPMode": "Ändra till AP-läge", "changeToAPModeDescription": "<PERSON> håller på att ansluta enheten till ett WiFi-nätverk, varvid anslutning<PERSON> till enheten bryts och du måste återansluta enheten via det konfigurerade nätverket.", "consumption": "Förbrukning", "currentDay": "Aktuell dag", "twoWeeks": "2 veckor", "oneYear": "1 år", "threeYears": "3 år", "passwordMinLength": "lösenordet måste innehålla minst {length} tecken.", "passwordNeedsLetter": "Lösenordet måste innehålla minst en bokstav.", "passwordNeedsNumber": "Lösenordet måste innehålla minst en siffra.", "portEmpty": "<PERSON><PERSON> får inte vara tom", "portInvalid": "Ogiltig port", "portOutOfRange": "Port måste vara mellan {rangeStart} och {rangeEnd}", "ipAddressEmpty": "IP-ad<PERSON><PERSON> får inte vara tom", "ipAddressInvalid": "Ogiltig IP-adress", "subnetMaskEmpty": "Subnätmasken får inte vara tom", "subnetMaskInvalid": "Ogiltig subnätmask", "gatewayEmpty": "Gateway kan inte vara tom", "gatewayInvalid": "Ogiltig gateway", "dnsEmpty": "DNS kan inte vara tom", "dnsInvalid": "Ogiltig DNS", "uriEmpty": "URI får inte vara tom", "uriInvalid": "Ogiltig URI", "electricityMeterChangedSuccessfully": "Elmät<PERSON><PERSON> har <PERSON>", "networkChangedSuccessfully": "Nätverkskonfigurationen har ändrats", "mqttChangedSuccessfully": "MQTT-konfiguration<PERSON> har <PERSON>", "modbusChangedSuccessfully": "Modbus-inställningarna har ä<PERSON>", "loginData": "<PERSON><PERSON><PERSON> inloggningsuppgifter", "valueConfigured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "electricityMeterHistoryNoData": "Inga uppgifter tillgängliga", "locationChangedSuccessfully": "<PERSON><PERSON><PERSON> har <PERSON>", "settingsNameFailEmpty": "<PERSON><PERSON> får inte vara tomt", "settingsNameFailLength": "Namnet får inte vara längre än {length} tecken", "solsticeChangedSuccesfully": "Solståndsinställningarna har <PERSON>", "relayFunctionChangedSuccesfully": "Reläfunktionen har ändrats", "relayFunctionHeader": "Reläfunktion", "dimmerValueChangedSuccesfully": "Tillkopplingsfunktionen har ändrats", "dimmerBehaviourChangedSuccesfully": "Dimringsfunktionen har ändrats", "dimmerBrightnessDescription": "Den lägsta och högsta ljusstyrkan påverkar alla dimmerns inställbara ljusstyrkor.", "dimmerSettingsChangedSuccesfully": "Grundinställningar<PERSON> har <PERSON>", "liveUpdateEnabled": "Live-test aktiverad", "liveUpdateDisabled": "Live-test inaktiverad", "liveUpdateDescription": "Det senast ändrade slidervärdet kommer att skickas till enheten", "demoDevices": "Demoenheter", "showDemoDevices": "Visa demoenheter", "deviceCategoryTimeSwitch": "Ko<PERSON><PERSON>ur", "deviceCategoryMultifunctionalRelay": "Multifunktionstidrelä", "deviceCategoryDimmer": "<PERSON><PERSON>", "deviceCategoryShutter": "Markis & jalusiaktor", "deviceCategoryRelay": "<PERSON><PERSON><PERSON>", "search": "<PERSON>ö<PERSON>", "configurationsHeader": "Kon<PERSON>gu<PERSON><PERSON>", "configurationsDescription": "<PERSON><PERSON><PERSON> kan du hantera dina sparade konfigurationer.", "configurationsNameFailEmpty": "Konfigurationsnamnet får inte vara tomt", "configurationDeleted": "Konfiguration raderad", "codeFound": "{codeType} kod <PERSON>känd", "errorCameraPermission": "Ge kameran tillåtelse att skanna ELTAKO-koden.", "authorizationSuccessful": "Enheten har auktoriserats ", "wifiAuthenticationResetConfirmationDescription": "Enheten är nu redo för en ny auktorisering.", "settingsResetConnectionHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsResetConnectionDescription": "Vill du verkligen återställa anslutningen?", "settingsResetConnectionConfirmationDescription": "Uppkopplingen har återställts.", "wiredInputChangedSuccesfully": "Tryckknappsfunktionen har ändrats.", "runtimeChangedSuccesfully": "Gångtiden har ä<PERSON>.", "expertModeActivated": "Expertläge aktiverat", "expertModeDeactivated": "Expertläge avaktiverat", "license": "Licens", "retry": "Försök igen", "provisioningConnectingHint": "Anslutningen till enheten håller på att upprättas. Detta kan ta upp till 1 minut.", "serialnumberEmpty": "Serienumret får inte vara tomt", "interfaceStateInactiveDescriptionBLE": "Bluetooth är avaktiverat, aktivera den för att upptäcka Bluetooth-enheter.", "interfaceStateDeniedDescriptionBLE": "Bluetooth-behörigheter beviljades inte.", "interfaceStatePermanentDeniedDescriptionBLE": "Bluetooth-behörigheter har inte beviljats. Vänligen aktivera dem i enhetens inställningar.", "requestPermission": "<PERSON><PERSON><PERSON><PERSON>", "goToSettings": "Gå till inställningar", "enableBluetooth": "Aktivera Bluetooth", "installed": "Installerad", "teachInDialogDescription": "<PERSON><PERSON><PERSON> du lära din enhet via {type}?", "useMatter": "Använda Matter", "relayMode": "Aktivera reläfunktion", "whatsNew": "Nytt i denna version", "migrationHint": "En migrering är nödvändig för att kunna använda de nya funktionerna.", "migrationHeader": "Migration", "migrationProgress": "Migration pågår...", "letsGo": "Nu kör vi!", "noDevicesFound": "Inga enheter hittades. Kontrollera att din enhet är i parningsläge.", "interfaceStateEmpty": "Inga enheter hittades", "ssidEmpty": "SSID får inte vara tomt", "passwordEmpty": "Lösenordet får inte vara tomt", "settingsDeleteSettingsHeader": "Återställ inställningar", "settingsDeleteSettingsDescription": "Vill du verkligen återställa alla inställningar?", "settingsDeleteSettingsConfirmationDescription": "Alla inställningar har återställts.", "locationNotFound": "<PERSON><PERSON><PERSON> hittades inte", "timerProgramEmptySaveHint": "Tidsprogrammet är tomt och kan inte sparas. Vill du avbryta ändringen?", "timerProgramDaysEmptySaveHint": "Inga dagar är valda. Vill du spara tidsprogrammet ändå?", "timeProgramNoDays": "Ett program utan aktiva dagar kan inte aktiveras.", "timeProgramColliding": "Tidsprogrammet kolliderar med program {program}", "timeProgramDuplicated": "Tidsprogrammet är ett duplikat av program {program}", "screenshotZgw16": "<PERSON><PERSON>", "interfaceStateUnknown": "Inga enheter hittades", "settingsPinChange": "Ändra PIN-kod", "timeProgrammOneTime": "eng<PERSON><PERSON><PERSON>", "timeProgrammRepeating": "upprepning", "generalIgnore": "<PERSON><PERSON><PERSON>", "timeProgramChooseDay": "<PERSON><PERSON><PERSON><PERSON> dag", "generalToday": "<PERSON><PERSON>", "generalTomorrow": "Imorgon", "bluetoothAndPINChangedSuccessfully": "Bluetooth och PIN-koden har <PERSON>.", "generalTextDimTime": "Dimringstid", "discoverySu62Description": "1-kanals kop<PERSON><PERSON>r Bluetooth", "bluetoothAlwaysOnTitle": "Alltid på", "bluetoothAlwaysOnDescription": "Bluetooth är permanent aktiverat.", "bluetoothAlwaysOnHint": "Obs: O<PERSON> den här inställningen är aktiverad är enheten permanent synlig för alla via Bluetooth! Det rekommenderas att ändra standard-PIN-koden.", "bluetoothManualStartupOnTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> på", "bluetoothManualStartupOnDescription": "<PERSON>fter att strömmen slagits på är Bluetooth aktivt i 3 minuter.", "bluetoothManualStartupOnHint": "Obs: Kopplingsläget är aktivt i 3 minuter och stängs sedan av. Om en ny anslutning ska upprättas måste knappen hållas intryckt i ca 5 sekunder.", "bluetoothManualStartupOffTitle": "<PERSON><PERSON> på", "bluetoothManualStartupOffDescription": "Bluetooth aktiveras manuellt via knappsatsen och är sedan aktivt i 3 minuter.", "bluetoothManualStartupOffHint": "Obs: <PERSON><PERSON><PERSON> att aktivera Bluetooth måste knappen på knappsatsen hållas intryckt i ca 5 sekunder.", "timeProgrammOneTimeRepeatingDescription": "Program kan antingen utföras upprepade gånger genom att alltid utföra en kopplingsoperation på de konfigurerade dagarna och tiderna, eller så kan de endast utföras en gång vid den konfigurerade kopplingstiden.", "versionHeader": "Version {version}", "releaseNotesHeader": "Releaseinformation", "release30Header": "Den nya Eltako Connect-appen är här!", "release30FeatureDesignHeader": "Ny design", "release30FeatureDesignDescription": "Appen har omarbetats helt och fått en ny design. Den är nu enklare och mer intuitiv att använda.", "release30FeaturePerformanceHeader": "Förbättrad prestanda", "release30FeaturePerformanceDescription": "Få en smidigare funktion och kortare laddningstider - för en bättre användarupplevelse.", "release30FeatureConfigurationHeader": "Konfigurationer mellan olika enheter", "release30FeatureConfigurationDescription": "Spara konfigurationer och överför dem till andra enheter. Även om det inte är en likadan enhet kan du t.ex. överföra konfigurationen från din S2U12DBT1+1-UC till en ASSU-BT eller vice versa.", "release31Header": "Den nya infällda 1-kanals tiduret med Bluetooth är här!", "release31Description": "Vad kan SU62PF-BT/UC?", "release31SU62Name": "SU62PF-BT/UC", "release31DeviceNote1": "Upp till 60 kopplingsprogram.", "release31DeviceNote2": "<PERSON><PERSON><PERSON> kan slå på enheter vid fasta kopplingstider eller via astro-funktionen baserat på soluppgång och solnedgång.", "release31DeviceNote3": "Slumpvalsläge: kopplingstiderna kan förskjutas med upp till 15 minuter.", "release31DeviceNote4": "Automatisk sommartid/vintertid: Klockan växlar automatiskt till sommar- eller vintertid.", "release31DeviceNote5": "<PERSON><PERSON> matnings- och s<PERSON>rsp<PERSON>nning 12-230V UC.", "release31DeviceNote6": "Tryckknappsingång för manuell styrning.", "release31DeviceNote7": "1 NO-kontakt potentialfri 10 A/250 V AC.", "release31DeviceNote8": "Engångsexekvering av tidsprogram.", "generalNew": "Nytt", "yearsAgo": "{count, plural, one {förra året} other {för {count} år sedan}}", "monthsAgo": "{count, plural, one {<PERSON><PERSON><PERSON> må<PERSON>} other {för {count} månader sedan}}", "weeksAgo": "{count, plural, one {fö<PERSON> veckan} other {för {count} veckor sedan}}", "daysAgo": "{count, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {för {count} dagar sedan}}", "minutesAgo": "{count, plural, one {En minut sedan} other {för {count} minuter sedan}}", "hoursAgo": "{count, plural, one {En timme sedan} other {för {count} timmar sedan}}", "secondsAgo": "{count, plural, one {En sekund sedan} other {för {count} sekunder sedan}}", "justNow": "Just nu", "discoveryEsripmDescription": "Relä IP Matter", "generalTextKidsRoom": "Barnkammarfunktion", "generalDescriptionKidsRoom": "Vid påslagning med en längre knapptryckning ({mode}) tänds ljuset med den lägsta ljusstyrkan efter ca 1 sekund och dimras långsamt upp så länge knappen hålls intryckt, utan att den senast sparade ljusstyrkan ändras.", "generalTextSceneButton": "<PERSON><PERSON><PERSON>k<PERSON><PERSON>", "settingsEnOceanConfigHeader": "EnOcean-konfiguration", "enOceanConfigChangedSuccessfully": "EnOcean-konfigurationen har ä<PERSON>.", "activateEnOceanRepeater": "Aktivera EnOcean Repeater", "enOceanRepeaterLevel": "Repeater-niv<PERSON>", "enOceanRepeaterLevel1": "1-niv<PERSON>", "enOceanRepeaterLevel2": "2-niv<PERSON>", "enOceanRepeaterOffDescription": "Inga trådlösa signaler från sensorer repeteras.", "enOceanRepeaterLevel1Description": "Endast de trådlösa signalerna från sensorerna tas emot, kontrolleras och vidarebefordras med full sändningseffekt. Trådlösa signaler från andra repeatrar ignoreras för att minska datamängden.", "enOceanRepeaterLevel2Description": "Förutom trådlösa signaler från sensorerna repeteras även signalerna från 1-nivå repeaters. En trådlös signal kan därför tas emot och förstärkas maximalt två gånger. Trådlösa repeatrar behöver inte läras in. De tar emot och förstärker trådlösa signaler från alla trådlösa sensorer i sitt mottagningsområde.", "settingsSensorHeader": "<PERSON><PERSON><PERSON>", "sensorChangedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> har <PERSON>", "wiredButton": "Trådbunden tryckknapp", "enOceanId": "EnOcean-ID", "enOceanAddManually": "<PERSON><PERSON> eller skanna <PERSON>-ID", "enOceanIdInvalid": "Ogiltigt EnOcean-ID", "enOceanAddAutomatically": "<PERSON><PERSON>r in med EnOcean Telegram", "enOceanAddDescription": "EnOcean protokollet gör det möjligt att lära in och styra tryckknappar i din aktor.\n\nVälj antingen det automatiska inlärningsalternativet med EnOcean telegram, för att koppla in tryckknappar med knapptryckningar, eller välj det manuella alternativet, för att skanna in eller knappa in EnOcean-ID:t.", "enOceanTelegram": "Telegram", "enOceanCodeScan": "<PERSON><PERSON> EnOcean-<PERSON> för din {sensorType} eller skanna <PERSON>-QR-koden för din {sensorType} för att lägga till den", "enOceanCode": "EnOcean QR-kod", "enOceanCodeScanDescription": "<PERSON><PERSON><PERSON> efter EnOcean-koden på din {sensorType} och skanna den med din kamera.", "enOceanButton": "EnOcean tryckk<PERSON>p", "enOceanBackpack": "EnOcean-adapter", "sensorNotAvailable": "Inga sensorer har änn<PERSON> lä<PERSON> in", "sensorAdd": "<PERSON><PERSON><PERSON> till <PERSON>er", "sensorCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON> inlärn<PERSON>", "sensorCancelDescription": "Vill du verkligen avbryta inlärningen?", "getEnOceanBackpack": "<PERSON><PERSON><PERSON> din EnOcean-adapter", "enOceanBackpackMissing": "<PERSON><PERSON><PERSON> att komma till den fantastiska världen av perfekta anslutningar och kommunikation behöver du en EnOcean-adapter.\nKlicka här för mer information", "sensorEditChangedSuccessfully": "{sensorName} har <PERSON><PERSON><PERSON>", "sensorConnectedVia": "ansluten via {deviceName}", "lastSeen": "<PERSON><PERSON> sedd", "setButtonOrientation": "Ställ in orientering", "setButtonType": "<PERSON><PERSON>", "button1Way": "1-v<PERSON><PERSON> tryckknapp", "button2Way": "2-v<PERSON><PERSON> tryckknapp", "button4Way": "4-v<PERSON><PERSON> tryck<PERSON>napp", "buttonUnset": "inte använd", "button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor": "Sensor", "sensorsFound": "{count, plural, =0 {inga sensorer funna} one {1 sensor funnen} other {{count} sensorer funna}}", "sensorSearch": "<PERSON><PERSON><PERSON> efter <PERSON>er", "searchAgain": "<PERSON><PERSON><PERSON> igen", "sensorTeachInHeader": "<PERSON><PERSON><PERSON> in {sensorType}", "sensorChooseHeader": "Välj {sensorType}", "sensorChooseDescription": "Vä<PERSON>j vilken tryckknapp du vill lära in.", "sensorCategoryDescription": "<PERSON><PERSON><PERSON><PERSON> kategori för den som du vill lära in.", "sensorName": "Tryckknappens namn", "sensorNameFooter": "<PERSON><PERSON> din tryckknapp", "sensorAddedSuccessfully": "{sensorName} har lärts in", "sensorDelete": "radera {sensorType}", "sensorDeleteHint": "Vill du verkligen radera {sensorType} {sensorName}?", "sensorDeletedSuccessfully": "{sensorName} har raderats", "buttonTapDescription": "<PERSON>ck på den tryckknapp som du vill lära in.", "waitingForTelegram": "Aktorn väntar på telegram", "copied": "<PERSON><PERSON><PERSON>", "pairingFailed": "{sensorType} <PERSON>r <PERSON>an in<PERSON>rd", "generalDescriptionUniversalbutton": "Med en universalknapp vänder man riktning genom att kort släppa knappen. Korta tryckningar sätter på eller stänger av.", "generalDescriptionDirectionalbutton": "Riktningsknappen är \"slå på och dimra upp\" upptill och \"slå av och dimra ner\" nedtill.", "matterForwardingDescription": "Knapptryckningen leds vidare till Matter", "none": "Ingen", "buttonNoneDescription": "<PERSON><PERSON><PERSON> har ingen funktion", "buttonUnsetDescription": "<PERSON><PERSON><PERSON> har ingen inställd funktion", "sensorButtonTypeChangedSuccessfully": "Tryckknappsfunktionen har ändrats", "forExample": "t.ex. {example}}", "enOceanQRCodeInvalidDescription": "Endast möjligt från produktionsdatum 44/20", "input": "Ingång", "buttonSceneValueOverride": "Åsidosätta scenknappens värde", "buttonSceneValueOverrideDescription": "Scenknappens värde kommer att skrivas över med det aktuella dimvärdet genom en lång knapptryckning", "buttonSceneDescription": "Scenknappen tänder med ett specifikt dimvärde", "buttonPress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "triggerOn": "Med en längre knapptryckning  på en universaltryckknapp eller en riktningsknapp på tillsidan", "triggerOff": "Med en dubbeltryckning på en universaltryckknapp eller en riktningsknapp på frånsidan", "centralOn": "Centralt på", "centralOff": "Central av", "centralButton": "Centraltryckknapp", "enOceanAdapterNotFound": "Ingen EnOcean-adapter hittades", "updateRequired": "Uppdatering krävs", "updateRequiredDescription": "Din app behöver en uppdatering för att stödja den nya enheten.", "release32Header": "Den nya BR64 med Matter och EnOcean samt den nya Bluetooth dosmonterade kopplingsuret SU62PF-BT/UC är nu tillgängliga!", "release32EUD64Header": "Den nya dosdimmern med Matter via Wi-Fi och upp till 300W är här!", "release32EUD64Note1": "Konfiguration av dimringshastighet, på/av-hastighet, barnkammar/insomningsläge och mycket mer.", "release32EUD64Note2": "Funktionaliteten hos EUD64NPN-IPM kan utökas med hjälp av adapter, t.ex. EnOcean-adapter EOA64.", "release32EUD64Note3": "Upp till 30 trådlösa EnOcean-tryckknappar kan anslutas till EUD64NPN-IPM via EnOcean-adaptern EOA64 och/eller vidarebefordras till Matter.", "release32EUD64Note4": "Två trådbundna tryckknappsingångar som kan styra EUD64NPN-IPM direkt eller vidarebefordras till Matter.", "release32ESR64Header": "Det nya potentialfria, dosmonterade reläpucken med Matter via Wi-Fi och upp till 16A är här!", "release32ESR64Note1": "Konfiguration av olika funktioner som t.ex. impuls<PERSON><PERSON> (ES), arb<PERSON><PERSON><PERSON><PERSON><PERSON> (ER), normalt sluten (ER-inverterad) och mycket mer.", "release32ESR64Note2": "Funktionaliteten hos ESR64PF-IPM kan utökas med hjälp av adapter, t.ex. EnOcean-adapter EOA64.", "release32ESR64Note3": "Upp till 30 trådlösa EnOcean-tryckknappar kan läras in i ESR64PF-IPM i kombination med EnOcean-adaptern EOA64   för direkt styrning och/eller vidarebefordras till Matter.", "release32ESR64Note4": "En trådbunden knappingång kan styra ESR64PF-IPM direkt eller vidarebefordras till Matter.", "buttonsFound": "{count, plural, =0 {inga knappar hittades} one {1 knapp hittades} other {{count} knappar hittades}}", "doubleImpuls": "med en dubbel impuls", "impulseDescription": "<PERSON><PERSON>r kanalen är tillkopplad, kop<PERSON>las den från med en impuls.", "locationServiceEnable": "Aktivera plats", "locationServiceDisabledDescription": "Platstjänster är inaktiverat. Din operativsystemversion använder dessa för att kunna hitta Bluetooth-enheter.", "locationPermissionDeniedNoPosition": "Platstjänster är inte på . Din systemversion kräver platstjänster för att kunna hitta Bluetooth-enheter. Tillåt platstjänster i inställningarna för din enhet.", "interfaceStatePermanentDeniedDescriptionDevicesAround": "Tillstånd för enheter i närheten beviljades inte. Aktivera behörigheten i inställningarna för din enhet.", "permissionNearbyDevices": "Enheter i din närhet", "release320Header": "<PERSON> n<PERSON>, kraftfulla universaldimmern EUD12NPN-BT/600W-230V är här!", "release320EUD600Header": "Vad kan den nya universaldimmern?", "release320EUD600Note1": "Universaldimmer med upp till 600 W effekt", "release320EUD600Note2": "Utbyggbar med effektutökare LUD12 upp till 3800W", "release320EUD600Note3": "Trådbunden styrning med universal- eller riktnings-tryckknapp", "release320EUD600Note4": "Centralt På och Av", "release320EUD600Note5": "Ingång för rörelsedetektor för extra bekvämlighet", "release320EUD600Note6": "Integrerat kopplingsur med 10 programplatser", "release320EUD600Note7": "Astrofunktion", "release320EUD600Note8": "Individuell ljusstyrka vid tillkoppling", "mqttClientCertificate": "Klientcertifikat", "mqttClientCertificateHint": "MQTT-klientcertifikat", "mqttClientKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mqttClientKeyHint": "MQTT-k<PERSON><PERSON><PERSON><PERSON>", "mqttClientPassword": "Klientlösenord", "mqttClientPasswordHint": "MQTT-klientlösenord", "mqttEnableHomeAssistantDiscovery": "Aktivera HomeAssistant MQTT-upptäckt", "modbusTcp": "Modbus TCP", "enableInterface": "Aktivera port", "busAddress": "BUS-adress", "busAddressWithAddress": "BUS-adress {index}", "deviceType": "Enhetstyp", "registerTable": "{count, plural, one {Register<PERSON>bell} other {Registertabell}}", "currentValues": "Aktuella värden", "requestRTU": "Fråga RTU", "requestPriority": "Frågeprioritet", "mqttForwarding": "MQTT vidarekoppling", "historicData": "Historisk data", "dataFormat": "Dataformat", "dataType": "Datatyp", "description": "Beskrivning", "readWrite": "Läsa/skriva", "unit": "<PERSON><PERSON><PERSON>", "registerTableReset": "Återställ registertabell", "registerTableResetDescription": "Skall registertabellen verkligen återställas?", "notConfigured": "Inte konfigurerad", "release330ZGW16Header": "Större uppdatering för ZGW16WL-IP", "release330Header": "ZGW16WL-IP med upp till 16 energimätare", "release330ZGW16Note1": "Stödjer upp till 16 ELTAKO Modbus-energimätare", "release330ZGW16Note2": "Stöd för Modbus TCP", "release330ZGW16Note3": "<PERSON><PERSON>d för MQTT Discovery", "screenshotButtonLivingRoom": "Tryckknapp för var<PERSON>", "registerChangedSuccessfully": "Registret har ändrats framgångsrikt", "serverCertificateEmpty": "Servercertifikatet får inte vara tomt", "registerTemplates": "Registrera mallar", "registerTemplateChangedSuccessfully": "Registermall framgångsrikt ändrad", "registerTemplateReset": "Återställ registermall", "registerTemplateResetDescription": "Ska registermallen verkligen nollställas?", "registerTemplateNotAvailable": "Inga registermallar tillgängliga", "rename": "Byt namn", "registerName": "Registernamn", "registerRenameDescription": "Ange ett eget namn för registret", "restart": "Starta om enheten", "restartDescription": "Vill du verkligen starta om enheten?", "restartConfirmationDescription": "Enheten startas om", "deleteAllElectricityMeters": "Radera alla energimätare", "deleteAllElectricityMetersDescription": "Vill du verkligen radera alla energimätare?", "deleteAllElectricityMetersConfirmationDescription": "Alla energimätare har raderats", "resetAllElectricityMeters": "Återställ alla energimätarkonfigurationer", "resetAllElectricityMetersDescription": "Vill du verkligen återställa alla energimätarkonfigurationer?", "resetAllElectricityMetersConfirmationDescription": "Alla energimätarkonfigurationer har återställts", "deleteElectricityMeterHistories": "Raders all energimätarhistorik", "deleteElectricityMeterHistoriesDescription": "Vill du verkligen radera all energimätarhistorik?", "deleteElectricityMeterHistoriesConfirmationDescription": "All energimätarhistorik har raderats", "multipleElectricityMetersSupportMissing": "Din enhet stöder för närvarande endast en energimätare. Vänligen uppdatera din firmware.", "consumptionWithUnit": "Förbrukning (kWh)", "exportWithUnit": "Leverans (kWh)", "importWithUnit": "Produktion (kWh)", "resourceWarningHeader": "Begränsade resurser", "mqttAndTcpResourceWarning": "Det är inte möjligt att använda MQTT och Modbus TCP samtidigt på grund av begränsade systemresurser. Avaktivera {protocol} först.", "mqttEnabled": "MQTT aktiverad", "redirectMQTT": "Gå till MQTT-inställningar", "redirectModbus": "Gå till Modbus-inställningar", "unsupportedSettingDescription": "Med din nuvarande firmware-version stöds inte vissa av enhetens inställningar. Uppdatera din firmware för att använda de nya funktionerna", "updateNow": "Uppdatering nu", "zgw241Hint": "Med den här uppdateringen är Modbus TCP aktiverat som standard och MQTT är avaktiverat. Detta kan ändras i inställningarna. Med stöd för upp till 16 räknare har många optimeringar gjorts, vilket kan leda till ändringar i enhetens inställningar. Starta om enheten efter att du har justerat inställningarna.", "deviceConfigChangedSuccesfully": "Gångtiden har ä<PERSON>.", "deviceConfiguration": "Konfiguration av enhet", "tiltModeToggle": "Tilt-läge", "tiltModeToggleFooter": "Om enheten installeras i Matter måste alla funktioner konfigureras om där", "shaderMovementDirection": "Backa upp/ner", "shaderMovementDirectionDescription": "Omvänd riktning för upp/ner-rörelse av motorn", "tiltTime": "Tilt körtid", "changeTiltModeDialogTitle": "{target, select, true {Enable} false {Disable} other {Change}} tiltfunktion", "changeTiltModeDialogConfirmation": "{target, select, true {Enable} false {Disable} other {Change}}", "generalTextSlatSetting": "Inställning av lameller", "generalTextPosition": "Position", "generalTextSlatPosition": "Slat position", "slatSettingDescription": "Beskrivning av lamellinställning", "scenePositionSliderDescription": "<PERSON><PERSON><PERSON><PERSON>", "sceneSlatPositionSliderDescription": "Lutning", "referenceRun": "Kalibreringskörning", "slatAutoSettingHint": "I det här läget spelar persiennernas position ingen roll innan lamellerna justeras till önskat lutningsläge.", "slatReversalSettingHint": "I det här läget stängs persiennerna helt innan lamellerna justeras till önskat lutningsläge.", "release340Header": "Det nya infällda ESB64NP-IPM-ställdonet för solavskärmning är här!", "release340ESB64Header": "Vad är ESB64NP-IPM kapabel till?", "release340ESB64Note1": "Vårt Matter Gateway-certifierade solskyddsställdon med valfri lamellfunktion", "release340ESB64Note2": "Två trådbundna knappingångar för manuell växling och vidarekoppling till Matter", "release340ESB64Note3": "Kan byggas ut med EnOcean-adapter (EOA64). T.ex. med EnOcean trådlös tryckknapp F4T55", "release340ESB64Note4": "Öppet för integrationer tack vare REST API baserat på OpenAPI-standarden", "activateTiltModeDialogText": "Om tiltfunktionen är aktiverad kommer alla inställningar att gå förlorade. Är du säker på att du vill aktivera tiltfunktionen?", "deactivateTiltModeDialogText": "Om tiltfunktionen avaktiveras går alla inställningar förlorade. Är du säker på att du vill avaktivera tiltfunktionen?"}