{"appName": "ELTAKO Connect", "discoveryHint": "<PERSON>z le Bluetooth de l'appareil pour vous connecter", "devicesFound": "{count, plural, =0 {Aucun appareil trouvé} one {1 appareil trouvé} other {{count} appareils trouvés}}", "@devicesFound": {"description": "Specifies how many device are found in the discovery overview", "type": "text"}, "discoveryDemodeviceName": "{count, plural, one {Demo device} other {Demo devices}}", "discoverySu12Description": "Horloge 2 canaux Bluetooth", "discoveryImprint": "Impression", "discoveryLegalnotice": "Mention légale", "generalSave": "<PERSON><PERSON><PERSON><PERSON>", "generalCancel": "Annuler", "detailsHeaderHardwareversion": "Version du matériel", "detailsHeaderSoftwareversion": "Version du logiciel", "detailsHeaderConnected": "Connecté", "detailsHeaderDisconnected": "Déconnecté", "detailsTimersectionHeader": "Programmes", "detailsTimersectionTimercount": "de 60 programmes utilisés", "detailsConfigurationsectionHeader": "Configuration", "detailsConfigurationPin": "Code PIN de l´appareil", "detailsConfigurationChannelsDescription": "Canal 1 :{channel1} | Canal 2 :  {channel2}", "settingsCentralHeader": "On/Off centralisé", "detailsConfigurationCentralDescription": "S'applique uniquement si le canal est réglé sur AUTO", "detailsConfigurationDevicedisplaylock": "Verrouiller l'affichage de l´appareil", "timerOverviewHeader": "Programmes", "timerOverviewTimersectionTimerinactivecount": "inactif", "timerDetailsListsectionDays1": "<PERSON><PERSON>", "timerDetailsListsectionDays2": "<PERSON><PERSON>", "timerDetailsListsectionDays3": "<PERSON><PERSON><PERSON><PERSON>", "timerDetailsListsectionDays4": "<PERSON><PERSON>", "timerDetailsListsectionDays5": "<PERSON><PERSON><PERSON><PERSON>", "timerDetailsListsectionDays6": "<PERSON><PERSON>", "timerDetailsListsectionDays7": "<PERSON><PERSON><PERSON>", "timerDetailsHeader": "Programme %@", "timerDetailsSunrise": "Lever du soleil", "generalToggleOff": "<PERSON><PERSON><PERSON>", "generalToggleOn": "Allumé", "timerDetailsImpuls": "Impulsion", "generalTextTime": "<PERSON><PERSON><PERSON>", "generalTextAstro": "Astro", "generalTextAuto": "Auto", "timerDetailsOffset": "Décalage horaire", "timerDetailsPlausibility": "<PERSON>r le contrôle de plausibilité", "timerDetailsPlausibilityDescription": "Si l'heure d'arrêt est réglée sur une heure antérieure à l'heure de mise en marche, les deux programmes sont ignorés, par exemple, mise en marche au lever du soleil et arrêt à 6h00 du matin. Il existe également des cas où la vérification n'est pas souhaitée, par exemple allumer au coucher du soleil et éteindre à 1h00 du matin.", "generalDone": "<PERSON><PERSON><PERSON><PERSON>", "generalDelete": "<PERSON><PERSON><PERSON><PERSON>", "timerDetailsImpulsDescription": "Modifier la configuration globale des impulsions", "settingsNameHeader": "Nom de l´appareil", "settingsNameDescription": "Ce nom est utilisé pour identifier l'appareil.", "settingsFactoryresetHeader": "Paramètres d'usine", "settingsFactoryresetDescription": "Quel contenu doit être remis à zéro ?", "settingsFactoryresetResetbluetooth": "Réinitialiser les paramètres Bluetooth", "settingsFactoryresetResettime": "Réinitialiser les paramètres de l'heure", "settingsFactoryresetResetall": "Revenir aux paramètres d'usine", "settingsDeletetimerHeader": "Supprimer des programmes", "settingsDeletetimerDescription": "Faut-il vraiment supprimer tous les programmes ?", "settingsDeletetimerAllchannels": "To<PERSON> les canaux", "settingsImpulseHeader": "Temps de commutation des impulsions", "settingsImpulseDescription": "Le temps de commutation de l'impulsion définit la durée de l'impulsion.", "generalTextRandommode": "Mode aléatoire", "settingsChannelsTimeoffsetHeader": "Décalage de l'heure du solstice", "settingsChannelsTimeoffsetDescription": "Été : {summerOffset} min | Hiver : {winterOffset} min", "settingsLocationHeader": "Localisation", "settingsLocationDescription": "Définir l´emplacement pour utiliser les fonctions astro.", "settingsLanguageHeader": "Langue de l´appareil", "settingsLanguageSetlanguageautomatically": "Définir la langue automatiquement", "settingsLanguageDescription": "Choisissez la langue pour le {deviceType}", "settingsLanguageGerman": "Allemand", "settingsLanguageFrench": "Français", "settingsLanguageEnglish": "<PERSON><PERSON><PERSON>", "settingsLanguageItalian": "Italien", "settingsLanguageSpanish": "Espagnol", "settingsDatetimeHeader": "Date et heure", "settingsDatetimeSettimeautomatically": "Appliquer l'heure du système", "settingsDatetimeSettimezoneautomatically": "Définir automatiquement le fuseau horaire", "generalTextTimezone": "<PERSON><PERSON> ho<PERSON>", "settingsDatetime24Hformat": "Format 24 heures", "settingsDatetimeSetsummerwintertimeautomatically": "Été/hiver automatique", "settingsDatetimeWinter": "Hiver", "settingsDatetimeSummer": "<PERSON><PERSON>", "settingsPasskeyHeader": "Code PIN actuel de l´appareil", "settingsPasskeyDescription": "Saisir le code PIN actuel de l'appareil", "timerDetailsActiveprogram": "Programme actif", "timerDetailsActivedays": "Jours actifs", "timerDetailsSuccessdialogHeader": "<PERSON><PERSON>", "timerDetailsSuccessdialogDescription": "Programme ajouté avec succès", "settingsRandommodeDescription": "Le mode aléatoire ne fonctionne qu'avec les programmes horaires, pas avec les programmes d´impulsions ou astro (lever ou coucher du soleil).", "settingsSolsticeHeader": "Décalage de l'heure du solstice", "settingsSolsticeDescription": "L'heure indique le décalage horaire par rapport au coucher du soleil. Le lever du soleil est inversé en conséquence.", "settingsSolsticeHint": "Exemple:\nEn hiver, il s'allume 30 minutes avant le coucher du soleil, ce qui signifie qu'il s'allume également 30 minutes après le lever du soleil.", "generalTextMinutesShort": "min", "settingsPinDescription": "Le code PIN est nécessaire pour la connexion.", "settingsPinHeader": "Nouveau code PIN de l'appareil", "settingsPinNewpinDescription": "Saisir un nouveau code PIN", "settingsPinNewpinRepeat": "Répéter le nouveau code PIN", "detailsProductinfo": "Informations sur le produit", "settingsDatetimeSettimeautodescription": "Choisir l'heure souhaitée", "minutes": "{count, plural, one {Minute} other {Minutes}}", "hours": "{count, plural, one {Hour} other {Hours}}", "seconds": "{count, plural, one {Second} other {Seconds}}", "generalTextChannel": "{count, plural, one {Canal} other {Canaux}}", "generalLabelChannel": "Canal {number}", "generalTextDate": "Date", "settingsDatetime24HformatDescription": "Choisir le format préféré", "settingsDatetimeSetsummerwintertime": "Été/hiver", "settingsDatetime24HformatValue24": "24h", "settingsDatetime24HformatValue12": "AM/PM", "detailsEdittimer": "Modifier les programmes", "settingsPinOldpinRepeat": "Répéter le code PIN actuel", "settingsPinCheckpin": "Vérification du code PIN", "detailsDevice": "{count, plural, one {<PERSON>ce} other {Devices}}", "detailsDisconnect": "Déconnexion", "settingsCentralDescription": "L'entrée A1 contrôle la marche/l´arrêt central.\nCentral On/Off ne prend effet que si le canal est réglé sur Central On/Off.", "settingsCentralHint": "Exemple:\nCanal 1 = Marche/Arrêt central\nCanal 2 = Désactivé\nA1 = Central On -> Seul K1 passe à On, K2 reste éteint.", "settingsCentralToggleheader": "Mode de la commande centralisée", "settingsCentralActivechannelsdescription": "Canaux actuels avec le réglage Central On/Off :", "settingsSolsticeSign": "<PERSON>e", "settingsDatetimeTimezoneDescription": "Heure d'Europe centrale", "generalButtonContinue": "<PERSON><PERSON><PERSON>", "settingsPinConfirmationDescription": "Changement de code PIN réussi", "settingsPinFailDescription": "Échec du changement de code PIN", "settingsPinFailHeader": "Échec", "settingsPinFailShort": "Le code PIN doit comporter exactement 6 chiffres.", "settingsPinFailWrong": "Le code PIN actuel est incorrect", "settingsPinFailMatch": "Les codes PIN ne correspondent pas", "discoveryLostconnectionHeader": "Connexion perdue", "discoveryLostconnectionDescription": "L'appareil a été déconnecté.", "settingsChannelConfigCentralDescription": "Se comporte comme AUTO et réagit également aux entrées centrales filaires.", "settingsChannelConfigOnDescription": "Commute le canal en permanence sur ON et ignore les programmes.", "settingsChannelConfigOffDescription": "Commute le canal en permanence sur OFF et ignore les programmes", "settingsChannelConfigAutoDescription": "Commutation en fonction de l'heure et des programmes astro", "bluetoothPermissionDescription": "Le Bluetooth est nécessaire pour la configuration des appareils.", "timerListitemOn": "Allumer", "timerListitemOff": "<PERSON><PERSON><PERSON>", "timerListitemUnknown": "Inconnu", "timerDetailsAstroHint": "L'emplacement doit être défini dans les paramètres pour que les programmes astro fonctionnent correctement.", "timerDetailsTrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timerDetailsSunset": "Coucher de soleil", "settingsLocationCoordinates": "Coordonnées", "settingsLocationLatitude": "Latitude", "settingsLocationLongitude": "Longitude", "timerOverviewEmptyday": "Aucun programme n'est actuellement utilisé pour {day}", "timerOverviewProgramloaded": "Les programmes sont chargés", "timerOverviewProgramchanged": "Le programme a été modifié", "settingsDatetimeProcessing": "La date et l'heure sont modifiées", "deviceNameEmpty": "La saisie ne doit pas être vide", "deviceNameHint": "La saisie ne doit pas contenir plus de {count} caractères.", "deviceNameChanged": "Le nom de l´appareil est modifié", "deviceNameChangedSuccessfully": "Le nom de l´appareil a été modifié avec succès.", "deviceNameChangedFailed": "Une erreur s'est produite.", "settingsPinConfirm": "Confirmer", "deviceShowInstructions": "1. <PERSON><PERSON> le Bluetooth de l´horloge avec SET\n2. A<PERSON><PERSON><PERSON> sur le bouton en haut pour lancer la recherche.", "deviceNameNew": "Entrer le nouveau nom de l´appareil", "settingsLanguageRetrieved": "La langue est récupérée", "detailsProgramsShow": "Montrer les programmes", "generalTextProcessing": "<PERSON><PERSON><PERSON><PERSON>er", "generalTextRetrieving": "sont récupérés", "settingsLocationPermission": "Autoriser ELTAKO Connect à accéder à la localisation de cet appareil.", "timerOverviewChannelloaded": "Les canaux sont chargés", "generalTextRandommodeChanged": "Le mode aléatoire est modifié", "detailsConfigurationsectionChanged": "La configuration est modifiée", "settingsSettimeFunctions": "Les fonctions horaires sont modifiées", "imprintContact": "Contact", "imprintPhone": "Téléphone", "imprintMail": "<PERSON><PERSON><PERSON>", "imprintRegistrycourt": "Tribunal d'enregistrement", "imprintRegistrynumber": "Numéro d'enregistrement", "imprintCeo": "Directeur <PERSON><PERSON><PERSON>", "imprintTaxnumber": "Numéro d'identification TVA", "settingsLocationCurrent": "Localisation actuelle", "generalTextReset": "Réinitialiser", "discoverySearchStart": "Démarrer la recherche", "discoverySearchStop": "<PERSON><PERSON><PERSON><PERSON> recherche", "settingsImpulsSaved": "Le temps de commutation des impulsions est mémorisé", "settingsCentralNochannel": "Il n'y a pas de canaux avec le réglage central On/Off.", "settingsFactoryresetBluetoothConfirmationDescription": "La connexion Bluetooth a été réinitialisée avec succès.", "settingsFactoryresetBluetoothFailDescription": "La réinitialisation des connexions Bluetooth a échoué.", "imprintPublisher": "<PERSON><PERSON><PERSON>", "discoveryDeviceConnecting": "Connexion...", "discoveryDeviceRestarting": "Redémarrage...", "generalTextConfigurationsaved": "La configuration du canal est sauvegardée.\n", "timerOverviewChannelssaved": "Sauvegarder les canaux", "timerOverviewSaved": "Horloge enregistrée\n", "timerSectionList": "Affichage liste", "timerSectionDayview": "Affichage jour", "generalTextChannelInstructions": "Réglages des canaux", "generalTextPublisher": "<PERSON><PERSON><PERSON>", "settingsDeletetimerDialog": "Voulez-vous vraiment supprimer tous les programmes ?", "settingsFactoryresetResetbluetoothDialog": "Voulez-vous vraiment réinitialiser tous les paramètres Bluetooth ?", "settingsCentralTogglecentral": "On/Off\nCentralisé", "generalTextConfirmation": "{serviceName} changement réussi.", "generalTextFailed": "{serviceName} changement é<PERSON>ué.", "settingsChannelConfirmationDescription": "Les canaux ont été changés avec succès.", "timerDetailsSaveHeader": "Sauvegarder le programme.", "timerDetailsDeleteHeader": "Supprimer le programme", "timerDetailsSaveDescription": "Sauvegarde du programme réussie.", "timerDetailsDeleteDescription": "Suppression du programme réussie.", "timerDetailsAlertweekdays": "Le programme ne peut pas être sauvegardé, car aucun jour de la semaine n'est sélectionné.", "generalTextOk": "OK", "settingsDatetimeChangesuccesfull": "La date et l'heure ont été modifiées avec succès.", "discoveryConnectionFailed": "La connexion a échoué", "discoveryDeviceResetrequired": "Aucune connexion n'a pu être établie avec l'appareil. Pour résoudre ce problème, supprimer l'appareil des paramètres Bluetooth. <PERSON> le problème persiste, contacter notre support technique.", "generalTextSearch": "Rechercher des appareils", "generalTextOr": "ou", "settingsFactoryresetProgramsConfirmationDescription": "Tous les programmes ont été supprimés avec succès.", "generalTextManualentry": "<PERSON><PERSON>", "settingsLocationSaved": "Emplacement sauvegardé", "settingsLocationAutosearch": "Recherche automatique de l'emplacement", "imprintPhoneNumber": "+49 711 / 9435 0000", "imprintMailAddress": "<EMAIL>", "settingsFactoryresetResetallDialog": "Voulez-vous vraiment réinitialiser l'appareil aux paramètres d'usine ?", "settingsFactoryresetFactoryConfirmationDescription": "L'appareil a été réinitialisé avec succès aux paramètres d'usine.", "settingsFactoryresetFactoryFailDescription": "Échec de la réinitialisation de l'appareil.", "imprintPhoneNumberIos": "+49711/94350000", "mfzFunctionA2Title": "<PERSON><PERSON>lai de réponse en 2 étapes (A2)", "mfzFunctionA2TitleShort": "<PERSON><PERSON>lai de réponse en 2 étapes (A2)", "mfzFunctionA2Description": "Lorsque la tension de commande est appliquée, le laps de temps T1 compris entre 0 et 60 secondes commence. A la fin de cette période, le contact 1-2 se ferme et le laps de temps t2 entre 0 et 60 secondes commence. A la fin de ce temps, le contact 3-4 se ferme. Après une interruption, la séquence temporelle recommence avec t1.", "mfzFunctionRvTitle": "<PERSON><PERSON><PERSON> (RV)", "mfzFunctionRvTitleShort": "RV | <PERSON><PERSON><PERSON>", "mfzFunctionRvDescription": "Lorsque la tension de commande est appliquée, le contact à fermeture change après 15-18. \nLorsque la tension de commande est interrompue, le laps de temps commence, à la fin duquel le contact à fermeture revient en position de repos. Peut être mis en marche pendant le laps de temps.", "mfzFunctionTiTitle": "Générateur d'impulsion démarrant avec une impulsion (TI; relais de clignotant)", "mfzFunctionTiTitleShort": "TI | Générateur d'impulsion démarrant avec sune impulsion", "mfzFunctionTiDescription": "Tant que la tension de commande est appliquée, le contact de commande se ferme et s'ouvre. Le temps de commutation dans les deux sens peut être réglé séparément. Lorsque la tension de commande est appliquée, le contact de commande passe immédiatement à 15-18.", "mfzFunctionAvTitle": "<PERSON><PERSON><PERSON> (AV; d<PERSON><PERSON> d'allumage)", "mfzFunctionAvTitleShort": "AV | <PERSON><PERSON><PERSON>", "mfzFunctionAvDescription": "Lorsque la tension de commande est appliquée, le laps de temps commence, à la fin duquel le contact à fermeture passe à 15-18. Après une interruption, le laps de temps recommence.", "mfzFunctionAvPlusTitle": "<PERSON><PERSON><PERSON> de réponse additif (AV+; délai de mise en marche)", "mfzFunctionAvPlusTitleShort": "AV+ | <PERSON><PERSON><PERSON> réponse additif", "mfzFunctionAvPlusDescription": "Fonctionne comme AV, mais après une interruption, le temps déjà écoulé reste mémorisé.", "mfzFunctionAwTitle": "<PERSON><PERSON><PERSON> (AW)", "mfzFunctionAwTitleShort": "AW | Relais temporisé", "mfzFunctionAwDescription": "Lorsque la tension de commande est interrompue, le contact change vers 15-18 et revient après que la temporisation se soit écoulée. Lorsque la tension de commande est appliquée pendant la temporisation, le contact revient immédiatement en position de repos et le temps restant est effacé.", "mfzFunctionIfTitle": "Mise en forme des impulsions (IF; MFZ12.1 uniquement)", "mfzFunctionIfTitleShort": "IF | Mise en forme des impulsions", "mfzFunctionIfDescription": "Lorsque la tension de commande est appliquée, le contact de fermeture change pour le temps réglé après 15-18. Les autres actionnements ne sont évalués qu'après l'écoulement du temps réglé.", "mfzFunctionEwTitle": "<PERSON><PERSON><PERSON> (EW)", "mfzFunctionEwTitleShort": "EW | Relais temporisé", "mfzFunctionEwDescription": "Lorsque la tension de commande est appliquée, le contact passe à 15-18 et revient après que la temporisation se soit écoulée. Lorsque la tension de commande est supprimée pendant la temporisation, le contact revient immédiatement en position de repos et le temps restant est effacé.", "mfzFunctionEawTitle": "Relais temporisé  à l'allumage et à l'extinction (EAW; MFZ12.1 seulement)", "mfzFunctionEawTitleShort": "EAW | Relais temporisé de mise en marche et d'arrêt", "mfzFunctionEawDescription": "Lorsque la tension de commande est appliquée et interrompue, le contact de fermeture passe à 15-18 et revient après le temps réglé.", "mfzFunctionTpTitle": "Générateur d'impulsions démarrant avec une pause (TP; relais de clignotant, MFZ12.1 seulement)", "mfzFunctionTpTitleShort": "TP | Générateur d'impulsions démarrant par une pause", "mfzFunctionTpDescription": "Descriptions fonctionnelles comme TI, mais lorsque la tension de commande est appliquée, le contact ne passe pas à 15-18, mais reste initialement à 15-16 ou ouvert.", "mfzFunctionIaTitle": "<PERSON><PERSON><PERSON> et mise en forme des impulsions commandés par impulsion (IA; MFZ12.1 uniquement)", "mfzFunctionIaTitleShort": "IA |  <PERSON><PERSON><PERSON>po<PERSON> commandé par impulsion", "mfzFunctionIaDescription": "Avec le début d'une impulsion de commande à partir de 20 ms commence le laps de temps t1, à la fin duquel le\nà la fin duquel le contact de fermeture change pour le temps t2 après 15-18 (par exemple pour les ouvreurs de portes automatiques). Si t1 est réglé sur le temps le plus court 0,1 s, IA fonctionne comme un façonneur d'impulsions à la fin duquel t2 expire, quelle que soit la longueur du signal de commande (min. 150 ms).", "mfzFunctionArvTitle": "Temporisation de réponse et délai d´ouverture (ARV)", "mfzFunctionArvTitleShort": "ARV | Temporisation de réponse et délai d´ouverture", "mfzFunctionArvDescription": "Lorsque la tension de commande est appliquée, la temporisation commence, à la fin de laquelle le contact de commande passe à 15 -18. Si la tension de commande est ensuite interrompue, une nouvelle temporisation commence, à la fin de laquelle le contact de commande revient en position de repos.\nAprès une interruption de la temporisation de réponse, la temporisation recommence.", "mfzFunctionArvPlusTitle": "Réponse additive et délai d´ouverture (ARV+)", "mfzFunctionArvPlusTitleShort": "ARV+ | Réponse additive et délai d´ouverture", "mfzFunctionArvPlusDescription": "Fonctionne comme ARV, mais après une interruption du délai de réponse, le temps déjà écoulé reste mémorisé.", "mfzFunctionEsTitle": "<PERSON><PERSON><PERSON>rup<PERSON>ur (ES)", "mfzFunctionEsTitleShort": "ES | Télérupteur", "mfzFunctionEsDescription": "Le contact à fermeture commute d'avant en arrière avec des impulsions de commande de 50 ms.", "mfzFunctionEsvTitle": "Télérupteur avec temporisation et alerte précoce d'arrêt (ESV)", "mfzFunctionEsvTitleShort": "ESV | Télérupteur avec temporisation", "mfzFunctionEsvDescription": "Fonctionne comme SRV. En outre, avec un avertissement préalable à l'extinction : à partir d'environ 30 secondes avant l'expiration du temps, l'éclairage clignote 3 fois à intervalles décroissants.", "mfzFunctionErTitle": "<PERSON><PERSON><PERSON> (ER)", "mfzFunctionErTitleShort": "ER | Relais", "mfzFunctionErDescription": "Tant que le contact de commande est fermé, le contact de travail passe de 15-16 à 15-18.", "mfzFunctionSrvTitle": "Télérupteur avec temporisation (SRV)", "mfzFunctionSrvTitleShort": "SRV | Télérupteur avec temporisation", "mfzFunctionSrvDescription": "Le contact à fermeture commute en alternance avec des impulsions de commande à partir de 50ms. En position de contact 15-18, le dispositif revient automatiquement en position de repos 15-16 après l'écoulement de la temporisation.", "detailsFunctionsHeader": "Fonctions", "mfzFunctionTimeHeader": "Temps (t{index})", "mfzFunctionOnDescription": "activé en permanence", "mfzFunctionOffDescription": "arr<PERSON><PERSON> permanent", "mfzFunctionMultiplier": "Facteur", "discoveryMfz12Description": "Relais horaire multifonctionnel Bluetooth", "mfzFunctionOnTitle": "activé en permanence", "mfzFunctionOnTitleShort": "activé en permanence", "mfzFunctionOffTitle": "arr<PERSON><PERSON> permanent", "mfzFunctionOffTitleShort": "arr<PERSON><PERSON> permanent", "mfzMultiplierSecondsFloatingpoint": "0.1 secondes", "mfzMultiplierMinutesFloatingpoint": "0.1 minutes", "mfzMultiplierHoursFloatingpoint": "0.1 heures", "mfzOverviewFunctionsloaded": "Les fonctions sont chargées", "mfzOverviewSaved": "Fonction sauvegardée", "settingsBluetoothHeader": "Bluetooth", "bluetoothChangedSuccessfully": "Le paramètre Bluetooth a été modifié avec succès.", "settingsBluetoothInformation": "Remarque : Si ce paramètre est activé, l'appareil sera visible en permanence par tout le monde via Bluetooth !\nIl est recommandé de modifier le code PIN de l'appareil.", "settingsBluetoothContinuousconnection": "Visibilité durable", "settingsBluetoothContinuousconnectionDescription": "En activant la visibilité permanente, Bluetooth reste actif sur l´appareil ({deviceType}) et ne doit pas être activé manuellement avant l'établissement de la connexion.", "settingsBluetoothTimeout": "Délais de connexion écoulé", "settingsBluetoothPinlimit": "limite du PIN", "settingsBluetoothTimeoutDescription": "La connexion est interrompue après {timeout} minutes d'inactivité.", "settingsBluetoothPinlimitDescription": "Pour des raisons de sécurité, vous disposez au maximum de {attempts} tentatives pour la saisie du code PIN. Ensuite, Bluetooth est désactivé \net doit être réactivé manuellement pour une nouvelle connexion. \nêtre activée à nouveau.", "settingsBluetoothPinAttempts": "tentatives", "settingsResetfunctionHeader": "Réinitialiser les fonctions", "settingsResetfunctionDialog": "Voulez-vous vraiment réinitialiser toutes les fonctions?", "settingsFactoryresetFunctionsConfirmationDescription": "Toutes les fonctions ont été réinitialisées avec succès.", "mfzFunctionTime": "Temps (t)", "discoveryConnectionFailedInfo": "Pas de connexion Bluetooth", "detailsConfigurationDevicedisplaylockDialogtext": "Immédiatement après le verrouillage de l’écran de l’appareil, Bluetooth est désactivé et doit être réactivé manuellement afin d’établir une nouvelle connexion.", "detailsConfigurationDevicedisplaylockDialogquestion": "Êtes-vous sûr de verrouiller l’affichage de l’appareil?", "settingsDemodevices": "Afficher les appareils de démonstration", "generalTextSettings": "Paramètres", "discoveryWifi": "WiFi", "settingsInformations": "Informations", "detailsConfigurationDimmingbehavior": "Comportement de variation", "detailsConfigurationSwitchbehavior": "Comportement du commutateur", "detailsConfigurationBrightness": "Luminosité", "detailsConfigurationMinimum": "Minimum", "detailsConfigurationMaximum": "Maximum", "detailsConfigurationSwitchesGr": "Groupe relais (GR)", "detailsConfigurationSwitchesGs": "Commutateur de groupe (GS)", "detailsConfigurationSwitchesCloserer": "Contact NO (ER)", "detailsConfigurationSwitchesClosererDescription": "Arrêt -> <PERSON><PERSON> (On) -> <PERSON><PERSON><PERSON><PERSON><PERSON> (Off)", "detailsConfigurationSwitchesOpenerer": "Contact NF (ER-Invers)", "detailsConfigurationSwitchesOpenererDescription": "On -> <PERSON><PERSON> (Off) -> <PERSON><PERSON><PERSON><PERSON><PERSON> (On)", "detailsConfigurationSwitchesSwitch": "Interrupteur", "detailsConfigurationSwitchesSwitchDescription": "Chaque pression sur le bouton-poussoir permet d'allumer et d'éteindre la lumière.", "detailsConfigurationSwitchesImpulsswitch": "Télérupteur", "detailsConfigurationSwitchesImpulsswitchDescription": "Le bouton-poussoir est brièvement pressé et relâché pour allumer ou éteindre la lumière.", "detailsConfigurationSwitchesClosererDescription2": "Maintenez le bouton-poussoir enfoncé. Lorsqu'il est relâché, le moteur s'arrête", "detailsConfigurationSwitchesImpulsswitchDescription2": "Le bouton-poussoir est pressé brièvement pour démarrer le moteur et pressé brièvement pour l'arrêter à nouveau.", "detailsConfigurationWifiloginScan": "Scanner le code QR", "detailsConfigurationWifiloginScannotvalid": "Le code scanné n'est pas valide", "detailsConfigurationWifiloginDescription": "Saisir le code", "detailsConfigurationWifiloginPassword": "Mot de passe", "discoveryEsbipDescription": "Actionneur de volets roulants et d'ombrage IP", "discoveryEsripDescription": "Télérupteurs IP", "discoveryEudipDescription": "Variateur de lumière universel IP", "generalTextLoad": "Chargement", "wifiBasicautomationsNotFound": "Aucune automatisation trouvée.", "wifiCodeInvalid": "Code non valide", "wifiCodeValid": "Code valide", "wifiAuthorizationLogin": "Connecter", "wifiAuthorizationLoginFailed": "La connexion a échoué", "wifiAuthorizationSerialnumber": "Numéro de série", "wifiAuthorizationProductiondate": "Date de production", "wifiAuthorizationProofofpossession": "PoP", "generalTextWifipassword": "Mot de passe WiFi", "generalTextUsername": "Nom d'utilisateur", "generalTextEnter": "OU ENTRER MANUELLEMENT", "wifiAuthorizationScan": "Scanner le code ELTAKO.", "detailsConfigurationDevicesNofunctionshinttext": "Cet appareil ne prend actuellement en charge aucun autre paramètre", "settingsUsedemodelay": "Utiliser le délai de d<PERSON>", "settingsImpulsLoad": "Temps de commutation des impulsions en cours de chargement", "settingsBluetoothLoad": "Paramètre Bluetooth en cours de chargement.", "detailsConfigurationsectionLoad": "Les configurations sont chargées", "generalTextLogin": "Connexion", "generalTextAuthentication": "S'authentifier", "wifiAuthorizationScanDescription": "Recherchez le code ELTAKO sur l'appareil WiFi ou sur la carte d'information ci-jointe et scannez-le avec votre appareil photo.", "wifiAuthorizationScanShort": "Scanner le code ELTAKO", "detailsConfigurationEdgemode": "Courve de variation", "detailsConfigurationEdgemodeLeadingedge": "Coupure de début de phase", "generalTextNetwork": "<PERSON><PERSON><PERSON>", "wifiAuthenticationSuccessful": "Authentification réussie", "detailsConfigurationsectionSavechange": "Configuration modifiée", "discoveryWifiAdddevice": "Ajouter un appareil Wi-Fi", "wifiAuthenticationDelay": "<PERSON><PERSON> peut durer jusqu'à 1 minute", "generalTextRetry": "Retenter", "wifiAuthenticationCredentials": "Veuillez entrer les informations d'identification de votre WiFi", "wifiAuthenticationSsid": "SSID", "wifiAuthenticationDelaylong": "<PERSON><PERSON> peut durer jusqu'à 1 minute jusqu'à ce que l'appareil soit prêt et s'affiche dans l'application", "wifiAuthenticationCredentialsShort": "Entrez les informations d'identification Wi-Fi", "wifiAuthenticationTeachin": "Appairage de l'appareil dans le WiFi", "wifiAuthenticationEstablish": "Établir la connexion avec l'appareil", "wifiAuthenticationEstablishLong": "L'appareil se connecte au Wi-Fi {ssid}", "wifiAuthenticationFailed": "La connexion a échoué. Débranchez l'appareil de l'alimentation pendant quelques secondes et réessayez de le connecter", "wifiAuthenticationReset": "Réinitialiser l'authentification", "wifiAuthenticationResetHint": "Toutes les données d'authentification seront supprimées.", "wifiAuthenticationInvaliddata": "Données d'authentification non valides", "wifiAuthenticationReauthenticate": "S'authentifier à nouveau", "wifiAddhkdeviceHeader": "Ajouter un appareil", "wifiAddhkdeviceDescription": "Connectez votre nouvel appareil ELTAKO à votre WIFI via l'application Apple Home.", "wifiAddhkdeviceStep1": "Ouvrez l'application Apple Home.", "wifiAddhkdeviceStep2": "Cliquez sur le plus dans le coin supérieur droit de l'application et sélectionnez **Add Device**.", "wifiAddhkdeviceStep3": "Su<PERSON>z les instructions de l'application.", "wifiAddhkdeviceStep4": "4. Votre appareil peut maintenant être configuré dans l'application ELTAKO Connect.", "detailsConfigurationRuntime": "Temps d'exécution", "detailsConfigurationRuntimeMode": "Mode", "generalTextManually": "<PERSON><PERSON>", "detailsConfigurationRuntimeAutoDescription": "L'actionneur d'ombrage détermine indépendamment le temps de fonctionnement pendant chaque déplacement de la position finale inférieure à la position finale supérieure (recommandé).\nAprès la mise en service, une telle\ndoit être effectuée de bas en haut sans interruption.\nsans interruption.", "detailsConfigurationRuntimeManuallyDescription": "La durée de fonctionnement du moteur d'ombrage est réglée manuellement via la durée ci-dessous.", "detailsConfigurationRuntimeDemoDescription": "Le mode démo est uniquement disponible via l'API REST.", "generalTextDemomodeActive": "Mode démo actif", "detailsConfigurationRuntimeDuration": "<PERSON><PERSON><PERSON>", "detailsConfigurationSwitchesGs4": "Commutateur de groupe (GS4)", "detailsConfigurationSwitchesGs4Description": "Interrupteur de groupe avec fonction d'inversion de marche pour la commande de stores", "screenshotSu12": "<PERSON><PERSON> jardin", "screenshotS2U12": "<PERSON><PERSON> jardin", "screenshotMfz12": "Pompe", "screenshotEsr62": "Lam<PERSON>", "screenshotEud62": "<PERSON><PERSON> p<PERSON>", "screenshotEsb62": "Ombrage balcon", "detailsConfigurationEdgemodeLeadingedgeDescription": "LC1-LC3 sont des positions de confort avec différentes courbes de variation pour les lampes LED 230 V dimmables, qui ne peuvent pas être variées suffisamment en AUTO en raison de leur conception et doivent donc être forcées en coupure de début de phase.", "detailsConfigurationEdgemodeAutoDescription": "AUTO permet la variation de tous les types de lampes.", "detailsConfigurationEdgemodeTrailingedge": "Coupure de fin de phase", "detailsConfigurationEdgemodeTrailingedgeDescription": "LC4-LC6 sont des positions de confort avec différentes courbes de variation pour les lampes LED 230 V dimmables.", "updateHeader": "Mise à jour du logiciel", "updateTitleStepSearch": "Recherche d'une mise à jour", "updateTitleStepFound": "Une mise à jour a été trouvée", "updateTitleStepDownload": "Téléchargement de la mise à jour", "updateTitleStepInstall": "Installation de la mise à jour", "updateTitleStepSuccess": "Mise à jour réussie", "updateTitleStepUptodate": "<PERSON><PERSON><PERSON><PERSON> à jour", "updateTitleStepFailed": "Échec de la mise à jour", "updateButtonSearch": "Recherche de mises à jour", "updateButtonInstall": "Installer la mise à jour", "updateCurrentversion": "Version actuelle", "updateNewversion": "Nouvelle mise à jour du firmware disponible", "updateHintPower": "La mise à jour ne démarre que si la sortie de l'appareil n'est pas active. L'appareil ne doit pas être débranché de l'alimentation électrique et l'application ne doit pas être abandonnée pendant la mise à jour !", "updateButton": "Mise à jour", "updateHintCompatibility": "Une mise à jour est recommandée, sinon certaines fonctions de l'application seront limitées.", "generalTextDetails": "Détails", "updateMessageStepMetadata": "Télécharger les informations de mise à jour", "updateMessageStepPrepare": "La mise à jour est obligatoire", "updateTitleStepUpdatesuccessful": "La mise à jour est terminée", "updateTextStepFailed": "La mise à jour est un peu difficile à réaliser, il faudra attendre quelques minutes avant de pouvoir procéder à une mise à jour automatique de l'appareil (connexion Internet obligatoire).", "configurationsNotavailable": "Aucune configuration n'est encore disponible", "configurationsAddHint": "Créez de nouvelles configurations en vous connectant à un appareil et en enregistrant une configuration.", "@configurationsAddHint": {"description": "Now available for all devices not only Bluetooth", "type": "text"}, "configurationsEdit": "Modifier la configuration", "generalTextName": "Nom", "configurationsDelete": "Supprimer la configuration", "configurationsDeleteHint": "La configuration: {configName} doit-elle vraiment être supprimée ?", "configurationsSave": "Sauvegarder la configuration", "configurationsSaveHint": "<PERSON><PERSON>, vous pouvez enregistrer la configuration de votre appareil actuel ou charger une configuration déjà enregistrée.", "configurationsImport": "Importer la configuration", "configurationsImportHint": "La configuration {configName} doit-elle vraiment être transférée ?", "generalTextConfigurations": "{count, plural, one {Configuration} other {Configurations}}", "configurationsStepPrepare": "La configuration est en cours de préparation", "configurationsStepName": "Saisir un nom pour la configuration", "configurationsStepSaving": "La configuration est sauvegardée", "configurationsStepSavedsuccessfully": "La configuration a été sauvegardée avec succès", "configurationsStepSavingfailed": "L'enregistrement de la configuration a échoué", "configurationsStepChoose": "Sélectionner une configuration", "configurationsStepImporting": "La configuration est importée", "configurationsStepImportedsuccessfully": "La configuration a été importée avec succès", "configurationsStepImportingfailed": "L'importation de la configuration a échoué", "discoveryAssuDescription": "Interrupteur horaire pour prise extérieure Bluetooth", "settingsDatetimeDevicetime": "Temps réel de l'appareil", "settingsDatetimeLoading": "Les réglages de l'heure sont chargés", "discoveryEud12Description": "Variateur universel Bluetooth", "generalTextOffdelay": "<PERSON><PERSON><PERSON>", "generalTextRemainingbrightness": "Luminosité restante", "generalTextSwitchonvalue": "Valeur d'enclenchement", "motionsensorTitleNoremainingbrightness": "Pas de luminosité résiduelle", "motionsensorTitleAlwaysremainingbrightness": "Avec une luminosité résiduelle", "motionsensorTitleRemainingbrightnesswithprogram": "Luminosité résiduelle par programme de commutation", "motionsensorTitleRemainingbrightnesswithprogramandzea": "Luminosité résiduelle via ZE et ZA", "motionsensorTitleNoremainingbrightnessauto": "Pas de luminosité résiduelle (semi-automatique)", "generalTextMotionsensor": "Détecteur de mouvement", "generalTextLightclock": "Réveil lumine<PERSON>", "generalTextSnoozeclock": "Fonction Snooze", "generalDescriptionLightclock": "Lo<PERSON> de l'allumage ({mode}), la lumière s'allume après environ 1 seconde à la luminosité la plus faible et augmente lentement sans modifier le dernier niveau de luminosité enregistré.", "generalDescriptionSnoozeclock": "Lors de l'extinction ({mode}), l'éclairage est réduit de la position de variation actuelle jusqu'à la luminosité minimale et s'éteint. L'éclairage peut être éteint à tout moment pendant le processus de variation en appuyant brièvement sur la touche. Une pression prolongée sur la touche pendant le processus de variation permet d'augmenter la luminosité et de mettre fin à la fonction \"snooze\".", "generalTextImmediately": "Immédiatement", "generalTextPercentage": "Pourcentage", "generalTextSwitchoffprewarning": "<PERSON><PERSON><PERSON><PERSON> d'extinction", "generalDescriptionSwitchoffprewarning": "Diminution lente jusqu'à la luminosité minimale", "generalDescriptionOffdelay": "L'appareil s'allume lorsque la tension de commande est appliquée. Si la tension de commande est interrompue, le délai commence à s'écouler, après quoi l'appareil s'éteint. L'appareil peut être mis en marche en aval pendant le délai.", "generalDescriptionBrightness": "La luminosité à laquelle la lampe est allumée par le variateur.", "generalDescriptionRemainingbrightness": "La valeur de variation en pourcentage à laquelle la lampe est variée après la désactivation du détecteur de mouvement.", "generalDescriptionRuntime": "Durée de fonctionnement de la fonction d'alarme lumineuse, de la luminosité minimale à la luminosité maximale.", "generalTextUniversalbutton": "Bouton-poussoir universel", "generalTextDirectionalbutton": "Bouton directionnel", "eud12DescriptionAuto": "Détection automatique UT/RT (avec diode RTD)", "eud12DescriptionRt": "avec diode de détection directionnelle RTD", "generalTextProgram": "Programme", "eud12MotionsensorOff": "Lorsque le détecteur de mouvement est réglé sur Off", "eud12ClockmodeTitleProgramze": "Programme et central On", "eud12ClockmodeTitleProgramza": "Programme et arrêt central", "eud12ClockmodeTitleProgrambuttonon": "Programme et UT/RT On", "eud12ClockmodeTitleProgrambuttonoff": "Programme et UT/RT Off", "eud12TiImpulseTitle": "Temps d'impulsion On (t1)", "eud12TiImpulseHeader": "Valeur de variation Temps d'impulsion On", "eud12TiImpulseDescription": "Valeur de variation en pourcentage à laquelle la lampe est variée lors de l´impulsion ON.", "eud12TiOffTitle": "Temps d'impulsion Off (t2)", "eud12TiOffHeader": "Valeur de variation Temps d'impulsion Arrêt", "eud12TiOffDescription": "La valeur de variation en pourcentage à laquelle la lampe est variée lors de l'impulsion OFF.", "generalTextButtonpermanentlight": "Bouton-poussoir pour lumière permanente", "generalDescriptionButtonpermanentlight": "Réglage de l'éclairage continu par bouton-poussoir de 0 à 10 heures par incréments de 0,5 heure. Activation en appuyant sur le bouton pendant plus d'une seconde (1x clignotement), désactivation en appuyant sur le bouton pendant plus de 2 secondes.", "generalTextNobuttonpermanentlight": "Pas de TSP", "generalTextBasicsettings": "Paramètres de base", "generalTextInputswitch": "Entrée bouton local (A1)", "generalTextOperationmode": "Mode de fonctionnement", "generalTextDimvalue": "Comportement d´allumage", "eud12TitleUsememory": "Utiliser la valeur de mémoire", "eud12DescriptionUsememory": "La valeur de mémoire correspond à la dernière valeur de variation réglée. Si la valeur mémoire est désactivée, la variation est toujours réglée sur la valeur d'enclenchement.", "generalTextStartup": "Luminosité à l'allumage", "generalDescriptionSwitchonvalue": "La valeur d'enclenchement est une valeur de luminosité réglable qui garantit un allumage sûr.", "generalTitleSwitchontime": "Du<PERSON><PERSON> de mise en service", "generalDescriptionSwitchontime": "Une fois le temps d'allumage écoulé, la lampe passe de la valeur d'allumage à la valeur de mémorisation.", "generalDescriptionStartup": "Certaines lampes LED nécessitent un courant d'appel plus élevé pour s'allumer de manière fiable. La lampe est allumée à cette valeur d'allumage, puis réduite à la valeur de mémoire après le temps d'allumage.", "eud12ClockmodeSubtitleProgramze": "Court clic sur Central On", "eud12ClockmodeSubtitleProgramza": "Clic court sur l'arrêt central", "eud12ClockmodeSubtitleProgrambuttonon": "Double-clic sur le bouton universel/le bouton de direction Activé", "eud12ClockmodeSubtitleProgrambuttonoff": "Double-clic sur le bouton universel/le bouton de direction Désactivé", "eud12FunctionStairlighttimeswitchTitleShort": "TLZ | Minuterie d'éclairage d'escalier", "eud12FunctionMinTitleShort": "MIN", "eud12FunctionMmxTitleShort": "MMX", "eud12FunctionTiDescription": "Minuterie avec temps d'allumage et d'extinction réglable de 0,5 seconde à 9,9 minutes. La luminosité peut être réglée de la luminosité minimale à la luminosité maximale.", "eud12FunctionAutoDescription": "Variateur universel avec réglage du détecteur de mouvement, de l'alarme lumineuse et de la fonction \"snooze\".", "eud12FunctionErDescription": "Relais de commutation, la luminosité peut être réglée de la luminosité minimale à la luminosité maximale.", "eud12FunctionEsvDescription": "Variateur universel avec réglage d'un délai d'extinction de 1 à 120 minutes. Préavis d'extinction à la fin par diminution de l'intensité lumineuse sélectionnable et réglable de 1 à 3 minutes. Les deux entrées centrales sont actives.", "eud12FunctionTlzDescription": "Réglage de la durée d'éclairage du bouton de 0 à 10 heures par incréments de 0,5 heure. Activation en appuyant sur le bouton pendant plus d'une seconde (1x clignotement), désactivation en appuyant sur le bouton pendant plus de 2 secondes.", "eud12FunctionMinDescription": "Variateur de lumière universel, qui se met à la luminosité minimale réglée lorsque la tension de commande est appliquée. La lumière est atténuée jusqu'à la luminosité maximale pendant la durée de variation réglée de 1 à 120 minutes. Lorsque la tension de commande est supprimée, la lumière s'éteint immédiatement, même pendant le temps de variation. Les deux entrées centrales sont actives.", "eud12FunctionMmxDescription": "Le variateur universel permet de régler la luminosité minimale lorsque la tension de commande est appliquée. Pendant la durée de variation programmée de 1 à 120 minutes, la lumière est atténuée jusqu'à la luminosité maximale. <PERSON><PERSON><PERSON><PERSON>, lorsque la tension de commande est supprimée, le variateur descend à la luminosité minimale réglée. Il s'éteint ensuite. Les deux entrées centrales sont actives.", "motionsensorSubtitleNoremainingbrightness": "Lorsque le détecteur de mouvement est réglé sur Off", "motionsensorSubtitleAlwaysremainingbrightness": "Lorsque le détecteur de mouvement est réglé sur Off", "motionsensorSubtitleRemainingbrightnesswithprogram": "Programme de commutation activé et désactivé avec BWM désactivé", "motionsensorSubtitleRemainingbrightnesswithprogramandzea": "Central On active le détecteur de mouvement, Central Off désactive le détecteur de mouvement, ainsi que par le programme de commutation.", "motionsensorSubtitleNoremainingbrightnessauto": "Le détecteur de mouvement s'éteint uniquement", "detailsDimsectionHeader": "Variation", "generalTextFast": "Rapide", "generalTextSlow": "Lentement", "eud12TextDimspeed": "Vitesse de variation", "eud12TextSwitchonspeed": "Vitesse d'allumage", "eud12TextSwitchoffspeed": "Vitesse d'extinction", "eud12DescriptionDimspeed": "La vitesse de variation est la vitesse à laquelle le variateur passe de la luminosité actuelle à la luminosité cible.", "eud12DescriptionSwitchonspeed": "La vitesse d'enclenchement est la vitesse dont le variateur a besoin pour s'allumer complètement.", "eud12DescriptionSwitchoffspeed": "La vitesse d'extinction est la vitesse dont le variateur a besoin pour s'éteindre complètement.", "settingsFactoryresetResetdimHeader": "Réinitialisation des paramètres de variation", "settingsFactoryresetResetdimDescription": "Faut-il vraiment réinitialiser tous les paramètres de variation ?", "settingsFactoryresetResetdimConfirmationDescription": "Les paramètres de variation ont été réinitialisés avec succès", "eud12TextSwitchonoffspeed": "Vitesse de marche/arrêt", "eud12DescriptionSwitchonoffspeed": "La vitesse d'allumage et d'extinction est la vitesse dont le variateur a besoin pour s'allumer ou s'éteindre complètement.", "timerDetailsDimtoval": "Allumé avec valeur de variation en %", "timerDetailsDimtovalDescription": "Le variateur s'allume toujours avec la valeur de variation fixe en %.", "timerDetailsDimtovalSubtitle": "Allumer avec {brightness}%", "timerDetailsDimtomem": "Activé avec la valeur de la mémoire", "timerDetailsDimtomemSubtitle": "Mise en marche avec valeur de mémoire", "timerDetailsMotionsensorwithremainingbrightness": "Luminosité résiduelle (BWM) Activé", "timerDetailsMotionsensornoremainingbrightness": "Luminosité résiduelle (BWM) Désactivé", "settingsRandommodeHint": "Lorsque le mode aléatoire est activé, tous les temps de commutation du canal sont décalés de manière aléatoire. Avec des heures d'allumage jusqu'à 15 minutes plus tôt et des heures d'arrêt jusqu'à 15 minutes plus tard.", "runtimeOffsetDescription": "Dépassement supplémentaire, après la fin du temps de trajet", "loadingTextDimvalue": "La valeur de variation est chargée", "discoveryEudipmDescription": "Télévariateur universel IP Matter", "generalTextOffset": "Dépassement", "eud12DimvalueTestText": "Envoyer la luminosité", "eud12DimvalueTestDescription": "La vitesse de variation actuellement réglée est prise en compte lors des tests.", "eud12DimvalueLoadText": "Charger la luminosité", "settingsDatetimeNotime": "Les réglages de la date et de l'heure doivent être lus sur l'écran de l'appareil.", "generalMatterText": "Matter", "generalMatterMessage": "Veuillez appairer votre appareil Matter à l'aide de l'une des applications suivantes.", "generalMatterOpengooglehome": "Ouvrir Google Home", "generalMatterOpenamazonalexa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalMatterOpensmartthings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalLabelProgram": "Programme {number}", "generalTextDone": "<PERSON><PERSON><PERSON><PERSON>", "settingsRandommodeDescriptionShort": "Lorsque le mode aléatoire est activé, tous les programmes de ce canal sont décalés de manière aléatoire jusqu'à 15 minutes. Les programmes d´allumage sont décalés à l'avance, les programmes d´extinction sont retardés.", "all": "Tous", "discoveryBluetooth": "Bluetooth", "success": "Su<PERSON>ès", "error": "<PERSON><PERSON><PERSON>", "timeProgramAdd": "Ajouter un programme horaire", "noConnection": "Pas de connexion", "timeProgramOnlyActive": "Programmes configurés", "timeProgramAll": "Tous les programmes", "active": "Actif", "inactive": "Inactif", "timeProgramSaved": "Programme {number} enregistré", "deviceLanguageSaved": "Langue de l'appareil sauvegardée", "generalTextTimeShort": "{time}", "programDeleteHint": "Le programme {index} doit-il vraiment être supprimé ?", "milliseconds": "{count, plural, one {Milliseconde} other {Millisecondes}}", "millisecondsWithValue": "{count, plural, one {{count} Milliseconde} other {{count} Millisecondes}}", "secondsWithValue": "{count, plural, one {{count} Seconde} other {{count} Secondes}}", "minutesWithValue": "{count, plural, one {{count} Minute} other {{count} Minutes}}", "hoursWithValue": "{count, plural, one {{count} <PERSON><PERSON>} other {{count} <PERSON><PERSON>}}", "settingsPinFailEmpty": "Le code PIN ne doit pas être vide", "detailsConfigurationWifiloginScanNoMatch": "Le code scanné ne correspond pas à l'appareil", "wifiAuthorizationPopIsEmpty": "Le PoP ne peut pas être vide", "wifiAuthenticationCredentialsHint": "Comme l'application ne peut pas accéder à votre mot de passe Wi-Fi privé, il n'est pas possible de vérifier l'exactitude de la saisie. Si aucune connexion n'est établie, vérifiez le mot de passe et saisissez-le à nouveau.", "generalMatterOpenApplehome": "Ouvrir Apple Home", "timeProgramNoActive": "Pas de programmes configurés", "timeProgramNoEmpty": "Pas de programme temporel libre disponible", "nameOfConfiguration": "Nom de la configuration", "currentDevice": "Appareil actuel", "export": "Exporter", "import": "Importer", "savedConfigurations": "Configurations enregistrées", "importableServicesLabel": "Les paramètres suivants peuvent être importés :", "notImportableServicesLabel": "Paramètres incompatibles", "deviceCategoryMeterGateway": "Passerelle pour compteurs", "deviceCategory2ChannelTimeSwitch": "Horloge à 2 canaux", "devicategoryOutdoorTimeSwitchBluetooth": "Horloge extérieure Bluetooth", "settingsModbusHeader": "Modbus", "settingsModbusDescription": "<PERSON><PERSON><PERSON>z le débit en bauds, la parité et le délai d'attente pour configurer la vitesse de transmission, la détection des erreurs et le temps d'attente.", "settingsModbusRTU": "Modbus RTU", "settingsModbusBaudrate": "Débit en bauds", "settingsModbusParity": "<PERSON><PERSON><PERSON>", "settingsModbusTimeout": "<PERSON><PERSON><PERSON> d'attente Modbus", "locationServiceDisabled": "La localisation est désactivée", "locationPermissionDenied": "Veuillez autoriser la localisation à demander votre position actuelle.", "locationPermissionDeniedPermanently": "Les autorisations de localisation sont refusées en permanence. Veuillez autoriser l'autorisation de localisation dans les paramètres de votre appareil pour demander votre position actuelle.", "lastSync": "Dernière synchronisation", "dhcpActive": "DHCP actif", "ipAddress": "IP", "subnetMask": "Masque de sous-rés<PERSON>", "standardGateway": "Passerelle par défaut", "dns": "DNS", "alternateDNS": "DNS alternatif", "errorNoNetworksFound": "Pas de réseau wifi trouvé", "availableNetworks": "Réseaux disponibles", "enableWifiInterface": "Activer l'interface WiFi", "enableLANInterface": "Activer l'interface LAN", "hintDontDisableAllInterfaces": "Assurez-vous que toutes les interfaces ne sont pas désactivées. La dernière interface activée est prioritaire.", "ssid": "SSID", "searchNetworks": "Recherche de réseau wifi", "errorNoNetworkEnabled": "Au moins une interface doit être active", "errorActiveNetworkInvalid": "Toutes les stations actives ne sont pas valides", "invalidNetworkConfiguration": "Configuration r<PERSON><PERSON> invalide", "generalDefault": "Défaut", "mqttHeader": "MQTT", "mqttConnected": "Connecté au courtier MQTT", "mqttDisconnected": "Pas de connexion au courtier MQTT", "mqttBrokerURI": "URI du courtier", "mqttBrokerURIHint": "URI du courtier MQTT", "mqttPort": "Port", "mqttPortHint": "Port MQTT", "mqttClientId": "Client-ID", "mqttClientIdHint": "MQTT Client-ID", "mqttUsername": "Nom d'utilisateur", "mqttUsernameHint": "Nom d'utilisateur MQTT", "mqttPassword": "Mot de passe", "mqttPasswordHint": "Mot de passe MQTT", "mqttCertificate": "Certificat", "mqttCertificateHint": "Certificat MQTT", "mqttTopic": "Sujet", "mqttTopicHint": "Sujet MQTT", "electricityMeter": "Compteur d'électricité", "electricityMeterCurrent": "Actuel", "electricityMeterHistory": "Historique", "electricityMeterReading": "<PERSON><PERSON><PERSON> de compteur", "connectivity": "Connectivité", "electricMeter": "{count, plural, one {Compteurs} other {Compteurs}}", "discoveryZGW16Description": "<PERSON>erelle <PERSON>-compteurs-MQTT", "bluetoothConnectionLost": "Perte de la connexion Bluetooth", "bluetoothConnectionLostDescription": "La connexion Bluetooth avec l'appareil a été perdue. Veuillez vérifier la connexion à l'appareil.", "openBluetoothSettings": "<PERSON><PERSON><PERSON><PERSON>r les paramètres Bluetooth", "password": "Mot de passe", "setInitialPassword": "Définir le mot de passe initial", "initialPasswordMinimumLength": "Le mot de passe doit comporter au moins {length} caractères.", "repeatPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> le mot de passe", "passwordsDoNotMatch": "Les mots de passe ne correspondent pas", "savePassword": "Sauvegarder le mot de passe", "savePasswordHint": "Le mot de passe est enregistré pour les connexions futures sur votre appareil.", "retrieveNtpServer": "Récupérer l'heure du serveur NTP", "retrieveNtpServerFailed": "La connexion au serveur NTP n'a pas pu être établie.", "retrieveNtpServerSuccess": "La connexion au serveur NTP a été établie avec succès.", "settingsPasswordNewPasswordDescription": "Saisir le nouveau mot de passe", "settingsPasswordConfirmationDescription": "Changement de mot de passe réussi", "dhcpRangeStart": "Début de la plage DHCP", "dhcpRangeEnd": "Fin de la plage DHCP", "forwardOnMQTT": "Transférer vers MQTT", "showAll": "<PERSON><PERSON><PERSON><PERSON> tout", "hide": "<PERSON><PERSON>", "changeToAPMode": "Passer en mode AP", "changeToAPModeDescription": "Vous êtes sur le point de connecter votre appareil à un réseau WiFi, auquel cas la connexion à l'appareil est déconnectée et vous devez vous reconnecter à votre appareil via le réseau configuré.", "consumption": "Consommation", "currentDay": "<PERSON>urn<PERSON> en cours", "twoWeeks": "2 semaines", "oneYear": "1 an", "threeYears": "3 ans", "passwordMinLength": "Le mot de passe doit comporter au moins {length} caractères.", "passwordNeedsLetter": "Le mot de passe doit contenir une lettre.", "passwordNeedsNumber": "Le mot de passe doit contenir un chiffre.", "portEmpty": "Le port ne peut pas être vide", "portInvalid": "Port non valide", "portOutOfRange": "Le port doit être compris entre {rangeStart} et {rangeEnd}.", "ipAddressEmpty": "L'adresse IP ne peut pas être vide", "ipAddressInvalid": "Adresse IP invalide", "subnetMaskEmpty": "Le masque de sous-réseau ne peut pas être vide", "subnetMaskInvalid": "Masque de sous-réseau non valide", "gatewayEmpty": "La passerelle ne peut pas être vide", "gatewayInvalid": "Passerelle invalide", "dnsEmpty": "Le DNS ne peut pas être vide", "dnsInvalid": "DNS invalide", "uriEmpty": "L'URI ne peut pas être vide", "uriInvalid": "URI non valide", "electricityMeterChangedSuccessfully": "Changement de compteur d'électricité réussi", "networkChangedSuccessfully": "La configuration du réseau a été modifiée avec succès", "mqttChangedSuccessfully": "La configuration MQTT a été modifiée avec succès", "modbusChangedSuccessfully": "Les paramètres Modbus ont été modifiés avec succès", "loginData": "Supprimer les données de connexion", "valueConfigured": "<PERSON><PERSON>gu<PERSON>", "electricityMeterHistoryNoData": "Pas de données disponibles", "locationChangedSuccessfully": "Changement de lieu réussi", "settingsNameFailEmpty": "Le nom ne peut être vide", "settingsNameFailLength": "Le nom ne peut pas comporter plus de {length} caractères.", "solsticeChangedSuccesfully": "Les paramètres du solstice ont été modifiés avec succès", "relayFunctionChangedSuccesfully": "Fonction relais modifiée avec succès", "relayFunctionHeader": "Fonction relais", "dimmerValueChangedSuccesfully": "Le comportement d´allumage a été modifié avec succès", "dimmerBehaviourChangedSuccesfully": "Modification réussie du comportement de variation", "dimmerBrightnessDescription": "La luminosité minimale et maximale affecte toutes les luminosités réglables du variateur.", "dimmerSettingsChangedSuccesfully": "Modification réussie des paramètres de base", "liveUpdateEnabled": "Test en direct activé", "liveUpdateDisabled": "Test en direct désactivé", "liveUpdateDescription": "La dernière valeur modifiée du curseur sera envoyée à l'appareil.", "demoDevices": "Dispositifs de démonstration", "showDemoDevices": "Montrer des appareils de démonstration", "deviceCategoryTimeSwitch": "Horloge programmable", "deviceCategoryMultifunctionalRelay": "Relais multifonction", "deviceCategoryDimmer": "Variateur", "deviceCategoryShutter": "Actionneur pour ombrage", "deviceCategoryRelay": "<PERSON><PERSON><PERSON>", "search": "Recherche", "configurationsHeader": "Configurations", "configurationsDescription": "<PERSON><PERSON><PERSON> vos <PERSON> ici.", "configurationsNameFailEmpty": "Le nom de la configuration ne peut pas être vide", "configurationDeleted": "Configuration supprimée", "codeFound": "{codeType} code détecté", "errorCameraPermission": "Veuillez autoriser la caméra à scanner le code ELTAKO.", "authorizationSuccessful": "Autorisé avec succès sur l'appareil", "wifiAuthenticationResetConfirmationDescription": "L'appareil est maintenant prêt à recevoir une nouvelle autorisation.", "settingsResetConnectionHeader": "Réinitialiser la connexion", "settingsResetConnectionDescription": "Voulez-vous vraiment réinitialiser la connexion ?", "settingsResetConnectionConfirmationDescription": "La connexion a été réinitialisée avec succès.", "wiredInputChangedSuccesfully": "Modification réussie du comportement du bouton-poussoir", "runtimeChangedSuccesfully": "Le temps d'exécution a été modifié avec succès", "expertModeActivated": "Mode expert activé", "expertModeDeactivated": "Mode expert <PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "Licence", "retry": "<PERSON><PERSON><PERSON><PERSON>", "provisioningConnectingHint": "La connexion de l'appareil est en cours d'établissement. <PERSON><PERSON> peut prendre jusqu'à 1 minute.", "serialnumberEmpty": "Le numéro de série ne peut pas être vide", "interfaceStateInactiveDescriptionBLE": "Bluetooth est désactivé, veuillez l'activer pour découvrir les appareils Bluetooth.", "interfaceStateDeniedDescriptionBLE": "Les autorisations Bluetooth n'ont pas été accordées.", "interfaceStatePermanentDeniedDescriptionBLE": "Les autorisations Bluetooth n'ont pas été accordées. Veuillez les activer dans les paramètres de votre appareil.", "requestPermission": "Demande d'autorisation", "goToSettings": "<PERSON>er dans les paramètres", "enableBluetooth": "Activer le bluetooth", "installed": "Installé", "teachInDialogDescription": "Souh<PERSON>ez-vous appairer votre appareil via {type} ?", "useMatter": "Utiliser Matter", "relayMode": "<PERSON>r le mode relais", "whatsNew": "Nouveau dans cette version", "migrationHint": "Une migration est nécessaire pour utiliser les nouvelles fonctionnalités.", "migrationHeader": "Migration", "migrationProgress": "Migration en cours...", "letsGo": "Allons-y !", "noDevicesFound": "Aucun appareil trouvé. Vérifiez si votre appareil est en mode d´appairage.", "interfaceStateEmpty": "Aucun dispositif n'a été trouvé", "ssidEmpty": "Le SSID ne peut pas être vide", "passwordEmpty": "Le mot de passe ne peut pas être vide", "settingsDeleteSettingsHeader": "Réinitialiser les paramètres", "settingsDeleteSettingsDescription": "Voulez-vous vraiment réinitialiser tous les paramètres ?", "settingsDeleteSettingsConfirmationDescription": "Tous les paramètres ont été réinitialisés avec succès.", "locationNotFound": "Emplacement non trouvé", "timerProgramEmptySaveHint": "Le programme horaire est vide. Voulez-vous annuler l'édition ?", "timerProgramDaysEmptySaveHint": "Aucun jour n'est sélectionné. Voulez-vous quand même enregistrer le programme horaire ?", "timeProgramNoDays": "Au moins un jour doit être activé", "timeProgramColliding": "Le programme horaire entre en collision avec le programme {program}.", "timeProgramDuplicated": "Le programme horaire est une copie du programme {program}.", "screenshotZgw16": "<PERSON><PERSON> individ<PERSON>", "interfaceStateUnknown": "Aucun appareil n'a été trouvé", "settingsPinChange": "Modifier le code PIN", "timeProgrammOneTime": "unique", "timeProgrammRepeating": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generalIgnore": "<PERSON><PERSON><PERSON>", "timeProgramChooseDay": "<PERSON><PERSON> le jour", "generalToday": "<PERSON><PERSON><PERSON>'hui", "generalTomorrow": "<PERSON><PERSON><PERSON>", "bluetoothAndPINChangedSuccessfully": "Bluetooth et code PIN modifiés avec succès", "generalTextDimTime": "Du<PERSON>e de la variation", "discoverySu62Description": "Horloge programmable à 1 canal Bluetooth", "bluetoothAlwaysOnTitle": "Marche continue", "bluetoothAlwaysOnDescription": "Bluetooth est activé en permanence.", "bluetoothAlwaysOnHint": "Remarque : si ce paramètre est activé, l'appareil est visible en permanence pour tout le monde via Bluetooth ! Il est recommandé de modifier le code PIN par défaut.", "bluetoothManualStartupOnTitle": "Allumage temporaire", "bluetoothManualStartupOnDescription": "Après la mise sous tension, le Bluetooth est activé pendant 3 minutes.", "bluetoothManualStartupOnHint": "Remarque : la fonction « prêt à appairer » est activée pendant 3 minutes et s'éteint ensuite. Si une nouvelle connexion doit être établie, le bouton doit être maintenu enfoncé pendant environ 5 secondes.", "bluetoothManualStartupOffTitle": "Allumage manuel", "bluetoothManualStartupOffDescription": "Le Bluetooth est activé manuellement à l'aide du bouton et reste actif pendant 3 minutes.", "bluetoothManualStartupOffHint": "Remarque : pour activer le Bluetooth, il faut maintenir le bouton de l'entrée de bouton-poussoir enfoncé pendant environ 5 secondes.", "timeProgrammOneTimeRepeatingDescription": "Les programmes peuvent soit être exécutés de manière répétée, en exécutant toujours une commutation aux jours et heures configurés, soit être exécutés une seule fois à l'heure de commutation configurée.", "versionHeader": "Version {version}", "releaseNotesHeader": "Notes de mise à jour", "release30Header": "La nouvelle application Eltako Connect est arrivée !", "release30FeatureDesignHeader": "Nouvelle conception", "release30FeatureDesignDescription": "L'application a été entièrement revue et bénéficie d'un nouveau design. Elle est désormais encore plus facile et plus intuitive à utiliser.", "release30FeaturePerformanceHeader": "Amélioration des performances", "release30FeaturePerformanceDescription": "Profitez d'une expérience plus fluide et de temps de chargement réduits - pour une expérience utilisateur sans heurts.", "release30FeatureConfigurationHeader": "Configurations inter-appareils", "release30FeatureConfigurationDescription": "Sauvegarder les configurations des appareils et les transférer à d'autres appareils. Même avec des produits différents, vous pouvez, par exemple, transférer la configuration de votre S2U12DBT1+1-UC vers un ASSU-BT ou vice versa.", "release31Header": "La nouvelle horloge programmable encastrée à 1 canal avec Bluetooth est arrivée !", "release31Description": "Que peut faire le SU62PF-BT/UC ?", "release31SU62Name": "SU62PF-BT/UC", "release31DeviceNote1": "Jusqu'à 60 programmes horaires.", "release31DeviceNote2": "Fonction astro : L'horloge change d'appareil en fonction du lever et du coucher du soleil.", "release31DeviceNote3": "Mode aléatoire : les heures de commutation peuvent être décalées de manière aléatoire jusqu'à 15 minutes.", "release31DeviceNote4": "Passage à l'heure d'été ou d'hiver : L'horloge passe automatiquement à l'heure d'été ou à l'heure d'hiver.", "release31DeviceNote5": "Tension universelle d'alimentation et de contrôle 12-230V UC.", "release31DeviceNote6": "Entrée de bouton-poussoir pour la commutation manuelle.", "release31DeviceNote7": "1 contact NO sans potentiel 10 A/250 V AC.", "release31DeviceNote8": "Exécution unique de programmes horaires.", "generalNew": "Nouveau", "yearsAgo": "{count, plural, one {<PERSON><PERSON>} other {Depuis {count} ann<PERSON>}}", "monthsAgo": "{count, plural, one {<PERSON><PERSON>} other {Depuis {count} <PERSON><PERSON>}}", "weeksAgo": "{count, plural, one {<PERSON><PERSON><PERSON>} other {Depuis {count} semaines}}", "daysAgo": "{count, plural, one {<PERSON><PERSON>} other {Depuis {count} jours}}", "minutesAgo": "{count, plural, one {depuis une minute} other {Depuis {count} minutes}}", "hoursAgo": "{count, plural, one {Depuis une heure} other {Depuis {count} heures}}", "secondsAgo": "{count, plural, one {Depuis une seconde} other {Depuis {count} secondes}}", "justNow": "À l´instant", "discoveryEsripmDescription": "Télérupteur IP Matter", "generalTextKidsRoom": "Fonction chambre d'enfant", "generalDescriptionKidsRoom": "Lors de l'allumage en appuyant longuement sur le bouton ({mode}), l'appareil s'allume après environ 1 seconde avec la luminosité la plus faible et, tant que vous continuez à appuyer sur le bouton, s'assombrit lentement sans modifier la luminosité enregistré.", "generalTextSceneButton": "Bouton de scénario", "settingsEnOceanConfigHeader": "Configuration EnOcean", "enOceanConfigChangedSuccessfully": "La configuration EnOcean a été modifiée avec succès.", "activateEnOceanRepeater": "<PERSON><PERSON> le répétiteur EnOcean", "enOceanRepeaterLevel": "Niveau du répétiteur", "enOceanRepeaterLevel1": "Niveau 1", "enOceanRepeaterLevel2": "Niveau 2", "enOceanRepeaterOffDescription": "Aucun signal radio n'est reçu des émetteurs.", "enOceanRepeaterLevel1Description": "Seuls les signaux radio des émetteurs sont reçus, vérifiés et retransmis à pleine puissance d'émission. Les signaux radio des autres répétiteurs sont ignorés afin de réduire la quantité de données.", "enOceanRepeaterLevel2Description": "Outre les signaux radio des émetteurs, les signaux radio des répétiteurs de niveau 1 sont également traités. Un signal radio peut donc être reçu et amplifié au maximum deux fois. Les répétiteurs radio n'ont pas besoin d'être appairés. Ils reçoivent et amplifient les signaux radio de tous les émetteurs radio présents dans leur zone de réception.", "settingsSensorHeader": "Emetteurs", "sensorChangedSuccessfully": "les émetteurs ont été modifiés avec succès", "wiredButton": "Bouton-p<PERSON><PERSON><PERSON> filaire", "enOceanId": "ID EnOcean", "enOceanAddManually": "<PERSON><PERSON> ou scanner l'ID EnOcean", "enOceanIdInvalid": "ID EnOcean invalide", "enOceanAddAutomatically": "Appairer avec un télégramme EnOcean", "enOceanAddDescription": "Le protocole radio EnOcean permet d'appairer des boutons-poussoir dans votre actionneur et de l'actionner.\n\nChoisissez soit l'appairage automatique avec télégramme EnOcean pour appairer les boutons en appuyant sur un bouton, soit choisissez la variante manuelle pour scanner ou saisir l'ID EnOcean de votre bouton.", "enOceanTelegram": "T<PERSON><PERSON>gramme", "enOceanCodeScan": "Saisissez l'identifiant EnOcean de votre {sensorType} ou scannez le code QR EnOcean de votre {sensorType} pour l'ajouter.", "enOceanCode": "Code QR EnOcean", "enOceanCodeScanDescription": "Recherchez le code QR EnOcean sur votre {sensorType} et scannez-le avec votre appareil photo.", "enOceanButton": "Bouton EnOcean", "enOceanBackpack": "Adaptateur EnOcean", "sensorNotAvailable": "Aucun bouton n'a encore été appairé", "sensorAdd": "A<PERSON>ter un émetteur", "sensorCancel": "Annuler l'appairage", "sensorCancelDescription": "Voulez-vous vraiment annuler le processus d’appairage ?", "getEnOceanBackpack": "Procurez-vous votre adaptateur EnOcean", "enOceanBackpackMissing": "Pour entrer dans le monde fantastique de la connectivité et de la communication parfaites, il vous faut adaptateur EnOcean !\nCliquez ici pour plus d'informations", "sensorEditChangedSuccessfully": "{sensorName} a été modifié avec succès", "sensorConnectedVia": "connecté via {deviceName}", "lastSeen": "Vu pour la dernière fois", "setButtonOrientation": "Établir l'orientation", "setButtonType": "Définir le type de bouton", "button1Way": "Bouton 1 canal", "button2Way": "Bouton 2 canaux", "button4Way": "Bouton 4 canaux", "buttonUnset": "Non occupé", "button": "Bouton", "sensor": "<PERSON><PERSON>", "sensorsFound": "{count, plural, =0 {Aucun émetteur trouvé} one {1 émetteur trouvé} other {{count} émetteurs trouvés}}", "sensorSearch": "Rechercher des émetteurs", "searchAgain": "Rechercher à nouveau", "sensorTeachInHeader": "Appairer {sensorType}", "sensorChooseHeader": "Choi<PERSON> {sensorType}", "sensorChooseDescription": "Sélectionnez un bouton que vous souhaitez utiliser pour l'appairage.", "sensorCategoryDescription": "Sélectionnez une catégorie que vous souhaitez appairer.", "sensorName": "Nom du bouton", "sensorNameFooter": "Donnez un nom au bouton", "sensorAddedSuccessfully": "{sensorName} a été appairé avec succès", "sensorDelete": "Supprimer le {sensorType}", "sensorDeleteHint": "Le {sensorType} {sensorName} doit-il vraiment être supprimé ?", "sensorDeletedSuccessfully": "{sensorName} a été supprimé avec succès", "buttonTapDescription": "Appuyez sur le bouton que vous souhaitez appairer.", "waitingForTelegram": "L'actionneur attend le télégramme", "copied": "<PERSON><PERSON><PERSON>", "pairingFailed": "{sensorType} d<PERSON><PERSON><PERSON> appairé", "generalDescriptionUniversalbutton": "Avec le bouton universel, le sens est inversé en relâchant brièvement le bouton. Des commandes de contrôle courtes s'activent ou s'éteignent.", "generalDescriptionDirectionalbutton": "Le bouton directionnel est « allumer et augmenter l'intensité » en haut et « éteindre et diminuer l'intensité » en bas.", "matterForwardingDescription": "La pression sur le bouton est transmise à Matter.", "none": "Aucun", "buttonNoneDescription": "Le bouton n'a aucune fonction.", "buttonUnsetDescription": "Le bouton n’a aucun comportement défini .", "sensorButtonTypeChangedSuccessfully": "Le type de bouton a été modifié avec succès", "forExample": "par ex. {example}}", "enOceanQRCodeInvalidDescription": "Uniquement possible à partir de la date de production 44/20", "input": "Entrée", "buttonSceneValueOverride": "<PERSON><PERSON><PERSON><PERSON> la valeur du bouton de scenario", "buttonSceneValueOverrideDescription": "La valeur du bouton de scenario est écrasée par la valeur de variation actuelle en maintenant le bouton enfoncé.", "buttonSceneDescription": "Le bouton de scenario s'allume avec une valeur de variation fixe", "buttonPress": "Pression du bouton", "triggerOn": "Bouton universel ou bouton directionnel côté allumage", "triggerOff": "Bouton universel ou bouton de direction côté extinction", "centralOn": "Allumage centralisé", "centralOff": "Extinction centralisée", "centralButton": "Bouton de commande centralisée", "enOceanAdapterNotFound": "Aucun adaptateur EnOcean n'a été trouvé", "updateRequired": "Mise à jour nécessaire", "updateRequiredDescription": "Votre application nécessite une mise à jour pour prendre en charge ce nouvel appareil.", "release32Header": "La nouvelle série 64 avec Matter et EnOcean ainsi que la nouvelle minuterie encastrée Bluetooth SU62PF-BT/UC sont désormais disponibles !", "release32EUD64Header": "Le nouveau variateur encastré à 1 canal avec Matter over Wi-Fi et jusqu'à 300W est arrivé !", "release32EUD64Note1": "Configuration de la vitesse de variation, de la vitesse d'allumage et d'extinction, du mode chambre d'enfant/sommeil, et bien plus encore.", "release32EUD64Note2": "Les fonctionnalités de l'EUD64NPN-IPM peuvent être étendues grâce à des adaptateurs, tels que l'adaptateur EnOcean EOA64.", "release32EUD64Note3": "Jusqu'à 30 boutons-poussoir sans fil EnOcean peuvent être directement reliés à l'EUD64NPN-IPM en combinaison avec l'adaptateur EnOcean EOA64 et transmis à Matter.", "release32EUD64Note4": "Deux entrées de boutons câblés peuvent être directement reliées à l'EUD64NPN-IPM ou transmises à Matter.", "release32ESR64Header": "Le nouvel actionneur de commutation à 1 canal, encastré et libre de potentiel, avec communication Wi-Fi et jusqu'à 16 A, est arrivé !", "release32ESR64Note1": "Configuration de diverses fonctions telles que télérupteur (ES), relai<PERSON> (ER), relais normalement fermé (ER-Inverse), et bien d'autres encore.", "release32ESR64Note2": "Les fonctionnalités de l'ESR64PF-IPM peuvent être étendues grâce à des adaptateurs, tels que l'adaptateur EnOcean EOA64.", "release32ESR64Note3": "Jusqu'à 30 boutons-poussoir sans fil EnOcean peuvent être directement reliés à l'ESR64PF-IPM en combinaison avec l'adaptateur EnOcean EOA64 et transmis à Matter.", "release32ESR64Note4": "Un bouton d'entrée câblé peut être directement relié à l'ESR64PF-IPM ou transmis à Matter.", "buttonsFound": "{count, plural, =0 {Aucun bouton trouvé} one {1 bouton trouvé} other {{count} boutons trouvés}}", "doubleImpuls": "avec une double impulsion", "impulseDescription": "Si le canal est activé, une impulsion l'éteindra.", "locationServiceEnable": "<PERSON>r le lieu", "locationServiceDisabledDescription": "L'emplacement est désactivé. La version de votre système d'exploitation a besoin de la localisation pour pouvoir trouver les appareils Bluetooth.", "locationPermissionDeniedNoPosition": "Les autorisations de localisation n'ont pas été accordées. La version de votre système d'exploitation nécessite des autorisations de localisation pour pouvoir trouver des appareils Bluetooth. Veuillez autoriser l'autorisation de localisation dans les paramètres de votre appareil.", "interfaceStatePermanentDeniedDescriptionDevicesAround": "L'autorisation pour les appareils à proximité n'a pas été accordée. Veuillez activer cette autorisation dans les paramètres de votre appareil.", "permissionNearbyDevices": "Appareils à proximité", "release320Header": "Le nouveau télévariateur universel EUD12NPN-BT/600W-230V, plus puissant, est arrivé !", "release320EUD600Header": "Que peut faire le nouveau télévariateur universel ?", "release320EUD600Note1": "Télévariateur universel d'une puissance maximale de 600W", "release320EUD600Note2": "Extensible avec l'extension de puissance LUD12 jusqu'à 3800W", "release320EUD600Note3": "Commande locale avec boutons-poussoirs universels ou directionnels", "release320EUD600Note4": "Fonctions centrales allumage / extinction", "release320EUD600Note5": "Borne pour détecteur de mouvement pour plus de commodité", "release320EUD600Note6": "Horloge intégrée avec 10 programmes de commutation", "release320EUD600Note7": "Fonction astro", "release320EUD600Note8": "Luminosité individuelle à l'allumage", "mqttClientCertificate": "Certificat du client", "mqttClientCertificateHint": "Certificat du client MQTT", "mqttClientKey": "Clé du client", "mqttClientKeyHint": "Clé du client MQTT", "mqttClientPassword": "Mot de passe du client", "mqttClientPasswordHint": "Mot de passe du client MQTT", "mqttEnableHomeAssistantDiscovery": "Activer la découverte MQTT de HomeAssistant", "modbusTcp": "Modbus TCP", "enableInterface": "Activer l'interface", "busAddress": "Adresse du <PERSON>", "busAddressWithAddress": "<PERSON><PERSON><PERSON> {index}", "deviceType": "Type d'appareil", "registerTable": "{count, plural, one {Tableau des registres} other {Tableaux des registres}}", "currentValues": "Valeurs actuelles", "requestRTU": "<PERSON><PERSON>e RTU", "requestPriority": "Priorité de la demande", "mqttForwarding": "Transfert vers MQTT", "historicData": "Données historiques", "dataFormat": "Format des données", "dataType": "Type de données", "description": "Description", "readWrite": "Lecture/écriture", "unit": "Unité", "registerTableReset": "Réinitialisation du tableau des registres", "registerTableResetDescription": "La table de registre doit-elle vraiment être réinitialisée ?", "notConfigured": "Non configuré", "release330ZGW16Header": "Mise à jour majeure pour le ZGW16WL-IP", "release330Header": "Le ZGW16WL-IP avec jusqu'à 16 compteurs d'électricité", "release330ZGW16Note1": "Prend en charge jusqu'à 16 compteurs d'électricité Modbus ELTAKO", "release330ZGW16Note2": "Support Modbus TCP", "release330ZGW16Note3": "Prise en charge de la découverte MQTT", "screenshotButtonLivingRoom": "Bouton-poussoir du salon", "registerChangedSuccessfully": "Changement de registre réussi", "serverCertificateEmpty": "Le certificat du serveur ne peut pas être vide", "registerTemplates": "Enregistrer les modèles", "registerTemplateChangedSuccessfully": "Le modèle de registre a été modifié avec succès", "registerTemplateReset": "Réinitialisation du modèle de registre", "registerTemplateResetDescription": "Le modèle de registre doit-il vraiment être réinitialisé ?", "registerTemplateNotAvailable": "Pas de modèle de registre disponible", "rename": "<PERSON>mmer", "registerName": "Nom du registre", "registerRenameDescription": "Saisir un nom personnalisé pour le registre", "restart": "Redémarrer l'appareil", "restartDescription": "Voulez-vous vraiment redémarrer l'appareil ?", "restartConfirmationDescription": "L'appareil est en train de redémarrer", "deleteAllElectricityMeters": "Effacer tous les compteurs d'électricité", "deleteAllElectricityMetersDescription": "Voulez-vous vraiment supprimer tous les compteurs d'électricité ?", "deleteAllElectricityMetersConfirmationDescription": "Tous les compteurs d'électricité ont été supprimés avec succès", "resetAllElectricityMeters": "Réinitialiser toutes les configurations des compteurs d'électricité", "resetAllElectricityMetersDescription": "Voulez-vous vraiment réinitialiser toutes les configurations des compteurs d'électricité ?", "resetAllElectricityMetersConfirmationDescription": "Toutes les configurations des compteurs d'électricité ont été réinitialisées avec succès", "deleteElectricityMeterHistories": "Supprimer tous les historiques des compteurs d'électricité", "deleteElectricityMeterHistoriesDescription": "Voulez-vous vraiment effacer tous les historiques des compteurs d'électricité ?", "deleteElectricityMeterHistoriesConfirmationDescription": "Tous les historiques des compteurs d'électricité ont été supprimés avec succès", "multipleElectricityMetersSupportMissing": "Votre appareil ne prend actuellement en charge qu'un seul compteur d'électricité. Veuillez mettre à jour votre firmware.", "consumptionWithUnit": "Consommation (kWh)", "exportWithUnit": "Liv<PERSON>son (kWh)", "importWithUnit": "Consommation (kWh)", "resourceWarningHeader": "Limites des ressources", "mqttAndTcpResourceWarning": "L'exploitation simultanée de MQTT et de Modbus TCP n'est pas possible en raison des ressources limitées du système. Désactivez d'abord {protocol}.", "mqttEnabled": "MQTT activé", "redirectMQTT": "Aller dans les paramètres MQTT", "redirectModbus": "Aller à Paramètres Modbus", "unsupportedSettingDescription": "Avec la version actuelle de votre micrologiciel, certains paramètres de l'appareil ne sont pas pris en charge. Veuillez mettre à jour votre micrologiciel pour utiliser les nouvelles fonctionnalités.", "updateNow": "Mise à jour", "zgw241Hint": "Avec cette mise à jour, Modbus TCP est activé par défaut et MQTT est désactivé. Ceci peut être modifié dans les paramètres. Avec la prise en charge d'un maximum de 16 compteurs, de nombreuses optimisations ont été réalisées, ce qui peut entraîner des modifications dans les paramètres de l'appareil. Veuillez redémarrer l'appareil après avoir ajusté les paramètres.", "deviceConfigChangedSuccesfully": "Le temps d'exécution a été modifié avec succès", "deviceConfiguration": "Configuration de l'appareil", "tiltModeToggle": "Mode d'inclinaison", "tiltModeToggleFooter": "Si l'appareil est installé dans Matter, toutes les fonctions doivent y être reconfigurées", "shaderMovementDirection": "Marche arrière haut/bas", "shaderMovementDirectionDescription": "Inverser le sens du mouvement du moteur vers le haut ou vers le bas", "tiltTime": "Durée d'exécution de l'inclinaison", "changeTiltModeDialogTitle": "{target, select, true {Enable} false {Disable} other {Change}} tilt function", "changeTiltModeDialogConfirmation": "{target, select, true {<PERSON>r} false {<PERSON>ésactiver} other {Change}}", "generalTextSlatSetting": "Réglage des lamelles", "generalTextPosition": "Position", "generalTextSlatPosition": "Position des lamelles", "slatSettingDescription": "Description du réglage des lamelles", "scenePositionSliderDescription": "<PERSON><PERSON>", "sceneSlatPositionSliderDescription": "Inclinaison", "referenceRun": "Cycle d'étalonnage", "slatAutoSettingHint": "Dans ce mode, la position des stores n'a pas d'importance avant que les lamelles ne s'ajustent à la position d'inclinaison souhaitée.", "slatReversalSettingHint": "Dans ce mode, les volets se ferment complètement avant que les lames ne s'ajustent à la position d'inclinaison souhaitée.", "release340Header": "Le nouvel actionneur d'ombrage encastré ESB64NP-IPM est arrivé !", "release340ESB64Header": "Quelles sont les capacités de l'ESB64NP-IPM ?", "release340ESB64Note1": "Notre actionneur d'ombrage certifié Matter Gateway avec fonction de lamelles en option", "release340ESB64Note2": "Deux entrées de boutons câblés pour la commutation manuelle et la transmission à Matter", "release340ESB64Note3": "Extensible avec l'adaptateur EnOcean (EOA64). Par exemple avec le bouton poussoir sans fil EnOcean F4T55", "release340ESB64Note4": "Ouvert aux intégrations grâce à l'API REST basée sur la norme OpenAPI", "activateTiltModeDialogText": "Si la fonction d'inclinaison est activée, tous les réglages seront perdus. Êtes-vous sûr de vouloir activer la fonction d'inclinaison ?", "deactivateTiltModeDialogText": "Si la fonction d'inclinaison est désactivée, tous les réglages seront perdus. Êtes-vous sûr de vouloir désactiver la fonction d'inclinaison ?"}