{"appName": "ELTAKO Connect", "discoveryHint": "Activate Bluetooth on the device to connect", "devicesFound": "{count, plural, =0 {No devices found} one {1 device found} other {{count} devices found}}", "@devicesFound": {"description": "Specifies how many device are found in the discovery overview", "type": "text"}, "discoveryDemodeviceName": "{count, plural, one {Demo device} other {Demo devices}}", "discoverySu12Description": "2-Channel Time Switch Bluetooth", "discoveryImprint": "Imprint", "discoveryLegalnotice": "Legal notice", "generalSave": "Save", "generalCancel": "Cancel", "detailsHeaderHardwareversion": "Hardware version", "detailsHeaderSoftwareversion": "Software version", "detailsHeaderConnected": "Connected", "detailsHeaderDisconnected": "Disconnected", "detailsTimersectionHeader": "Programs", "detailsTimersectionTimercount": "of 60 programs used", "detailsConfigurationsectionHeader": "Configuration", "detailsConfigurationPin": "Device PIN", "detailsConfigurationChannelsDescription": "Channel 1: {channel1} | Channel 2: {channel2}", "settingsCentralHeader": "Central On/Off", "detailsConfigurationCentralDescription": "Only applies if the channel is set to AUTO", "detailsConfigurationDevicedisplaylock": "Lock device display", "timerOverviewHeader": "Programs", "timerOverviewTimersectionTimerinactivecount": "inactive", "timerDetailsListsectionDays1": "Monday", "timerDetailsListsectionDays2": "Tuesday", "timerDetailsListsectionDays3": "Wednesday", "timerDetailsListsectionDays4": "Thursday", "timerDetailsListsectionDays5": "Friday", "timerDetailsListsectionDays6": "Saturday", "timerDetailsListsectionDays7": "Sunday", "timerDetailsHeader": "Program", "timerDetailsSunrise": "Sunrise", "generalToggleOff": "Off", "generalToggleOn": "On", "timerDetailsImpuls": "Impulse", "generalTextTime": "Time", "generalTextAstro": "Astro", "generalTextAuto": "Auto", "timerDetailsOffset": "Time Offset", "timerDetailsPlausibility": "Activate plausibility check", "timerDetailsPlausibilityDescription": "If the off time is set to an earlier time than the on time, both programs are ignored, e.g. switch on at sunrise and switch off at 6:00 am. There are also situations where the check is not wanted, e.g. switching on at sunset and switching off at 1:00 am.", "generalDone": "Done", "generalDelete": "Delete", "timerDetailsImpulsDescription": "Change global impulse configuration", "settingsNameHeader": "Device name", "settingsNameDescription": "This name is also displayed in Discovery.", "settingsFactoryresetHeader": "Factory settings", "settingsFactoryresetDescription": "Which content should be reset?", "settingsFactoryresetResetbluetooth": "Reset Bluetooth settings", "settingsFactoryresetResettime": "Reset time settings", "settingsFactoryresetResetall": "Set to factory settings", "settingsDeletetimerHeader": "Delete programs", "settingsDeletetimerDescription": "Do you really want to delete all timers?", "settingsDeletetimerAllchannels": "All channels", "settingsImpulseHeader": "Impulse Switch Time", "settingsImpulseDescription": "The impulse Switch Time sets the duration of the impulse.", "generalTextRandommode": "Random mode", "settingsChannelsTimeoffsetHeader": "Solar time offset", "settingsChannelsTimeoffsetDescription": "Summer: {summerOffset} min | Winter: {winterOffset} min", "settingsLocationHeader": "Location", "settingsLocationDescription": "Set your location to use astro functions.", "settingsLanguageHeader": "Device language", "settingsLanguageSetlanguageautomatically": "Set language automatically", "settingsLanguageDescription": "Choose the language for the {deviceType}", "settingsLanguageGerman": "German", "settingsLanguageFrench": "French", "settingsLanguageEnglish": "English", "settingsLanguageItalian": "Italian", "settingsLanguageSpanish": "Spanish", "settingsDatetimeHeader": "Date and time", "settingsDatetimeSettimeautomatically": "Apply system time", "settingsDatetimeSettimezoneautomatically": "Set timezone automatically", "generalTextTimezone": "Timezone", "settingsDatetime24Hformat": "24 hour format", "settingsDatetimeSetsummerwintertimeautomatically": "Summer-/Wintertime automatically", "settingsDatetimeWinter": "Winter", "settingsDatetimeSummer": "Summer", "settingsPasskeyHeader": "Current device PIN", "settingsPasskeyDescription": "Enter the current device PIN", "timerDetailsActiveprogram": "Program active", "timerDetailsActivedays": "Active days", "timerDetailsSuccessdialogHeader": "Successful", "timerDetailsSuccessdialogDescription": "Program added successfully", "settingsRandommodeDescription": "The random mode only works with timer based programs but not with impulse based programs or astro based programs (sunrise / sunset).", "settingsSolsticeHeader": "Solar time offset", "settingsSolsticeDescription": "The set time defines the time-offset to solar time and it gets inverted respectively.", "settingsSolsticeHint": "For Example: \nIn winter the switch occurs 30 minutes before sunset in response the switch at sunrise also occurs 30 minutes in advance.", "generalTextMinutesShort": "min", "settingsPinDescription": "The PIN is required for the connection.", "settingsPinHeader": "New device PIN", "settingsPinNewpinDescription": "Enter a new PIN", "settingsPinNewpinRepeat": "Repeat the new PIN", "detailsProductinfo": "Product information", "settingsDatetimeSettimeautodescription": "Choose the preferred time", "minutes": "{count, plural, one {Minute} other {Minutes}}", "hours": "{count, plural, one {Hour} other {Hours}}", "seconds": "{count, plural, one {Second} other {Seconds}}", "generalTextChannel": "{count, plural, one {Channel} other {Channels}}", "generalLabelChannel": "Channel {number}", "generalTextDate": "Date", "settingsDatetime24HformatDescription": "Choose the preferred format", "settingsDatetimeSetsummerwintertime": "Summer-/Winter time", "settingsDatetime24HformatValue24": "24h", "settingsDatetime24HformatValue12": "AM/PM", "detailsEdittimer": "Edit programs", "settingsPinOldpinRepeat": "Please repeat the current PIN", "settingsPinCheckpin": "Checking PIN", "detailsDevice": "{count, plural, one {<PERSON>ce} other {Devices}}", "detailsDisconnect": "Disconnect", "settingsCentralDescription": "The input A1 controls the Central On/Off.\nCentral On/Off only applies if the channel is set to Central On/Off.", "settingsCentralHint": "Example:\nChannel 1 = Central On/Off\nChannel 2 = Off\nA1 = Central On -> Only C1 switches to On, C2 stays Off", "settingsCentralToggleheader": "Central Input switches", "settingsCentralActivechannelsdescription": "Current channels with the setting Central On/Off:", "settingsSolsticeSign": "Sign", "settingsDatetimeTimezoneDescription": "Central European Time", "generalButtonContinue": "Continue", "settingsPinConfirmationDescription": "PIN change successful", "settingsPinFailDescription": "PIN change failed", "settingsPinFailHeader": "Failure", "settingsPinFailShort": "The PIN has to be exactly 6 digits long", "settingsPinFailWrong": "The current PIN is incorrect", "settingsPinFailMatch": "The PINs do not match", "discoveryLostconnectionHeader": "Connection lost", "discoveryLostconnectionDescription": "The device has been disconnected.", "settingsChannelConfigCentralDescription": "Behaves like AUTO and also listens to the wired central inputs", "settingsChannelConfigOnDescription": "Switches the channel permanently to ON and ignores the programs", "settingsChannelConfigOffDescription": "Switches the channel permanently to OFF and ignores the programs", "settingsChannelConfigAutoDescription": "Switches in relation to the time and astro programs", "bluetoothPermissionDescription": "Bluetooth is required for the configuration of the devices.", "timerListitemOn": "Turn on", "timerListitemOff": "Turn off", "timerListitemUnknown": "Unknown", "timerDetailsAstroHint": "The location must be set in the settings for the astro programs to work correctly.", "timerDetailsTrigger": "<PERSON><PERSON>", "timerDetailsSunset": "Sunset", "settingsLocationCoordinates": "Coordinates", "settingsLocationLatitude": "Latitude", "settingsLocationLongitude": "Longitude", "timerOverviewEmptyday": "No programs are currently used for {day}", "timerOverviewProgramloaded": "Programs are loaded", "timerOverviewProgramchanged": "Program was changed", "settingsDatetimeProcessing": "Date and time is changed", "deviceNameEmpty": "Input must not be empty", "deviceNameHint": "The input must not contain more than {count} characters.", "deviceNameChanged": "Device name is changed", "deviceNameChangedSuccessfully": "Device name was successfully changed", "deviceNameChangedFailed": "An error has occurred.", "settingsPinConfirm": "Confirm", "deviceShowInstructions": "1. Activate the Bluetooth of the watch with SET\n2. Tap the button at the top to start the search.", "deviceNameNew": "Enter new device name", "settingsLanguageRetrieved": "Language is retrieved", "detailsProgramsShow": "Show programs", "generalTextProcessing": "Please wait", "generalTextRetrieving": "are retrieved", "settingsLocationPermission": "Allow ELTAKO Connect to access this device's location", "timerOverviewChannelloaded": "Channels are loaded", "generalTextRandommodeChanged": "Random mode is changed", "detailsConfigurationsectionChanged": "Configuration is changed", "settingsSettimeFunctions": "Time functions are changed", "imprintContact": "Contact", "imprintPhone": "Phone", "imprintMail": "Mail", "imprintRegistrycourt": "Register court", "imprintRegistrynumber": "Registration number", "imprintCeo": "Managing Director", "imprintTaxnumber": "Sales tax identification number", "settingsLocationCurrent": "Current location", "generalTextReset": "Reset", "discoverySearchStart": "Start search", "discoverySearchStop": "Stop search", "settingsImpulsSaved": "Impulse Switch Time is stored", "settingsCentralNochannel": "There are no channels with the Central On/Off setting", "settingsFactoryresetBluetoothConfirmationDescription": "Bluetooth connection was successfully reset.", "settingsFactoryresetBluetoothFailDescription": "Bluetooth connections reset failed.", "imprintPublisher": "Publisher", "discoveryDeviceConnecting": "Connection is established", "discoveryDeviceRestarting": "Restarting...", "generalTextConfigurationsaved": "Channel configuration saved.", "timerOverviewChannelssaved": "Save channels", "timerOverviewSaved": "Timer saved", "timerSectionList": "List view", "timerSectionDayview": "Day view", "generalTextChannelInstructions": "Channel settings", "generalTextPublisher": "Publisher", "settingsDeletetimerDialog": "Do you really want to delete all programmes?", "settingsFactoryresetResetbluetoothDialog": "Do you really want to reset all Bluetooth settings?", "settingsCentralTogglecentral": "Central\nOn/Off", "generalTextConfirmation": "{serviceName} change successful.", "generalTextFailed": "{serviceName} change failed.", "settingsChannelConfirmationDescription": "Channels were successfully changed.", "timerDetailsSaveHeader": "Save program", "timerDetailsDeleteHeader": "Delete program", "timerDetailsSaveDescription": "Saving program successful.", "timerDetailsDeleteDescription": "Deleting program successful.", "timerDetailsAlertweekdays": "The program can't be saved, because no weekdays are selected.", "generalTextOk": "OK", "settingsDatetimeChangesuccesfull": "Date and time were successfully changed.", "discoveryConnectionFailed": "Connection failed", "discoveryDeviceResetrequired": "No connection could be established with the device. To solve this problem, delete the device from your Bluetooth settings. If the problem persists, please contact our technical support.", "generalTextSearch": "Search devices", "generalTextOr": "or", "settingsFactoryresetProgramsConfirmationDescription": "All programs were successfully deleted.", "generalTextManualentry": "Manual entry", "settingsLocationSaved": "Location saved", "settingsLocationAutosearch": "Search location automatically", "imprintPhoneNumber": "+49 711 / 9435 0000", "imprintMailAddress": "<EMAIL>", "settingsFactoryresetResetallDialog": "Do you really want to reset the device to factory settings?", "settingsFactoryresetFactoryConfirmationDescription": "The device has been successfully reset to factory settings.", "settingsFactoryresetFactoryFailDescription": "Device reset failed.", "imprintPhoneNumberIos": "+49711/94350000", "mfzFunctionA2Title": "2-stage response delay (A2)", "mfzFunctionA2TitleShort": "2-stage response delay (A2)", "mfzFunctionA2Description": "When the control voltage is applied, time lapse T1 between 0 and 60 seconds begins. At the end of this period, contact 1-2 closes and the time lapse t2 between 0 and 60 seconds begins. At the end of this time, contact 3-4 closes. After an interruption, the time sequence starts again with t1.", "mfzFunctionRvTitle": "Off Delay (RV)", "mfzFunctionRvTitleShort": "RV | Off Delay", "mfzFunctionRvDescription": "When the control voltage is applied the relay contact switches to 15-18. When the control voltage is interrupted the timing period is started; on time-out the relay contact returns to normal position. Resettable during the timing period.", "mfzFunctionTiTitle": "Clock generator starting with impulse (TI)", "mfzFunctionTiTitleShort": "TI | clock generator starting with impulse", "mfzFunctionTiDescription": "As long as the control voltage is applied, the operating contact closes and opens. The change-over time in both directions can be set separately. When the control voltage is applied, the operating contact changes immediately to 15-18.", "mfzFunctionAvTitle": "Operate delay (AV)", "mfzFunctionAvTitleShort": "AV | operate delay", "mfzFunctionAvDescription": "When the control voltage is applied the timing period is started; on time-out the relay contact changes to 15-18. After an interruption, the timing period is restarted.", "mfzFunctionAvPlusTitle": "Operate delay additive (AV+)", "mfzFunctionAvPlusTitleShort": "AV+ | operate delay additive", "mfzFunctionAvPlusDescription": "Function same as AV. However, after an interruption the elapsed time is stored.", "mfzFunctionAwTitle": "Fleeting NC contact (AW)", "mfzFunctionAwTitleShort": "AW | fleeting NC contact", "mfzFunctionAwDescription": "When the control voltage is interrupted the NO contact changes to 15-18, and reverts on wiping time-out. If the control voltage is applied during the wiping time the NO contact immediately reverts to 15-16 and the residual time is cancelled.", "mfzFunctionIfTitle": "Pulse shaper (IF)", "mfzFunctionIfTitleShort": "IF | pulse shaper", "mfzFunctionIfDescription": "When the control voltage is applied the relay contact changes to 15-18 for the set time. Further control impulses are evaluated only after the set time has elapsed.", "mfzFunctionEwTitle": "Fleeting NO contact (EW)", "mfzFunctionEwTitleShort": "EW | fleeting NO contact", "mfzFunctionEwDescription": "When the control voltage is applied the NO contact changes to 15-18 and reverts on wiping time-out. If the control voltage is removed during the wiping time the NO contact immediately reverts to 15-16 and the residual time is cancelled.", "mfzFunctionEawTitle": "Fleeting NO contact and fleeting NC contact (EAW)", "mfzFunctionEawTitleShort": "EAW | fleeting NO contact and fleeting NC contact", "mfzFunctionEawDescription": "When the control voltage is applied or interrupted the relay contact changes to 15-18 and reverts after the set wiping time.", "mfzFunctionTpTitle": "Clock generator starting with pause (TP)", "mfzFunctionTpTitleShort": "TP | clock generator starting with pause", "mfzFunctionTpDescription": "Description of function same as for TI, except that, when the control voltage is applied, the contact initially remains at 15-16 rather than changing to 15-18.", "mfzFunctionIaTitle": "Impulse controlled pickup delay (e.g. automatic door opener) (IA)", "mfzFunctionIaTitleShort": "IA | impulse controlled pickup delay", "mfzFunctionIaDescription": "The timing period t1 starts with a control impulse from 50ms; on time-out the relay contact changes for the timing period t2 to 15-18 for 1 second (e.g. for automatic door opener). If t1 is set to t1 min = 0.1 seconds, the IA operates as pulse shaper, when timing period t2 elapses, independent of the duration of the control impulse (min. 150 ms).", "mfzFunctionArvTitle": "Operate and release delay (ARV)", "mfzFunctionArvTitleShort": "ARV | operate and release delay", "mfzFunctionArvDescription": "When the control voltage is applied, the timeout begins, at the end of which the operating contact changes to 15 -18. If the control voltage is then interrupted, a new timeout begins, at the end of which the operating contact returns to the rest position.\nAfter an interruption of the response delay, the timeout starts again.", "mfzFunctionArvPlusTitle": "Operate and release delay additive (ARV+)", "mfzFunctionArvPlusTitleShort": "ARV+ | operate and release delay additive", "mfzFunctionArvPlusDescription": "Same function as ARV, but after an interruption of the operate delay the elapsed time is stored.", "mfzFunctionEsTitle": "Impulse switch (ES)", "mfzFunctionEsTitleShort": "ES | impulse switch", "mfzFunctionEsDescription": "With control impulses from 50ms the make contact switches to and from.", "mfzFunctionEsvTitle": "Impulse switch with release delay and switch-off early-warning function (ESV)", "mfzFunctionEsvTitleShort": "ESV | impulse switch with release delay and switch-off early-warning function", "mfzFunctionEsvDescription": "Function same as SRV. Additionally with switch-off early warning: approx. 30 sec. before time-out the lighting starts flickering 3 times at gradually shorter time intervals.", "mfzFunctionErTitle": "<PERSON><PERSON> (ER)", "mfzFunctionErTitleShort": "ER | relay", "mfzFunctionErDescription": "As long as the control contact is closed the make contact reverts from 15-16 to 15-18.", "mfzFunctionSrvTitle": "Release-delay impulse switch (SRV)", "mfzFunctionSrvTitleShort": "SRV | release-delay impulse switch", "mfzFunctionSrvDescription": "With control impulses from 50ms the make contact switches to and fro. In the contact position 15-18, the device switches automatically to the rest position 15-16 on delay time-out.", "detailsFunctionsHeader": "Functions", "mfzFunctionTimeHeader": "Time (t{index})", "mfzFunctionOnDescription": "permanent ON", "mfzFunctionOffDescription": "permanent OFF", "mfzFunctionMultiplier": "Factor", "discoveryMfz12Description": "Multifunction time relay Bluetooth", "mfzFunctionOnTitle": "permanent ON", "mfzFunctionOnTitleShort": "permanent ON", "mfzFunctionOffTitle": "permanent OFF", "mfzFunctionOffTitleShort": "permanent OFF", "mfzMultiplierSecondsFloatingpoint": "0.1 seconds", "mfzMultiplierMinutesFloatingpoint": "0.1 minutes", "mfzMultiplierHoursFloatingpoint": "0.1 hours", "mfzOverviewFunctionsloaded": "Functions are loaded", "mfzOverviewSaved": "Function saved", "settingsBluetoothHeader": "Bluetooth", "bluetoothChangedSuccessfully": "Bluetooth setting has been successfully changed.", "settingsBluetoothInformation": "Note: If this setting is activated, the device is permanently visible to everyone via Bluetooth!\nIt is recommended to change the device PIN.", "settingsBluetoothContinuousconnection": "Permanent visibility", "settingsBluetoothContinuousconnectionDescription": "By enabling persistent visibility, Bluetooth remains active on the device ({deviceType}) and does not need to be manually activated before establishing a connection.", "settingsBluetoothTimeout": "Connection timeout", "settingsBluetoothPinlimit": "PIN limit", "settingsBluetoothTimeoutDescription": "The connection is disconnected after {timeout} minutes of inactivity.", "settingsBluetoothPinlimitDescription": "For security reasons, you have a maximum of {attempts} attempts for entering the PIN. Bluetooth is then deactivated and must be manually reactivated for a new connection.", "settingsBluetoothPinAttempts": "attempts", "settingsResetfunctionHeader": "Reset functions", "settingsResetfunctionDialog": "Do you really want to reset all functions?", "settingsFactoryresetFunctionsConfirmationDescription": "All functions have been successfully reset.", "mfzFunctionTime": "Time (t)", "discoveryConnectionFailedInfo": "No Bluetooth connection", "detailsConfigurationDevicedisplaylockDialogtext": "Immediately after locking the device display, Bluetooth gets deactivated and has to be reactivated manually in order to establish a new connection.", "detailsConfigurationDevicedisplaylockDialogquestion": "Are you sure to lock the device display?", "settingsDemodevices": "Show demo devices", "generalTextSettings": "Settings", "discoveryWifi": "WiFi", "settingsInformations": "Information", "detailsConfigurationDimmingbehavior": "Dimming behaviour", "detailsConfigurationSwitchbehavior": "Switch behaviour", "detailsConfigurationBrightness": "Brightness", "detailsConfigurationMinimum": "Minimum", "detailsConfigurationMaximum": "Maximum", "detailsConfigurationSwitchesGr": "Group relay (GR)", "detailsConfigurationSwitchesGs": "Group switch (GS)", "detailsConfigurationSwitchesCloserer": "Normally opened contact (NO/ER)", "detailsConfigurationSwitchesClosererDescription": "Off -> <PERSON><PERSON><PERSON><PERSON> pressed (On) -> Release (Off)", "detailsConfigurationSwitchesOpenerer": "Normally closed contact (NC/ER-Invers)", "detailsConfigurationSwitchesOpenererDescription": "On -> <PERSON><PERSON><PERSON><PERSON> pressed (Off) -> Release (On)", "detailsConfigurationSwitchesSwitch": "Switch", "detailsConfigurationSwitchesSwitchDescription": "With each switch change, the light is switched on and off", "detailsConfigurationSwitchesImpulsswitch": "Impulse switch", "detailsConfigurationSwitchesImpulsswitchDescription": "<PERSON><PERSON><PERSON><PERSON> is briefly pressed and released to turn the light on or off", "detailsConfigurationSwitchesClosererDescription2": "As long as the pushbutton is pressed, the motor is running.", "detailsConfigurationSwitchesImpulsswitchDescription2": "<PERSON><PERSON><PERSON><PERSON> is briefly pressed to start the motor and briefly pressed to stop it again", "detailsConfigurationWifiloginScan": "Scan QR-Code", "detailsConfigurationWifiloginScannotvalid": "Scanned code is not valid", "detailsConfigurationWifiloginDescription": "Enter code", "detailsConfigurationWifiloginPassword": "Password", "discoveryEsbipDescription": "Shutter and blind actuator IP", "discoveryEsripDescription": "Impulse switching relay IP", "discoveryEudipDescription": "Universal dimmer switch IP", "generalTextLoad": "Loading", "wifiBasicautomationsNotFound": "No automation found.", "wifiCodeInvalid": "Invalid code", "wifiCodeValid": "Valid code", "wifiAuthorizationLogin": "Connect", "wifiAuthorizationLoginFailed": "Log in failed", "wifiAuthorizationSerialnumber": "Serial number", "wifiAuthorizationProductiondate": "Production date", "wifiAuthorizationProofofpossession": "PoP", "generalTextWifipassword": "WiFi password", "generalTextUsername": "Username", "generalTextEnter": "OR ENTER MANUALLY", "wifiAuthorizationScan": "Scan the ELTAKO code.", "detailsConfigurationDevicesNofunctionshinttext": "This device currently does not support any other settings", "settingsUsedemodelay": "Use demo delay", "settingsImpulsLoad": "Impulse switching time is loaded", "settingsBluetoothLoad": "Bluetooth setting is being loaded.", "detailsConfigurationsectionLoad": "Configurations are loaded", "generalTextLogin": "Log in", "generalTextAuthentication": "Authenticate", "wifiAuthorizationScanDescription": "Look for the ELTAKO code on the WiFi device or on the included info sheet and align it in the camera frame at the top.", "wifiAuthorizationScanShort": "Scan ELTAKO code", "detailsConfigurationEdgemode": "Dimming curve", "detailsConfigurationEdgemodeLeadingedge": "Leading edge", "generalTextNetwork": "Network", "wifiAuthenticationSuccessful": "Authentication successful", "detailsConfigurationsectionSavechange": "Configuration Changed", "discoveryWifiAdddevice": "Add WiFi device", "wifiAuthenticationDelay": "This can last up to 1 minute", "generalTextRetry": "Retry", "wifiAuthenticationCredentials": "Please enter the password of your WiFi", "wifiAuthenticationSsid": "SSID", "wifiAuthenticationDelaylong": "It can last up to 1 minute until the device is ready and appears in the app", "wifiAuthenticationCredentialsShort": "Enter WiFi password", "wifiAuthenticationTeachin": "Teach in the device into the WiFi", "wifiAuthenticationEstablish": "Establish connection to the device", "wifiAuthenticationEstablishLong": "Device connects to WiFi {ssid}", "wifiAuthenticationFailed": "Connection failed. Disconnect the device from power for a few seconds and retry to connect it", "wifiAuthenticationReset": "Reset authentication", "wifiAuthenticationResetHint": "All authentication data will be deleted.", "wifiAuthenticationInvaliddata": "Authentication data invalid", "wifiAuthenticationReauthenticate": "Authenticate again", "wifiAddhkdeviceHeader": "Add device", "wifiAddhkdeviceDescription": "Connect your new ELTAKO device to your WiFi via the Apple Home app.", "wifiAddhkdeviceStep1": "1. Open the Apple Home app.", "wifiAddhkdeviceStep2": "2. Click on the plus in the top right corner of the app and select **Add Device**.", "wifiAddhkdeviceStep3": "3. Follow the instructions of the app.", "wifiAddhkdeviceStep4": "4. Now your device can be configured in the ELTAKO Connect app.", "detailsConfigurationRuntime": "Runtime", "detailsConfigurationRuntimeMode": "Mode", "generalTextManually": "Manually", "detailsConfigurationRuntimeAutoDescription": "The shading actuator independently determines the runtime of the shading motor during each movement from the lower to the upper end position (recommended).\nAfter initial startup or changes, such a movement should be performed from the bottom to the top without interruption.", "detailsConfigurationRuntimeManuallyDescription": "The runtime of the shading motor is set manually via the duration below.\nPlease make sure that the configured runtime matches the actual runtime of your shading motor. \nAfter initial startup or changes, such a movement should be performed from the bottom to the top without interruption.", "detailsConfigurationRuntimeDemoDescription": "LCD mode is only available via REST API", "generalTextDemomodeActive": "Demo mode active", "detailsConfigurationRuntimeDuration": "Duration", "detailsConfigurationSwitchesGs4": "Group switch (GS4)", "detailsConfigurationSwitchesGs4Description": "Group switch with jog reversing function for controlling blinds", "screenshotSu12": "Outdoor Light", "screenshotS2U12": "Outdoor Light", "screenshotMfz12": "Pump", "screenshotEsr62": "<PERSON><PERSON>", "screenshotEud62": "Ceiling light", "screenshotEsb62": "Shutters balcony", "detailsConfigurationEdgemodeLeadingedgeDescription": "LC1-LC3 are comfort positions with different dimming curves for dimmable 230 V LED lamps, which cannot be dimmed far enough on AUTO due to their design and must therefore be forced to phase angle control.", "detailsConfigurationEdgemodeAutoDescription": "AUTO allows dimming of all types of lamps.", "detailsConfigurationEdgemodeTrailingedge": "Trailing edge", "detailsConfigurationEdgemodeTrailingedgeDescription": "LC4-LC6 are comfort positions with different dimming curves for dimmable 230 V LED lamps.", "updateHeader": "Firmware Update", "updateTitleStepSearch": "Searching for an update", "updateTitleStepFound": "An update was found", "updateTitleStepDownload": "Downloading update", "updateTitleStepInstall": "Installing update", "updateTitleStepSuccess": "Update successful", "updateTitleStepUptodate": "Already up to date", "updateTitleStepFailed": "Update failed", "updateButtonSearch": "Search for updates", "updateButtonInstall": "Install update", "updateCurrentversion": "Current version", "updateNewversion": "New firmware update available", "updateHintPower": "The update only starts when the output of the device is not active. The device should not be disconnected from the power supply and the app should not be exited during the update!", "updateButton": "Update", "updateHintCompatibility": "An update is recommended, otherwise some functions in the app will be limited.", "generalTextDetails": "Details", "updateMessageStepMetadata": "Loading update information", "updateMessageStepPrepare": "Update is being prepared", "updateTitleStepUpdatesuccessful": "Update is being checked", "updateTextStepFailed": "Unfortunately something went wrong during the update, try again in a few minutes or wait until your device updates automatically (internet connection required).", "configurationsNotavailable": "There are no configurations available yet", "configurationsAddHint": "Create new configurations by connecting to a device and saving a configuration.", "@configurationsAddHint": {"description": "Now available for all devices not only Bluetooth", "type": "text"}, "configurationsEdit": "Edit configuration", "generalTextName": "Name", "configurationsDelete": "Delete configuration", "configurationsDeleteHint": "Should the configuration: {configName} really be deleted?", "configurationsSave": "Save configuration", "configurationsSaveHint": "Export the current configuration of your Device, or load one of your existing configurations.", "configurationsImport": "Import configuration", "configurationsImportHint": "Should the configuration {configName} really be transferred?", "generalTextConfigurations": "{count, plural, one {Configuration} other {Configurations}}", "configurationsStepPrepare": "Configuration is being prepared", "configurationsStepName": "Enter a name for the configuration", "configurationsStepSaving": "Configuration is saving", "configurationsStepSavedsuccessfully": "Configuration was saved successfully", "configurationsStepSavingfailed": "Saving the configuration failed", "configurationsStepChoose": "Select a configuration", "configurationsStepImporting": "Configuration is importing", "configurationsStepImportedsuccessfully": "Configuration was imported successfully", "configurationsStepImportingfailed": "Importing configuration failed", "discoveryAssuDescription": "Outdoor Socket Time Switch Bluetooth", "settingsDatetimeDevicetime": "Actual device time", "settingsDatetimeLoading": "Time settings are loaded", "discoveryEud12Description": "Universal dimmer switch Bluetooth", "generalTextOffdelay": "Off Delay", "generalTextRemainingbrightness": "Remaining brightness", "generalTextSwitchonvalue": "Switch on value", "motionsensorTitleNoremainingbrightness": "No residual brightness", "motionsensorTitleAlwaysremainingbrightness": "With residual brightness", "motionsensorTitleRemainingbrightnesswithprogram": "Residual brightness via switching program", "motionsensorTitleRemainingbrightnesswithprogramandzea": "Residual brightness via ZE and ZA", "motionsensorTitleNoremainingbrightnessauto": "No residual brightness (semi-automatic)", "generalTextMotionsensor": "Motion detector", "generalTextLightclock": "Light alarm clock", "generalTextSnoozeclock": "Snooze function", "generalDescriptionLightclock": "When switching on ({mode}), the light is switched on after approx. 1 second at the lowest brightness and slowly dimmed up without changing the last saved brightness level.", "generalDescriptionSnoozeclock": "When switching off ({mode}), the lighting is dimmed down from the current dimming position to the minimum brightness and switched off. The lighting can be switched off at any time during the dimming down process by pressing the button briefly. A long press during the dimming process dims up and ends the snooze function.", "generalTextImmediately": "Immediately", "generalTextPercentage": "Percentage", "generalTextSwitchoffprewarning": "Switch-off prewarning", "generalDescriptionSwitchoffprewarning": "Slow dimming to minimum brightness", "generalDescriptionOffdelay": "The device switches on when the control voltage is applied. If the control voltage is interrupted, the time lapse begins, after which the device switches off. The appliance can be switched on downstream during the time lapse.", "generalDescriptionBrightness": "The brightness at which the lamp is switched on by the dimmer.", "generalDescriptionRemainingbrightness": "The dimming value in per cent to which the lamp is dimmed after the motion detector is switched off.", "generalDescriptionRuntime": "Running time of the light alarm function from minimum brightness to maximum brightness.", "generalTextUniversalbutton": "Universal push-button", "generalTextDirectionalbutton": "Direction button", "eud12DescriptionAuto": "Automatic detection UT/RT (with directional sensor diode RTD)", "eud12DescriptionRt": "with directional sensing diode RTD", "generalTextProgram": "Program", "eud12MotionsensorOff": "With motion detector set to Off", "eud12ClockmodeTitleProgramze": "Program and Central On", "eud12ClockmodeTitleProgramza": "Program and Central Off", "eud12ClockmodeTitleProgrambuttonon": "Program and UT/RT On", "eud12ClockmodeTitleProgrambuttonoff": "Program and UT/RT Off", "eud12TiImpulseTitle": "Pulse time On (t1)", "eud12TiImpulseHeader": "Dimming value Pulse time On", "eud12TiImpulseDescription": "The dimming value as a percentage\n to which the lamp is dimmed at pulse time ON.", "eud12TiOffTitle": "Pulse time Off (t2)", "eud12TiOffHeader": "Dimming value Pulse time Off", "eud12TiOffDescription": "The dimming value is a percentage to which the lamp is dimmed at pulse time OFF.", "generalTextButtonpermanentlight": "Permanent push-button light", "generalDescriptionButtonpermanentlight": "Setting the push-button continuous light from 0 to 10 hours in 0.5 hour increments. Activation by pressing the button for longer than 1 second (1x flickering), deactivation by pressing the button for longer than 2 seconds.", "generalTextNobuttonpermanentlight": "No TSP", "generalTextBasicsettings": "Basic settings", "generalTextInputswitch": "Local button input (A1)", "generalTextOperationmode": "Operating mode", "generalTextDimvalue": "Power on behaviour", "eud12TitleUsememory": "Use memory value", "eud12DescriptionUsememory": "The memory value corresponds to the last dimming value set. If the memory value is deactivated, dimming is always set to the switch-on value.", "generalTextStartup": "Switch-on brightness", "generalDescriptionSwitchonvalue": "The switch-on value is an adjustable brightness value that guarantees safe switch-on.", "generalTitleSwitchontime": "Switch-on time", "generalDescriptionSwitchontime": "After the switch-on time has elapsed, the lamp is dimmed from the switch-on value to the memory value.", "generalDescriptionStartup": "Some LED lamps require a higher inrush current to switch on reliably. The lamp is switched on at this switch-on value and then dimmed to the memory value after the switch-on time.", "eud12ClockmodeSubtitleProgramze": "Short click on Central On", "eud12ClockmodeSubtitleProgramza": "Short click on central off", "eud12ClockmodeSubtitleProgrambuttonon": "Double-click on universal button/direction button On", "eud12ClockmodeSubtitleProgrambuttonoff": "Double-click on universal button/direction button Off", "eud12FunctionStairlighttimeswitchTitleShort": "TLZ | Staircase lighting timer", "eud12FunctionMinTitleShort": "MIN", "eud12FunctionMmxTitleShort": "MMX", "eud12FunctionTiDescription": "Timer with adjustable switch-on and switch-off time from 0.5 seconds to 9.9 minutes. The brightness can be set from minimum brightness to maximum brightness.", "eud12FunctionAutoDescription": "Universal dimmer switch with setting for motion detector, light alarm and snooze function", "eud12FunctionErDescription": "Switching relay, the brightness can be set from minimum brightness to maximum brightness.", "eud12FunctionEsvDescription": "Universal dimmer switch with setting of a switch-off delay from 1 to 120 minutes. Switch-off pre-warning at the end by dimming down selectable and adjustable from 1 to 3 minutes. Both central inputs active.", "eud12FunctionTlzDescription": "Setting the button light duration from 0 to 10 hours in 0.5 hour increments. Activation by pressing the button for longer than 1 second (1x flickering), deactivation by pressing the button for longer than 2 seconds.", "eud12FunctionMinDescription": "Universal dimmer switch, switches to the set minimum brightness when the control voltage is applied. The light is dimmed to maximum brightness within the set dimming time of 1 to 120 minutes. When the control voltage is removed, the light is switched off immediately, even during the dimming time. Both central inputs active.", "eud12FunctionMmxDescription": "Universal dimmer switch, switches to the set minimum brightness when the control voltage is applied. During the set dimming time of 1 to 120 minutes, the light is dimmed to the maximum brightness. However, when the control voltage is removed, the dimmer dims down to the set minimum brightness. It is then switched off. Both central inputs active.", "motionsensorSubtitleNoremainingbrightness": "With motion detector set to Off", "motionsensorSubtitleAlwaysremainingbrightness": "With motion detector set to Off", "motionsensorSubtitleRemainingbrightnesswithprogram": "Switching program activated and deactivated with BWM off", "motionsensorSubtitleRemainingbrightnesswithprogramandzea": "Central On activates motion sensor, Central Off deactivates motion sensor, as well as by switching program", "motionsensorSubtitleNoremainingbrightnessauto": "Motion detector only switches off", "detailsDimsectionHeader": "Dimming", "generalTextFast": "Fast", "generalTextSlow": "Slowly", "eud12TextDimspeed": "Dimming speed", "eud12TextSwitchonspeed": "Switch-on speed", "eud12TextSwitchoffspeed": "Switch-off speed", "eud12DescriptionDimspeed": "The dimming speed is the speed at which the dimmer dims from the current brightness to the target brightness.", "eud12DescriptionSwitchonspeed": "The switch-on speed is the speed that the dimmer requires to switch on completely.", "eud12DescriptionSwitchoffspeed": "The switch-off speed is the speed that the dimmer requires to switch off completely.", "settingsFactoryresetResetdimHeader": "Reset dimming settings", "settingsFactoryresetResetdimDescription": "Should all dimming settings really be reset?", "settingsFactoryresetResetdimConfirmationDescription": "Dimming settings have been successfully reset", "eud12TextSwitchonoffspeed": "On/off speed", "eud12DescriptionSwitchonoffspeed": "The switch-on/switch-off speed is the speed that the dimmer requires to switch on or off completely.", "timerDetailsDimtoval": "On with dimming value in %", "timerDetailsDimtovalDescription": "The dimmer always switches on with the fixed dimming value in %.", "timerDetailsDimtovalSubtitle": "Switch on with {brightness}%", "timerDetailsDimtomem": "On with memory value", "timerDetailsDimtomemSubtitle": "Switching on with memory value", "timerDetailsMotionsensorwithremainingbrightness": "Residual brightness (BWM) On", "timerDetailsMotionsensornoremainingbrightness": "Residual brightness (BWM) Off", "settingsRandommodeHint": "With random mode activated all programs of this channel are randomly offset by up to 15 minutes. On-timers are offset in advance, Off-timers are delayed.", "runtimeOffsetDescription": "Additional overrun, after the journey time has elapsed", "loadingTextDimvalue": "Dimming value is loaded", "discoveryEudipmDescription": "Universal dimmer switch IP Matter", "generalTextOffset": "Overrun", "eud12DimvalueTestText": "Send brightness", "eud12DimvalueTestDescription": "The currently set dim speed is taken into account during testing.", "eud12DimvalueLoadText": "Load brightness", "settingsDatetimeNotime": "The date and time settings must be read out via the device display.", "generalMatterText": "Matter", "generalMatterMessage": "Please teach in your Matter device using one of the following apps", "generalMatterOpengooglehome": "Open Google Home", "generalMatterOpenamazonalexa": "Open Amazon Alexa", "generalMatterOpensmartthings": "Open SmartThings", "generalLabelProgram": "Program {number}", "generalTextDone": "Done", "settingsRandommodeDescriptionShort": "With random mode activated all programs of this channel are randomly offset by up to 15 minutes. On-timers are offset in advance, Off-timers are delayed.", "all": "All", "discoveryBluetooth": "Bluetooth", "success": "Success", "error": "Error", "timeProgramAdd": "Add time program", "noConnection": "No connection", "timeProgramOnlyActive": "Configured programs", "timeProgramAll": "All programs", "active": "Active", "inactive": "Inactive", "timeProgramSaved": "Time program {number} saved", "deviceLanguageSaved": "Device language saved", "generalTextTimeShort": "{time}", "programDeleteHint": "Should program {index} really be deleted?", "milliseconds": "{count, plural, one {Millisecond} other {Milliseconds}}", "millisecondsWithValue": "{count, plural, one {{count} Millisecond} other {{count} Milliseconds}}", "secondsWithValue": "{count, plural, one {{count} Second} other {{count} Seconds}}", "minutesWithValue": "{count, plural, one {{count} Minute} other {{count} Minutes}}", "hoursWithValue": "{count, plural, one {{count} Hour} other {{count} Hours}}", "settingsPinFailEmpty": "The PIN must not be empty", "detailsConfigurationWifiloginScanNoMatch": "Scanned code does not match the device", "wifiAuthorizationPopIsEmpty": "PoP cannot be empty", "wifiAuthenticationCredentialsHint": "As the app cannot access your private Wi-Fi password, it is not possible to check the correctness of the entry. If no connection is established, check the password and enter it again.", "generalMatterOpenApplehome": "Open Apple Home", "timeProgramNoActive": "No configured programs", "timeProgramNoEmpty": "No free time program available", "nameOfConfiguration": "Configuration name", "currentDevice": "Current device", "export": "Export", "import": "Import", "savedConfigurations": "Saved configurations", "importableServicesLabel": "The following settings can be imported:", "notImportableServicesLabel": "Incompatible settings", "deviceCategoryMeterGateway": "Meter Gateway", "deviceCategory2ChannelTimeSwitch": "2-channel time switch", "devicategoryOutdoorTimeSwitchBluetooth": "Outdoor time switch Bluetooth", "settingsModbusHeader": "Modbus", "settingsModbusDescription": "Adjust the baud rate, parity, and timeout to configure the transmission speed, error detection and waiting time.", "settingsModbusRTU": "Modbus RTU", "settingsModbusBaudrate": "Baudrate", "settingsModbusParity": "Parity", "settingsModbusTimeout": "Modbus Timeout", "locationServiceDisabled": "Location is disabled", "locationPermissionDenied": "Please allow the location permission to request your current position.", "locationPermissionDeniedPermanently": "Location permissions are permanently denied, please allow the location permission in your device settings to request your current position.", "lastSync": "Last sync", "dhcpActive": "DHCP active", "ipAddress": "IP", "subnetMask": "Subnet mask", "standardGateway": "Default gateway", "dns": "DNS", "alternateDNS": "Alternate DNS", "errorNoNetworksFound": "No wifi network found", "availableNetworks": "Available networks", "enableWifiInterface": "Enable WiFi interface", "enableLANInterface": "Enable LAN interface", "hintDontDisableAllInterfaces": "Make sure that not all interfaces are disabled. The last activated interface has priority.", "ssid": "SSID", "searchNetworks": "Search networks", "errorNoNetworkEnabled": "At least one station must be active", "errorActiveNetworkInvalid": "Not all active stations are valid", "invalidNetworkConfiguration": "Invalid network configuration", "generalDefault": "<PERSON><PERSON><PERSON>", "mqttHeader": "MQTT", "mqttConnected": "Connected to MQTT broker", "mqttDisconnected": "No connection to MQTT broker", "mqttBrokerURI": "Broker <PERSON>", "mqttBrokerURIHint": "MQTT broker URI", "mqttPort": "Port", "mqttPortHint": "MQTT port", "mqttClientId": "Client-ID", "mqttClientIdHint": "MQTT Client-ID", "mqttUsername": "Username", "mqttUsernameHint": "MQTT username", "mqttPassword": "Password", "mqttPasswordHint": "MQTT password", "mqttCertificate": "Certificate", "mqttCertificateHint": "MQTT certificate", "mqttTopic": "Topic", "mqttTopicHint": "MQTT topic", "electricityMeter": "Electricity meter", "electricityMeterCurrent": "Current", "electricityMeterHistory": "History", "electricityMeterReading": "Meter reading", "connectivity": "Connectivity", "electricMeter": "{count, plural, one {Electric meter} other {Electric meters}}", "discoveryZGW16Description": "Modbus-Energy-Meters-MQTT-Gateway", "bluetoothConnectionLost": "Bluetooth connection lost", "bluetoothConnectionLostDescription": "The Bluetooth connection to the device has been lost. Please check the connection to the device.", "openBluetoothSettings": "Open Bluetooth settings", "password": "Password", "setInitialPassword": "Set initial password", "initialPasswordMinimumLength": "The password must be at least {length} characters long", "repeatPassword": "Repeat password", "passwordsDoNotMatch": "Passwords do not match", "savePassword": "Save password", "savePasswordHint": "The password is saved for future connections on your device.", "retrieveNtpServer": "Retrieve time from NTP server", "retrieveNtpServerFailed": "The connection to the NTP server could not be established.", "retrieveNtpServerSuccess": "The connection to the NTP server was successful.", "settingsPasswordNewPasswordDescription": "Enter new password", "settingsPasswordConfirmationDescription": "Password change successful", "dhcpRangeStart": "DHCP range start", "dhcpRangeEnd": "DHCP range end", "forwardOnMQTT": "Forward to MQTT", "showAll": "Show all", "hide": "<PERSON>de", "changeToAPMode": "Change to AP mode", "changeToAPModeDescription": "You are about to connect your device to a WiFi network, in which case the connection to the device is disconnected and you must reconnect to your device via the configured network.", "consumption": "Consumption", "currentDay": "Current day", "twoWeeks": "2 Weeks", "oneYear": "1 Year", "threeYears": "3 Years", "passwordMinLength": "Password needs at least {length} characters.", "passwordNeedsLetter": "Password must contain a letter.", "passwordNeedsNumber": "Password must contain a number.", "portEmpty": "Port cannot be empty", "portInvalid": "Invalid port", "portOutOfRange": "Port must be between {rangeStart} and {rangeEnd}", "ipAddressEmpty": "IP address cannot be empty", "ipAddressInvalid": "Invalid IP address", "subnetMaskEmpty": "Subnet mask cannot be empty", "subnetMaskInvalid": "Invalid subnet mask", "gatewayEmpty": "Gateway cannot be empty", "gatewayInvalid": "Invalid gateway", "dnsEmpty": "DNS cannot be empty", "dnsInvalid": "Invalid DNS", "uriEmpty": "URI cannot be empty", "uriInvalid": "Invalid URI", "electricityMeterChangedSuccessfully": "Electricity meter successfully changed", "networkChangedSuccessfully": "Network configuration successfully changed", "mqttChangedSuccessfully": "MQTT configuration successfully changed", "modbusChangedSuccessfully": "Modbus settings successfully changed", "loginData": "Delete login data", "valueConfigured": "Configured", "electricityMeterHistoryNoData": "No data available", "locationChangedSuccessfully": "Location successfully changed", "settingsNameFailEmpty": "Name cannot be empty", "settingsNameFailLength": "Name must not be longer than {length} characters", "solsticeChangedSuccesfully": "Summer/Wintertime settings successfully changed", "relayFunctionChangedSuccesfully": "Relay-Function successfully changed", "relayFunctionHeader": "Relay function", "dimmerValueChangedSuccesfully": "Power on behaviour successfully changed", "dimmerBehaviourChangedSuccesfully": "Dimming behaviour successfully changed", "dimmerBrightnessDescription": "The minimum and maximum brightness affects all adjustable brightnesses of the dimmer.", "dimmerSettingsChangedSuccesfully": "Basic settings successfully changed", "liveUpdateEnabled": "Live test enabled", "liveUpdateDisabled": "Live test disabled", "liveUpdateDescription": "The last changed slider value will be send to the device", "demoDevices": "Demo devices", "showDemoDevices": "Show demo devices", "deviceCategoryTimeSwitch": "Time switch", "deviceCategoryMultifunctionalRelay": "Multifunctional relay", "deviceCategoryDimmer": "<PERSON><PERSON>", "deviceCategoryShutter": "Roller Shutters and Blinds", "deviceCategoryRelay": "<PERSON><PERSON>", "search": "Search", "configurationsHeader": "Configurations", "configurationsDescription": "Manage your configurations here.", "configurationsNameFailEmpty": "Configuration name cannot be empty", "configurationDeleted": "Configuration deleted", "codeFound": "{codeType} code found", "errorCameraPermission": "Please allow the camera permission to scan the ELTAKO code.", "authorizationSuccessful": "Successfully authorized on device", "wifiAuthenticationResetConfirmationDescription": "The device is now ready for a new authorization.", "settingsResetConnectionHeader": "Reset connection", "settingsResetConnectionDescription": "Do you really want to reset the connection?", "settingsResetConnectionConfirmationDescription": "Connection has been successfully reset.", "wiredInputChangedSuccesfully": "Switch behaviour successfully changed", "runtimeChangedSuccesfully": "Runtime behaviour successfully changed", "expertModeActivated": "Expert mode activated", "expertModeDeactivated": "Expert mode deactivated", "license": "License", "retry": "Retry", "provisioningConnectingHint": "Device connection is being established. This can take up to 1 minute.", "serialnumberEmpty": "Serial number cannot be empty", "interfaceStateInactiveDescriptionBLE": "Bluetooth is disabled, please enable it to discover Bluetooth devices.", "interfaceStateDeniedDescriptionBLE": "Bluetooth permissions weren't granted.", "interfaceStatePermanentDeniedDescriptionBLE": "Bluetooth permissions weren't granted. Please enable them in your device settings.", "requestPermission": "Request permission", "goToSettings": "Go to settings", "enableBluetooth": "Enable bluetooth", "installed": "Installed", "teachInDialogDescription": "Would you like to teach in your device with {type}?", "useMatter": "Use Matter", "relayMode": "Activate relay-mode", "whatsNew": "New in this version", "migrationHint": "A migration is necessary to use the new features.", "migrationHeader": "Migration", "migrationProgress": "Migration in progress...", "letsGo": "Let's go!", "noDevicesFound": "No devices found. Check whether your device is in pairing mode.", "interfaceStateEmpty": "No devices were found", "ssidEmpty": "SSID cannot be empty", "passwordEmpty": "Password cannot be empty", "settingsDeleteSettingsHeader": "Reset Settings", "settingsDeleteSettingsDescription": "Do you really want to reset all settings?", "settingsDeleteSettingsConfirmationDescription": "All settings have been successfully reset.", "locationNotFound": "Location not found", "timerProgramEmptySaveHint": "The time program is empty. Do you want to cancel the editing?", "timerProgramDaysEmptySaveHint": "No days are selected. Do you want to save the time program anyway?", "timeProgramNoDays": "At least one day must be activated", "timeProgramColliding": "Time program collides with program {program}", "timeProgramDuplicated": "Time program is a duplicate of program {program}", "screenshotZgw16": "Detached house", "interfaceStateUnknown": "No devices found", "settingsPinChange": "Change PIN", "timeProgrammOneTime": "one-time", "timeProgrammRepeating": "repeating", "generalIgnore": "Ignore", "timeProgramChooseDay": "Choose day", "generalToday": "Today", "generalTomorrow": "Tomorrow", "bluetoothAndPINChangedSuccessfully": "Bluetooth and PIN successfully changed", "generalTextDimTime": "Dimming time", "discoverySu62Description": "1-Channel Time Switch Bluetooth", "bluetoothAlwaysOnTitle": "Always on", "bluetoothAlwaysOnDescription": "Bluetooth is permanently enabled.", "bluetoothAlwaysOnHint": "Note: If this setting is activated, the device is permanently visible to everyone via Bluetooth! It is recommended to change the default PIN.", "bluetoothManualStartupOnTitle": "Temporary on", "bluetoothManualStartupOnDescription": "After power is applied, Bluetooth is activated for 3 minutes.", "bluetoothManualStartupOnHint": "Note: Pairing standby is activated for 3 minutes and then switches off. If a new connection is to be established, the button must be held down for approx. 5 seconds.", "bluetoothManualStartupOffTitle": "Manual startup", "bluetoothManualStartupOffDescription": "Bluetooth is manually activated via the button input.", "bluetoothManualStartupOffHint": "Note: To activate Bluetooth, the button on the button input must be held down for approx. 5 seconds.", "timeProgrammOneTimeRepeatingDescription": "Programmes can either be executed repeatedly by always carrying out a switching operation on the configured days and times, or they can be executed only once at the configured switching time.", "versionHeader": "Version {version}", "releaseNotesHeader": "Release notes", "release30Header": "The new ELTAKO Connect app is here!", "release30FeatureDesignHeader": "New design", "release30FeatureDesignDescription": "The app has been completely revised and has a new design. It is now even easier and more intuitive to use.", "release30FeaturePerformanceHeader": "Improved performance", "release30FeaturePerformanceDescription": "Enjoy an enhanced set-up and reduced loading times - for a better user experience.", "release30FeatureConfigurationHeader": "Cross-device configurations", "release30FeatureConfigurationDescription": "Save device configurations and transfer them to other devices. Even if they do not have the same hardware, you can, for example, transfer the configuration of your S2U12DBT1+1-UC to an ASSU-BT or vice versa.", "release31Header": "The new flush-mounted 1-channel time switch with Bluetooth is here!", "release31Description": "What can the SU62PF-BT/UC do?", "release31SU62Name": "SU62PF-BT/UC", "release31DeviceNote1": "Up to 60 time programs.", "release31DeviceNote2": "Astro function: The clock switches devices based on sunrise and sunset.", "release31DeviceNote3": "Random mode: switching times can be randomly shifted by up to 15 minutes.", "release31DeviceNote4": "Summer/winter time changeover: The clock automatically switches to summer or winter time.", "release31DeviceNote5": "Universal supply and control voltage 12-230V UC.", "release31DeviceNote6": "Push-button input for manual switching.", "release31DeviceNote7": "1 NO contact potential-free 10 A/250 V AC.", "release31DeviceNote8": "One-time execution of time programs.", "generalNew": "New", "yearsAgo": "{count, plural, one {Last year} other {{count} years ago}}", "monthsAgo": "{count, plural, one {Last month} other {{count} months ago}}", "weeksAgo": "{count, plural, one {Last week} other {{count} weeks ago}}", "daysAgo": "{count, plural, one {Yesterday} other {{count} days ago}}", "minutesAgo": "{count, plural, one {A minute ago} other {{count} minutes ago}}", "hoursAgo": "{count, plural, one {An hour ago} other {{count} hours ago}}", "secondsAgo": "{count, plural, one {A second ago} other {{count} seconds ago}}", "justNow": "Just now", "discoveryEsripmDescription": "Impulse switching relay IP Matter", "generalTextKidsRoom": "Childrens room function", "generalDescriptionKidsRoom": "When switching on with a longer push-button action ({mode}), the light is switched on at the lowest brightness level after approx. 1 second and slowly dimmed up as long as the push-button is held down, without changing the last saved brightness level.", "generalTextSceneButton": "Scene button", "settingsEnOceanConfigHeader": "EnOcean configuration", "enOceanConfigChangedSuccessfully": "EnOcean configuration successfully changed", "activateEnOceanRepeater": "Activate EnOcean Repeater", "enOceanRepeaterLevel": "Repeater level", "enOceanRepeaterLevel1": "One level", "enOceanRepeaterLevel2": "Two levels", "enOceanRepeaterOffDescription": "No wireless signals are received from sensors.", "enOceanRepeaterLevel1Description": "Only the wireless signals from sensors are received, checked and forwarded at full transmission power. Wireless signals from other repeaters are ignored to reduce the amount of data.", "enOceanRepeaterLevel2Description": "In addition to the wireless signals from sensors, the wireless signals from 1-level repeaters are also processed. A wireless signal can therefore be received and amplified a maximum of two times. Wireless repeaters do not need to be taught in. They receive and amplify the wireless signals from all wireless sensors in their reception area.", "settingsSensorHeader": "Sensors", "sensorChangedSuccessfully": "Sensors successfully changed", "wiredButton": "Wired pushbutton", "enOceanId": "EnOcean-ID", "enOceanAddManually": "Enter or scan EnOcean-ID", "enOceanIdInvalid": "Invalid EnOcean-ID", "enOceanAddAutomatically": "Teach-in with EnOcean Telegram", "enOceanAddDescription": "The EnOcean wireless protocol makes it possible to teach-in and operate push-buttons in your actuator.\n\nChoose either automatic teach-in to teach-in push-buttons at the touch of a button or select the manual option to scan or type in the EnOcean ID of your push-button.", "enOceanTelegram": "Telegram", "enOceanCodeScan": "Enter the EnOcean-ID of your {sensorType} or scan the EnOcean-QR-Code of your {sensorType}, to add it", "enOceanCode": "EnOcean QR code", "enOceanCodeScanDescription": "Search for the EnOcean code on your {sensorType} and scan it with your camera.", "enOceanButton": "EnOcean pushbutton", "enOceanBackpack": "EnOcean adapter", "sensorNotAvailable": "No sensors have been paired yet", "sensorAdd": "Add sensors", "sensorCancel": "Cancel teach-in", "sensorCancelDescription": "Do you really want to cancel the button teach-in?", "getEnOceanBackpack": "Get your EnOcean adapter", "enOceanBackpackMissing": "To enter the fantastic world of perfect connectivity and communication, you need an EnOcean adapter.\nClick here for more information", "sensorEditChangedSuccessfully": "{sensorName} successfully changed", "sensorConnectedVia": "connected via {deviceName}", "lastSeen": "Last seen", "setButtonOrientation": "Set orientation", "setButtonType": "Set button type", "button1Way": "1-way pushbutton", "button2Way": "2-way pushbutton", "button4Way": "4-way pushbutton", "buttonUnset": "not set", "button": "Push button", "sensor": "Sensor", "sensorsFound": "{count, plural, =0 {No sensors found} one {1 sensor found} other {{count} sensors found}}", "sensorSearch": "Search for sensors", "searchAgain": "Search again", "sensorTeachInHeader": "Teach in {sensorType}", "sensorChooseHeader": "Choose {sensorType}", "sensorChooseDescription": "Choose a button to teach in", "sensorCategoryDescription": "Select the category of the sensor you want to add.", "sensorName": "Button name", "sensorNameFooter": "Name your button", "sensorAddedSuccessfully": "{sensorName} was added successfully", "sensorDelete": "Delete {sensorType}", "sensorDeleteHint": "Do you really want to delete the {sensorType} {sensorName}?", "sensorDeletedSuccessfully": "{sensorName} was deleted successfully", "buttonTapDescription": "Tap the push button you want to add.", "waitingForTelegram": "The actuator is waiting for the telegram", "copied": "<PERSON>pied", "pairingFailed": "{sensorType} already paired", "generalDescriptionUniversalbutton": "With the universal button, the direction is reversed by briefly releasing the button. Short control commands switch on or off.", "generalDescriptionDirectionalbutton": "The direction button is 'switch on and dim up' at the top and 'switch off and dim down' at the bottom.", "matterForwardingDescription": "Forwarding of the Matter telegrams", "none": "None", "buttonNoneDescription": "The button has no functionality", "buttonUnsetDescription": "The button has no behaviour set", "sensorButtonTypeChangedSuccessfully": "Button type successfully changed", "forExample": "e.g. {example}}", "enOceanQRCodeInvalidDescription": "Only possible from production date 44/20", "input": "Input", "buttonSceneValueOverride": "Override scene button value", "buttonSceneValueOverrideDescription": "The scene button value will be overwritten with the current dim value through a button long press", "buttonSceneDescription": "The scene button turns on at a specific dim value", "buttonPress": "But<PERSON> pressed", "triggerOn": "Universal push-button or direction button pressed on turn on side", "triggerOff": "Universal push-button or direction button pressed on turn off side", "centralOn": "Central On", "centralOff": "Central Off", "centralButton": "Central Button", "enOceanAdapterNotFound": "No EnOcean adapter found", "updateRequired": "Update required", "updateRequiredDescription": "Your app requires an update to support this new device.", "release32Header": "The new BR64 with Matter and EnOcean and the new SU62 Bluetooth flush-mounted time switch are now available!", "release32EUD64Header": "The new flush-mounted 1-channel dimmer with Matter over Wi-Fi and up to 300W is here!", "release32EUD64Note1": "Configuration of dimming speed, on/off speed, children’s room/sleep mode, and much more.", "release32EUD64Note2": "The functionality of the EUD64NPN-IPM can be expanded through adapters, such as the EnOcean adapter EOA64.", "release32EUD64Note3": "Up to 30 EnOcean wireless switches can be directly linked to the EUD64NPN-IPM in combination with the EnOcean adapter EOA64 and forwarded to Matter.", "release32EUD64Note4": "Two wired button inputs can be directly linked to the EUD64NPN-IPM or forwarded to Matter.", "release32ESR64Header": "The new potential-free flush-mounted 1-channel switch actuator with Matter over Wi-Fi and up to 16A is here!", "release32ESR64Note1": "Configuration of various functions such as pulse switch (ES), relay function (ER), normally closed (ER-Inverse), and much more.", "release32ESR64Note2": "The functionality of the ESR64PF-IPM can be expanded through adapters, such as the EnOcean adapter EOA64.", "release32ESR64Note3": "Up to 30 EnOcean wireless switches can be directly linked to the ESR64PF-IPM in combination with the EnOcean adapter EOA64 and forwarded to Matter.", "release32ESR64Note4": "One wired button input can be directly linked to the ESR64PF-IPM or forwarded to Matter.", "buttonsFound": "{count, plural, =0 {No buttons found} one {1 button found} other {{count} buttons found}}", "doubleImpuls": "with a double impulse", "impulseDescription": "If the channel is on, it is turned off by an impulse.", "locationServiceEnable": "Activate location", "locationServiceDisabledDescription": "Location is disabled. Your operating system version needs the location to be able to find Bluetooth devices.", "locationPermissionDeniedNoPosition": "Location permissions have not been granted. Your operating system version requires location permissions to be able to find Bluetooth devices. Please allow the location permission in your device settings.", "interfaceStatePermanentDeniedDescriptionDevicesAround": "Nearby devices permission wasn't granted. Please enable the permission in your device settings.", "permissionNearbyDevices": "Nearby devices", "release320Header": "The new powerful universal dimmer EUD12NPN-BT/600W-230V is here!", "release320EUD600Header": "What can the new universal dimmer do?", "release320EUD600Note1": "Universal dimmer with up to 600W power", "release320EUD600Note2": "Expandable with LUD12 power extension up to 3800W", "release320EUD600Note3": "Local operation with universal or directional push-button", "release320EUD600Note4": "Central functions On / Off", "release320EUD600Note5": "Motion detector input for added convenience", "release320EUD600Note6": "Integrated timer with 10 switching programs", "release320EUD600Note7": "Astro function", "release320EUD600Note8": "Individual switch-on brightness", "mqttClientCertificate": "Client Certificate", "mqttClientCertificateHint": "MQTT Client Certificate", "mqttClientKey": "Client Key", "mqttClientKeyHint": "MQTT Client Key", "mqttClientPassword": "Client Password", "mqttClientPasswordHint": "MQTT Client Password", "mqttEnableHomeAssistantDiscovery": "Enable HomeAssistant MQTT Discovery", "modbusTcp": "Modbus TCP", "enableInterface": "Enable interface", "busAddress": "Bus address", "busAddressWithAddress": "Bus address {index}", "deviceType": "Device type", "registerTable": "{count, plural, one {Register table} other {Register tables}}", "currentValues": "Current values", "requestRTU": "Request RTU", "requestPriority": "Request priority", "mqttForwarding": "Forwarding to MQTT", "historicData": "Historic data", "dataFormat": "Data format", "dataType": "Data type", "description": "Description", "readWrite": "Read/Write", "unit": "Unit", "registerTableReset": "Reset register table", "registerTableResetDescription": "Should the register table really be reset?", "notConfigured": "Not configured", "release330ZGW16Header": "Major update for the ZGW16WL-IP", "release330Header": "The ZGW16WL-IP with up to 16 electricity meters", "release330ZGW16Note1": "Supports up to 16 ELTAKO Modbus electricity meters", "release330ZGW16Note2": "Modbus TCP support", "release330ZGW16Note3": "MQTT Discovery support", "screenshotButtonLivingRoom": "Living room push-button", "registerChangedSuccessfully": "Register successfully changed", "serverCertificateEmpty": "Server certificate cannot be empty", "registerTemplates": "Register templates", "registerTemplateChangedSuccessfully": "Register template successfully changed", "registerTemplateReset": "Reset Register template", "registerTemplateResetDescription": "Should the register template really be reset?", "registerTemplateNotAvailable": "No Register templates available", "rename": "<PERSON><PERSON>", "registerName": "Register name", "registerRenameDescription": "Enter a custom name for the register", "restart": "Restart device", "restartDescription": "Do you really want to restart the device?", "restartConfirmationDescription": "The device is now restarting", "deleteAllElectricityMeters": "Delete all electricity meters", "deleteAllElectricityMetersDescription": "Do you really want to delete all electricity meters?", "deleteAllElectricityMetersConfirmationDescription": "All electricity meters have been successfully deleted", "resetAllElectricityMeters": "Reset all electricity meter configurations", "resetAllElectricityMetersDescription": "Do you really want to reset all electricity meter configurations?", "resetAllElectricityMetersConfirmationDescription": "All electricity meter configurations have been successfully reset", "deleteElectricityMeterHistories": "Delete all electricity meter histories", "deleteElectricityMeterHistoriesDescription": "Do you really want to delete all electricity meter histories?", "deleteElectricityMeterHistoriesConfirmationDescription": "All electricity meter histories have been successfully deleted", "multipleElectricityMetersSupportMissing": "Your device currently supports only one electricity meter. Please update your firmware.", "consumptionWithUnit": "Usage (kWh)", "exportWithUnit": "Delivery (kWh)", "importWithUnit": "Consumption (kWh)", "resourceWarningHeader": "Resource limitations", "mqttAndTcpResourceWarning": "Operating MQTT and Modbus TCP at the same time is not possible due to limited system resources. Deactivate {protocol} first.", "mqttEnabled": "MQTT enabled", "redirectMQTT": "Go to MQTT Settings", "redirectModbus": "Go to Modbus Settings", "unsupportedSettingDescription": "With your current firmware version, some of the device settings are not supported. Please update your firmware to use the new features", "updateNow": "Update now", "zgw241Hint": "With this update, Modbus TCP is enabled by default and MQTT is disabled. This can be changed in the settings. With support for up to 16 counters, many optimisations have been made; this may lead to changes in the device settings. Please restart the device after adjusting the settings.", "deviceConfigChangedSuccesfully": "Device Configuration successfully changed", "deviceConfiguration": "Device configuration", "tiltModeToggle": "Tilt mode", "tiltModeToggleFooter": "If the device is set up in Matter, all functions must be reconfigured there", "shaderMovementDirection": "Reverse Up/Down", "shaderMovementDirectionDescription": "Reverse the direction for Up/Down movement of the motor", "tiltTime": "Tilt runtime", "changeTiltModeDialogTitle": "{target, select, true {Enable} false {Disable} other {Change}} tilt function", "changeTiltModeDialogConfirmation": "{target, select, true {Enable} false {Disable} other {Change}}", "generalTextSlatSetting": "Slat setting", "generalTextPosition": "Position", "generalTextSlatPosition": "Slat position", "slatSettingDescription": "Slat Setting Description", "scenePositionSliderDescription": "Height", "sceneSlatPositionSliderDescription": "Tilt", "referenceRun": "Calibration run", "slatAutoSettingHint": "In this mode, the position of the shades don't matter before the slats adjust adjust to the desired tilt position.", "slatReversalSettingHint": "In this mode, the shades will fully close before the slats adjust to the desired tilt position.", "release340Header": "The new ESB64NP-IPM flush-mounted matter shading actuator is here!", "release340ESB64Header": "What is the ESB64NP-IPM capable of?", "release340ESB64Note1": "Our Matter Gateway-certified shading actuator with optional slat function", "release340ESB64Note2": "Two wired button inputs for manual switching and forwarding to Matter", "release340ESB64Note3": "Expandable with EnOcean adapter (EOA64). E.g. with EnOcean wireless push button F4T55", "release340ESB64Note4": "Open for integrations thanks to REST API based on OpenAPI standard", "activateTiltModeDialogText": "If the tilt function is enabled, all settings will be lost. Are you sure you want to enable the tilt function?", "deactivateTiltModeDialogText": "If the tilt function is disabled, all settings will be lost. Are you sure you want to disable the tilt function?", "shareConfiguration": "Share configuration {name}", "configurationSharedSuccessfully": "Configuration shared successfully", "configurationShareFailed": "Sharing configuration failed"}