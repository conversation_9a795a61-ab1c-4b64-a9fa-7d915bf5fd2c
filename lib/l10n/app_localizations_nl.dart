// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Dutch Flemish (`nl`).
class AppLocalizationsNl extends AppLocalizations {
  AppLocalizationsNl([String locale = 'nl']) : super(locale);

  @override
  String get appName => 'ELTAKO Connect';

  @override
  String get discoveryHint =>
      'Activeer Bluetooth op het apparaat om verbinding te maken';

  @override
  String devicesFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count apparaten gevonden',
      one: '1 apparaat gevonden',
      zero: 'Geen apparaat gevonden',
    );
    return '$_temp0';
  }

  @override
  String discoveryDemodeviceName(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Demo apparaten',
      one: 'Demo apparaat',
    );
    return '$_temp0';
  }

  @override
  String get discoverySu12Description => '2-Ka<PERSON>als Schakelklok Bluetooth';

  @override
  String get discoveryImprint => 'Afdruk';

  @override
  String get discoveryLegalnotice => 'Juridische mededeling';

  @override
  String get generalSave => 'Opslaan';

  @override
  String get generalCancel => 'Annuleren';

  @override
  String get detailsHeaderHardwareversion => 'Hardware versie';

  @override
  String get detailsHeaderSoftwareversion => 'Software versie';

  @override
  String get detailsHeaderConnected => 'Verbonden';

  @override
  String get detailsHeaderDisconnected => 'Verbinding verbroken';

  @override
  String get detailsTimersectionHeader => 'Programma\'s';

  @override
  String get detailsTimersectionTimercount => 'gebruikt door 60 programma\'s';

  @override
  String get detailsConfigurationsectionHeader => 'Configuratie';

  @override
  String get detailsConfigurationPin => 'Apparaat-PIN';

  @override
  String detailsConfigurationChannelsDescription(
    Object channel1,
    Object channel2,
  ) {
    return 'Kanaal 1: $channel1 en Kanaal 2: $channel2';
  }

  @override
  String get settingsCentralHeader => 'Centraal aan/uit';

  @override
  String get detailsConfigurationCentralDescription =>
      'Werkt alleen als het kanaal op Auto staat.';

  @override
  String get detailsConfigurationDevicedisplaylock => 'Display vergrendelen';

  @override
  String get timerOverviewHeader => 'Programma\'s';

  @override
  String get timerOverviewTimersectionTimerinactivecount => 'inactief';

  @override
  String get timerDetailsListsectionDays1 => 'Maandag';

  @override
  String get timerDetailsListsectionDays2 => 'Dinsdag';

  @override
  String get timerDetailsListsectionDays3 => 'Woensdag';

  @override
  String get timerDetailsListsectionDays4 => 'Donderdag';

  @override
  String get timerDetailsListsectionDays5 => 'Vrijdag';

  @override
  String get timerDetailsListsectionDays6 => 'Zaterdag';

  @override
  String get timerDetailsListsectionDays7 => 'Zondag';

  @override
  String get timerDetailsHeader => 'Programma';

  @override
  String get timerDetailsSunrise => 'Zonsopkomst';

  @override
  String get generalToggleOff => 'Uit';

  @override
  String get generalToggleOn => 'Aan';

  @override
  String get timerDetailsImpuls => 'Impuls';

  @override
  String get generalTextTime => 'Tijd';

  @override
  String get generalTextAstro => 'Astro';

  @override
  String get generalTextAuto => 'Auto';

  @override
  String get timerDetailsOffset => 'Tijdverschuiving';

  @override
  String get timerDetailsPlausibility => 'Activeer plausibiliteitscontrole';

  @override
  String get timerDetailsPlausibilityDescription =>
      'Als de \'Uit\'-tijd voor de \'Aan\'-tijd ligt, worden beide tijden genegeerd, bijvoorbeeld inschakelen bij zonsopgang en uitschakelen om 6.00 uur in de ochtend. Er zijn ook constellaties waarbij de test ongewenst is, bijvoorbeeld het inschakelen bij zonsondergang en uitschakelen om 01.00 uur.';

  @override
  String get generalDone => 'Gedaan';

  @override
  String get generalDelete => 'Verwijderen';

  @override
  String get timerDetailsImpulsDescription =>
      'Verander de globale impuls configuratie';

  @override
  String get settingsNameHeader => 'Apparaat naam';

  @override
  String get settingsNameDescription =>
      'Deze naam wordt gebruikt om het apparaat te identificeren.';

  @override
  String get settingsFactoryresetHeader => 'Fabrieksinstellingen';

  @override
  String get settingsFactoryresetDescription =>
      'Welke inhoud moet worden gereset?';

  @override
  String get settingsFactoryresetResetbluetooth =>
      'Bluetooth-instellingen resetten';

  @override
  String get settingsFactoryresetResettime => 'Tijdinstellingen resetten';

  @override
  String get settingsFactoryresetResetall =>
      'Terugzetten naar fabrieksinstellingen';

  @override
  String get settingsDeletetimerHeader => 'Programma\'s verwijderen';

  @override
  String get settingsDeletetimerDescription =>
      'Moeten alle programma\'s echt verwijderd worden?';

  @override
  String get settingsDeletetimerAllchannels => 'Alle kanalen';

  @override
  String get settingsImpulseHeader => 'Impuls-schakeltijd';

  @override
  String get settingsImpulseDescription =>
      'De impuls-schakeltijd geeft de duur van de puls aan.';

  @override
  String get generalTextRandommode => 'Willekeurige modus';

  @override
  String get settingsChannelsTimeoffsetHeader =>
      'Tijdsverschuiving van de zonnewende';

  @override
  String settingsChannelsTimeoffsetDescription(
    Object summerOffset,
    Object winterOffset,
  ) {
    return 'Zomer: $summerOffset Min | Winter: $winterOffset Min';
  }

  @override
  String get settingsLocationHeader => 'Plaats';

  @override
  String get settingsLocationDescription =>
      'Stel uw locatie in om astro-functies te gebruiken.';

  @override
  String get settingsLanguageHeader => 'Apparaattaal';

  @override
  String get settingsLanguageSetlanguageautomatically =>
      'Taal automatisch instellen';

  @override
  String settingsLanguageDescription(Object deviceType) {
    return 'Kies de taal voor de $deviceType';
  }

  @override
  String get settingsLanguageGerman => 'Duits';

  @override
  String get settingsLanguageFrench => 'Frans';

  @override
  String get settingsLanguageEnglish => 'Engels';

  @override
  String get settingsLanguageItalian => 'Italiaans';

  @override
  String get settingsLanguageSpanish => 'Spaans';

  @override
  String get settingsDatetimeHeader => 'Datum en tijd';

  @override
  String get settingsDatetimeSettimeautomatically => 'Systeemtijd toepassen';

  @override
  String get settingsDatetimeSettimezoneautomatically =>
      'Tijdzone automatisch instellen';

  @override
  String get generalTextTimezone => 'Tijdzone';

  @override
  String get settingsDatetime24Hformat => '24-uurs-formaat';

  @override
  String get settingsDatetimeSetsummerwintertimeautomatically =>
      'Zomer-/wintertijd automatisch';

  @override
  String get settingsDatetimeWinter => 'Winter';

  @override
  String get settingsDatetimeSummer => 'Zomer';

  @override
  String get settingsPasskeyHeader => 'Huidige apparaat-PIN';

  @override
  String get settingsPasskeyDescription => 'Voer de huidige apparaat-PIN in';

  @override
  String get timerDetailsActiveprogram => 'Programma actief';

  @override
  String get timerDetailsActivedays => 'Actieve dagen';

  @override
  String get timerDetailsSuccessdialogHeader => 'Succesvol';

  @override
  String get timerDetailsSuccessdialogDescription =>
      'Programma succesvol toegevoegd';

  @override
  String get settingsRandommodeDescription =>
      'De willekeurige modus werkt alleen bij tijd-programma\'s, niet bij impuls- of astro-programma\'s (zonsopgang of zonsondergang).';

  @override
  String get settingsSolsticeHeader => 'Verschuiving van de zonnewendetijd';

  @override
  String get settingsSolsticeDescription =>
      'De tijd geeft de tijdsverschuiving vanaf zonsondergang aan. De zonsopgang wordt dienovereenkomstig omgekeerd.';

  @override
  String get settingsSolsticeHint =>
      'Voorbeeld:\nIn de winter schakelt hij 30 minuten voor zonsondergang, wat betekent dat hij ook 30 minuten na zonsopgang schakelt.';

  @override
  String get generalTextMinutesShort => 'Min';

  @override
  String get settingsPinDescription => 'De PIN is vereist voor de verbinding.';

  @override
  String get settingsPinHeader => 'Nieuwe apparaat-PIN';

  @override
  String get settingsPinNewpinDescription => 'Voer een nieuwe PIN in';

  @override
  String get settingsPinNewpinRepeat => 'Herhaal de nieuwe PIN';

  @override
  String get detailsProductinfo => 'Productinformatie';

  @override
  String get settingsDatetimeSettimeautodescription =>
      'Kies het gewenste tijdstip';

  @override
  String minutes(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'minuten',
      one: 'minuut',
    );
    return '$_temp0';
  }

  @override
  String hours(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'uren',
      one: 'uur',
    );
    return '$_temp0';
  }

  @override
  String seconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'seconden',
      one: 'seconde',
    );
    return '$_temp0';
  }

  @override
  String generalTextChannel(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'kanalen',
      one: 'kanaal',
    );
    return '$_temp0';
  }

  @override
  String generalLabelChannel(Object number) {
    return 'Kanaal $number';
  }

  @override
  String get generalTextDate => 'Datum';

  @override
  String get settingsDatetime24HformatDescription =>
      'Kies het gewenste formaat';

  @override
  String get settingsDatetimeSetsummerwintertime => 'Zomer-/wintertijd';

  @override
  String get settingsDatetime24HformatValue24 => '24h';

  @override
  String get settingsDatetime24HformatValue12 => 'AM/PM';

  @override
  String get detailsEdittimer => 'Programma\'s bewerken';

  @override
  String get settingsPinOldpinRepeat => 'Herhaal de huidige PIN';

  @override
  String get settingsPinCheckpin => 'PIN controleren';

  @override
  String detailsDevice(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'apparaten',
      one: 'apparaat',
    );
    return '$_temp0';
  }

  @override
  String get detailsDisconnect => 'Verbinding verbreken';

  @override
  String get settingsCentralDescription =>
      'De ingang A1 regelt de centraal aan/uit.\nCentraal aan/uit is alleen van toepassing als het kanaal op centraal aan/uit staat.';

  @override
  String get settingsCentralHint =>
      'Voorbeeld:\nKanaal 1 = Centraal aan/uit\nKanaal 2 = Uit\nA1 = Centraal aan -> Alleen K1 schakelt aan, K2 blijft uit';

  @override
  String get settingsCentralToggleheader => 'Centrale ingang schakelt';

  @override
  String get settingsCentralActivechannelsdescription =>
      'Huidige kanalen met de instelling centraal aan/uit:';

  @override
  String get settingsSolsticeSign => 'Teken';

  @override
  String get settingsDatetimeTimezoneDescription => 'Midden-Europese tijd';

  @override
  String get generalButtonContinue => 'Doorgaan';

  @override
  String get settingsPinConfirmationDescription => 'PIN-wijziging geslaagd';

  @override
  String get settingsPinFailDescription => 'PIN-wijziging mislukt';

  @override
  String get settingsPinFailHeader => 'Mislukt';

  @override
  String get settingsPinFailShort => 'De PIN moet precies 6 cijfers lang zijn';

  @override
  String get settingsPinFailWrong => 'De actuele PIN is onjuist';

  @override
  String get settingsPinFailMatch => 'De PIN-codes komen niet overeen';

  @override
  String get discoveryLostconnectionHeader => 'Verbinding verbroken';

  @override
  String get discoveryLostconnectionDescription =>
      'De verbinding van het apparaat is verbroken.';

  @override
  String get settingsChannelConfigCentralDescription =>
      'Gedraagt zich als AUTO en reageert ook op de bedrade centrale ingangen';

  @override
  String get settingsChannelConfigOnDescription =>
      'Schakelt het kanaal permanent op AAN en negeert de programma\'s';

  @override
  String get settingsChannelConfigOffDescription =>
      'Schakelt het kanaal permanent UIT en negeert de programma\'s';

  @override
  String get settingsChannelConfigAutoDescription =>
      'Schakelt volgens de tijd- en astroprogramma\'s.';

  @override
  String get bluetoothPermissionDescription =>
      'Bluetooth is vereist voor de configuratie van de apparaten.';

  @override
  String get timerListitemOn => 'Schakel aan';

  @override
  String get timerListitemOff => 'Schakel uit';

  @override
  String get timerListitemUnknown => 'Onbekend';

  @override
  String get timerDetailsAstroHint =>
      'De locatie moet worden ingesteld in de instellingen om de astro-programma\'s correct te laten werken.';

  @override
  String get timerDetailsTrigger => 'Trigger';

  @override
  String get timerDetailsSunset => 'Zonsondergang';

  @override
  String get settingsLocationCoordinates => 'Coördinaten';

  @override
  String get settingsLocationLatitude => 'Breedtegraad';

  @override
  String get settingsLocationLongitude => 'Lengtegraad';

  @override
  String timerOverviewEmptyday(Object day) {
    return 'Er worden momenteel geen programma\'s gebruikt voor $day';
  }

  @override
  String get timerOverviewProgramloaded => 'Programma\'s worden geladen';

  @override
  String get timerOverviewProgramchanged => 'Programma is gewijzigd';

  @override
  String get settingsDatetimeProcessing => 'Datum en tijd zijn gewijzigd';

  @override
  String get deviceNameEmpty => 'Invoer mag niet leeg zijn';

  @override
  String deviceNameHint(Object count) {
    return 'De invoer mag niet meer dan $count tekens bevatten.';
  }

  @override
  String get deviceNameChanged => 'Apparaatnaam is gewijzigd.';

  @override
  String get deviceNameChangedSuccessfully =>
      'Apparaatnaam is succesvol gewijzigd.';

  @override
  String get deviceNameChangedFailed => 'Er is een fout opgetreden.';

  @override
  String get settingsPinConfirm => 'Bevestigen';

  @override
  String get deviceShowInstructions =>
      '1. Activeer Bluetooth van de klok met SET\n2. Tik op de knop hierboven om het zoeken te starten';

  @override
  String get deviceNameNew => 'Voer een nieuwe apparaatnaam in';

  @override
  String get settingsLanguageRetrieved => 'Taal wordt opgehaald';

  @override
  String get detailsProgramsShow => 'Programma\'s weergeven';

  @override
  String get generalTextProcessing => 'Even geduld a.u.b.';

  @override
  String get generalTextRetrieving => 'worden opgehaald.';

  @override
  String get settingsLocationPermission =>
      'Geef ELTAKO Connect toegang tot de locatie van dit apparaat';

  @override
  String get timerOverviewChannelloaded => 'Kanalen worden geladen';

  @override
  String get generalTextRandommodeChanged => 'Willekeurige modus is gewijzigd';

  @override
  String get detailsConfigurationsectionChanged => 'Configuratie is gewijzigd';

  @override
  String get settingsSettimeFunctions => 'Tijdfuncties zijn gewijzigd';

  @override
  String get imprintContact => 'Contact';

  @override
  String get imprintPhone => 'Telefoon';

  @override
  String get imprintMail => 'E-mail';

  @override
  String get imprintRegistrycourt => 'Registratie rechtbank';

  @override
  String get imprintRegistrynumber => 'Registratie nummer';

  @override
  String get imprintCeo => 'Directeur';

  @override
  String get imprintTaxnumber => 'BTW-identificatienummer';

  @override
  String get settingsLocationCurrent => 'Huidige locatie';

  @override
  String get generalTextReset => 'Resetten';

  @override
  String get discoverySearchStart => 'Start met zoeken';

  @override
  String get discoverySearchStop => 'Stop met zoeken';

  @override
  String get settingsImpulsSaved => 'Impuls-schakeltijd is opgeslagen';

  @override
  String get settingsCentralNochannel =>
      'Er zijn geen kanalen met de instelling centraal aan/uit';

  @override
  String get settingsFactoryresetBluetoothConfirmationDescription =>
      'De Bluetooth-verbinding is succesvol gereset.';

  @override
  String get settingsFactoryresetBluetoothFailDescription =>
      'Resetten van Bluetooth-verbindingen mislukt.';

  @override
  String get imprintPublisher => 'Uitgeverij';

  @override
  String get discoveryDeviceConnecting =>
      'De verbinding word tot stand gebracht';

  @override
  String get discoveryDeviceRestarting => 'Opnieuw opstarten...';

  @override
  String get generalTextConfigurationsaved => 'Kanaalconfiguratie opgeslagen.';

  @override
  String get timerOverviewChannelssaved => 'Kanalen opslaan';

  @override
  String get timerOverviewSaved => 'Timer opgeslagen';

  @override
  String get timerSectionList => 'Lijstweergave';

  @override
  String get timerSectionDayview => 'Dagweergave';

  @override
  String get generalTextChannelInstructions => 'Kanaalinstellingen';

  @override
  String get generalTextPublisher => 'Uitgeverij';

  @override
  String get settingsDeletetimerDialog =>
      'Wilt u echt alle programma\'s wissen?';

  @override
  String get settingsFactoryresetResetbluetoothDialog =>
      'Wilt u echt alle Bluetooth-instellingen resetten?';

  @override
  String get settingsCentralTogglecentral => 'Centraal\naan/uit';

  @override
  String generalTextConfirmation(Object serviceName) {
    return '$serviceName is succesvol gewijzigd.';
  }

  @override
  String generalTextFailed(Object serviceName) {
    return '$serviceName kan niet worden gewijzigd.';
  }

  @override
  String get settingsChannelConfirmationDescription =>
      'Kanalen zijn succesvol gewijzigd.';

  @override
  String get timerDetailsSaveHeader => 'Programma opslaan';

  @override
  String get timerDetailsDeleteHeader => 'Programma verwijderen';

  @override
  String get timerDetailsSaveDescription =>
      'Het programma is succesvol opgeslagen.';

  @override
  String get timerDetailsDeleteDescription =>
      'Programma is succesvol verwijderd.';

  @override
  String get timerDetailsAlertweekdays =>
      'Het programma kan niet worden opgeslagen, omdat er geen weekdagen zijn geselecteerd.';

  @override
  String get generalTextOk => 'OK';

  @override
  String get settingsDatetimeChangesuccesfull =>
      'De tijd is succesvol veranderd.';

  @override
  String get discoveryConnectionFailed => 'Verbinding mislukt';

  @override
  String get discoveryDeviceResetrequired =>
      'Er kon geen verbinding met het apparaat tot stand worden gebracht. Om dit probleem op te lossen, verwijdert u het apparaat uit uw Bluetooth-instellingen. Neem contact op met onze technische ondersteuning als het probleem aanhoudt.';

  @override
  String get generalTextSearch => 'Zoek apparaten';

  @override
  String get generalTextOr => 'of';

  @override
  String get settingsFactoryresetProgramsConfirmationDescription =>
      'Alle programma\'s zijn succesvol verwijderd.';

  @override
  String get generalTextManualentry => 'Handmatige invoer';

  @override
  String get settingsLocationSaved => 'Locatie opgeslagen';

  @override
  String get settingsLocationAutosearch => 'Zoek locatie automatisch';

  @override
  String get imprintPhoneNumber => '+31 6 504 190 67';

  @override
  String get imprintMailAddress => '<EMAIL>';

  @override
  String get settingsFactoryresetResetallDialog =>
      'Wilt u het apparaat echt terugzetten naar de fabrieksinstellingen?';

  @override
  String get settingsFactoryresetFactoryConfirmationDescription =>
      'Het apparaat is succesvol teruggezet naar de fabrieksinstellingen.';

  @override
  String get settingsFactoryresetFactoryFailDescription =>
      'Apparaat resetten is mislukt.';

  @override
  String get imprintPhoneNumberIos => '+31 6 504 190 67';

  @override
  String get mfzFunctionA2Title => '2-traps responsvertraging (A2)';

  @override
  String get mfzFunctionA2TitleShort => '2-traps responsvertraging (A2)';

  @override
  String get mfzFunctionA2Description =>
      'Bij het aanleggen van de stuurspanning begint de tijdsverloop T1 tussen 0 en 60 seconden. Aan het einde van deze periode sluit contact 1-2 en begint het tijdsverloop T2 tussen 0 en 60 seconden. Aan het einde van deze tijd sluit contact 3-4. Na een onderbreking begint het tijdverloop opnieuw met T1.';

  @override
  String get mfzFunctionRvTitle => 'Uitschakelvertraging (RV)';

  @override
  String get mfzFunctionRvTitleShort => 'RV | Uitschakelvertraging';

  @override
  String get mfzFunctionRvDescription =>
      'Bij het aanleggen van de stuurspanning schakelt het relaiscontact naar 15-18. Bij het wegvallen van de stuurspanning wordt de afvalvertragingstijd gestart; bij het beëindigen van de tijd keert het relaiscontact terug naar de normale stand. Resetbaar tijdens de afvalvertragingstijd.';

  @override
  String get mfzFunctionTiTitle => 'Impulsgever beginnend met puls (TI)';

  @override
  String get mfzFunctionTiTitleShort => 'TI | impulsgever beginnend met puls';

  @override
  String get mfzFunctionTiDescription =>
      'Zolang de stuurspanning aanwezig is, sluit en opent het schakelcontact. De omschakeltijd is in beide richtingen apart instelbaar. Bij het inschakelen van de stuurspanning verandert het schakelcontact direct naar 15-18.';

  @override
  String get mfzFunctionAvTitle => 'Vertraagd opkomend (AV)';

  @override
  String get mfzFunctionAvTitleShort => 'AV | vertraagd opkomend';

  @override
  String get mfzFunctionAvDescription =>
      'Wanneer de stuurspanning wordt aangelegd, wordt de opkomvertragingstijd gestart; bij het beëindigen van de tijd verandert het schakelcontact naar 15-18. Na een onderbreking wordt de opkomvertragingstijd opnieuw gestart.';

  @override
  String get mfzFunctionAvPlusTitle => 'Vertraagd opkomend met geheugen (AV+)';

  @override
  String get mfzFunctionAvPlusTitleShort =>
      'AV+ | vertraagd opkomend met geheugen';

  @override
  String get mfzFunctionAvPlusDescription =>
      'De functie is hetzelfde als AV. Na een onderbreking wordt de verstreken tijd echter opgeslagen.';

  @override
  String get mfzFunctionAwTitle => 'Uitschakelwissend relais (AW)';

  @override
  String get mfzFunctionAwTitleShort => 'AW | uitschakelwissend relais';

  @override
  String get mfzFunctionAwDescription =>
      'Wanneer de stuurspanning wordt onderbroken, verandert het contact naar 15-18 en keert terug na het beëindigen van de tijd. Als tijdens de wistijd de stuurspanning aanwezig is, springt het contact onmiddellijk terug naar 15-16 en vervalt de resterende tijd.';

  @override
  String get mfzFunctionIfTitle => 'Impulsvormer (IF; alleen MFZ12.1)';

  @override
  String get mfzFunctionIfTitleShort => 'IF | impulsvormer';

  @override
  String get mfzFunctionIfDescription =>
      'Bij het aanleggen van de stuurspanning verandert het schakelcontact gedurende de ingestelde tijd naar 15-18. Verdere stuurimpulsen worden pas na het verstrijken van de ingestelde tijd geëvalueerd.';

  @override
  String get mfzFunctionEwTitle => 'Inschakelwissend relais (EW)';

  @override
  String get mfzFunctionEwTitleShort => 'EW | inschakelwissend relais';

  @override
  String get mfzFunctionEwDescription =>
      'Wanneer de stuurspanning wordt aangelegd, verandert het contact naar 15-18 en keert terug na het beëindigen van de tijd. Als de stuurspanning tijdens de wistijd wegvalt, schakelt het contact onmiddellijk terug naar 15-16 en vervalt de resterende tijd.';

  @override
  String get mfzFunctionEawTitle =>
      'Inschakel- en uitschakelwissend relais (EAW)';

  @override
  String get mfzFunctionEawTitleShort =>
      'EAW | inschakel- en uitschakelwissend relais';

  @override
  String get mfzFunctionEawDescription =>
      'Bij het aanleggen of onderbreken van de stuurspanning schakelt het schakelcontact naar 15-18 en keert terug na de ingestelde wistijd.';

  @override
  String get mfzFunctionTpTitle => 'Impulsgever beginnend met pauze (TP)';

  @override
  String get mfzFunctionTpTitleShort => 'TP | impulsgever beginnend met pauze';

  @override
  String get mfzFunctionTpDescription =>
      'Beschrijving van de functie hetzelfde als voor TI, behalve dat wanneer de stuurspanning wordt toegepast, het contact aanvankelijk op 15-16 blijft in plaats van te veranderen naar 15-18.';

  @override
  String get mfzFunctionIaTitle =>
      'Impulsgestuurd vertraagd opkomend bijv. automatische deuropeners (IA; alleen MFZ12.1))';

  @override
  String get mfzFunctionIaTitleShort =>
      'IA | impulsgestuurd vertraagd opkomend';

  @override
  String get mfzFunctionIaDescription =>
      'De tijdsduur T1 start met een stuurimpuls vanaf 50ms; bij het beëindigen van de tijd wisselt het schakelcontact gedurende de tijdsduur T2 naar 15-18 gedurende 1 seconde (bijv. voor automatische deuropener). Als T1 is ingesteld op T1 min = 0,1 seconden werkt de IA als impulsvormer wanneer de tijd T2 verstrijkt, onafhankelijk van de duur van de stuurimpuls (min. 150 ms).';

  @override
  String get mfzFunctionArvTitle =>
      'Vertraagd opkomend en vertraagd\nafvallend (ARV)';

  @override
  String get mfzFunctionArvTitleShort =>
      'ARV | vertraagd opkomend en vertraagd\nafvallend';

  @override
  String get mfzFunctionArvDescription =>
      'Wanneer de stuurspanning wordt aangelegd, begint de tijd, bij het beëindigen van de tijd wordt het schakelcontact naar de stand 15 -18 gebracht. Als de stuurspanning vervolgens wordt onderbroken, begint een nieuwe tijd, aan het einde van deze tijd schakelt het schakelcontact weer terug naar de ruststand.\nNa een onderbreking van de opkomvertraging begint de tijd opnieuw.';

  @override
  String get mfzFunctionArvPlusTitle =>
      'Vertraagd opkomend en vertraagd afvallend met geheugen (ARV+)';

  @override
  String get mfzFunctionArvPlusTitleShort =>
      'ARV+ | vertraagd opkomend en vertraagd afvallend met geheugen';

  @override
  String get mfzFunctionArvPlusDescription =>
      'Dezelfde functie als ARV, maar na een onderbreking van de opkomvertraging wordt de verstreken tijd opgeslagen.';

  @override
  String get mfzFunctionEsTitle => 'Impulsrelais (ES)';

  @override
  String get mfzFunctionEsTitleShort => 'ES | impulsrelais';

  @override
  String get mfzFunctionEsDescription =>
      'Bij elke stuurimpuls vanaf 50 ms schakelt het schakelcontact om.';

  @override
  String get mfzFunctionEsvTitle =>
      'Impulsrelais met afvalvertraging en uitschakelvoorwaarschuwing (ESV)';

  @override
  String get mfzFunctionEsvTitleShort =>
      'ESV | impulsrelais met afvalvertraging en uitschakelvoorwaarschuwing';

  @override
  String get mfzFunctionEsvDescription =>
      'Functie hetzelfde als SRV. Bovendien met uitschakelvoorwaarschuwing: ca. 30 sec. voor het beëindigen van de tijd begint de verlichting 3 keer te knipperen in geleidelijk kortere tijdsintervallen.';

  @override
  String get mfzFunctionErTitle => 'Relais (ER)';

  @override
  String get mfzFunctionErTitleShort => 'ER | relais';

  @override
  String get mfzFunctionErDescription =>
      'Bij het aanleggen van de stuurspanning schakelt het schakelcontact van 15-16 naar 15-18.';

  @override
  String get mfzFunctionSrvTitle => 'Impulsrelais met afvalvertraging (SRV)';

  @override
  String get mfzFunctionSrvTitleShort =>
      'SRV | impulsrelais met afvalvertraging';

  @override
  String get mfzFunctionSrvDescription =>
      'Bij elke stuurimpuls vanaf 50 ms schakelt het schakelcontact heen en weer. In de schakelstand 15-18 schakelt het apparaat na de afvalvertragingstijd automatisch weer terug naar de ruststand 15-16.';

  @override
  String get detailsFunctionsHeader => 'Functies';

  @override
  String mfzFunctionTimeHeader(Object index) {
    return 'Tijd (t$index)';
  }

  @override
  String get mfzFunctionOnDescription => 'permanent AAN';

  @override
  String get mfzFunctionOffDescription => 'permanent UIT';

  @override
  String get mfzFunctionMultiplier => 'Factor';

  @override
  String get discoveryMfz12Description =>
      'Multifunctioneel-tijdrelais Bluetooth';

  @override
  String get mfzFunctionOnTitle => 'Aan (ON)';

  @override
  String get mfzFunctionOnTitleShort => 'Aan (ON)';

  @override
  String get mfzFunctionOffTitle => 'Uit (OFF)';

  @override
  String get mfzFunctionOffTitleShort => 'Uit (OFF)';

  @override
  String get mfzMultiplierSecondsFloatingpoint => '0.1 seconden';

  @override
  String get mfzMultiplierMinutesFloatingpoint => '0.1 minuten';

  @override
  String get mfzMultiplierHoursFloatingpoint => '0.1 uren';

  @override
  String get mfzOverviewFunctionsloaded => 'Functies worden geladen';

  @override
  String get mfzOverviewSaved => 'Functie opgeslagen';

  @override
  String get settingsBluetoothHeader => 'Bluetooth';

  @override
  String get bluetoothChangedSuccessfully =>
      'Bluetooth-instelling is succesvol gewijzigd.';

  @override
  String get settingsBluetoothInformation =>
      'Opmerking: Als deze instelling is geactiveerd, is het apparaat permanent zichtbaar voor iedereen via Bluetooth! Het wordt aanbevolen om de pincode van het apparaat te wijzigen.';

  @override
  String get settingsBluetoothContinuousconnection =>
      'Permanente zichtbaarheid';

  @override
  String settingsBluetoothContinuousconnectionDescription(Object deviceType) {
    return 'Door de zichtbaarheid van het apparaat in te schakelen, blijft Bluetooth actief op de apparaat ($deviceType) en hoeft deze niet handmatig te worden geactiveerd voordat een verbinding tot stand kan worden gebracht.';
  }

  @override
  String get settingsBluetoothTimeout => 'Time-out verbinding';

  @override
  String get settingsBluetoothPinlimit => 'PIN-beperking';

  @override
  String settingsBluetoothTimeoutDescription(Object timeout) {
    return 'De verbinding wordt verbroken na $timeout minuten inactiviteit.';
  }

  @override
  String settingsBluetoothPinlimitDescription(Object attempts) {
    return 'Om veiligheidsredenen heeft u een maximum van $attempts pogingen om de PIN in te voeren. Bluetooth wordt daarna gedeactiveerd en moet handmatig weer opnieuw worden geactiveerd voor een nieuwe verbinding.';
  }

  @override
  String get settingsBluetoothPinAttempts => 'pogingen';

  @override
  String get settingsResetfunctionHeader => 'Functies resetten';

  @override
  String get settingsResetfunctionDialog =>
      'Wilt u echt alle functies resetten?';

  @override
  String get settingsFactoryresetFunctionsConfirmationDescription =>
      'Alle functies zijn succesvol gereset.';

  @override
  String get mfzFunctionTime => 'Tijd (t)';

  @override
  String get discoveryConnectionFailedInfo => 'Geen Bluetooth-verbinding';

  @override
  String get detailsConfigurationDevicedisplaylockDialogtext =>
      'Onmiddellijk na het vergrendelen van het display van het apparaat wordt Bluetooth gedeactiveerd en moet deze handmatig opnieuw worden geactiveerd om een nieuwe verbinding tot stand te brengen.';

  @override
  String get detailsConfigurationDevicedisplaylockDialogquestion =>
      'Weet u zeker dat u het display van het apparaat wilt vergrendelen?';

  @override
  String get settingsDemodevices => 'Demo-apparaten weergeven';

  @override
  String get generalTextSettings => 'Instellingen';

  @override
  String get discoveryWifi => 'WiFi';

  @override
  String get settingsInformations => 'Informatie';

  @override
  String get detailsConfigurationDimmingbehavior => 'Dimgedrag';

  @override
  String get detailsConfigurationSwitchbehavior => 'Schakelaar gedrag';

  @override
  String get detailsConfigurationBrightness => 'Helderheid';

  @override
  String get detailsConfigurationMinimum => 'Minimale helderheid';

  @override
  String get detailsConfigurationMaximum => 'Maximale helderheid';

  @override
  String get detailsConfigurationSwitchesGr => 'Groepen relais (GR)';

  @override
  String get detailsConfigurationSwitchesGs => 'Groepen impulsrelais (GS)';

  @override
  String get detailsConfigurationSwitchesCloserer => 'NO-relais (ER)';

  @override
  String get detailsConfigurationSwitchesClosererDescription =>
      'Uit -> ingedrukt houden (aan) -> loslaten (uit)';

  @override
  String get detailsConfigurationSwitchesOpenerer => 'NC-relais (ER-Invers)';

  @override
  String get detailsConfigurationSwitchesOpenererDescription =>
      'Aan -> ingedrukt houden (uit) -> loslaten (aan)';

  @override
  String get detailsConfigurationSwitchesSwitch => 'Wisselschakelaar';

  @override
  String get detailsConfigurationSwitchesSwitchDescription =>
      'Met elke schakeling wordt het licht in- en uitgeschakeld';

  @override
  String get detailsConfigurationSwitchesImpulsswitch => 'Impuls-relais';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription =>
      'Pulsdrukker wordt kort ingedrukt en losgelaten om het licht aan of uit te doen';

  @override
  String get detailsConfigurationSwitchesClosererDescription2 =>
      'Houd de pulsdrukker ingedrukt. Bij het loslaten stopt de motor';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription2 =>
      'Pulsdrukker wordt kort ingedrukt om de motor te starten en kort ingedrukt om hem weer te stoppen';

  @override
  String get detailsConfigurationWifiloginScan => 'Scan QR-code';

  @override
  String get detailsConfigurationWifiloginScannotvalid =>
      'Gescande code is niet geldig';

  @override
  String get detailsConfigurationWifiloginDescription => 'Code invoeren';

  @override
  String get detailsConfigurationWifiloginPassword => 'Wachtwoord';

  @override
  String get discoveryEsbipDescription => 'Rolluik- en zonweringsactor IP';

  @override
  String get discoveryEsripDescription => 'Impuls-schakelrelais IP';

  @override
  String get discoveryEudipDescription => 'Universele dimmer IP';

  @override
  String get generalTextLoad => 'Laden';

  @override
  String get wifiBasicautomationsNotFound => 'Geen automatisering gevonden.';

  @override
  String get wifiCodeInvalid => 'Ongeldige code';

  @override
  String get wifiCodeValid => 'Geldige code';

  @override
  String get wifiAuthorizationLogin => 'Verbinden';

  @override
  String get wifiAuthorizationLoginFailed => 'Inloggen mislukt';

  @override
  String get wifiAuthorizationSerialnumber => 'Serienummer';

  @override
  String get wifiAuthorizationProductiondate => 'Productiedatum';

  @override
  String get wifiAuthorizationProofofpossession => 'PoP';

  @override
  String get generalTextWifipassword => 'WiFi-wachtwoord';

  @override
  String get generalTextUsername => 'Gebruikersnaam';

  @override
  String get generalTextEnter => 'OF HANDMATIG INVOEREN';

  @override
  String get wifiAuthorizationScan => 'Scan de ELTAKO-code.';

  @override
  String get detailsConfigurationDevicesNofunctionshinttext =>
      'Dit apparaat ondersteunt momenteel geen andere instellingen.␣';

  @override
  String get settingsUsedemodelay => 'Gebruik demo-vertraging';

  @override
  String get settingsImpulsLoad => 'Impuls-schakeltijd wordt geladen';

  @override
  String get settingsBluetoothLoad => 'Bluetooth-instelling wordt geladen.';

  @override
  String get detailsConfigurationsectionLoad => 'Configuraties worden geladen';

  @override
  String get generalTextLogin => 'Aanmelden';

  @override
  String get generalTextAuthentication => 'Authenticeren';

  @override
  String get wifiAuthorizationScanDescription =>
      'Zoek de ELTAKO-code op het WiFi-apparaat of op de meegeleverde datasheet en scan deze met je camera.';

  @override
  String get wifiAuthorizationScanShort => 'ELTAKO-code scannen';

  @override
  String get detailsConfigurationEdgemode => 'Dimcurve';

  @override
  String get detailsConfigurationEdgemodeLeadingedge => 'Faseaansnijding';

  @override
  String get generalTextNetwork => 'Netwerk';

  @override
  String get wifiAuthenticationSuccessful => 'Authenticatie geslaagd';

  @override
  String get detailsConfigurationsectionSavechange => 'Configuratie gewijzigd';

  @override
  String get discoveryWifiAdddevice => 'WiFi-apparaat toevoegen';

  @override
  String get wifiAuthenticationDelay => 'Dit kan tot 1 minuut duren';

  @override
  String get generalTextRetry => 'Opnieuw proberen';

  @override
  String get wifiAuthenticationCredentials =>
      'Voer de inloggegevens van uw WiFi-netwerk in';

  @override
  String get wifiAuthenticationSsid => 'SSID';

  @override
  String get wifiAuthenticationDelaylong =>
      'Het kan tot 1 minuut duren voordat het apparaat gereed is\nen in de app wordt weergegeven';

  @override
  String get wifiAuthenticationCredentialsShort => 'Voer WiFi-gegevens in';

  @override
  String get wifiAuthenticationTeachin => 'Koppel het apparaat met WiFi';

  @override
  String get wifiAuthenticationEstablish => 'Maak verbinding met het apparaat';

  @override
  String wifiAuthenticationEstablishLong(Object ssid) {
    return 'Apparaat maakt verbinding met WiFi $ssid';
  }

  @override
  String get wifiAuthenticationFailed =>
      'Verbinding mislukt. Koppel het apparaat een paar seconden los van de stroom en probeer het opnieuw.';

  @override
  String get wifiAuthenticationReset => 'Authenticatie resetten';

  @override
  String get wifiAuthenticationResetHint =>
      'Alle authenticatiegegevens worden verwijderd.';

  @override
  String get wifiAuthenticationInvaliddata => 'Authenticatiegegevens ongeldig';

  @override
  String get wifiAuthenticationReauthenticate => 'Opnieuw authenticeren';

  @override
  String get wifiAddhkdeviceHeader => 'Apparaat toevoegen';

  @override
  String get wifiAddhkdeviceDescription =>
      'Verbind je nieuwe ELTAKO apparaat met je WiFi-netwerk via de Apple Home app.';

  @override
  String get wifiAddhkdeviceStep1 => 'Open de Apple Home app.';

  @override
  String get wifiAddhkdeviceStep2 =>
      'Klik op het plusje in de rechterbovenhoek van de app en selecteer **Apparaat toevoegen**.';

  @override
  String get wifiAddhkdeviceStep3 => 'Volg de instructies van de app.';

  @override
  String get wifiAddhkdeviceStep4 =>
      '4. Nu kan uw apparaat worden geconfigureerd in de ELTAKO-connect app.';

  @override
  String get detailsConfigurationRuntime => 'Looptijd';

  @override
  String get detailsConfigurationRuntimeMode => 'Modus';

  @override
  String get generalTextManually => 'Handmatig';

  @override
  String get detailsConfigurationRuntimeAutoDescription =>
      'De zonweringsactor bepaalt zelfstandig de looptijd bij elke beweging van de onderste naar de bovenste eindstand (aanbevolen).\nNa de inbedrijfstelling moet de zonwering zonder onderbreking bewegen van beneden naar boven.';

  @override
  String get detailsConfigurationRuntimeManuallyDescription =>
      'De looptijd van de zonweringsmotor wordt handmatig ingesteld met behulp van onderstaande tijdsduur.\nZorg ervoor dat de ingestelde looptijd overeenkomt met de daadwerkelijke looptijd van uw zonweringsmotor.\nNa inbedrijfstelling of wijzigingen moet de zonwering van onder naar boven lopen zonder onderbreking.';

  @override
  String get detailsConfigurationRuntimeDemoDescription =>
      'LCD-display-modus is alleen beschikbaar via REST API.';

  @override
  String get generalTextDemomodeActive => 'Demo-modus actief';

  @override
  String get detailsConfigurationRuntimeDuration => 'Duur';

  @override
  String get detailsConfigurationSwitchesGs4 =>
      'Groepenschakelaar met tip-omkeerfunctie (GS4)';

  @override
  String get detailsConfigurationSwitchesGs4Description =>
      'Groepenschakelaar met tip-omkeerfunctie voor het aansturen van jaloezieën';

  @override
  String get screenshotSu12 => 'Tuinverlichting';

  @override
  String get screenshotS2U12 => 'Tuinverlichting';

  @override
  String get screenshotMfz12 => 'Pomp';

  @override
  String get screenshotEsr62 => 'Lamp';

  @override
  String get screenshotEud62 => 'Plafondlamp';

  @override
  String get screenshotEsb62 => 'Rolluik balkon';

  @override
  String get detailsConfigurationEdgemodeLeadingedgeDescription =>
      'LC1-LC3 zijn comfort-posities met verschillende dimcurves voor dimbare 230 V-LED-lampen, die op grond van hun ontwerp niet ver genoeg kunnen worden gedimd op AUTO en daarom moeten worden gedwongen tot faseaansnijding.';

  @override
  String get detailsConfigurationEdgemodeAutoDescription =>
      'Met AUTO kunnen alle soorten lampen worden gedimd.';

  @override
  String get detailsConfigurationEdgemodeTrailingedge => 'Faseafsnijding';

  @override
  String get detailsConfigurationEdgemodeTrailingedgeDescription =>
      'LC4-LC6 zijn Comfort-posities met verschillende dimcurves voor dimbare 230 V-LED-lampen.';

  @override
  String get updateHeader => 'Firmware update';

  @override
  String get updateTitleStepSearch => 'Er wordt gezocht naar een update';

  @override
  String get updateTitleStepFound => 'Er is een update gevonden';

  @override
  String get updateTitleStepDownload => 'Update wordt gedownload';

  @override
  String get updateTitleStepInstall => 'Update wordt geïnstalleerd';

  @override
  String get updateTitleStepSuccess => 'Update succesvol';

  @override
  String get updateTitleStepUptodate => 'Al bijgewerkt';

  @override
  String get updateTitleStepFailed => 'Update mislukt';

  @override
  String get updateButtonSearch => 'Op zoek naar updates';

  @override
  String get updateButtonInstall => 'Update installeren';

  @override
  String get updateCurrentversion => 'Huidige versie';

  @override
  String get updateNewversion => 'Nieuwe firmware update beschikbaar';

  @override
  String get updateHintPower =>
      'De update start pas als de uitgang van het apparaat niet actief is. Het apparaat mag tijdens de update niet van de stroomvoorziening worden losgekoppeld en de app mag niet worden afgesloten!';

  @override
  String get updateButton => 'Update';

  @override
  String get updateHintCompatibility =>
      'Een update wordt aanbevolen, anders zijn sommige functies in de app slechts beperkt beschikbaar.';

  @override
  String get generalTextDetails => 'Details';

  @override
  String get updateMessageStepMetadata => 'Laden van update informatie';

  @override
  String get updateMessageStepPrepare => 'Update wordt voorbereid';

  @override
  String get updateTitleStepUpdatesuccessful => 'Update wordt gecontroleerd';

  @override
  String get updateTextStepFailed =>
      'Helaas is er iets misgegaan tijdens de update, probeer het over een paar minuten opnieuw of wacht tot uw apparaat automatisch wordt bijgewerkt (internetverbinding vereist).';

  @override
  String get configurationsNotavailable =>
      'Er zijn nog geen configuraties beschikbaar.';

  @override
  String get configurationsAddHint =>
      'Maak nieuwe configuraties aan, door een verbinding te maken met het apparaat en een configuratie op te slaan.';

  @override
  String get configurationsEdit => 'Configuratie bewerken';

  @override
  String get generalTextName => 'Naam';

  @override
  String get configurationsDelete => 'Configuratie verwijderen';

  @override
  String configurationsDeleteHint(Object configName) {
    return 'Moet de configuratie: $configName echt worden verwijderd?';
  }

  @override
  String get configurationsSave => 'Configuratie opslaan';

  @override
  String get configurationsSaveHint =>
      'Hier kunt u de configuratie van uw huidige apparaat opslaan, of een reeds opgeslagen configuratie laden.';

  @override
  String get configurationsImport => 'Configuratie importeren';

  @override
  String configurationsImportHint(Object configName) {
    return 'Moet de configuratie $configName echt worden overgedragen?';
  }

  @override
  String generalTextConfigurations(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'configuraties',
      one: 'configuratie',
    );
    return '$_temp0';
  }

  @override
  String get configurationsStepPrepare => 'Configuratie wordt voorbereid';

  @override
  String get configurationsStepName => 'Voer een naam in voor de configuratie';

  @override
  String get configurationsStepSaving => 'Configuratie wordt opgeslagen';

  @override
  String get configurationsStepSavedsuccessfully =>
      'Configuratie is succesvol opgeslagen';

  @override
  String get configurationsStepSavingfailed =>
      'Opslaan van de configuratie is mislukt';

  @override
  String get configurationsStepChoose => 'Selecteer een configuratie';

  @override
  String get configurationsStepImporting => 'Configuratie wordt geïmporteerd';

  @override
  String get configurationsStepImportedsuccessfully =>
      'De configuratie is met succes geïmporteerd';

  @override
  String get configurationsStepImportingfailed =>
      'Importeren configuratie mislukt';

  @override
  String get discoveryAssuDescription =>
      'Buitentussenstekker tijdschakelklok Bluetooth 230V';

  @override
  String get settingsDatetimeDevicetime => 'Huidige apparaattijd';

  @override
  String get settingsDatetimeLoading => 'Tijdinstellingen worden geladen';

  @override
  String get discoveryEud12Description => 'Universele dimmer Bluetooth';

  @override
  String get generalTextOffdelay => 'Uitschakelvertraging';

  @override
  String get generalTextRemainingbrightness => 'resterende lichtsterkte';

  @override
  String get generalTextSwitchonvalue => 'Inschakelwaarde';

  @override
  String get motionsensorTitleNoremainingbrightness =>
      'Geen resterende lichtsterkte';

  @override
  String get motionsensorTitleAlwaysremainingbrightness =>
      'Met resterende lichtsterkte';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogram =>
      'Resterende lichtsterkte via schakelprogramma';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogramandzea =>
      'Resterende lichtsterkte via CA (centraal aan) en CU (centraal uit)';

  @override
  String get motionsensorTitleNoremainingbrightnessauto =>
      'Geen resterende lichtsterkte (halfautomatisch)';

  @override
  String get generalTextMotionsensor => 'Bewegingsmelder';

  @override
  String get generalTextLightclock => 'Lichtwekker';

  @override
  String get generalTextSnoozeclock => 'Sluimerfunctie';

  @override
  String generalDescriptionLightclock(Object mode) {
    return 'Bij het inschakelen ($mode) wordt het licht na ongeveer 1 seconde op de laagste dimstand ingeschakeld en langzaam omhoog gedimd, zonder het laatst opgeslagen lichtniveau te wijzigen.';
  }

  @override
  String generalDescriptionSnoozeclock(Object mode) {
    return 'Bij het uitschakelen ($mode) wordt de verlichting vanuit de huidige dimstand naar de minimale lichtsterkte gedimd en uitgeschakeld. De verlichting kan op elk moment tijdens het dimproces worden uitgeschakeld door de pulsdrukker kort in te drukken. Als je de pulsdrukker lang indrukt tijdens het dimproces, wordt de verlichting omhoog gedimd en wordt de sluimerfunctie beëindigd.';
  }

  @override
  String get generalTextImmediately => 'Onmiddellijk';

  @override
  String get generalTextPercentage => 'Percentage';

  @override
  String get generalTextSwitchoffprewarning => 'Uitschakel voorwaarschuwing';

  @override
  String get generalDescriptionSwitchoffprewarning =>
      'Langzaam dimmen tot minimale lichtsterkte';

  @override
  String get generalDescriptionOffdelay =>
      'Het apparaat schakelt in als de stuurspanning wordt aangelegd. Als de stuurspanning wordt onderbroken, begint de tijdsverloop, daarna schakelt het apparaat weer uit. Gedurende de tijdsverloop kan het apparaat worden ingeschakeld.\n';

  @override
  String get generalDescriptionBrightness =>
      'De lichtsterkte waarbij de lamp wordt ingeschakeld door de dimmer.';

  @override
  String get generalDescriptionRemainingbrightness =>
      'De dimwaarde in procenten tot waar de lamp naar gedimd wordt nadat de bewegingsmelder is uitgeschakeld.';

  @override
  String get generalDescriptionRuntime =>
      'Looptijd van de lichtwekkerfunctie van minimale lichtsterkte tot maximale lichtsterkte.';

  @override
  String get generalTextUniversalbutton => 'Universele pulsdrukker';

  @override
  String get generalTextDirectionalbutton => 'Richtingspulsdrukker';

  @override
  String get eud12DescriptionAuto =>
      'Automatische detectie UP/RP (met richtingsdiode RTD)';

  @override
  String get eud12DescriptionRt => 'met richtingsgevoelige diode RTD';

  @override
  String get generalTextProgram => 'Programma';

  @override
  String get eud12MotionsensorOff => 'Met bewegingsmelder ingesteld op uit';

  @override
  String get eud12ClockmodeTitleProgramze => 'Programma en centraal aan';

  @override
  String get eud12ClockmodeTitleProgramza => 'Programma en centraal uit';

  @override
  String get eud12ClockmodeTitleProgrambuttonon => 'Programma en UP/RP aan';

  @override
  String get eud12ClockmodeTitleProgrambuttonoff => 'Programma en UP/RP uit';

  @override
  String get eud12TiImpulseTitle => 'Impulstijd aan (t1)';

  @override
  String get eud12TiImpulseHeader => 'Dimwaarde impulstijd aan';

  @override
  String get eud12TiImpulseDescription =>
      'De dimwaarde in percentage, waarnaar de lamp gedimd wordt als de impulstijd AAN is.';

  @override
  String get eud12TiOffTitle => 'Impulstijd uit (t2)';

  @override
  String get eud12TiOffHeader => 'Dimwaarde Impulstijd uit';

  @override
  String get eud12TiOffDescription =>
      'De dimwaarde in percentage waarnaar de lamp wordt gedimd als de impulstijd UIT is.';

  @override
  String get generalTextButtonpermanentlight => 'Permanent lichtpulsdrukker';

  @override
  String get generalDescriptionButtonpermanentlight =>
      'Instelling van het continu lichtpulsdrukker van 0 tot 10 uur in stappen van 0,5 uur. Activering door de pulksdrukker langer dan 1 seconde in te drukken (1x knipperen), deactivering door de pulsdrukker langer dan 2 seconden in te drukken.';

  @override
  String get generalTextNobuttonpermanentlight => 'Geen PLP';

  @override
  String get generalTextBasicsettings => 'Basisinstellingen';

  @override
  String get generalTextInputswitch => 'Ingang lokale pulsdrukker (A1)';

  @override
  String get generalTextOperationmode => 'Bedrijfsmodus';

  @override
  String get generalTextDimvalue => 'Inschakel gedrag';

  @override
  String get eud12TitleUsememory => 'Geheugenwaarde gebruiken';

  @override
  String get eud12DescriptionUsememory =>
      'De geheugenwaarde komt overeen met de laatst ingestelde dimwaarde. Als de geheugenfunctie is gedeactiveerd, wordt de dimmer altijd ingeschakeld op de inschakelwaarde.';

  @override
  String get generalTextStartup => 'Lichtsterkte inschakelen';

  @override
  String get generalDescriptionSwitchonvalue =>
      'De inschakelwaarde is een instelbare lichtsterkte waarde die een veilige inschakeling garandeert.';

  @override
  String get generalTitleSwitchontime => 'Inschakeltijd';

  @override
  String get generalDescriptionSwitchontime =>
      'Nadat de inschakeltijd is verstreken, wordt de lamp van de inschakelwaarde gedimd naar de geheugenwaarde.';

  @override
  String get generalDescriptionStartup =>
      'Sommige LED lampen hebben een hogere inschakelstroom nodig om betrouwbaar in te schakelen. De lamp wordt bij deze inschakelwaarde ingeschakeld en na de inschakeltijd gedimd naar de geheugenwaarde.';

  @override
  String get eud12ClockmodeSubtitleProgramze => 'Korte klik op centraal aan';

  @override
  String get eud12ClockmodeSubtitleProgramza => 'Korte klik op centraal uit';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonon =>
      'Dubbelklik op universele pulsdrukker/richtingspulsdrukker aan';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonoff =>
      'Dubbelklik op universele pulsdrukker/richtingspulsdrukker uit';

  @override
  String get eud12FunctionStairlighttimeswitchTitleShort =>
      'TLZ | Trappenhuisverlichting timer';

  @override
  String get eud12FunctionMinTitleShort => 'MIN';

  @override
  String get eud12FunctionMmxTitleShort => 'MMX';

  @override
  String get eud12FunctionTiDescription =>
      'Timer met instelbare in- en uitschakeltijd van 0,5 seconden tot 9,9 minuten. De lichtsterkte kan worden ingesteld van minimale licht- tot maximale lichtsterkte.';

  @override
  String get eud12FunctionAutoDescription =>
      'Universele dimmer met instelling voor bewegingsmelder, lichtwekker en sluimerfunctie';

  @override
  String get eud12FunctionErDescription =>
      'Schakelrelais, de lichtsterkte kan worden ingesteld van minimale licht- tot maximale lichtsterkte.';

  @override
  String get eud12FunctionEsvDescription =>
      'Universele dimmer met instelling van een uitschakelvertraging van 1 tot 120 minuten. Uitschakelvoorwaarschuwing op het einde van omlaag dimmen instelbaar van 1 tot 3 minuten. Beide centrale ingangen actief.';

  @override
  String get eud12FunctionTlzDescription =>
      'Instellen van de verlichtingsduur van 0 tot 10 uur in stappen van 0,5 uur. Activering door de pulsdrukker  langer dan 1 seconde in te drukken (1x knipperen), deactivering door de knop langer dan 2 seconden in te drukken.';

  @override
  String get eud12FunctionMinDescription =>
      'Universele dimmer, dimt naar de ingestelde minimale lichtsterkte wanneer de stuurspanning wordt aangesloten. Het licht wordt omhoog gedimd tot de maximale lichtsterkte binnen de ingestelde dimtijd van 1 tot 120 minuten. Als de stuurspanning wordt verwijderd, wordt het licht onmiddellijk uitgeschakeld, zelfs tijdens de dimtijd. Beide centrale ingangen actief.';

  @override
  String get eud12FunctionMmxDescription =>
      'Universele dimmer, dimt naar de ingestelde minimale lichtsterkte wanneer de stuurspanning wordt aangesloten. Tijdens de ingestelde dimtijd van 1 tot 120 minuten wordt het licht gedimd tot de maximale lichtsterkte. Wanneer de stuurspanning echter wordt verwijderd, dimt de dimmer tot de ingestelde minimale lichtsterkte. Daarna wordt uitgeschakeld. Beide centrale ingangen actief.';

  @override
  String get motionsensorSubtitleNoremainingbrightness =>
      'Met bewegingsmelder ingesteld op uit';

  @override
  String get motionsensorSubtitleAlwaysremainingbrightness =>
      'Met bewegingsmelder ingesteld op uit';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogram =>
      'Schakelprogramma geactiveerd en gedeactiveerd met BWM-uit';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogramandzea =>
      'Centraal aan activeert de bewegingsmelder, centraal uit deactiveert de bewegingsmelder, zoals ook met het schakelprogramma';

  @override
  String get motionsensorSubtitleNoremainingbrightnessauto =>
      'Bewegingsmelder schakelt alleen uit';

  @override
  String get detailsDimsectionHeader => 'Dimmen';

  @override
  String get generalTextFast => 'Snel';

  @override
  String get generalTextSlow => 'Langzaam';

  @override
  String get eud12TextDimspeed => 'Dimsnelheid';

  @override
  String get eud12TextSwitchonspeed => 'Inschakelsnelheid';

  @override
  String get eud12TextSwitchoffspeed => 'Uitschakelsnelheid';

  @override
  String get eud12DescriptionDimspeed =>
      'De dimsnelheid is de snelheid waarmee de dimmer dimt van de huidige licht- naar de gewenste lichtsterkte.';

  @override
  String get eud12DescriptionSwitchonspeed =>
      'De inschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig in te schakelen.';

  @override
  String get eud12DescriptionSwitchoffspeed =>
      'De uitschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig uit te schakelen.';

  @override
  String get settingsFactoryresetResetdimHeader => 'Diminstellingen resetten';

  @override
  String get settingsFactoryresetResetdimDescription =>
      'Moeten alle diminstellingen echt worden gereset?';

  @override
  String get settingsFactoryresetResetdimConfirmationDescription =>
      'De diminstellingen zijn succesvol gereset';

  @override
  String get eud12TextSwitchonoffspeed => 'Aan/uit-snelheid';

  @override
  String get eud12DescriptionSwitchonoffspeed =>
      'De in-/uitschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig in of uit te schakelen.';

  @override
  String get timerDetailsDimtoval => 'Aan met dimwaarde in %';

  @override
  String get timerDetailsDimtovalDescription =>
      'De dimmer schakelt altijd in met de vaste dimwaarde in %.';

  @override
  String timerDetailsDimtovalSubtitle(Object brightness) {
    return 'Inschakelen met $brightness%';
  }

  @override
  String get timerDetailsDimtomem => 'Aan met geheugenwaarde';

  @override
  String get timerDetailsDimtomemSubtitle => 'Inschakelen met geheugenwaarde';

  @override
  String get timerDetailsMotionsensorwithremainingbrightness =>
      'Resterende lichtsterkte (BWM) aan';

  @override
  String get timerDetailsMotionsensornoremainingbrightness =>
      'Resterende lichtsterkte (BWM) uit';

  @override
  String get settingsRandommodeHint =>
      'Als de willekeurige modus is ingeschakeld, worden alle schakeltijden van het kanaal willekeurig verschoven. Met inschakeltijden tot 15 minuten eerder en uitschakeltijden tot 15 minuten later.';

  @override
  String get runtimeOffsetDescription =>
      'Extra naloop na het verstrijken van de looptijd. Hiermee kan ervoor worden gezorgd dat de eindpositie wordt bereikt.';

  @override
  String get loadingTextDimvalue => 'Dimwaarde is geladen';

  @override
  String get discoveryEudipmDescription => 'Universele dimmer IP Matter';

  @override
  String get generalTextOffset => 'Naloop';

  @override
  String get eud12DimvalueTestText => 'Lichtsterkte verzenden';

  @override
  String get eud12DimvalueTestDescription =>
      'Tijdens het testen wordt er rekening gehouden met de huidige ingestelde dimsnelheid.';

  @override
  String get eud12DimvalueLoadText => 'Lichtsterkte laden';

  @override
  String get settingsDatetimeNotime =>
      'De datum- en tijdinstellingen moeten worden uitgelezen via het display van het apparaat.';

  @override
  String get generalMatterText => 'Matter';

  @override
  String get generalMatterMessage =>
      'Leer uw Matter-apparaat in met een van de volgende apps.';

  @override
  String get generalMatterOpengooglehome => 'Google Home openen';

  @override
  String get generalMatterOpenamazonalexa => 'Open Amazon Alexa';

  @override
  String get generalMatterOpensmartthings => 'Open SmartThings';

  @override
  String generalLabelProgram(Object number) {
    return 'Programma $number';
  }

  @override
  String get generalTextDone => 'Gereed';

  @override
  String get settingsRandommodeDescriptionShort =>
      'Als de willekeurige modus is ingeschakeld, worden alle schakeltijden van het kanaal willekeurig verschoven. Met inschakeltijden tot 15 minuten eerder en uitschakeltijden tot 15 minuten later.';

  @override
  String get all => 'Alle';

  @override
  String get discoveryBluetooth => 'Bluetooth';

  @override
  String get success => 'Succesvol';

  @override
  String get error => 'Fout';

  @override
  String get timeProgramAdd => 'Tijdprogramma toevoegen';

  @override
  String get noConnection => 'Geen verbinding';

  @override
  String get timeProgramOnlyActive => 'Geconfigureerde programma\'s';

  @override
  String get timeProgramAll => 'Alle programma\'s';

  @override
  String get active => 'Actief';

  @override
  String get inactive => 'Inactief';

  @override
  String timeProgramSaved(Object number) {
    return 'Programma $number opgeslagen';
  }

  @override
  String get deviceLanguageSaved => 'Apparaattaal opgeslagen';

  @override
  String generalTextTimeShort(Object time) {
    return '$time klok';
  }

  @override
  String programDeleteHint(Object index) {
    return 'Moet programma $index echt worden verwijderd?';
  }

  @override
  String milliseconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'milliseconden',
      one: 'milliseconde',
    );
    return '$_temp0';
  }

  @override
  String millisecondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count milliseconden',
      one: '$count milliseconde',
    );
    return '$_temp0';
  }

  @override
  String secondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count seconden',
      one: '$count seconde',
    );
    return '$_temp0';
  }

  @override
  String minutesWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count minuten',
      one: '$count minuut',
    );
    return '$_temp0';
  }

  @override
  String hoursWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count uren',
      one: '$count uur',
    );
    return '$_temp0';
  }

  @override
  String get settingsPinFailEmpty => 'De pincode mag niet leeg zijn.';

  @override
  String get detailsConfigurationWifiloginScanNoMatch =>
      'De gescande code komt niet overeen met het apparaat';

  @override
  String get wifiAuthorizationPopIsEmpty => 'PoP mag niet leeg zijn';

  @override
  String get wifiAuthenticationCredentialsHint =>
      'Aangezien de app geen toegang heeft tot je privé Wi-Fi-wachtwoord, is het niet mogelijk om de juistheid van de invoer te controleren. Als er geen verbinding tot stand komt, controleer dan het wachtwoord en voer het opnieuw in.';

  @override
  String get generalMatterOpenApplehome => 'Open Apple Home';

  @override
  String get timeProgramNoActive => 'Geen geconfigureerde programma\'s';

  @override
  String get timeProgramNoEmpty => 'Geen lege tijdsprogramma beschikbaar';

  @override
  String get nameOfConfiguration => 'Naam van de configuratie';

  @override
  String get currentDevice => 'Huidig apparaat';

  @override
  String get export => 'Exporteren';

  @override
  String get import => 'Importeren';

  @override
  String get savedConfigurations => 'Opgeslagen configuraties';

  @override
  String get importableServicesLabel =>
      'De volgende instellingen kunnen worden geïmporteerd:';

  @override
  String get notImportableServicesLabel => 'Incompatibele instellingen:';

  @override
  String get deviceCategoryMeterGateway => 'Meter-Gateway';

  @override
  String get deviceCategory2ChannelTimeSwitch =>
      '2-kanaals schakelklok Bluetooth';

  @override
  String get devicategoryOutdoorTimeSwitchBluetooth =>
      'Buiten tussenstekker-tijdschakelaar Bluetooth';

  @override
  String get settingsModbusHeader => 'Modbus';

  @override
  String get settingsModbusDescription =>
      'Pas de baudrate, pariteit en time-out aan om de transmissiesnelheid, foutdetectie en wachttijd te configureren.';

  @override
  String get settingsModbusRTU => 'Modbus RTU';

  @override
  String get settingsModbusBaudrate => 'Baudrate';

  @override
  String get settingsModbusParity => 'Pariteit';

  @override
  String get settingsModbusTimeout => 'Modbus time-out';

  @override
  String get locationServiceDisabled => 'Locatie is uitgeschakeld';

  @override
  String get locationPermissionDenied =>
      'Geef locatietoestemming om uw huidige positie op te halen.';

  @override
  String get locationPermissionDeniedPermanently =>
      'Locatietoestemmingen zijn permanent geweigerd, sta de locatietoestemming toe in de instellingen van je apparaat om je huidige positie op te vragen.';

  @override
  String get lastSync => 'Laatste synchronisatie';

  @override
  String get dhcpActive => 'DHCP actief';

  @override
  String get ipAddress => 'IP';

  @override
  String get subnetMask => 'Subnetmasker';

  @override
  String get standardGateway => 'Standaard gateway';

  @override
  String get dns => 'DNS';

  @override
  String get alternateDNS => 'Alternatief DNS';

  @override
  String get errorNoNetworksFound => 'Geen WiFi-netwerk gevonden';

  @override
  String get availableNetworks => 'Beschikbare netwerken';

  @override
  String get enableWifiInterface => 'WiFi-interface inschakelen';

  @override
  String get enableLANInterface => 'LAN-interface inschakelen';

  @override
  String get hintDontDisableAllInterfaces =>
      'Zorg ervoor dat niet alle interfaces uitgeschakeld zijn. De laatst geactiveerde interface heeft prioriteit.';

  @override
  String get ssid => 'SSID';

  @override
  String get searchNetworks => 'WiFi-Netwerken zoeken';

  @override
  String get errorNoNetworkEnabled =>
      'Er moet minstens één interface actief zijn';

  @override
  String get errorActiveNetworkInvalid =>
      'Niet alle actieve interfaces zijn geldig';

  @override
  String get invalidNetworkConfiguration => 'Ongeldige netwerkconfiguratie';

  @override
  String get generalDefault => 'Standaard';

  @override
  String get mqttHeader => 'MQTT';

  @override
  String get mqttConnected => 'Verbonden met MQTT-broker';

  @override
  String get mqttDisconnected => 'Geen verbinding met MQTT-broker';

  @override
  String get mqttBrokerURI => 'Broker URI';

  @override
  String get mqttBrokerURIHint => 'MQTT-broker URI';

  @override
  String get mqttPort => 'Poort';

  @override
  String get mqttPortHint => 'MQTT-poort';

  @override
  String get mqttClientId => 'Client-ID';

  @override
  String get mqttClientIdHint => 'MQTT client-ID';

  @override
  String get mqttUsername => 'Gebruikersnaam';

  @override
  String get mqttUsernameHint => 'MQTT-gebruikersnaam';

  @override
  String get mqttPassword => 'Wachtwoord';

  @override
  String get mqttPasswordHint => 'MQTT-wachtwoord';

  @override
  String get mqttCertificate => 'Certificaat (optioneel)';

  @override
  String get mqttCertificateHint => 'MQTT-certificaat';

  @override
  String get mqttTopic => 'Topic';

  @override
  String get mqttTopicHint => 'MQTT-topic';

  @override
  String get electricityMeter => 'Elektriciteitsmeter';

  @override
  String get electricityMeterCurrent => 'Actueel';

  @override
  String get electricityMeterHistory => 'Geschiedenis';

  @override
  String get electricityMeterReading => 'Meterstand';

  @override
  String get connectivity => 'Connectiviteit';

  @override
  String electricMeter(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Elektriciteitsmeters',
      one: 'Elektriciteitsmeter',
    );
    return '$_temp0';
  }

  @override
  String get discoveryZGW16Description =>
      'Modbus-elektriciteitsmeter-MQTT-gateway';

  @override
  String get bluetoothConnectionLost => 'Bluetooth verbinding verloren';

  @override
  String get bluetoothConnectionLostDescription =>
      'De Bluetooth verbinding met het apparaat is onderbroken. Zorg ervoor dat het apparaat binnen bereik is.';

  @override
  String get openBluetoothSettings => 'Open instellingen';

  @override
  String get password => 'Wachtwoord';

  @override
  String get setInitialPassword => 'Initieel wachtwoord gegeven';

  @override
  String initialPasswordMinimumLength(Object length) {
    return 'Het wachtwoord moet minimaal $length tekens lang zijn.';
  }

  @override
  String get repeatPassword => 'Herhaal wachtwoord';

  @override
  String get passwordsDoNotMatch => 'Wachtwoorden komen niet overeen';

  @override
  String get savePassword => 'Wachtwoord opslaan';

  @override
  String get savePasswordHint =>
      'Het wachtwoord wordt opgeslagen voor toekomstige verbindingen op uw apparaat.';

  @override
  String get retrieveNtpServer => 'Tijd ophalen van een NTP-server';

  @override
  String get retrieveNtpServerFailed =>
      'De verbinding met de NTP-server kon niet tot stand worden gebracht.';

  @override
  String get retrieveNtpServerSuccess =>
      'De verbinding met de NTP-server was succesvol.';

  @override
  String get settingsPasswordNewPasswordDescription =>
      'Nieuw wachtwoord invoeren';

  @override
  String get settingsPasswordConfirmationDescription =>
      'Wachtwoord succesvol gewijzigd';

  @override
  String get dhcpRangeStart => 'Start DHCP-bereik';

  @override
  String get dhcpRangeEnd => 'Einde DHCP-bereik';

  @override
  String get forwardOnMQTT => 'Doorsturen naar MQTT';

  @override
  String get showAll => 'Alles weergeven';

  @override
  String get hide => 'Verberg';

  @override
  String get changeToAPMode => 'Overschakelen naar AP-modus';

  @override
  String get changeToAPModeDescription =>
      'U staat op het punt uw apparaat te verbinden met een WiFi-netwerk. In dit geval wordt de verbinding met het apparaat verbroken en moet u opnieuw verbinding maken met uw apparaat via het geconfigureerde netwerk.';

  @override
  String get consumption => 'Verbruik';

  @override
  String get currentDay => 'Huidige dag';

  @override
  String get twoWeeks => '2 weken';

  @override
  String get oneYear => '1 jaar';

  @override
  String get threeYears => '3 jaren';

  @override
  String passwordMinLength(Object length) {
    return 'Het wachtwoord moet minimaal $length tekens lang zijn.';
  }

  @override
  String get passwordNeedsLetter =>
      'Het wachtwoord moet minimaal één letter bevatten.';

  @override
  String get passwordNeedsNumber =>
      'Het wachtwoord moet minimaal één cijfer bevatten.';

  @override
  String get portEmpty => 'Poort mag niet leeg zijn';

  @override
  String get portInvalid => 'Ongeldige poort';

  @override
  String portOutOfRange(Object rangeEnd, Object rangeStart) {
    return 'De poort moet tussen $rangeStart en $rangeEnd liggen.';
  }

  @override
  String get ipAddressEmpty => 'IP-adres mag niet leeg zijn';

  @override
  String get ipAddressInvalid => 'Ongeldig IP-adres';

  @override
  String get subnetMaskEmpty => 'Subnetmasker mag niet leeg zijn';

  @override
  String get subnetMaskInvalid => 'Ongeldig subnetmasker';

  @override
  String get gatewayEmpty => 'Gateway mag niet leeg zijn';

  @override
  String get gatewayInvalid => 'Ongeldige gateway';

  @override
  String get dnsEmpty => 'DNS mag niet leeg zijn';

  @override
  String get dnsInvalid => 'Ongeldig DNS';

  @override
  String get uriEmpty => 'URI mag niet leeg zijn';

  @override
  String get uriInvalid => 'Ongeldige URI';

  @override
  String get electricityMeterChangedSuccessfully =>
      'De elektriciteitsmeter is met succes gewijzigd.';

  @override
  String get networkChangedSuccessfully =>
      'De netwerkconfiguratie is succesvol gewijzigd.';

  @override
  String get mqttChangedSuccessfully =>
      'De MQTT-configuratie is succesvol gewijzigd.';

  @override
  String get modbusChangedSuccessfully =>
      'Modbus-instellingen zijn succesvol gewijzigd.';

  @override
  String get loginData => 'Inloggegevens verwijderen';

  @override
  String get valueConfigured => 'Geconfigureerd';

  @override
  String get electricityMeterHistoryNoData => 'Geen gegevens beschikbaar';

  @override
  String get locationChangedSuccessfully => 'Locatie is succesvol gewijzigd.';

  @override
  String get settingsNameFailEmpty => 'Naam mag niet leeg zijn.';

  @override
  String settingsNameFailLength(Object length) {
    return 'De naam mag niet langer zijn dan $length tekens.';
  }

  @override
  String get solsticeChangedSuccesfully =>
      'De tijdsverschuiving voor de zonnewende is met succes gewijzigd.';

  @override
  String get relayFunctionChangedSuccesfully =>
      'De relaisfunctie is succesvol gewijzigd.';

  @override
  String get relayFunctionHeader => 'Relaisfunctie';

  @override
  String get dimmerValueChangedSuccesfully =>
      'Het inschakelgedrag is succesvol gewijzigd.';

  @override
  String get dimmerBehaviourChangedSuccesfully =>
      'Het dimgedrag is succesvol gewijzigd.';

  @override
  String get dimmerBrightnessDescription =>
      'De minimale en maximale lichtsterkte zijn van invloed op alle instelbare helderheidsniveaus van de dimmer.';

  @override
  String get dimmerSettingsChangedSuccesfully =>
      'De basisinstellingen zijn succesvol gewijzigd.';

  @override
  String get liveUpdateEnabled => 'Live test geactiveerd';

  @override
  String get liveUpdateDisabled => 'Live test gedeactiveerd';

  @override
  String get liveUpdateDescription =>
      'De laatst gewijzigde slider-waarde wordt naar het apparaat verzonden.';

  @override
  String get demoDevices => 'Demo apparaten';

  @override
  String get showDemoDevices => 'Demo apparaten weergeven';

  @override
  String get deviceCategoryTimeSwitch => 'Tijdschakelklok';

  @override
  String get deviceCategoryMultifunctionalRelay =>
      'Multifunctioneel-tijdrelais';

  @override
  String get deviceCategoryDimmer => 'Dimmer';

  @override
  String get deviceCategoryShutter => 'Rolluik- en zonweringactor';

  @override
  String get deviceCategoryRelay => 'Relais';

  @override
  String get search => 'Zoeken';

  @override
  String get configurationsHeader => 'Configuraties';

  @override
  String get configurationsDescription =>
      'Hier kunt u uw opgeslagen configuraties beheren.';

  @override
  String get configurationsNameFailEmpty =>
      'Configuratienaam mag niet leeg zijn.';

  @override
  String get configurationDeleted => 'Configuratie verwijderd';

  @override
  String codeFound(Object codeType) {
    return '$codeType Code herkent';
  }

  @override
  String get errorCameraPermission =>
      'Geef toegang tot de camera om de ELTAKO-code te scannen.';

  @override
  String get authorizationSuccessful =>
      'Succesvol geautoriseerd op het apparaat';

  @override
  String get wifiAuthenticationResetConfirmationDescription =>
      'Authenticatie is succesvol gereset.';

  @override
  String get settingsResetConnectionHeader => 'Verbinding resetten';

  @override
  String get settingsResetConnectionDescription =>
      'Moet de verbinding echt worden gereset?';

  @override
  String get settingsResetConnectionConfirmationDescription =>
      'De verbinding is succesvol gereset.';

  @override
  String get wiredInputChangedSuccesfully =>
      'Het pulsdrukker gedrag is succesvol gewijzigd.';

  @override
  String get runtimeChangedSuccesfully => 'Looptijd is succesvol gewijzigd.';

  @override
  String get expertModeActivated => 'Expert-modus geactiveerd';

  @override
  String get expertModeDeactivated => 'Expert-modus gedeactiveerd';

  @override
  String get license => 'Licentie';

  @override
  String get retry => 'Opnieuw proberen';

  @override
  String get provisioningConnectingHint =>
      'Apparaatverbinding wordt tot stand gebracht. Dit kan tot 1 minuut duren.';

  @override
  String get serialnumberEmpty => 'Serienummer mag niet leeg zijn';

  @override
  String get interfaceStateInactiveDescriptionBLE =>
      'Bluetooth is uitgeschakeld, schakel dit in om Bluetooth-apparaten te vinden.';

  @override
  String get interfaceStateDeniedDescriptionBLE =>
      'Bluetooth-machtigingen zijn niet verleend.';

  @override
  String get interfaceStatePermanentDeniedDescriptionBLE =>
      'Bluetooth-machtigingen zijn niet verleend. Sta Bluetooth-verbindingen toe in uw apparaatinstellingen.';

  @override
  String get requestPermission => 'Toestemming vragen';

  @override
  String get goToSettings => 'Ga naar instellingen';

  @override
  String get enableBluetooth => 'Bluetooth activeren';

  @override
  String get installed => 'Geïnstalleerd';

  @override
  String teachInDialogDescription(Object type) {
    return 'Wil je een apparaat via $type inleren?';
  }

  @override
  String get useMatter => 'Matter gebruiken';

  @override
  String get relayMode => 'Relaismodus activeren';

  @override
  String get whatsNew => 'Nieuw in deze versie';

  @override
  String get migrationHint =>
      'Om ervoor te zorgen dat u de nieuwe functies kunt gebruiken, moeten we uw gegevens migreren.';

  @override
  String get migrationHeader => 'Migratie';

  @override
  String get migrationProgress => 'Wij ruimen op...';

  @override
  String get letsGo => 'Laten we beginnen';

  @override
  String get noDevicesFound =>
      'Geen apparaten gevonden. Controleer of uw apparaat in de verbindingsmodus staat.';

  @override
  String get interfaceStateEmpty => 'Geen apparaten gevonden';

  @override
  String get ssidEmpty => 'SSID mag niet leeg zijn';

  @override
  String get passwordEmpty => 'Wachtwoord mag niet leeg zijn';

  @override
  String get settingsDeleteSettingsHeader => 'Instellingen resetten';

  @override
  String get settingsDeleteSettingsDescription =>
      'Wil je echt alle instellingen resetten?';

  @override
  String get settingsDeleteSettingsConfirmationDescription =>
      'Alle instellingen zijn succesvol gereset.';

  @override
  String get locationNotFound => 'Locatie niet gevonden';

  @override
  String get timerProgramEmptySaveHint =>
      'Het tijdprogramma is leeg en kan niet worden opgeslagen. Bewerken beeindigen?';

  @override
  String get timerProgramDaysEmptySaveHint =>
      'Geen dagen geselecteerd. Het tijdprogramma toch opslaan?';

  @override
  String get timeProgramNoDays =>
      'Een programma zonder actieve dagen kan niet worden geactiveerd.';

  @override
  String timeProgramColliding(Object program) {
    return 'Tijdprogramma botst met programma $program.';
  }

  @override
  String timeProgramDuplicated(Object program) {
    return 'Het tijdprogramma is een duplicaat van programma $program.';
  }

  @override
  String get screenshotZgw16 => 'Vrijstaand huis';

  @override
  String get interfaceStateUnknown => 'Geen apparaten gevonden';

  @override
  String get settingsPinChange => 'PIN veranderen';

  @override
  String get timeProgrammOneTime => 'Eenmalig';

  @override
  String get timeProgrammRepeating => 'Herhalend';

  @override
  String get generalIgnore => 'Negeren';

  @override
  String get timeProgramChooseDay => 'Dag kiezen';

  @override
  String get generalToday => 'Vandaag';

  @override
  String get generalTomorrow => 'Morgen';

  @override
  String get bluetoothAndPINChangedSuccessfully =>
      'Bluetooth en pincode zijn succesvol gewijzigd.';

  @override
  String get generalTextDimTime => 'Dimtijd';

  @override
  String get discoverySu62Description => '1-kanaals tijdschakelklok Bluetooth';

  @override
  String get bluetoothAlwaysOnTitle => 'Permanent aan';

  @override
  String get bluetoothAlwaysOnDescription =>
      'Bluetooth is permanent ingeschakeld.';

  @override
  String get bluetoothAlwaysOnHint =>
      'Opmerking: Als deze instelling geactiveerd is, is het apparaat permanent zichtbaar voor iedereen via Bluetooth! Het wordt aanbevolen om de standaardpincode te wijzigen.';

  @override
  String get bluetoothManualStartupOnTitle => 'Tijdelijk-aan';

  @override
  String get bluetoothManualStartupOnDescription =>
      'Nadat de voeding is ingeschakeld, wordt Bluetooth gedurende 3 minuten geactiveerd.';

  @override
  String get bluetoothManualStartupOnHint =>
      'Opmerking: Pairing-modus wordt gedurende 3 minuten geactiveerd en schakelt daarna uit. Als u een nieuwe verbinding tot stand wilt brengen, moet u de knop ongeveer 5 seconden ingedrukt houden.';

  @override
  String get bluetoothManualStartupOffTitle => 'Handmatig aan';

  @override
  String get bluetoothManualStartupOffDescription =>
      'Bluetooth wordt handmatig geactiveerd via de pulsdrukkeringang en is dan voor 3 minuten actief.';

  @override
  String get bluetoothManualStartupOffHint =>
      'Opmerking: Om Bluetooth te activeren, moet de pulsdrukker op de pulsdrukkeringang ongeveer 5 seconden bediend worden.';

  @override
  String get timeProgrammOneTimeRepeatingDescription =>
      'Programma\'s kunnen herhaaldelijk worden uitgevoerd door altijd op de geconfigureerde dagen en tijden een schakelproces uit te voeren, of ze kunnen slechts één keer op het geconfigureerde schakeltijdstip worden uitgevoerd.';

  @override
  String versionHeader(Object version) {
    return 'Versie $version';
  }

  @override
  String get releaseNotesHeader => 'Uitgave notities';

  @override
  String get release30Header => 'De nieuwe Eltako Connect app is er!';

  @override
  String get release30FeatureDesignHeader => 'Nieuw ontwerp';

  @override
  String get release30FeatureDesignDescription =>
      'De app is volledig herzien en heeft een nieuw ontwerp. Hij is nu nog eenvoudiger en intuïtiever te gebruiken.';

  @override
  String get release30FeaturePerformanceHeader => 'Verbeterde prestaties';

  @override
  String get release30FeaturePerformanceDescription =>
      'Geniet van een soepelere ervaring en kortere laadtijden - voor een soepele gebruikerservaring.';

  @override
  String get release30FeatureConfigurationHeader =>
      'Configuraties voor meerdere apparaten';

  @override
  String get release30FeatureConfigurationDescription =>
      'Apparaatconfiguraties opslaan en overdragen naar andere apparaten. Zelfs als ze niet dezelfde hardware hebben, kun je bijvoorbeeld de configuratie van een S2U12DBT1+1-UC overbrengen naar een ASSU-BT of omgekeerd.';

  @override
  String get release31Header =>
      'De nieuwe 1-kanaals inbouwklok met Bluetooth is er!';

  @override
  String get release31Description => 'Wat kan de SU62PF-BT/UC?';

  @override
  String get release31SU62Name => 'SU62PF-BT/UC';

  @override
  String get release31DeviceNote1 => 'Tot 60 schakelprogramma\'s.';

  @override
  String get release31DeviceNote2 =>
      'De klok schakelt op vaste tijden of via de astrofunctie apparaten op basis van zonsopgang en zonsondergang.';

  @override
  String get release31DeviceNote3 =>
      'Willekeurige modus: schakeltijden kunnen willekeurig tot 15 minuten worden verschoven.';

  @override
  String get release31DeviceNote4 =>
      'Omschakeling zomer-/wintertijd: De klok schakelt automatisch naar zomer- of wintertijd.';

  @override
  String get release31DeviceNote5 =>
      'Universele voedings- en stuurspanning 12-230V UC.';

  @override
  String get release31DeviceNote6 =>
      'Pulsdrukkeringang voor handmatig schakelen.';

  @override
  String get release31DeviceNote7 =>
      '1 NO-contact potentiaalvrij 10 A/250 V AC.';

  @override
  String get release31DeviceNote8 =>
      'Eenmalige uitvoering van tijdprogramma\'s.';

  @override
  String get generalNew => 'Nieuw';

  @override
  String yearsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count jaren geleden',
      one: 'Laatste jaar',
    );
    return '$_temp0';
  }

  @override
  String monthsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count maanden geleden',
      one: 'laatste maand',
    );
    return '$_temp0';
  }

  @override
  String weeksAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count weken geleden',
      one: 'Laatste week',
    );
    return '$_temp0';
  }

  @override
  String daysAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count dagen geleden',
      one: 'gisteren',
    );
    return '$_temp0';
  }

  @override
  String minutesAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count minuten geleden',
      one: 'een minuut geleden',
    );
    return '$_temp0';
  }

  @override
  String hoursAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count uur geleden',
      one: 'een uur geleden',
    );
    return '$_temp0';
  }

  @override
  String secondsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count seconden geleden',
      one: 'een seconde geleden',
    );
    return '$_temp0';
  }

  @override
  String get justNow => 'Zojuist';

  @override
  String get discoveryEsripmDescription => 'Impuls-schakelrelais IP Matter';

  @override
  String get generalTextKidsRoom => 'Kinderkamerfunctie';

  @override
  String generalDescriptionKidsRoom(Object mode) {
    return 'Bij het inschakelen ($mode) wordt de verlichting na ongeveer 1 seconde op het laagste helderheidsniveau ingeschakeld en langzaam hoog gedimd zolang de pulsdrukker ingedrukt wordt gehouden, zonder het laatst opgeslagen helderheidsniveau te wijzigen.';
  }

  @override
  String get generalTextSceneButton => 'Scénepulsdrukker';

  @override
  String get settingsEnOceanConfigHeader => 'EnOcean configuratie';

  @override
  String get enOceanConfigChangedSuccessfully =>
      'EnOcean configuratie met succes gewijzigd';

  @override
  String get activateEnOceanRepeater => 'EnOcean repeater activeren';

  @override
  String get enOceanRepeaterLevel => 'Repeater level';

  @override
  String get enOceanRepeaterLevel1 => 'Level-1';

  @override
  String get enOceanRepeaterLevel2 => 'Level-2';

  @override
  String get enOceanRepeaterOffDescription =>
      'Er worden geen draadloze signalen ontvangen van sensoren.';

  @override
  String get enOceanRepeaterLevel1Description =>
      'Alleen de draadloze signalen van sensoren worden ontvangen, gecontroleerd en doorgestuurd op vol zendvermogen. Draadloze signalen van andere repeaters worden genegeerd om de hoeveelheid gegevens te beperken.';

  @override
  String get enOceanRepeaterLevel2Description =>
      'Naast de draadloze signalen van sensoren worden ook de draadloze signalen van level-1 repeaters verwerkt. Een draadloos signaal kan dus maximaal twee keer ontvangen en versterkt worden. Draadloze repeaters hoeven niet ingeleerd te worden. Ze ontvangen en versterken de draadloze signalen van alle draadloze sensoren in hun ontvangstgebied.';

  @override
  String get settingsSensorHeader => 'Sensoren';

  @override
  String get sensorChangedSuccessfully =>
      'Sensoren werden met succes veranderd.';

  @override
  String get wiredButton => 'Bedrade pulsdrukker';

  @override
  String get enOceanId => 'EnOcean-ID';

  @override
  String get enOceanAddManually => 'EnOcean-ID invoeren of scannen';

  @override
  String get enOceanIdInvalid => 'Ongeldige EnOcean-ID';

  @override
  String get enOceanAddAutomatically => 'Met EnOcean-telegram inleren';

  @override
  String get enOceanAddDescription =>
      'Het draadloze EnOcean-protocol maakt het mogelijk om pulsdrukkers in uw actor in te leren en te bedienen.\n\nKies voor automatisch inleren met EnOcean-telegram om pulsdrukkers in te leren door op een pulsdrukker te drukken of kies voor de handmatige variant om de EnOcean-ID van je pulsdrukker in te scannen of in te voeren.';

  @override
  String get enOceanTelegram => 'Telegram';

  @override
  String enOceanCodeScan(Object sensorType) {
    return 'Voer de EnOcean-ID van je $sensorType in of scan de EnOcean-QR-code van je $sensorType om deze toe te voegen.';
  }

  @override
  String get enOceanCode => 'EnOcean QR-code';

  @override
  String enOceanCodeScanDescription(Object sensorType) {
    return 'Zoek naar de EnOcean QR-code op je $sensorType en scan deze met je camera.';
  }

  @override
  String get enOceanButton => 'EnOcean pulsdrukker';

  @override
  String get enOceanBackpack => 'EnOcean-adapter';

  @override
  String get sensorNotAvailable => 'Er zijn nog geen sensoren ingeleerd.';

  @override
  String get sensorAdd => 'Sensoren toevoegen';

  @override
  String get sensorCancel => 'Inleren afbreken';

  @override
  String get sensorCancelDescription =>
      'Wil je echt het inleerproces afbreken?';

  @override
  String get getEnOceanBackpack => 'Bestel een EnOcean-adapter';

  @override
  String get enOceanBackpackMissing =>
      'Om de fantastische wereld van perfecte connectiviteit en communicatie te betreden, heb je een EnOcean-adapter nodig.\nKlik hier voor meer informatie';

  @override
  String sensorEditChangedSuccessfully(Object sensorName) {
    return '$sensorName werd met succes gewijzigd';
  }

  @override
  String sensorConnectedVia(Object deviceName) {
    return 'verbonden via $deviceName';
  }

  @override
  String get lastSeen => 'Laatst gezien';

  @override
  String get setButtonOrientation => 'Oriëntatie vaststellen';

  @override
  String get setButtonType => 'Type pulsdrukker instellen';

  @override
  String get button1Way => '1-kanaal pulsdrukker';

  @override
  String get button2Way => '2-kanaalse pulsdrukker';

  @override
  String get button4Way => '4-kanaalse pulsdrukker';

  @override
  String get buttonUnset => 'niet ingesteld';

  @override
  String get button => 'Pulsdukker';

  @override
  String get sensor => 'Sensor';

  @override
  String sensorsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count sensoren gevonden',
      one: '1 sensor gevonden',
      zero: 'geen sensoren gevonden',
    );
    return '$_temp0';
  }

  @override
  String get sensorSearch => 'Naar sensoren zoeken';

  @override
  String get searchAgain => 'Opnieuw zoeken';

  @override
  String sensorTeachInHeader(Object sensorType) {
    return '$sensorType inleren';
  }

  @override
  String sensorChooseHeader(Object sensorType) {
    return '$sensorType kiezen';
  }

  @override
  String get sensorChooseDescription =>
      'Kies een pulsdrukker die je wilt inleren';

  @override
  String get sensorCategoryDescription =>
      'Selecteer de categorie van de sensor die je wilt inleren.';

  @override
  String get sensorName => 'Pulsdrukkernaam';

  @override
  String get sensorNameFooter => 'Geef je pulsdrukker een naam';

  @override
  String sensorAddedSuccessfully(Object sensorName) {
    return '$sensorName is met succes ingeleerd';
  }

  @override
  String sensorDelete(Object sensorType) {
    return '$sensorType verwijderen';
  }

  @override
  String sensorDeleteHint(Object sensorName, Object sensorType) {
    return 'Wil je echt de $sensorType $sensorName verwijderen?';
  }

  @override
  String sensorDeletedSuccessfully(Object sensorName) {
    return '$sensorName is met succes verwijderd';
  }

  @override
  String get buttonTapDescription =>
      'Bedien de pulsdrukker die je wilt toevoegen.';

  @override
  String get waitingForTelegram => 'De actor wacht op het telegram';

  @override
  String get copied => 'Gekopieerd';

  @override
  String pairingFailed(Object sensorType) {
    return '$sensorType al ingeleerd';
  }

  @override
  String get generalDescriptionUniversalbutton =>
      'Met de universele pulsdrukker wordt de richting omgekeerd door de pulsdrukker kort los te laten. Korte pulsen schakelen aan of uit.';

  @override
  String get generalDescriptionDirectionalbutton =>
      'Bij de richtingspulsdrukker is boven \'inschakelen en omhoog dimmen\' en onderaan \'uitzetten en omlaag dimmen\'.';

  @override
  String get matterForwardingDescription =>
      'De toetsaanslag wordt aan Matter doorgestuurd.';

  @override
  String get none => 'Geen';

  @override
  String get buttonNoneDescription =>
      'Deze pulsdrukker heeft geen functionaliteit';

  @override
  String get buttonUnsetDescription =>
      'Er is geen functie aan de pulsdrukker toegewezen.';

  @override
  String get sensorButtonTypeChangedSuccessfully =>
      'Pulsdrukker-type is met succes gewijzigd';

  @override
  String forExample(Object example) {
    return 'bijv. $example';
  }

  @override
  String get enOceanQRCodeInvalidDescription =>
      'Alleen mogelijk vanaf productiedatum 44/20';

  @override
  String get input => 'Ingang';

  @override
  String get buttonSceneValueOverride =>
      'Opgeslagen scènepuldrukker overschrijven';

  @override
  String get buttonSceneValueOverrideDescription =>
      'De scènepulsdrukker wordt overschreven met de huidige dimwaarde als de pulsdrukker lang wordt ingedrukt.';

  @override
  String get buttonSceneDescription =>
      'De scèneknop schakelt aan met een vaste dimwaarde';

  @override
  String get buttonPress => 'Pulsdrukker ingedrukt';

  @override
  String get triggerOn =>
      'met lang bedienen bij een universele pulsdrukker of richtingspulsdrukker op de inschakelzijde';

  @override
  String get triggerOff =>
      'met een dubbele puls op de universele pulsdrukker of richtingspulsdrukker op de uitschakelzijde';

  @override
  String get centralOn => 'Centraal aan';

  @override
  String get centralOff => 'Centraal uit';

  @override
  String get centralButton => 'Centrale pulsdrukker';

  @override
  String get enOceanAdapterNotFound => 'Geen EnOcean plug-in adapter gevonden';

  @override
  String get updateRequired => 'Bijwerken vereist';

  @override
  String get updateRequiredDescription =>
      'Je app heeft een update nodig om dit nieuwe apparaat te ondersteunen.';

  @override
  String get release32Header =>
      'De nieuwe 64-serie met Matter en EnOcean geïntegreerd en de nieuwe Bluetooth-inbouwklok SU62PF-BT/UC zijn nu verkrijgbaar!';

  @override
  String get release32EUD64Header =>
      'De nieuwe 1-kanaals inbouwdimmer met Matter via Wi-Fi en tot 300W is er!';

  @override
  String get release32EUD64Note1 =>
      'Configuratie van dimsnelheid, aan/uit-snelheid, kinderkamer/sluimerfunctie en nog veel meer.';

  @override
  String get release32EUD64Note2 =>
      'De functionaliteit van de EUD64NPN-IPM kan worden uitgebreid met adapters, zoals bijv. de EnOcean plug-in adapter EOA64.';

  @override
  String get release32EUD64Note3 =>
      'Tot 30 draadloze EnOcean pulsdrukkers kunnen direct worden gekoppeld aan de EUD64NPN-IPM in combinatie met de EnOcean plug-in adapter EOA64 en worden doorgestuurd naar Matter.';

  @override
  String get release32EUD64Note4 =>
      'Twee bedrade pulsdrukkeringangen kunnen rechtstreeks worden gekoppeld aan de EUD64NPN-IPM of rechtstreeks worden doorgestuurd naar Matter.';

  @override
  String get release32ESR64Header =>
      'De nieuwe potentiaalvrije inbouw 1-kanaals schakelactor met Matter via Wi-Fi en tot 16A is er!';

  @override
  String get release32ESR64Note1 =>
      'Configuratie van verschillende functies zoals impulsrelais (ES), relaisfunctie (ER), normaal gesloten (ER-Inverse) en nog veel meer.';

  @override
  String get release32ESR64Note2 =>
      'De functionaliteit van de ESR64PF-IPM kan worden uitgebreid met adapters, zoals bijv. de EnOcean-adapter EOA64.';

  @override
  String get release32ESR64Note3 =>
      'Tot 30 draadloze EnOcean-pulsdrukkers kunnen rechtstreeks worden gekoppeld aan de ESR64PF-IPM in combinatie met de EnOcean-adapter EOA64 en worden vervolgens doorgestuurd naar de Matter.';

  @override
  String get release32ESR64Note4 =>
      'Één bedrade pulsdrukker-ingang kan rechtstreeks worden gekoppeld aan de ESR64PF-IPM of rechtstreeks worden doorgestuurd naar Matter.';

  @override
  String buttonsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count pulsdrukkers gevonden',
      one: '1 pulsdrukker gevonden',
      zero: 'geen pulsdrukkers gevonden',
    );
    return '$_temp0';
  }

  @override
  String get doubleImpuls => 'met een dubbele impuls';

  @override
  String get impulseDescription =>
      'Als het kanaal is ingeschakeld, wordt deze door een impuls uitgeschakeld.';

  @override
  String get locationServiceEnable => 'Locatie activeren';

  @override
  String get locationServiceDisabledDescription =>
      'Locatie is uitgeschakeld. De versie van uw besturingssysteem vereist de locatie om Bluetooth-apparaten te vinden.';

  @override
  String get locationPermissionDeniedNoPosition =>
      'Er zijn geen locatierechten verleend. De versie van uw besturingssysteem vereist locatierechten om Bluetooth-apparaten te vinden. Geef toestemming voor locatiebepaling in de instellingen van uw apparaat.';

  @override
  String get interfaceStatePermanentDeniedDescriptionDevicesAround =>
      'Er is geen toestemming verleend voor apparaten in de buurt. Geef toestemming voor apparaten in de buurt in de instellingen van uw apparaat.';

  @override
  String get permissionNearbyDevices => 'Apparaten in de buurt';

  @override
  String get release320Header =>
      'De nieuwe, krachtigere universele dimmer EUD12NPN-BT/600W-230V is er!';

  @override
  String get release320EUD600Header => 'Wat kan de nieuwe universele dimmer?';

  @override
  String get release320EUD600Note1 => 'Universele dimmer tot 600W vermogen';

  @override
  String get release320EUD600Note2 =>
      'Uitbreidbaar met vermogensuitbreiding LUD12 tot 3800W';

  @override
  String get release320EUD600Note3 =>
      'Lokale bediening met universele of richtingspulsdrukker';

  @override
  String get release320EUD600Note4 => 'Centrale functies aan / uit';

  @override
  String get release320EUD600Note5 =>
      'Bewegingsdetector ingang voor extra comfort';

  @override
  String get release320EUD600Note6 =>
      'Geïntegreerde astroklok met 10 schakelprogramma\'s';

  @override
  String get release320EUD600Note7 => 'Astro-functie';

  @override
  String get release320EUD600Note8 => 'Individuele inschakelhelderheid';

  @override
  String get mqttClientCertificate => 'Clientcertificaat';

  @override
  String get mqttClientCertificateHint => 'MQTT-clientcertificaat';

  @override
  String get mqttClientKey => 'Clientsleutel';

  @override
  String get mqttClientKeyHint => 'MQTT-clientsleutel';

  @override
  String get mqttClientPassword => 'Clientwachtwoord';

  @override
  String get mqttClientPasswordHint => 'MQTT-clientwachtwoord';

  @override
  String get mqttEnableHomeAssistantDiscovery =>
      'HomeAssistant MQTT-detectie inschakelen';

  @override
  String get modbusTcp => 'Modbus-TCP';

  @override
  String get enableInterface => 'Interface activeren';

  @override
  String get busAddress => 'Busadres';

  @override
  String busAddressWithAddress(Object index) {
    return 'Busadres $index';
  }

  @override
  String get deviceType => 'Apparaattype';

  @override
  String registerTable(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Registratie tafel',
      one: 'Registratie tafel',
    );
    return '$_temp0';
  }

  @override
  String get currentValues => 'Actuele waarden';

  @override
  String get requestRTU => 'Query-RTU';

  @override
  String get requestPriority => 'Queryprioriteit';

  @override
  String get mqttForwarding => 'MQTT-doorsturen';

  @override
  String get historicData => 'Historische gegevens';

  @override
  String get dataFormat => 'Gegevensformaat';

  @override
  String get dataType => 'Gegevenstype';

  @override
  String get description => 'Beschrijving';

  @override
  String get readWrite => 'Lezen/Schrijven';

  @override
  String get unit => 'Eenheid';

  @override
  String get registerTableReset => 'Registertabel resetten';

  @override
  String get registerTableResetDescription =>
      'Wilt u de registertabel echt resetten?';

  @override
  String get notConfigured => 'Niet geconfigureerd';

  @override
  String get release330ZGW16Header => 'Uitgebreide update voor de ZGW16WL-IP';

  @override
  String get release330Header => 'De ZGW16WL-IP met maximaal 16 kWh-meters';

  @override
  String get release330ZGW16Note1 =>
      'Ondersteuning tot maximaal 16 ELTAKO Modbus kWh-meters';

  @override
  String get release330ZGW16Note2 => 'Ondersteuning voor Modbus TCP';

  @override
  String get release330ZGW16Note3 => 'Ondersteuning voor MQTT-detectie';

  @override
  String get screenshotButtonLivingRoom => 'Pulsdrukker woonkamer';

  @override
  String get registerChangedSuccessfully =>
      'Het register is succesvol gewijzigd.';

  @override
  String get serverCertificateEmpty =>
      'Het servercertificaat mag niet leeg zijn.';

  @override
  String get registerTemplates => 'Register sjablonen';

  @override
  String get registerTemplateChangedSuccessfully =>
      'Het registersjabloon is succesvol gewijzigd.';

  @override
  String get registerTemplateReset => 'Register sjabloon resetten';

  @override
  String get registerTemplateResetDescription =>
      'Wilt u het registersjabloon echt resetten?';

  @override
  String get registerTemplateNotAvailable =>
      'Geen registersjablonen beschikbaar';

  @override
  String get rename => 'Hernoemen';

  @override
  String get registerName => 'Register naam';

  @override
  String get registerRenameDescription =>
      'Geef het register een aangepaste naam';

  @override
  String get restart => 'Apparaat opnieuw opstarten';

  @override
  String get restartDescription =>
      'Wilt u het apparaat echt opnieuw opstarten?';

  @override
  String get restartConfirmationDescription =>
      'Het apparaat zal nu opnieuw opstarten';

  @override
  String get deleteAllElectricityMeters => 'Verwijder alle kWh-meters';

  @override
  String get deleteAllElectricityMetersDescription =>
      'Wilt u echt alle kWh-meters verwijderen?';

  @override
  String get deleteAllElectricityMetersConfirmationDescription =>
      'Alle kWh-meters zijn succesvol verwijderd';

  @override
  String get resetAllElectricityMeters => 'Reset alle kWh-meterconfiguraties';

  @override
  String get resetAllElectricityMetersDescription =>
      'Wilt u echt alle kWh-meterconfiguraties resetten?';

  @override
  String get resetAllElectricityMetersConfirmationDescription =>
      'Alle kWh-meterconfiguraties zijn succesvol gereset';

  @override
  String get deleteElectricityMeterHistories =>
      'Alle kWh-metergeschiedenissen verwijderen';

  @override
  String get deleteElectricityMeterHistoriesDescription =>
      'Wilt u echt alle kWh-metergeschiedenissen verwijderen?';

  @override
  String get deleteElectricityMeterHistoriesConfirmationDescription =>
      'Alle kWh-metergeschiedenissen zijn succesvol verwijderd';

  @override
  String get multipleElectricityMetersSupportMissing =>
      'Uw apparaat ondersteunt momenteel slechts één kWh-meter. Update uw firmware.';

  @override
  String get consumptionWithUnit => 'Verbruik (kWh)';

  @override
  String get exportWithUnit => 'Levering (kWh)';

  @override
  String get importWithUnit => 'Verbruik (kWh)';

  @override
  String get resourceWarningHeader => 'Beperkingen in middelen';

  @override
  String mqttAndTcpResourceWarning(Object protocol) {
    return 'Het is niet mogelijk om MQTT en Modbus TCP tegelijkertijd te gebruiken vanwege beperkte systeembronnen. Deactiveer $protocol eerst.';
  }

  @override
  String get mqttEnabled => 'MQTT ingeschakeld';

  @override
  String get redirectMQTT => 'Ga naar MQTT-instellingen';

  @override
  String get redirectModbus => 'Ga naar Modbus-instellingen';

  @override
  String get unsupportedSettingDescription =>
      'Met uw huidige firmwareversie worden sommige apparaatinstellingen niet ondersteund. Update uw firmware om de nieuwe functies te gebruiken';

  @override
  String get updateNow => 'Nu bijwerken';

  @override
  String get zgw241Hint =>
      'Met deze update is Modbus TCP standaard ingeschakeld en MQTT uitgeschakeld. Dit kan worden gewijzigd in de instellingen. Met ondersteuning voor maximaal 16 tellers zijn er veel optimalisaties doorgevoerd; dit kan leiden tot wijzigingen in de instellingen van het apparaat. Herstart het apparaat na het aanpassen van de instellingen.';

  @override
  String get deviceConfigChangedSuccesfully =>
      'Looptijd is succesvol gewijzigd.';

  @override
  String get deviceConfiguration => 'Apparaatconfiguratie';

  @override
  String get tiltModeToggle => 'Kantelmodus';

  @override
  String get tiltModeToggleFooter =>
      'Als het apparaat is ingesteld in Matter, moeten alle functies daar opnieuw worden geconfigureerd.';

  @override
  String get shaderMovementDirection => 'Achteruit Omhoog/Omlaag';

  @override
  String get shaderMovementDirectionDescription =>
      'Keer de richting om voor omhoog/omlaag-bewegingen van de motor';

  @override
  String get tiltTime => 'Kantel runtime';

  @override
  String changeTiltModeDialogTitle(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Enable',
      'false': 'Disable',
      'other': 'Change',
    });
    return '$_temp0 kantelfunctie';
  }

  @override
  String changeTiltModeDialogConfirmation(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Enable',
      'false': 'Disable',
      'other': 'Change',
    });
    return '$_temp0';
  }

  @override
  String get generalTextSlatSetting => 'Instelling lamellen';

  @override
  String get generalTextPosition => 'Positie';

  @override
  String get generalTextSlatPosition => 'Lamelpositie';

  @override
  String get slatSettingDescription => 'Beschrijving lamelleninstelling';

  @override
  String get scenePositionSliderDescription => 'Hoogte';

  @override
  String get sceneSlatPositionSliderDescription => 'Kantelen';

  @override
  String get referenceRun => 'Kalibratierun';

  @override
  String get slatAutoSettingHint =>
      'In deze modus is de stand van de zonwering niet van belang voordat de lamellen zich aanpassen aan de gewenste kantelstand.';

  @override
  String get slatReversalSettingHint =>
      'In deze modus sluit de zonwering volledig voordat de lamellen zich aanpassen aan de gewenste kantelpositie.';

  @override
  String get release340Header =>
      'De nieuwe ESB64NP-IPM-actuator voor inbouwzonwering is er!';

  @override
  String get release340ESB64Header => 'Wat kan de ESB64NP-IPM?';

  @override
  String get release340ESB64Note1 =>
      'Onze Matter Gateway-gecertificeerde zonweringactuator met optionele lamellenfunctie';

  @override
  String get release340ESB64Note2 =>
      'Twee bedrade knopingangen voor handmatig schakelen en doorsturen naar Matter';

  @override
  String get release340ESB64Note3 =>
      'Uitbreidbaar met EnOcean adapter (EOA64). Bijvoorbeeld met EnOcean draadloze drukknop F4T55';

  @override
  String get release340ESB64Note4 =>
      'Open voor integraties dankzij REST API gebaseerd op OpenAPI-standaard';

  @override
  String get activateTiltModeDialogText =>
      'Als de kantelfunctie is ingeschakeld, gaan alle instellingen verloren. Weet je zeker dat je de kantelfunctie wilt inschakelen?';

  @override
  String get deactivateTiltModeDialogText =>
      'Als je de kantelfunctie uitschakelt, gaan alle instellingen verloren. Weet je zeker dat je de kantelfunctie wilt uitschakelen?';

  @override
  String shareConfiguration(Object name) {
    return 'Share configuration $name';
  }

  @override
  String get configurationSharedSuccessfully =>
      'Configuration shared successfully';

  @override
  String get configurationShareFailed => 'Sharing configuration failed';
}

/// The translations for Dutch Flemish, as used in Belgium (`nl_BE`).
class AppLocalizationsNlBe extends AppLocalizationsNl {
  AppLocalizationsNlBe() : super('nl_BE');

  @override
  String get appName => 'ELTAKO Connect';

  @override
  String get discoveryHint =>
      'Activeer Bluetooth op het toestel om te verbinden';

  @override
  String devicesFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count devices found',
      one: '1 device found',
      zero: 'No devices found',
    );
    return '$_temp0';
  }

  @override
  String discoveryDemodeviceName(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Demo devices',
      one: 'Demo device',
    );
    return '$_temp0';
  }

  @override
  String get discoverySu12Description => '2-kanaals schakelklok Bluetooth';

  @override
  String get discoveryImprint => 'Impressum';

  @override
  String get discoveryLegalnotice => 'Juridische kennisgeving';

  @override
  String get generalSave => 'Bewaren';

  @override
  String get generalCancel => 'Annuleren';

  @override
  String get detailsHeaderHardwareversion => 'Hardware versie';

  @override
  String get detailsHeaderSoftwareversion => 'Software versie';

  @override
  String get detailsHeaderConnected => 'Verbonden';

  @override
  String get detailsHeaderDisconnected => 'Verbinding verbroken';

  @override
  String get detailsTimersectionHeader => 'Programma\'s';

  @override
  String get detailsTimersectionTimercount => 'van de 60 programma\'s gebruikt';

  @override
  String get detailsConfigurationsectionHeader => 'Configuratie';

  @override
  String get detailsConfigurationPin => 'PIN-code toestel';

  @override
  String detailsConfigurationChannelsDescription(
    Object channel1,
    Object channel2,
  ) {
    return 'Kanaal 1: $channel1 | Kanaal 2:  $channel2';
  }

  @override
  String get settingsCentralHeader => 'Centraal aan/uit';

  @override
  String get detailsConfigurationCentralDescription =>
      'Geldt alleen als het kanaal op AUTO staat';

  @override
  String get detailsConfigurationDevicedisplaylock => 'Scherm vergrendelen';

  @override
  String get timerOverviewHeader => 'Programma\'s';

  @override
  String get timerOverviewTimersectionTimerinactivecount => 'inactief';

  @override
  String get timerDetailsListsectionDays1 => 'Maandag';

  @override
  String get timerDetailsListsectionDays2 => 'Dinsdag';

  @override
  String get timerDetailsListsectionDays3 => 'Woensdag';

  @override
  String get timerDetailsListsectionDays4 => 'Donderdag';

  @override
  String get timerDetailsListsectionDays5 => 'Vrijdag';

  @override
  String get timerDetailsListsectionDays6 => 'Zaterdag';

  @override
  String get timerDetailsListsectionDays7 => 'Zondag';

  @override
  String get timerDetailsHeader => 'Programma';

  @override
  String get timerDetailsSunrise => 'Zonsopgang';

  @override
  String get generalToggleOff => 'Uit';

  @override
  String get generalToggleOn => 'Aan';

  @override
  String get timerDetailsImpuls => 'Impuls';

  @override
  String get generalTextTime => 'Tijd';

  @override
  String get generalTextAstro => 'Astro';

  @override
  String get generalTextAuto => 'Auto';

  @override
  String get timerDetailsOffset => 'Tijdsverschil';

  @override
  String get timerDetailsPlausibility => 'Activeer plausibiliteitscontrole';

  @override
  String get timerDetailsPlausibilityDescription =>
      'Als de uit-tijd ingesteld is op een eerdere tijd dan de aan-tijd, worden beide programma\'s genegeerd, bv. inschakelen bij zonsopgang en uitschakelen om 6:00 uur \'s morgens. Er zijn ook situaties waarin de controle niet gewenst is, bv. inschakelen bij zonsondergang en uitschakelen om 1:00 uur \'s morgens.';

  @override
  String get generalDone => 'Klaar';

  @override
  String get generalDelete => 'Wissen';

  @override
  String get timerDetailsImpulsDescription =>
      'Om te wijzigen, ga naar de toestelconfiguratie';

  @override
  String get settingsNameHeader => 'Naam toestel';

  @override
  String get settingsNameDescription =>
      'Deze naam dient als identificatie van het toestel.';

  @override
  String get settingsFactoryresetHeader => 'Fabrieksinstellingen';

  @override
  String get settingsFactoryresetDescription =>
      'Welke inhoud moet worden gereset?';

  @override
  String get settingsFactoryresetResetbluetooth =>
      'Reset Bluetooth-instellingen';

  @override
  String get settingsFactoryresetResettime => 'Reset instellingen tijd';

  @override
  String get settingsFactoryresetResetall =>
      'Terugzetten naar fabrieksinstellingen';

  @override
  String get settingsDeletetimerHeader => 'Wis programma\'s';

  @override
  String get settingsDeletetimerDescription =>
      'Moeten echt alle programma\'s gewist worden?';

  @override
  String get settingsDeletetimerAllchannels => 'Alle kanalen';

  @override
  String get settingsImpulseHeader => 'Impulstijd';

  @override
  String get settingsImpulseDescription =>
      'De impulstijd bepaalt de duur van de impuls.';

  @override
  String get generalTextRandommode => 'Toevalsmodus';

  @override
  String get settingsChannelsTimeoffsetHeader => 'Zonnewende tijdsverschil';

  @override
  String settingsChannelsTimeoffsetDescription(
    Object summerOffset,
    Object winterOffset,
  ) {
    return 'Zomer: ${summerOffset}min | Winter: ${winterOffset}min';
  }

  @override
  String get settingsLocationHeader => 'Locatie';

  @override
  String get settingsLocationDescription =>
      'Stel uw locatie in voor de astro functies.';

  @override
  String get settingsLanguageHeader => 'Taal toestel';

  @override
  String get settingsLanguageSetlanguageautomatically =>
      'Stel taal automatisch in';

  @override
  String settingsLanguageDescription(Object deviceType) {
    return 'Kies de taal voor de $deviceType';
  }

  @override
  String get settingsLanguageGerman => 'Duits';

  @override
  String get settingsLanguageFrench => 'Frans';

  @override
  String get settingsLanguageEnglish => 'Engels';

  @override
  String get settingsLanguageItalian => 'Italiaans';

  @override
  String get settingsLanguageSpanish => 'Spaans';

  @override
  String get settingsDatetimeHeader => 'Datum en tijd';

  @override
  String get settingsDatetimeSettimeautomatically => 'Gebruik systeemtijd';

  @override
  String get settingsDatetimeSettimezoneautomatically =>
      'Stel tijdszone automatisch in';

  @override
  String get generalTextTimezone => 'Tijdszone';

  @override
  String get settingsDatetime24Hformat => '24 uur weergave';

  @override
  String get settingsDatetimeSetsummerwintertimeautomatically =>
      'Automatisch zomer- wintertijd';

  @override
  String get settingsDatetimeWinter => 'Winter';

  @override
  String get settingsDatetimeSummer => 'Zomer';

  @override
  String get settingsPasskeyHeader => 'Huidige toestel PIN';

  @override
  String get settingsPasskeyDescription => 'Voer de huidige toestel PIN in';

  @override
  String get timerDetailsActiveprogram => 'Programma activeren';

  @override
  String get timerDetailsActivedays => 'Actieve dagen';

  @override
  String get timerDetailsSuccessdialogHeader => 'Gelukt';

  @override
  String get timerDetailsSuccessdialogDescription =>
      'Programma succesvol toegevoegd';

  @override
  String get settingsRandommodeDescription =>
      'De toevalsmodus werkt alleen met programma\'s die gebaseerd zijn op tijd, niet bij impulsen of astrofuncties (zonsopgang / zonsondergang).';

  @override
  String get settingsSolsticeHeader => 'Zonnewende tijdsverschil';

  @override
  String get settingsSolsticeDescription =>
      'De tijd geeft het tijdsverschil met de zonsondergang aan. De zonsopgang wordt respectievelijk omgekeerd.';

  @override
  String get settingsSolsticeHint =>
      'Bijvoorbeeld:\nIn de winter wordt er 30 minuten voor zonsondergang geschakeld, waardoor er tevens 30 minuten na zonsopgang geschakeld wordt.';

  @override
  String get generalTextMinutesShort => 'min';

  @override
  String get settingsPinDescription => 'De PIN is vereist voor de verbinding.';

  @override
  String get settingsPinHeader => 'PIN-code nieuwe toestellen';

  @override
  String get settingsPinNewpinDescription => 'Voer een nieuwe PIN-code in';

  @override
  String get settingsPinNewpinRepeat => 'Herhaal de nieuwe PIN-code';

  @override
  String get detailsProductinfo => 'Productinformatie';

  @override
  String get settingsDatetimeSettimeautodescription => 'Kies de gewenste tijd';

  @override
  String minutes(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'minuten',
      one: 'minuut',
    );
    return '$_temp0';
  }

  @override
  String hours(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'uren',
      one: 'uur',
    );
    return '$_temp0';
  }

  @override
  String seconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'seconden',
      one: 'seconde',
    );
    return '$_temp0';
  }

  @override
  String generalTextChannel(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'kanalen',
      one: 'kanaal',
    );
    return '$_temp0';
  }

  @override
  String generalLabelChannel(Object number) {
    return 'Kanaal $number';
  }

  @override
  String get generalTextDate => 'Datum';

  @override
  String get settingsDatetime24HformatDescription =>
      'Kies de gewenste weergave';

  @override
  String get settingsDatetimeSetsummerwintertime => 'Zomer- wintertijd';

  @override
  String get settingsDatetime24HformatValue24 => '24h';

  @override
  String get settingsDatetime24HformatValue12 => '12 uur';

  @override
  String get detailsEdittimer => 'Wijzig programma\'s';

  @override
  String get settingsPinOldpinRepeat => 'Herhaal de huidige PIN';

  @override
  String get settingsPinCheckpin => 'Controle PIN';

  @override
  String detailsDevice(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Devices',
      one: 'Device',
    );
    return '$_temp0';
  }

  @override
  String get detailsDisconnect => 'Verbreek verbinding';

  @override
  String get settingsCentralDescription =>
      'De ingang A1 regelt de centraal aan/uit.\nCentraal aan/uit is alleen van toepassing als het kanaal ingesteld is op centraal aan/uit.';

  @override
  String get settingsCentralHint =>
      'Voorbeeld:\nKanaal 1 = centraal aan/uit\nKanaal 2 = uit\nA1 = centraal aan -> enkel kanaal 1 gaat aan, kanaal 2 blijft uit.';

  @override
  String get settingsCentralToggleheader => 'Centrale input schakelaars';

  @override
  String get settingsCentralActivechannelsdescription =>
      'Huidige kanalen met de setting centraal aan/uit:';

  @override
  String get settingsSolsticeSign => 'Symbool';

  @override
  String get settingsDatetimeTimezoneDescription => 'Centraal-Europese tijd';

  @override
  String get generalButtonContinue => 'Ga verder';

  @override
  String get settingsPinConfirmationDescription => 'Wijziging PIN gelukt';

  @override
  String get settingsPinFailDescription => 'Wijziging PIN mislukt';

  @override
  String get settingsPinFailHeader => 'Fout';

  @override
  String get settingsPinFailShort => 'De PIN moet precies 6 digits lang zijn';

  @override
  String get settingsPinFailWrong => 'De huidige PIN is niet correct';

  @override
  String get settingsPinFailMatch => 'De PIN-codes komen niet overeen';

  @override
  String get discoveryLostconnectionHeader => 'Verbinding verbroken';

  @override
  String get discoveryLostconnectionDescription =>
      'De verbinding met het toestel is verbroken.';

  @override
  String get settingsChannelConfigCentralDescription =>
      'Gedraagt zich als AUTO en reageert bijkomend op de gekableerde centrale ingangen.';

  @override
  String get settingsChannelConfigOnDescription =>
      'Schakelt het kanaal permanent naar AAN en negeert de programma\'s';

  @override
  String get settingsChannelConfigOffDescription =>
      'Schakelt het kanaal permanent naar UIT en negeert de programma\'s';

  @override
  String get settingsChannelConfigAutoDescription =>
      'Schakelt in functie van de tijd en astro programma\'s';

  @override
  String get bluetoothPermissionDescription =>
      'Bluetooth is vereist voor de configuratie van de toestellen.';

  @override
  String get timerListitemOn => 'Zet aan';

  @override
  String get timerListitemOff => 'Zet uit';

  @override
  String get timerListitemUnknown => 'Onbekend';

  @override
  String get timerDetailsAstroHint =>
      'De locatie moet worden ingesteld in de instellingen om de astroprogramma\'s correct te laten werken.';

  @override
  String get timerDetailsTrigger => 'Trigger';

  @override
  String get timerDetailsSunset => 'Zonsondergang';

  @override
  String get settingsLocationCoordinates => 'Coördinaten';

  @override
  String get settingsLocationLatitude => 'Breedtegraad';

  @override
  String get settingsLocationLongitude => 'Lengtegraad';

  @override
  String timerOverviewEmptyday(Object day) {
    return 'Er worden momenteel geen programma\'s gebruikt voor $day';
  }

  @override
  String get timerOverviewProgramloaded => 'Programma\'s worden geladen';

  @override
  String get timerOverviewProgramchanged => 'Programma werd gewijzigd.';

  @override
  String get settingsDatetimeProcessing => 'Datum en tijd zijn gewijzigd.';

  @override
  String get deviceNameEmpty => 'Input mag niet leeg zijn.';

  @override
  String deviceNameHint(Object count) {
    return 'De input mag niet meer dan $count karakters bevatten.';
  }

  @override
  String get deviceNameChanged => 'Naam toestel is gewijzigd.';

  @override
  String get deviceNameChangedSuccessfully =>
      'Naam toestel is succesvol gewijzigd.';

  @override
  String get deviceNameChangedFailed => 'Er is een fout opgetreden.';

  @override
  String get settingsPinConfirm => 'Bevestig';

  @override
  String get deviceShowInstructions =>
      '1. Activeer de Bluetooth van de horloge met SET\n2. Tik op de knop bovenaan om het zoeken te starten.';

  @override
  String get deviceNameNew => 'Voeg nieuwe naam toestel in';

  @override
  String get settingsLanguageRetrieved => 'De taal is gevonden';

  @override
  String get detailsProgramsShow => 'Toon programma\'s';

  @override
  String get generalTextProcessing => 'Gelieve te wachten';

  @override
  String get generalTextRetrieving => 'zijn gevonden.';

  @override
  String get settingsLocationPermission =>
      'ELTAKO Connect toegang geven tot de locatie van dit toestel.';

  @override
  String get timerOverviewChannelloaded => 'Kanalen zijn opgeladen';

  @override
  String get generalTextRandommodeChanged => 'Toevalsmodus is gewijzigd';

  @override
  String get detailsConfigurationsectionChanged => 'Configuratie is gewijzigd';

  @override
  String get settingsSettimeFunctions => 'Tijdfuncties zijn gewijzigd';

  @override
  String get imprintContact => 'Contact';

  @override
  String get imprintPhone => 'Telefoon';

  @override
  String get imprintMail => 'Mail';

  @override
  String get imprintRegistrycourt => 'Bevoegde rechtbank';

  @override
  String get imprintRegistrynumber => 'Registratienummer';

  @override
  String get imprintCeo => 'Algemeen Directeur';

  @override
  String get imprintTaxnumber => 'BTW nr.';

  @override
  String get settingsLocationCurrent => 'Huidige locatie';

  @override
  String get generalTextReset => 'Reset';

  @override
  String get discoverySearchStart => 'Start zoeken';

  @override
  String get discoverySearchStop => 'Stop zoeken';

  @override
  String get settingsImpulsSaved => 'Impulstijd is opgeslagen';

  @override
  String get settingsCentralNochannel =>
      'Er zijn geen kanalen met de centraal aan/uit functie';

  @override
  String get settingsFactoryresetBluetoothConfirmationDescription =>
      'Bluetoothverbinding was succesvol gereset.';

  @override
  String get settingsFactoryresetBluetoothFailDescription =>
      'Reset Bluetoothverbindingen mislukt.';

  @override
  String get imprintPublisher => 'Uitgever';

  @override
  String get discoveryDeviceConnecting => 'Verbinding is gemaakt';

  @override
  String get discoveryDeviceRestarting => 'Herstarten';

  @override
  String get generalTextConfigurationsaved => 'Configuratie kanaal opgeslagen.';

  @override
  String get timerOverviewChannelssaved => 'Sla de kanalen op';

  @override
  String get timerOverviewSaved => 'Timer opgeslagen';

  @override
  String get timerSectionList => 'Lijstweergave';

  @override
  String get timerSectionDayview => 'Dagweergave';

  @override
  String get generalTextChannelInstructions => 'Settings kanaal';

  @override
  String get generalTextPublisher => 'Uitgever';

  @override
  String get settingsDeletetimerDialog =>
      'Wenst u werkelijk alle programma\'s te wissen?';

  @override
  String get settingsFactoryresetResetbluetoothDialog =>
      'Wenst u werkelijk alle Bluetooth settings te resetten?';

  @override
  String get settingsCentralTogglecentral => 'Centraal aan/uit';

  @override
  String generalTextConfirmation(Object serviceName) {
    return '$serviceName wijziging succesvol.';
  }

  @override
  String generalTextFailed(Object serviceName) {
    return '$serviceName wijziging mislukt.';
  }

  @override
  String get settingsChannelConfirmationDescription =>
      'Kanalen werden succesvol veranderd.';

  @override
  String get timerDetailsSaveHeader => 'Sla programma op';

  @override
  String get timerDetailsDeleteHeader => 'Wis programma';

  @override
  String get timerDetailsSaveDescription => 'Opslag programma gelukt.';

  @override
  String get timerDetailsDeleteDescription => 'Wissen programma gelukt.';

  @override
  String get timerDetailsAlertweekdays =>
      'Het programma kan niet opgeslagen worden, omdat er geen weekdagen geselecteerd zijn.';

  @override
  String get generalTextOk => 'OK';

  @override
  String get settingsDatetimeChangesuccesfull =>
      'Datum en tijd zijn succesvol gewijzigd.';

  @override
  String get discoveryConnectionFailed => 'Verbinding mislukt';

  @override
  String get discoveryDeviceResetrequired =>
      'Er kon geen verbinding gemaakt worden met het toestel. Wis het toestel uit uw Bluetooth settings om het probleem op te lossen. Neem contact op met onze technische dienst indien dit niet lukt.';

  @override
  String get generalTextSearch => 'Zoek toestellen';

  @override
  String get generalTextOr => 'of';

  @override
  String get settingsFactoryresetProgramsConfirmationDescription =>
      'Alle programma\'s zijn succesvol gewist.';

  @override
  String get generalTextManualentry => 'Handmatige invoer';

  @override
  String get settingsLocationSaved => 'Locatie opgeslagen';

  @override
  String get settingsLocationAutosearch => 'Zoek locatie automatisch';

  @override
  String get imprintPhoneNumber => '+49 711 / 9435 0000';

  @override
  String get imprintMailAddress => '<EMAIL>';

  @override
  String get settingsFactoryresetResetallDialog =>
      'Wilt u het toestel zeker terugzetten naar de fabrieksinstellingen?';

  @override
  String get settingsFactoryresetFactoryConfirmationDescription =>
      'Het toestel is succesvol teruggezet in de fabrieksinstellingen.';

  @override
  String get settingsFactoryresetFactoryFailDescription =>
      'Reset toetsel mislukt.';

  @override
  String get imprintPhoneNumberIos => '+49711/94350000';

  @override
  String get mfzFunctionA2Title => '2-traps inschakelvertraging (A2)';

  @override
  String get mfzFunctionA2TitleShort => '2-traps inschakelvertraging (A2)';

  @override
  String get mfzFunctionA2Description =>
      'Bij het aanleggen van de stuurspanning begint de ingestelde tijd T1, instelbaar tussen 0 en 60 seconden, te lopen. Na afloop van T1 sluit het contact 1-2 en begint de ingestelde tijd T2, instelbaar tussen 0 en 60 seconden, te lopen. Na afloop van T2 sluit het contact 3-4. Na een onderbreking begint de tijd T1 opnieuw te lopen.';

  @override
  String get mfzFunctionRvTitle =>
      'Vertraagd afvallend (RV; uitschakelvertraging)';

  @override
  String get mfzFunctionRvTitleShort => 'RV | Vertraagd afvallend';

  @override
  String get mfzFunctionRvDescription =>
      'Bij het aanleggen van de stuurspanning schakelt het contact naar 15-18. Wanneer de stuurspanning onderbroken wordt, begint de ingestelde tijd te lopen, en bij het verstrijken hiervan schakelt het contact terug in de ruststand. Resetbaar tijdens de ingestelde tijd.';

  @override
  String get mfzFunctionTiTitle =>
      'Tijdrelais beginnend met impuls (TI; knipperrelais)';

  @override
  String get mfzFunctionTiTitleShort => 'TI | tijdrelais beginnend met impuls';

  @override
  String get mfzFunctionTiDescription =>
      'Zolang de stuurspanning aanligt, sluit en opent het werkcontact. De omschakeltijd in beide richtingen kan afzonderlijk ingesteld worden. Wanneer de stuurspanning wordt aangelegd, wisselt het werkcontact onmiddellijk naar 15-18.';

  @override
  String get mfzFunctionAvTitle =>
      'Vertraagd opkomend (AV; inschakelvertraging)';

  @override
  String get mfzFunctionAvTitleShort => 'AV | vertraagd opkomend';

  @override
  String get mfzFunctionAvDescription =>
      'Bij het aanleggen van de stuurspanning begint de ingestelde tijd te lopen en bij het verstrijken hiervan schakelt het contact naar 15-18. Na een onderbreking begint de tijd opnieuw af te lopen.';

  @override
  String get mfzFunctionAvPlusTitle =>
      'Vertraagd opkomend met geheugen (AV+; inschakelvertraging)';

  @override
  String get mfzFunctionAvPlusTitleShort =>
      'AV+ | vertraagd opkomend met geheugen';

  @override
  String get mfzFunctionAvPlusDescription =>
      'Dezelfde functie als AV, maar onthoudt de reeds afgelopen tijd na een onderbreking.';

  @override
  String get mfzFunctionAwTitle => 'Uitschakelwisrelais (AW)';

  @override
  String get mfzFunctionAwTitleShort => 'AW | uitschakelwisrelais';

  @override
  String get mfzFunctionAwDescription =>
      'Bij het onderbreken van de stuurspanning  schakelt het NO-contact naar 15-18 en keert na het aflopen van de ingestelde tijd terug. Bij het aanleggen van de stuurspanning tijdens het aflopen van de tijd keert het contact onmiddellijk in de ruststand terug en wordt de resterende tijd gewist.';

  @override
  String get mfzFunctionIfTitle => 'Impulsgever (IF; enkel MFZ12.1)';

  @override
  String get mfzFunctionIfTitleShort => 'IF | impulsgever';

  @override
  String get mfzFunctionIfDescription =>
      'Bij het aanleggen van de stuurspanning wisselt het werkcontact voor de ingestelde tijd naar 15-18. Bijkomende aansturingen worden pas na afloop van de ingestelde tijd uitgevoerd.';

  @override
  String get mfzFunctionEwTitle => 'Inschakelwisrelais (EW)';

  @override
  String get mfzFunctionEwTitleShort => 'EW | inschakelwisrelais';

  @override
  String get mfzFunctionEwDescription =>
      'Bij het aanleggen van de stuurspanning schakelt het NO contact om naar 15-18 en keert na het aflopen van de ingestelde tijd terug. Als de stuurspanning wegvalt tijdens de wistijd, schakelt het NO contact onmiddellijk terug in de ruststand en wordt de resterende tijd gewist.';

  @override
  String get mfzFunctionEawTitle =>
      'Inschakel- en uitschakelwissend (EAW; enkel MFZ12.1)';

  @override
  String get mfzFunctionEawTitleShort =>
      'EAW | inschakel- en uitschakelwissend';

  @override
  String get mfzFunctionEawDescription =>
      'Bij het aanleggen en onderbreken van de stuurspanning wisselt het werkcontact naar 15-18 en keert na afloop van de ingestelde wistijd terug.';

  @override
  String get mfzFunctionTpTitle =>
      'Impulsgever beginnend met pauze (TP; knipperrelais, enkel MFZ12.1)';

  @override
  String get mfzFunctionTpTitleShort => 'TP | impulsgever beginnend met pauze';

  @override
  String get mfzFunctionTpDescription =>
      'Zelfde functiebeschrijving als voor TI, behalve dat het contact niet wisselt naar 15-18 bij het aanleggen van de stuurspanning, maar open blijft bij 15-16.';

  @override
  String get mfzFunctionIaTitle =>
      'Impulsgestuurd vertraagd opkomend en impulsgevend (vb. automatische deuropener) (IA; enkel MFZ12.1)';

  @override
  String get mfzFunctionIaTitleShort =>
      'IA | impulsgestuurd vertraagd opkomend';

  @override
  String get mfzFunctionIaDescription =>
      'Bij een stuurimpuls vanaf 20ms. begint het tijdsverloop t1 te lopen. Na het verstrijken daarvan wisselt het contact voor het tijdsverloop t2 naar 15-18 (vb. voor automatische deuropener). Indien t1 ingesteld wordt op de kortste tijd 0,1 sec. , dan functioneert IA als impulsgever waarbij t2 afloopt, onafhankelijk van de lengte van het stuursignaal (min. 150 ms).';

  @override
  String get mfzFunctionArvTitle =>
      'Vertraagd opkomend en vertraagd afvallend (ARV)';

  @override
  String get mfzFunctionArvTitleShort =>
      'ARV | vertraagd opkomend en vertraagd afvallend (ARV)';

  @override
  String get mfzFunctionArvDescription =>
      'Bij het aanleggen van de stuurspanning begint de tijd te lopen. Na verloop daarvan wisselt het contact naar 15-18. Indien daarna de stuurspanning onderbroken wordt, begint een volgend tijdsverloop zodat na afloop daarvan het contact terugkeert in de ruststand.\nNa een onderbreking van de inschakelvertraging begint de tijd opnieuw te lopen.';

  @override
  String get mfzFunctionArvPlusTitle =>
      'Vertraagd opkomend en vertraagd afvallend met geheugen (ARV+)';

  @override
  String get mfzFunctionArvPlusTitleShort =>
      'ARV+ | vertraagd opkomend en vertraagd afvallend met geheugen';

  @override
  String get mfzFunctionArvPlusDescription =>
      'Dezelfde functie als ARV, maar onthoudt de reeds verstreken tijd na een onderbreking van de inschakelvertraging.';

  @override
  String get mfzFunctionEsTitle => 'Teleruptor (ES)';

  @override
  String get mfzFunctionEsTitleShort => 'ES | teleruptor';

  @override
  String get mfzFunctionEsDescription =>
      'Met stuurimpulsen vanaf 50ms schakelt het werkcontact aan en uit.';

  @override
  String get mfzFunctionEsvTitle =>
      'Teleruptor met afvalvertraging en uitschakelverwittiging (ESV)';

  @override
  String get mfzFunctionEsvTitleShort =>
      'ESV | teleruptor met afvalvertraging en uitschakelverwittiging';

  @override
  String get mfzFunctionEsvDescription =>
      'Dezelfde functie als SRV. Echter met uitschakelverwittiging: ca. 30 seconden voor het beëindigen van de ingestelde tijd knippert de verlichting 3 maal in steeds korter wordende intervallen.';

  @override
  String get mfzFunctionErTitle => 'Relais (ER)';

  @override
  String get mfzFunctionErTitleShort => 'ER | relais';

  @override
  String get mfzFunctionErDescription =>
      'Zo lang het stuurcontact gesloten is, schakelt het werkcontact van 15-16 naar 15-18.';

  @override
  String get mfzFunctionSrvTitle => 'Teleruptor met afvalvertraging';

  @override
  String get mfzFunctionSrvTitleShort => 'SRV | teleruptor met afvalvertraging';

  @override
  String get mfzFunctionSrvDescription =>
      'Met stuurimpulsen vanaf 50ms schakelt het werkcontact aan en uit. In de contactstand 15-18 schakelt het toestel na afloop van de vertragingstijd automatisch in ruststand 15-16.';

  @override
  String get detailsFunctionsHeader => 'Functies';

  @override
  String mfzFunctionTimeHeader(Object index) {
    return 'Tijd (t$index)';
  }

  @override
  String get mfzFunctionOnDescription => 'permanent AAN';

  @override
  String get mfzFunctionOffDescription => 'permanent UIT';

  @override
  String get mfzFunctionMultiplier => 'Factor';

  @override
  String get discoveryMfz12Description => 'Multifunctie tijdrelais Bluetooth';

  @override
  String get mfzFunctionOnTitle => 'Aan (ON)';

  @override
  String get mfzFunctionOnTitleShort => 'Aan (ON)';

  @override
  String get mfzFunctionOffTitle => 'Uit (OFF)';

  @override
  String get mfzFunctionOffTitleShort => 'Uit (OFF)';

  @override
  String get mfzMultiplierSecondsFloatingpoint => '0.1 seconden';

  @override
  String get mfzMultiplierMinutesFloatingpoint => '0.1 minuten';

  @override
  String get mfzMultiplierHoursFloatingpoint => '0.1 uren';

  @override
  String get mfzOverviewFunctionsloaded => 'Functies zijn geladen';

  @override
  String get mfzOverviewSaved => 'Functies zijn opgeslagen';

  @override
  String get settingsBluetoothHeader => 'Bluetooth';

  @override
  String get bluetoothChangedSuccessfully =>
      'Bluetooth settings zijn succesvol gewijzigd.';

  @override
  String get settingsBluetoothInformation =>
      'Nota : als deze instelling geactiveerd is, is het toestel voor iedereen permanent zichtbaar via Bluetooth!\nHet is aanbevolen om de PIN-code te wijzigen.';

  @override
  String get settingsBluetoothContinuousconnection =>
      'Permanente zichtbaarheid';

  @override
  String settingsBluetoothContinuousconnectionDescription(Object deviceType) {
    return 'Door permanente zichtbaarheid in te schakelen, blijft Bluetooth actief op het toestel ($deviceType) en moet deze niet handmatig geactiveerd worden voor het maken van verbinding.';
  }

  @override
  String get settingsBluetoothTimeout => 'Verbindingstijd verlopen';

  @override
  String get settingsBluetoothPinlimit => 'PIN limiet';

  @override
  String settingsBluetoothTimeoutDescription(Object timeout) {
    return 'De verbinding is verbroken na $timeout minuten van inactiviteit.';
  }

  @override
  String settingsBluetoothPinlimitDescription(Object attempts) {
    return 'Om veiligheidsredenen heeft u een maximum van $attempts aantal pogingen om de PIN in te voeren. Bluetooth wordt dan gedeactiveerd en moet manueel gereactiveerd worden voor een nieuwe verbinding.';
  }

  @override
  String get settingsBluetoothPinAttempts => 'pogingen';

  @override
  String get settingsResetfunctionHeader => 'Reset functies';

  @override
  String get settingsResetfunctionDialog =>
      'Wenst u werkelijk alle functies te resetten ?';

  @override
  String get settingsFactoryresetFunctionsConfirmationDescription =>
      'Alle functies werden succesvol gereset.';

  @override
  String get mfzFunctionTime => 'Tijd (t)';

  @override
  String get discoveryConnectionFailedInfo => 'Geen Bluetooth verbinding';

  @override
  String get detailsConfigurationDevicedisplaylockDialogtext =>
      'Onmiddellijk na het vergrendelen van de display van het toestel, wordt Bluetooth gedeactiveerd en moet dus manueel gereactiveerd worden om een nieuwe verbinding te maken.';

  @override
  String get detailsConfigurationDevicedisplaylockDialogquestion =>
      'Wenst u echt de display van het toestel te vergrendelen ?';

  @override
  String get settingsDemodevices => 'Toon demo toestellen';

  @override
  String get generalTextSettings => 'Settings';

  @override
  String get discoveryWifi => 'WiFi';

  @override
  String get settingsInformations => 'Informatie';

  @override
  String get detailsConfigurationDimmingbehavior => 'Dimgedrag';

  @override
  String get detailsConfigurationSwitchbehavior => 'Schakelgedrag';

  @override
  String get detailsConfigurationBrightness => 'Helderheid';

  @override
  String get detailsConfigurationMinimum => 'Minimale helderheid';

  @override
  String get detailsConfigurationMaximum => 'Maximale helderheid';

  @override
  String get detailsConfigurationSwitchesGr => 'Groepenrelais (GR)';

  @override
  String get detailsConfigurationSwitchesGs => 'Groepenschakelaar (GS)';

  @override
  String get detailsConfigurationSwitchesCloserer => 'Schakelrelais NO (ER)';

  @override
  String get detailsConfigurationSwitchesClosererDescription =>
      'Uit -> ingedrukt houden (Aan) -> loslaten (Uit)';

  @override
  String get detailsConfigurationSwitchesOpenerer =>
      'Schakelrelais NC (ER-invers)';

  @override
  String get detailsConfigurationSwitchesOpenererDescription =>
      'Aan-> ingedrukt houden (Uit) -> loslaten (Aan)';

  @override
  String get detailsConfigurationSwitchesSwitch => 'Schakelaar';

  @override
  String get detailsConfigurationSwitchesSwitchDescription =>
      'Met elke schakelaar wordt het licht aan- of uitgeschakeld';

  @override
  String get detailsConfigurationSwitchesImpulsswitch => 'Impulsschakelaar';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription =>
      'Schakelaar wordt kort ingedrukt en losgelaten om het licht aan of uit te doen';

  @override
  String get detailsConfigurationSwitchesClosererDescription2 =>
      'Houd de schakelaar ingedrukt. Bij loslaten, stopt de motor.';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription2 =>
      'Schakelaar wordt kort ingedrukt om de motor te starten en kort ingedrukt om hem weer te stoppen.';

  @override
  String get detailsConfigurationWifiloginScan => 'Scan QR-code';

  @override
  String get detailsConfigurationWifiloginScannotvalid =>
      'Gescande code is niet geldig';

  @override
  String get detailsConfigurationWifiloginDescription => 'Voer code in';

  @override
  String get detailsConfigurationWifiloginPassword => 'Paswoord';

  @override
  String get discoveryEsbipDescription => 'Rolluik- en jaloeziebediening IP';

  @override
  String get discoveryEsripDescription => 'Impulsschakelaar relais IP';

  @override
  String get discoveryEudipDescription => 'Universele dimmerschakelaar IP';

  @override
  String get generalTextLoad => 'Aan het opladen';

  @override
  String get wifiBasicautomationsNotFound => 'Geen automatisering gevonden.';

  @override
  String get wifiCodeInvalid => 'Code ongeldig';

  @override
  String get wifiCodeValid => 'Code geldig';

  @override
  String get wifiAuthorizationLogin => 'Verbind';

  @override
  String get wifiAuthorizationLoginFailed => 'Log in mislukt';

  @override
  String get wifiAuthorizationSerialnumber => 'Serienummer';

  @override
  String get wifiAuthorizationProductiondate => 'Fabricatiedatum';

  @override
  String get wifiAuthorizationProofofpossession => 'PoP';

  @override
  String get generalTextWifipassword => 'WiFi paswoord';

  @override
  String get generalTextUsername => 'Gebruikersnaam';

  @override
  String get generalTextEnter => 'OF VOER MANUEEL IN';

  @override
  String get wifiAuthorizationScan => 'Scan de ELTAKO-code.';

  @override
  String get detailsConfigurationDevicesNofunctionshinttext =>
      'Dit toestel ondersteunt momenteel geen andere instellingen';

  @override
  String get settingsUsedemodelay => 'Gebruik demo vertraging';

  @override
  String get settingsImpulsLoad => 'Impuls schakeltijd is geladen';

  @override
  String get settingsBluetoothLoad =>
      'Bluetooth instellingen worden opgeladen.';

  @override
  String get detailsConfigurationsectionLoad => 'Configuraties zijn opgeladen';

  @override
  String get generalTextLogin => 'Log in';

  @override
  String get generalTextAuthentication => 'Verifiëren';

  @override
  String get wifiAuthorizationScanDescription =>
      'Zoek op het WiFi-toestel of op de meegeleverde infokaart  de ELTAKO-code en scan die met uw camera.';

  @override
  String get wifiAuthorizationScanShort => 'Scan de ELTAKO-code';

  @override
  String get detailsConfigurationEdgemode => 'Dimcurve';

  @override
  String get detailsConfigurationEdgemodeLeadingedge => 'Fase-aansnijding';

  @override
  String get generalTextNetwork => 'Netwerk';

  @override
  String get wifiAuthenticationSuccessful => 'Verifiëren succesvol';

  @override
  String get detailsConfigurationsectionSavechange => 'Configuratie gewijzigd';

  @override
  String get discoveryWifiAdddevice => 'Voeg WiFi toestel toe';

  @override
  String get wifiAuthenticationDelay => 'Dit kan tot 1 minuut duren';

  @override
  String get generalTextRetry => 'Probeer opnieuw';

  @override
  String get wifiAuthenticationCredentials => 'Voer de gegevens van uw WiFi in';

  @override
  String get wifiAuthenticationSsid => 'SSID';

  @override
  String get wifiAuthenticationDelaylong =>
      'Het kan tot 1 minuut duren voordat het toestel klaar is en in de app verschijnt';

  @override
  String get wifiAuthenticationCredentialsShort => 'Voer WiFi gegevens in';

  @override
  String get wifiAuthenticationTeachin => 'Toestel inleren in de WiFi';

  @override
  String get wifiAuthenticationEstablish => 'Maak verbinding met het toestel';

  @override
  String wifiAuthenticationEstablishLong(Object ssid) {
    return 'Toestel maakt verbinding met WiFi $ssid';
  }

  @override
  String get wifiAuthenticationFailed =>
      'Verbinding mislukt. Onderbreek de stroom naar het toestel gedurende enkele seconden en probeer het opnieuw te verbinden';

  @override
  String get wifiAuthenticationReset => 'Reset verificatie';

  @override
  String get wifiAuthenticationResetHint =>
      'Alle verificatiegegevens werden gewist.';

  @override
  String get wifiAuthenticationInvaliddata =>
      'Gegevens verificatie niet geldig';

  @override
  String get wifiAuthenticationReauthenticate => 'Verifieer opnieuw';

  @override
  String get wifiAddhkdeviceHeader => 'Voeg toestel toe';

  @override
  String get wifiAddhkdeviceDescription =>
      'Verbind uw nieuw ELTAKO-toestel met uw WiFi via de Apple Home app.';

  @override
  String get wifiAddhkdeviceStep1 => '1. Open de Apple Home app.';

  @override
  String get wifiAddhkdeviceStep2 =>
      '2. Klik op de plus in de rechter bovenhoek van de app en selecteer **Add Device**.';

  @override
  String get wifiAddhkdeviceStep3 => '3. Volg de instructies van de app.';

  @override
  String get wifiAddhkdeviceStep4 =>
      '4. Nu kan uw toestel geconfigureerd worden in de ELTAKO-Connect app.';

  @override
  String get detailsConfigurationRuntime => 'Looptijd';

  @override
  String get detailsConfigurationRuntimeMode => 'Mode';

  @override
  String get generalTextManually => 'Manueel';

  @override
  String get detailsConfigurationRuntimeAutoDescription =>
      'De zonweringsactor bepaalt zelfstandig de looptijd van de zonweringsmotor bij elke beweging van de onderste naar de bovenste eindstand (aanbevolen).\nNa de eerste inbedrijfstelling of veranderingen, moet een dergelijke beweging zonder onderbreking van beneden naar boven uitgevoerd worden.';

  @override
  String get detailsConfigurationRuntimeManuallyDescription =>
      'De looptijd van de zonweringsmotor wordt handmatig ingesteld via onderstaande looptijd.\nControleer of de ingestelde looptijd overeenkomt met de werkelijke looptijd van uw zonweringsmotor. \nNa de eerste inbedrijfstelling of veranderingen, moet een dergelijke beweging zonder onderbreking van beneden naar boven uitgevoerd worden.';

  @override
  String get detailsConfigurationRuntimeDemoDescription =>
      'De LCD-display-modus is enkel beschikbaar via REST API';

  @override
  String get generalTextDemomodeActive => 'Demo mode actief';

  @override
  String get detailsConfigurationRuntimeDuration => 'Duur';

  @override
  String get detailsConfigurationSwitchesGs4 =>
      'Groepenschakelaar met tip-omkeerfunctie (GS4)';

  @override
  String get detailsConfigurationSwitchesGs4Description =>
      'Groepenschakelaar met tip-omkeerfunctie voor het bedienen van jaloezieën';

  @override
  String get screenshotSu12 => 'Buitenverlichting';

  @override
  String get screenshotS2U12 => 'Buitenverlichting';

  @override
  String get screenshotMfz12 => 'Pomp';

  @override
  String get screenshotEsr62 => 'Lamp';

  @override
  String get screenshotEud62 => 'Plafondlamp';

  @override
  String get screenshotEsb62 => 'Zonnewering balkon';

  @override
  String get detailsConfigurationEdgemodeLeadingedgeDescription =>
      'LC1-LC3 zijn comfortposities met verschillende dimcurves voor dimbare 230 V LED-lampen, die zich wegens hun constructie niet voldoende laten afdimmen in de positie AUTO (fase-afsnijding) en dus gedimd moeten worden in fase-aansnijding.';

  @override
  String get detailsConfigurationEdgemodeAutoDescription =>
      'AUTO laat dimming toe van alle types van lampen.';

  @override
  String get detailsConfigurationEdgemodeTrailingedge => 'Fase-afsnijding';

  @override
  String get detailsConfigurationEdgemodeTrailingedgeDescription =>
      'LC4-LC6 zijn comfortposities met verschillende dimcurves voor dimbare 230V LED-lampen.';

  @override
  String get updateHeader => 'Firmware update';

  @override
  String get updateTitleStepSearch => 'Zoeken voor een update';

  @override
  String get updateTitleStepFound => 'Update gevonden';

  @override
  String get updateTitleStepDownload => 'Downloaden van de update';

  @override
  String get updateTitleStepInstall => 'Installeren van de update';

  @override
  String get updateTitleStepSuccess => 'Update geslaagd';

  @override
  String get updateTitleStepUptodate => 'Is reeds geüpdated';

  @override
  String get updateTitleStepFailed => 'Update mislukt';

  @override
  String get updateButtonSearch => 'Zoek naar updates';

  @override
  String get updateButtonInstall => 'Installeer update';

  @override
  String get updateCurrentversion => 'Huidige versie';

  @override
  String get updateNewversion => 'Nieuwe firmware update beschikbaar';

  @override
  String get updateHintPower =>
      'De update start enkel wanneer de uitgang van het toestel niet actief is. Het toestel mag niet losgekoppeld worden van de voeding en de app mag niet worden afgesloten tijdens de update!';

  @override
  String get updateButton => 'Update';

  @override
  String get updateHintCompatibility =>
      'Een update is aanbevolen, anders zullen sommige functies in de app beperkt zijn.';

  @override
  String get generalTextDetails => 'Details';

  @override
  String get updateMessageStepMetadata => 'Update informatie opladen';

  @override
  String get updateMessageStepPrepare => 'Update wordt voorbereid';

  @override
  String get updateTitleStepUpdatesuccessful => 'Update wordt gecontroleerd';

  @override
  String get updateTextStepFailed =>
      'Jammer genoeg liep er iets fout tijdens de update, probeer het opnieuw binnen een paar minuten of wacht totdat uw toestel automatisch updatet (internetverbinding vereist).';

  @override
  String get configurationsNotavailable =>
      'Er zijn nog geen configuraties beschikbaar.';

  @override
  String get configurationsAddHint =>
      'Maak nieuwe configuraties aan door een verbinding te maken en sla ze op.';

  @override
  String get configurationsEdit => 'Wijzig configuratie';

  @override
  String get generalTextName => 'Naam';

  @override
  String get configurationsDelete => 'Wis configuratie';

  @override
  String configurationsDeleteHint(Object configName) {
    return 'Wenst u werkelijk de configuratie: $configName te wissen ?';
  }

  @override
  String get configurationsSave => 'Sla configuratie op';

  @override
  String get configurationsSaveHint =>
      'Hier kan u de configuratie van uw huidig toestel opslaan, of een reeds bestaande configuratie opladen.';

  @override
  String get configurationsImport => 'Importeer configuratie';

  @override
  String configurationsImportHint(Object configName) {
    return 'Wenst u werkelijk de configuratie $configName te transfereren ?';
  }

  @override
  String generalTextConfigurations(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Configurations',
      one: 'Configuration',
    );
    return '$_temp0';
  }

  @override
  String get configurationsStepPrepare => 'Configuratie wordt voorbereid';

  @override
  String get configurationsStepName => 'Voer een naam in voor de configuratie';

  @override
  String get configurationsStepSaving => 'Configuratie wordt opgeslagen';

  @override
  String get configurationsStepSavedsuccessfully =>
      'Configuratie was succesvol opgeslagen';

  @override
  String get configurationsStepSavingfailed => 'Opslaan configuratie mislukt';

  @override
  String get configurationsStepChoose => 'Kies een configuratie';

  @override
  String get configurationsStepImporting => 'Configuratie wordt geïmporteerd';

  @override
  String get configurationsStepImportedsuccessfully =>
      'Configuratie was succesvol geïmporteerd';

  @override
  String get configurationsStepImportingfailed =>
      'Importeren configuratie mislukt';

  @override
  String get discoveryAssuDescription =>
      'Buiten stopcontact schakelklok Bluetooth';

  @override
  String get settingsDatetimeDevicetime => 'Huidige toestel tijd';

  @override
  String get settingsDatetimeLoading => 'Instellingen tijd zijn opgeladen';

  @override
  String get discoveryEud12Description => 'Universele dimschakelaar Bluetooth';

  @override
  String get generalTextOffdelay => 'Afvalvertraging';

  @override
  String get generalTextRemainingbrightness => 'Resterende helderheid';

  @override
  String get generalTextSwitchonvalue => 'Inschakelwaarde';

  @override
  String get motionsensorTitleNoremainingbrightness =>
      'Geen resterende helderheid';

  @override
  String get motionsensorTitleAlwaysremainingbrightness =>
      'Met resterende helderheid';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogram =>
      'Restlichtsterkte via schakelprogramma';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogramandzea =>
      'Resthelderheid via ZE en ZA';

  @override
  String get motionsensorTitleNoremainingbrightnessauto =>
      'Geen resthelderheid (halfautomatisch)';

  @override
  String get generalTextMotionsensor => 'Bewegingsmelder';

  @override
  String get generalTextLightclock => 'Lichtwekker';

  @override
  String get generalTextSnoozeclock => 'Sluimerfunctie';

  @override
  String generalDescriptionLightclock(Object mode) {
    return 'Bij het inschakelen ($mode) wordt het licht na ongeveer 1 seconde op de laagste helderheid ingeschakeld en langzaam hooggedimd zonder het laatst opgeslagen helderheidsniveau te wijzigen.';
  }

  @override
  String generalDescriptionSnoozeclock(Object mode) {
    return 'Bij het uitschakelen ($mode) wordt de verlichting vanuit de huidige dimstand naar de minimale helderheid gedimd en uitgeschakeld. De verlichting kan op elk moment tijdens het dimproces worden uitgeschakeld door de knop kort in te drukken. Als u de knop lang indrukt tijdens het dimproces, wordt de verlichting gedimd en wordt de sluimerfunctie beëindigd.';
  }

  @override
  String get generalTextImmediately => 'Onmiddellijk';

  @override
  String get generalTextPercentage => 'Percentage';

  @override
  String get generalTextSwitchoffprewarning =>
      'Waarschuwing vooraf uitschakelen';

  @override
  String get generalDescriptionSwitchoffprewarning =>
      'Langzaam dimmen naar minimale helderheid';

  @override
  String get generalDescriptionOffdelay =>
      'Het apparaat wordt ingeschakeld wanneer de stuurspanning wordt toegepast. Als de stuurspanning wordt onderbroken, begint het tijdsverloop, waarna het apparaat uitschakelt. Tijdens het tijdsverloop kan het apparaat stroomafwaarts worden ingeschakeld.';

  @override
  String get generalDescriptionBrightness =>
      'De helderheid waarbij de lamp wordt ingeschakeld door de dimmer.';

  @override
  String get generalDescriptionRemainingbrightness =>
      'De dimwaarde in procenten tot waar de lamp wordt gedimd nadat de bewegingsdetector is uitgeschakeld.';

  @override
  String get generalDescriptionRuntime =>
      'Looptijd van de lichtalarmfunctie van minimale helderheid tot maximale helderheid.';

  @override
  String get generalTextUniversalbutton => 'Universele drukknop';

  @override
  String get generalTextDirectionalbutton => 'Richtingknop';

  @override
  String get eud12DescriptionAuto =>
      'Automatische detectie UT/RT (met richtingssensordiode RTD)';

  @override
  String get eud12DescriptionRt => 'met richtingsgevoelige diode RTD';

  @override
  String get generalTextProgram => 'Programma';

  @override
  String get eud12MotionsensorOff => 'Met bewegingsdetector ingesteld op Uit';

  @override
  String get eud12ClockmodeTitleProgramze => 'Programma en centrale op';

  @override
  String get eud12ClockmodeTitleProgramza => 'Programma en Centraal Uit';

  @override
  String get eud12ClockmodeTitleProgrambuttonon => 'Programma en UT/RT Aan';

  @override
  String get eud12ClockmodeTitleProgrambuttonoff => 'Programma en UT/RT Uit';

  @override
  String get eud12TiImpulseTitle => 'Impulstijd Aan (t1)';

  @override
  String get eud12TiImpulseHeader => 'Dimwaarde impulstijd Aan';

  @override
  String get eud12TiImpulseDescription =>
      'De dimwaarde in procenten tot waartoe de lamp wordt gedimd bij impulstijd Aan.';

  @override
  String get eud12TiOffTitle => 'Impulstijd Uit (t2)';

  @override
  String get eud12TiOffHeader => 'Dimwaarde impulstijd Uit';

  @override
  String get eud12TiOffDescription =>
      'De dimwaarde in procenten tot waartoe de lamp wordt gedimd bij impulstijd Uit.';

  @override
  String get generalTextButtonpermanentlight => 'Permanent drukknoplicht';

  @override
  String get generalDescriptionButtonpermanentlight =>
      'Instelling van het continu licht met drukknop van 0 tot 10 uur in stappen van 0,5 uur. Activering door de knop langer dan 1 seconde in te drukken (1x flikkeren), deactivering door de knop langer dan 2 seconden in te drukken.';

  @override
  String get generalTextNobuttonpermanentlight => 'Geen TSP';

  @override
  String get generalTextBasicsettings => 'Basisinstellingen';

  @override
  String get generalTextInputswitch => 'Lokale knopingang (A1)';

  @override
  String get generalTextOperationmode => 'Bedrijfsmodus';

  @override
  String get generalTextDimvalue => 'inschakelgedrag';

  @override
  String get eud12TitleUsememory => 'Geheugenwaarde gebruiken';

  @override
  String get eud12DescriptionUsememory =>
      'De geheugenwaarde komt overeen met de laatst ingestelde dimwaarde. Als de geheugenwaarde is gedeactiveerd, wordt het dimmen altijd ingesteld op de inschakelwaarde.';

  @override
  String get generalTextStartup => 'Helderheid inschakelen';

  @override
  String get generalDescriptionSwitchonvalue =>
      'De inschakelwaarde is een instelbare helderheidswaarde die een veilige inschakeling garandeert.';

  @override
  String get generalTitleSwitchontime => 'Inschakeltijd';

  @override
  String get generalDescriptionSwitchontime =>
      'Nadat de inschakeltijd is verstreken, wordt de lamp gedimd van de inschakelwaarde naar de geheugenwaarde.';

  @override
  String get generalDescriptionStartup =>
      'Sommige LED-lampen hebben een hogere inschakelstroom nodig om betrouwbaar in te schakelen. De lamp wordt bij deze inschakelwaarde ingeschakeld en na de inschakeltijd gedimd naar de geheugenwaarde.';

  @override
  String get eud12ClockmodeSubtitleProgramze => 'Klik kort op Centraal Aan';

  @override
  String get eud12ClockmodeSubtitleProgramza => 'Korte klik op centraal uit';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonon =>
      'Dubbelklik op universele knop/richtingsknop Aan';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonoff =>
      'Dubbelklik op universele knop/richtingsknop Uit';

  @override
  String get eud12FunctionStairlighttimeswitchTitleShort =>
      'TLZ | Verlichtingstimer trap';

  @override
  String get eud12FunctionMinTitleShort => 'MIN';

  @override
  String get eud12FunctionMmxTitleShort => 'MMX';

  @override
  String get eud12FunctionTiDescription =>
      'Timer met instelbare in- en uitschakeltijd van 0,5 seconden tot 9,9 minuten. De helderheid kan worden ingesteld van minimale helderheid tot maximale helderheid.';

  @override
  String get eud12FunctionAutoDescription =>
      'Universele dimschakelaar met instelling voor bewegingsmelder, lichtalarm en sluimerfunctie.';

  @override
  String get eud12FunctionErDescription =>
      'Door relais te schakelen kan de helderheid worden ingesteld van minimale helderheid tot maximale helderheid.';

  @override
  String get eud12FunctionEsvDescription =>
      'Universele dimschakelaar met instelling van een uitschakelvertraging van 1 tot 120 minuten. Uitschakelwaarschuwing op het einde door dimmen instelbaar en instelbaar van 1 tot 3 minuten. Beide centrale ingangen actief.';

  @override
  String get eud12FunctionTlzDescription =>
      'Instellen van de verlichtingsduur van de knop van 0 tot 10 uur in stappen van 0,5 uur. Activering door de knop langer dan 1 seconde in te drukken (1x flikkeren), deactivering door de knop langer dan 2 seconden in te drukken.';

  @override
  String get eud12FunctionMinDescription =>
      'Universele dimschakelaar, schakelt naar de ingestelde minimale helderheid wanneer de stuurspanning wordt toegepast. Het licht wordt gedimd tot de maximale helderheid binnen de ingestelde dimtijd van 1 tot 120 minuten. Als de stuurspanning wordt verwijderd, wordt het licht onmiddellijk uitgeschakeld, zelfs tijdens de dimtijd. Beide centrale ingangen actief.';

  @override
  String get eud12FunctionMmxDescription =>
      'Universele dimschakelaar, schakelt naar de ingestelde minimale helderheid wanneer de stuurspanning wordt toegepast. Tijdens de ingestelde dimtijd van 1 tot 120 minuten wordt het licht gedimd tot de maximale helderheid. Wanneer de stuurspanning echter wordt verwijderd, dimt de dimmer tot de ingestelde minimale helderheid. Daarna wordt hij uitgeschakeld. Beide centrale ingangen actief.';

  @override
  String get motionsensorSubtitleNoremainingbrightness =>
      'Met bewegingsdetector ingesteld op Uit';

  @override
  String get motionsensorSubtitleAlwaysremainingbrightness =>
      'Met bewegingsdetector ingesteld op Uit';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogram =>
      'Schakelprogramma geactiveerd en gedeactiveerd met BWM-Uit';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogramandzea =>
      'Centraal AAN activeert de bewegingssensor, Centraal UIT deactiveert de bewegingssensor, maar ook door het schakelen van het programma';

  @override
  String get motionsensorSubtitleNoremainingbrightnessauto =>
      'Bewegingsdetector schakelt alleen uit';

  @override
  String get detailsDimsectionHeader => 'Dimmen';

  @override
  String get generalTextFast => 'Snel';

  @override
  String get generalTextSlow => 'Langzaam';

  @override
  String get eud12TextDimspeed => 'Dimsnelheid';

  @override
  String get eud12TextSwitchonspeed => 'Inschakelsnelheid';

  @override
  String get eud12TextSwitchoffspeed => 'Uitschakelsnelheid';

  @override
  String get eud12DescriptionDimspeed =>
      'De dimsnelheid is de snelheid waarmee de dimmer dimt van de huidige helderheid naar de gewenste helderheid.';

  @override
  String get eud12DescriptionSwitchonspeed =>
      'De inschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig in te schakelen.';

  @override
  String get eud12DescriptionSwitchoffspeed =>
      'De uitschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig uit te schakelen.';

  @override
  String get settingsFactoryresetResetdimHeader => 'Diminstellingen resetten';

  @override
  String get settingsFactoryresetResetdimDescription =>
      'Moeten echt alle diminstellingen worden gereset?';

  @override
  String get settingsFactoryresetResetdimConfirmationDescription =>
      'De diminstellingen zijn gereset';

  @override
  String get eud12TextSwitchonoffspeed => 'Aan/uit-schakelsnelheid';

  @override
  String get eud12DescriptionSwitchonoffspeed =>
      'De in-/uitschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig in of uit te schakelen.';

  @override
  String get timerDetailsDimtoval => 'Aan met dimwaarde in %';

  @override
  String get timerDetailsDimtovalDescription =>
      'De dimmer schakelt altijd in met de vaste dimwaarde in %.';

  @override
  String timerDetailsDimtovalSubtitle(Object brightness) {
    return 'Inschakelen met $brightness%';
  }

  @override
  String get timerDetailsDimtomem => 'Aan met geheugenwaarde';

  @override
  String get timerDetailsDimtomemSubtitle => 'Inschakelen met geheugenwaarde';

  @override
  String get timerDetailsMotionsensorwithremainingbrightness =>
      'Restlichtsterkte (BWM) Aan';

  @override
  String get timerDetailsMotionsensornoremainingbrightness =>
      'Restlichtsterkte (BWM) Uit';

  @override
  String get settingsRandommodeHint =>
      'Bij geactiveerde toevalsmodus worden alle programma\'s van dit kanaal willekeurig tot 15 minuten verschoven. On-timers worden vooraf verschoven, Off-timers worden vertraagd.';

  @override
  String get runtimeOffsetDescription =>
      'Bijkomende nalooptijd, na afloop van de tijd. Die kan gebruikt worden om zeker het eindpunt te bereiken.';

  @override
  String get loadingTextDimvalue => 'Dimwaarde wordt geladen';

  @override
  String get discoveryEudipmDescription =>
      'Universele dimmerschakelaar IP Matter';

  @override
  String get generalTextOffset => 'Naloop';

  @override
  String get eud12DimvalueTestText => 'Helderheid verzenden';

  @override
  String get eud12DimvalueTestDescription =>
      'Tijdens het testen wordt rekening gehouden met de huidige ingestelde dimsnelheid.';

  @override
  String get eud12DimvalueLoadText => 'Helderheid laden';

  @override
  String get settingsDatetimeNotime =>
      'De datum- en tijdinstellingen moeten worden uitgelezen via het scherm van het apparaat.';

  @override
  String get generalMatterText => 'Matter';

  @override
  String get generalMatterMessage =>
      'Leer je Matter-apparaat in met één van de volgende apps.';

  @override
  String get generalMatterOpengooglehome => 'Open Google Home';

  @override
  String get generalMatterOpenamazonalexa => 'Open Amazon Alexa';

  @override
  String get generalMatterOpensmartthings => 'Open SmartThings';

  @override
  String generalLabelProgram(Object number) {
    return 'Programma $number';
  }

  @override
  String get generalTextDone => 'Klaar';

  @override
  String get settingsRandommodeDescriptionShort =>
      'Bij ingeschakelde toevalsmodus worden alle schakeltijden van het kanaal toevallig verschoven. Bij inschakeltijden tot 15 minuten eerder en uitschakeltijden tot 15 minuten later.';

  @override
  String get all => 'Alle';

  @override
  String get discoveryBluetooth => 'Bluetooth';

  @override
  String get success => 'Gelukt';

  @override
  String get error => 'Fout';

  @override
  String get timeProgramAdd => 'Tijdprogramma toevoegen';

  @override
  String get noConnection => 'Geen verbinding';

  @override
  String get timeProgramOnlyActive => 'Geconfigureerde programma\'s';

  @override
  String get timeProgramAll => 'Alle programma\'s';

  @override
  String get active => 'Actief';

  @override
  String get inactive => 'Non actief';

  @override
  String timeProgramSaved(Object number) {
    return 'Programma $number opgeslaan';
  }

  @override
  String get deviceLanguageSaved => 'Taal toestel opgeslaan';

  @override
  String generalTextTimeShort(Object time) {
    return '$time uur';
  }

  @override
  String programDeleteHint(Object index) {
    return 'Moet programma $index echt verwijderd worden?';
  }

  @override
  String milliseconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'milliseconde',
      one: 'milliseconde',
    );
    return '$_temp0';
  }

  @override
  String millisecondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count milliseconden',
      one: '$count milliseconde',
    );
    return '$_temp0';
  }

  @override
  String secondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count seconde',
      one: '$count seconde',
    );
    return '$_temp0';
  }

  @override
  String minutesWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count minuten',
      one: '$count minuut',
    );
    return '$_temp0';
  }

  @override
  String hoursWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count uren',
      one: '$count uur',
    );
    return '$_temp0';
  }

  @override
  String get settingsPinFailEmpty => 'De PIN mag niet leeg zijn.';

  @override
  String get detailsConfigurationWifiloginScanNoMatch =>
      'De gescande code past niet bij het toestel.';

  @override
  String get wifiAuthorizationPopIsEmpty => 'PoP mag niet leeg zijn.';

  @override
  String get wifiAuthenticationCredentialsHint =>
      'Omdat de app geen toegang heeft tot uw privaat WLAN-paswoord, kan uw ingave niet op juistheid gecontroleerd worden. Indien er geen verbinding ontstaan is, dient u het paswoord te controleren en opnieuw in te voeren.';

  @override
  String get generalMatterOpenApplehome => 'Apple Home openen';

  @override
  String get timeProgramNoActive => 'Geen geconfigureerde programma\'s.';

  @override
  String get timeProgramNoEmpty => 'Geen vrij tijdprogramma beschikbaar.';

  @override
  String get nameOfConfiguration => 'Naam van de configuratie';

  @override
  String get currentDevice => 'Huidig toestel';

  @override
  String get export => 'Exporteren';

  @override
  String get import => 'Importeren';

  @override
  String get savedConfigurations => 'Opgeslagen configuratie';

  @override
  String get importableServicesLabel =>
      'Volgende instellingen kunnen geïmporteerd worden:';

  @override
  String get notImportableServicesLabel => 'Ongeldige instellingen';

  @override
  String get deviceCategoryMeterGateway => 'Teller-gateway';

  @override
  String get deviceCategory2ChannelTimeSwitch =>
      '2-kanaals tijdschakelklok Bluetooth';

  @override
  String get devicategoryOutdoorTimeSwitchBluetooth =>
      'Buitenstekker tijdschakelklok Bluetooth';

  @override
  String get settingsModbusHeader => 'Modbus';

  @override
  String get settingsModbusDescription =>
      'Pas de baudrate, prioriteit en timeout aan, om de overdrachtsnelheid, foutherkenning en wachttijd te configureren.';

  @override
  String get settingsModbusRTU => 'Modbus RTU';

  @override
  String get settingsModbusBaudrate => 'Baudrate';

  @override
  String get settingsModbusParity => 'Prioriteit';

  @override
  String get settingsModbusTimeout => 'Modbus timeout';

  @override
  String get locationServiceDisabled => 'Standplaats is gedeactiveerd';

  @override
  String get locationPermissionDenied =>
      'Geef uw locatie-autorisatie toestemming om uw huidige positie op te halen.';

  @override
  String get locationPermissionDeniedPermanently =>
      'De toestemming van uw locatie is continu geblokkeerd. Gelieve de toestemming van uw standplaats in uw toestelinstellingen toe te staan om uw actuele positie op te vragen.';

  @override
  String get lastSync => 'laatst gesynchroniseerd';

  @override
  String get dhcpActive => 'DHCP actief';

  @override
  String get ipAddress => 'IP';

  @override
  String get subnetMask => 'Subnetmasker';

  @override
  String get standardGateway => 'Standaard gateway';

  @override
  String get dns => 'DNS';

  @override
  String get alternateDNS => 'Alternatieve DNS';

  @override
  String get errorNoNetworksFound => 'Geen WiFi netwerk gevonden';

  @override
  String get availableNetworks => 'Beschikbare netwerken';

  @override
  String get enableWifiInterface => 'WiFi interface activeren';

  @override
  String get enableLANInterface => 'LAN interface activeren';

  @override
  String get hintDontDisableAllInterfaces =>
      'Let erop, dat niet alle interfaces gedeactiveerd zijn.\nDe laatst geactiveerde interface heeft prioriteit.';

  @override
  String get ssid => 'SSID';

  @override
  String get searchNetworks => 'WiFi netwerken zoeken';

  @override
  String get errorNoNetworkEnabled =>
      'Minstens één interface moet geactiveerd zijn.';

  @override
  String get errorActiveNetworkInvalid =>
      'Niet alle actieve stations zijn geldig.';

  @override
  String get invalidNetworkConfiguration => 'Ongeldige netwerkconfiguratie';

  @override
  String get generalDefault => 'Standaard';

  @override
  String get mqttHeader => 'MQTT';

  @override
  String get mqttConnected => 'Verbinding met MQTT-broker';

  @override
  String get mqttDisconnected => 'Geen verbinding met MQTT-broker.';

  @override
  String get mqttBrokerURI => 'Broker URI';

  @override
  String get mqttBrokerURIHint => 'MQTT-Broker URI';

  @override
  String get mqttPort => 'Poort';

  @override
  String get mqttPortHint => 'MQTT-poort';

  @override
  String get mqttClientId => 'Client-ID';

  @override
  String get mqttClientIdHint => 'MQTT Client-ID';

  @override
  String get mqttUsername => 'Gebruikersnaam';

  @override
  String get mqttUsernameHint => 'MQTT-Gebruikersnaam';

  @override
  String get mqttPassword => 'Paswoord';

  @override
  String get mqttPasswordHint => 'MQTT-paswoord';

  @override
  String get mqttCertificate => 'Certificaat (optioneel)';

  @override
  String get mqttCertificateHint => 'MQTT-certificaat';

  @override
  String get mqttTopic => 'Topic';

  @override
  String get mqttTopicHint => 'MQTT-topic';

  @override
  String get electricityMeter => 'Energieteller';

  @override
  String get electricityMeterCurrent => 'Huidig';

  @override
  String get electricityMeterHistory => 'Verloop';

  @override
  String get electricityMeterReading => 'Tellerstand';

  @override
  String get connectivity => 'Verbinding';

  @override
  String electricMeter(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'energiemeters',
      one: 'energiemeter',
    );
    return '$_temp0';
  }

  @override
  String get discoveryZGW16Description => 'Modbus-energiemeter-MQTT-gateway';

  @override
  String get bluetoothConnectionLost => 'Bluetooth verbinding verloren.';

  @override
  String get bluetoothConnectionLostDescription =>
      'De Bluetooth verbinding met het toestel werd verbroken. Het toestel dient binnen het bereik te zijn.';

  @override
  String get openBluetoothSettings => 'Instellingen openen';

  @override
  String get password => 'Paswoord';

  @override
  String get setInitialPassword => 'Initieel paswoord toewijzen';

  @override
  String initialPasswordMinimumLength(Object length) {
    return 'Het paswoord moet minstens $length karakters lang zijn.';
  }

  @override
  String get repeatPassword => 'Paswoord herhalen';

  @override
  String get passwordsDoNotMatch => 'De paswoorden stemmen niet overeen.';

  @override
  String get savePassword => 'Paswoord opslaan';

  @override
  String get savePasswordHint =>
      'Het paswoord wordt voor toekomstige aanmeldingen opgeslaan op uw toestel.';

  @override
  String get retrieveNtpServer => 'Haal tijd op van de NTP-server';

  @override
  String get retrieveNtpServerFailed =>
      'De verbinding met de NTP-server kon niet gemaakt worden.';

  @override
  String get retrieveNtpServerSuccess =>
      'De verbinding met de NTP-server was geslaagd.';

  @override
  String get settingsPasswordNewPasswordDescription => 'Nieuw paswoord ingeven';

  @override
  String get settingsPasswordConfirmationDescription =>
      'Wijziging paswoord geslaagd';

  @override
  String get dhcpRangeStart => 'DHCP startbereik';

  @override
  String get dhcpRangeEnd => 'DHCP eindbereik';

  @override
  String get forwardOnMQTT => 'Doorsturen van MQTT';

  @override
  String get showAll => 'Alles aanduiden';

  @override
  String get hide => 'Verbergen';

  @override
  String get changeToAPMode => 'Wisselen in de AP-modus';

  @override
  String get changeToAPModeDescription =>
      'U staat op het punt om uw toestel met een WiFi netwerk te verbinden. In dit geval wordt de verbinding naar het toestel gescheiden en dient u zich opnieuw over het geconfigureerde netwerk met uw toestel te verbinden.';

  @override
  String get consumption => 'Verbruik';

  @override
  String get currentDay => 'Vandaag';

  @override
  String get twoWeeks => '2 weken';

  @override
  String get oneYear => '1 jaar';

  @override
  String get threeYears => '3 jaar';

  @override
  String passwordMinLength(Object length) {
    return 'Het paswoord moet minstens $length karakters lang zijn.';
  }

  @override
  String get passwordNeedsLetter =>
      'Het paswoord moet minstens één hoofdletter bevatten.';

  @override
  String get passwordNeedsNumber =>
      'Het paswoord moet minstens één cijfer bevatten.';

  @override
  String get portEmpty => 'Poort mag niet leeg zijn';

  @override
  String get portInvalid => 'Ongeldige poort';

  @override
  String portOutOfRange(Object rangeEnd, Object rangeStart) {
    return 'De poort moet tussen $rangeStart en $rangeEnd zijn.';
  }

  @override
  String get ipAddressEmpty => 'IP-adres mag niet leeg zijn.';

  @override
  String get ipAddressInvalid => 'Ongeldig IP-adres';

  @override
  String get subnetMaskEmpty => 'Subnetmasker mag niet leeg zijn.';

  @override
  String get subnetMaskInvalid => 'Ongeldig subnetmasker';

  @override
  String get gatewayEmpty => 'Gateway mag niet leeg zijn.';

  @override
  String get gatewayInvalid => 'Ongeldig gateway';

  @override
  String get dnsEmpty => 'DNS mag niet leeg zijn.';

  @override
  String get dnsInvalid => 'Ongeldige DNS';

  @override
  String get uriEmpty => 'URI mag niet leeg zijn.';

  @override
  String get uriInvalid => 'Ongeldige URI';

  @override
  String get electricityMeterChangedSuccessfully =>
      'Energieteller werd succesvol gewijzigd.';

  @override
  String get networkChangedSuccessfully =>
      'Netwerkconfiguratie werd succesvol gewijzigd.';

  @override
  String get mqttChangedSuccessfully =>
      'MQTT-configuratie werd succesvol gewijzigd.';

  @override
  String get modbusChangedSuccessfully =>
      'Modbus instellingen werden succesvol gewijzigd.';

  @override
  String get loginData => 'Login data wissen';

  @override
  String get valueConfigured => 'Geconfigureerd';

  @override
  String get electricityMeterHistoryNoData => 'Geen data beschikbaar';

  @override
  String get locationChangedSuccessfully =>
      'Standplaats werd succesvol gewijzigd.';

  @override
  String get settingsNameFailEmpty => 'Naam mag niet leeg zijn.';

  @override
  String settingsNameFailLength(Object length) {
    return 'Naam moet langer zijn dan $length karakters.';
  }

  @override
  String get solsticeChangedSuccesfully =>
      'Tijdsverschil tijdens de zonnewende werd succesvol gewijzigd.';

  @override
  String get relayFunctionChangedSuccesfully =>
      'Relaisfunctie werd succesvol gewijzigd.';

  @override
  String get relayFunctionHeader => 'Relaisfunctie';

  @override
  String get dimmerValueChangedSuccesfully =>
      'Inschakelgedrag werd succesvol gewijzigd.';

  @override
  String get dimmerBehaviourChangedSuccesfully =>
      'Dimgedrag werd succesvol gewijzigd.';

  @override
  String get dimmerBrightnessDescription =>
      'De minimum en maximum helderheid heeft betrekking op alle instelbare helderheden van de dimmer.';

  @override
  String get dimmerSettingsChangedSuccesfully =>
      'Basisinstellingen werden succesvol gewijzigd.';

  @override
  String get liveUpdateEnabled => 'Live test geactiveerd';

  @override
  String get liveUpdateDisabled => 'Live test gedeactiveerd';

  @override
  String get liveUpdateDescription =>
      'De laatst gewijzigde schuifwaarde wordt naar het toestel gestuurd.';

  @override
  String get demoDevices => 'Demo toestel';

  @override
  String get showDemoDevices => 'Demo toestel aanduiden';

  @override
  String get deviceCategoryTimeSwitch => 'Tijdschakelklok';

  @override
  String get deviceCategoryMultifunctionalRelay => 'Multifunctie-tijdrelais';

  @override
  String get deviceCategoryDimmer => 'Dimmer';

  @override
  String get deviceCategoryShutter => 'Actor voor rolluiken en zonneweringen';

  @override
  String get deviceCategoryRelay => 'Relais';

  @override
  String get search => 'Zoeken';

  @override
  String get configurationsHeader => 'Configuraties';

  @override
  String get configurationsDescription =>
      'Hier kan u uw opgeslagen configuraties beheren.';

  @override
  String get configurationsNameFailEmpty =>
      'Naam van de configuratie mag niet leeg zijn.';

  @override
  String get configurationDeleted => 'Configuratie gewist';

  @override
  String codeFound(Object codeType) {
    return '$codeType code herkend';
  }

  @override
  String get errorCameraPermission =>
      'Gelieve toegang tot uw camera te geven om de ELTAKO-code te scannen.';

  @override
  String get authorizationSuccessful => 'Succesvol toegestaan aan het toestel';

  @override
  String get wifiAuthenticationResetConfirmationDescription =>
      'Authentificatie werd succesvol teruggezet.';

  @override
  String get settingsResetConnectionHeader => 'Verbinding terugzetten';

  @override
  String get settingsResetConnectionDescription =>
      'Moet de verbinding werkelijk teruggezet worden?';

  @override
  String get settingsResetConnectionConfirmationDescription =>
      'Verbinding werd succesvol teruggezet.';

  @override
  String get wiredInputChangedSuccesfully =>
      'Drukknopgedrag werd succesvol gewijzigd.';

  @override
  String get runtimeChangedSuccesfully => 'Looptijd werd succesvol gewijzigd.';

  @override
  String get expertModeActivated => 'Experten-modus geactiveerd';

  @override
  String get expertModeDeactivated => 'Experten-modus gedeactiveerd';

  @override
  String get license => 'Licenties';

  @override
  String get retry => 'Probeer opnieuw';

  @override
  String get provisioningConnectingHint =>
      'Verbinding wordt gemaakt met het toestel. Dit kan tot 1 minuut duren.';

  @override
  String get serialnumberEmpty => 'Serienummer mag niet leeg zijn.';

  @override
  String get interfaceStateInactiveDescriptionBLE =>
      'Bluetooth is gedeactiveerd. Gelieve het te activeren om het Bluetooth toestel te vinden.';

  @override
  String get interfaceStateDeniedDescriptionBLE =>
      'Bluetooth-machtigingen zijn niet verleend';

  @override
  String get interfaceStatePermanentDeniedDescriptionBLE =>
      'Bluetooth-machtigingen zijn niet verleend. Gelieve Bluetooth-verbindingen toe te staan in de instellingen van uw toestel.';

  @override
  String get requestPermission => 'Vraag machtiging aan';

  @override
  String get goToSettings => 'Naar de instellingen';

  @override
  String get enableBluetooth => 'Bluetooth activeren';

  @override
  String get installed => 'Geïnstalleerd';

  @override
  String teachInDialogDescription(Object type) {
    return 'Wenst u uw toestel via $type in te leren?';
  }

  @override
  String get useMatter => 'Matter gebruiken';

  @override
  String get relayMode => 'Relaismodus activeren';

  @override
  String get whatsNew => 'Nieuw in deze versie';

  @override
  String get migrationHint =>
      'Opdat u uw nieuwe functies zou kunnen aanwenden, dienen wij uw data te migreren.';

  @override
  String get migrationHeader => 'Migratie';

  @override
  String get migrationProgress => 'Wij ruimen op...';

  @override
  String get letsGo => 'We zijn ermee bezig';

  @override
  String get noDevicesFound =>
      'Geen toestellen gevonden. Gelieve te controleren of uw toestel in de Pairing-Modus is.';

  @override
  String get interfaceStateEmpty => 'Geen toestellen gevonden';

  @override
  String get ssidEmpty => 'SSID mag niet leeg zijn';

  @override
  String get passwordEmpty => 'Paswoord mag niet leeg zijn';

  @override
  String get settingsDeleteSettingsHeader => 'Instellingen terugplaatsen';

  @override
  String get settingsDeleteSettingsDescription =>
      'Moeten echt alle instellingen teruggeplaatst worden?';

  @override
  String get settingsDeleteSettingsConfirmationDescription =>
      'Alle instellingen werden succesvol teruggeplaatst.';

  @override
  String get locationNotFound => 'Standplaats niet gevonden.';

  @override
  String get timerProgramEmptySaveHint =>
      'Het tijdprogramma is leeg en kan niet opgeslagen worden. Bewerking beëindigen?';

  @override
  String get timerProgramDaysEmptySaveHint =>
      'Geen dagen gekozen. Het tijdprogramma toch opslaan?';

  @override
  String get timeProgramNoDays =>
      'Een programma zonder actieve dagen kan niet geactiveerd worden.';

  @override
  String timeProgramColliding(Object program) {
    return 'Tijdprogramma botst met programma $program.';
  }

  @override
  String timeProgramDuplicated(Object program) {
    return 'Tijdprogramma is een duplicaat van programma $program.';
  }

  @override
  String get screenshotZgw16 => 'Vrijstaand huis';

  @override
  String get interfaceStateUnknown => 'Geen toestellen gevonden';

  @override
  String get settingsPinChange => 'Wijzig PIN';

  @override
  String get timeProgrammOneTime => 'Eenmalig';

  @override
  String get timeProgrammRepeating => 'Herhalend';

  @override
  String get generalIgnore => 'Negeren';

  @override
  String get timeProgramChooseDay => 'Dag kiezen';

  @override
  String get generalToday => 'Vandaag';

  @override
  String get generalTomorrow => 'Morgen';

  @override
  String get bluetoothAndPINChangedSuccessfully =>
      'Bluetooth en PIN succesvol gewijzigd';

  @override
  String get generalTextDimTime => 'Dimtijd';

  @override
  String get discoverySu62Description => '1-kanaals schakelklok Bluetooth';

  @override
  String get bluetoothAlwaysOnTitle => 'Continu aan';

  @override
  String get bluetoothAlwaysOnDescription =>
      'Bluetooth is continu geactiveerd.';

  @override
  String get bluetoothAlwaysOnHint =>
      'Opgelet: indien deze instelling geactiveerd is, is dit toestel via Bluetooth permanent zichtbaar voor iedereen! Het is aanbevolen om de standaard PIN-code te veranderen.';

  @override
  String get bluetoothManualStartupOnTitle => 'Tijdelijk aan';

  @override
  String get bluetoothManualStartupOnDescription =>
      'Na het aanleggen van de spanning wordt Bluetooth voor 3 minuten geactiveerd.';

  @override
  String get bluetoothManualStartupOnHint =>
      'Opgelet: Pairing standby wordt gedurende 3 minuten geactiveerd en schakelt dan uit. Als er een nieuwe verbinding tot stand moet gebracht worden, dient men de knop ongeveer 5 seconden ingedrukt te houden.';

  @override
  String get bluetoothManualStartupOffTitle => 'Manueel aan';

  @override
  String get bluetoothManualStartupOffDescription =>
      'Bluetooth wordt manueel geactiveerd via de knopingang en is dan 3 minuten actief.';

  @override
  String get bluetoothManualStartupOffHint =>
      'Opgelet: om Bluetooth te activeren, moet de deukknop aan de knopingang voor ca. 5 seconden ingedrukt blijven.';

  @override
  String get timeProgrammOneTimeRepeatingDescription =>
      'Programma\'s kunnen herhaaldelijk worden uitgevoerd door altijd een schakeling uit te voeren op de geconfigureerde dagen en tijden, of ze kunnen slechts eenmaal worden uitgevoerd op de geconfigureerde schakeltijd.';

  @override
  String versionHeader(Object version) {
    return 'Versie $version';
  }

  @override
  String get releaseNotesHeader => 'Release opmerkingen';

  @override
  String get release30Header => 'De nieuwe ELTAKO Connect-app is uit!';

  @override
  String get release30FeatureDesignHeader => 'Nieuwe design';

  @override
  String get release30FeatureDesignDescription =>
      'De app werd compleet herwerkt en schittert in een nieuwe design. De bediening is nu nog gemakkelijker en intuïtiever.';

  @override
  String get release30FeaturePerformanceHeader => 'Betere prestaties';

  @override
  String get release30FeaturePerformanceDescription =>
      'Geniet van een soepelere ervaring en verkorte oplaadtijden - voor een betere gebruiksbeleving.';

  @override
  String get release30FeatureConfigurationHeader =>
      'Apparaatoverkoepelende configuraties';

  @override
  String get release30FeatureConfigurationDescription =>
      'Laad toestelconfiguraties op en breng ze over op andere toestellen. Zelfs als ze niet dezelfde hardware hebben, kan u vb. de configuratie van uw S2U12DBT1+1-UC  overhevelen op een ASSU-BT en omgekeerd.';

  @override
  String get release31Header =>
      'Vanaf heden de nieuwe 1-kanaals inbouwschakelklok met Bluetooth!';

  @override
  String get release31Description => 'Wat kan de SU62PF-BT/UC?';

  @override
  String get release31SU62Name => 'SU62PF-BT/UC';

  @override
  String get release31DeviceNote1 => 'Tot 60 schakelprogramma\'s.';

  @override
  String get release31DeviceNote2 =>
      'De klok kan toestellen schakelen volgens vaste tijden of met de astro-functie gebaseerd op de zonsopgang en zonsondergang.';

  @override
  String get release31DeviceNote3 =>
      'Toevalsmodus: schakeltijden kunnen toevallig tot wel 15 minuten verschoven worden.';

  @override
  String get release31DeviceNote4 =>
      'Zomer-/wintertijd: de klok schakelt automatisch op zomer- en wintertijd.';

  @override
  String get release31DeviceNote5 =>
      'Universele voedings- en stuurspanning 12-230V UC.';

  @override
  String get release31DeviceNote6 =>
      'Ingang met drukknop voor handmatig schakelen.';

  @override
  String get release31DeviceNote7 => '1 NO potentiaalvrij 10 A/250 V AC.';

  @override
  String get release31DeviceNote8 => 'Eenmalig uitvoeren van tijdprogramma\'s.';

  @override
  String get generalNew => 'Nieuw';

  @override
  String yearsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'voor $count jaren',
      one: 'vorig jaar',
    );
    return '$_temp0';
  }

  @override
  String monthsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'voor $count maanden',
      one: 'vorige maand',
    );
    return '$_temp0';
  }

  @override
  String weeksAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'voor $count weken',
      one: 'vorige week',
    );
    return '$_temp0';
  }

  @override
  String daysAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'voor $count dagen',
      one: 'gisteren',
    );
    return '$_temp0';
  }

  @override
  String minutesAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'voor $count minuten',
      one: 'voor één minuut',
    );
    return '$_temp0';
  }

  @override
  String hoursAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'voor $count uren',
      one: 'voor één uur',
    );
    return '$_temp0';
  }

  @override
  String secondsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'voor $count seconden',
      one: 'voor één seconde',
    );
    return '$_temp0';
  }

  @override
  String get justNow => 'Zojuist';

  @override
  String get discoveryEsripmDescription => 'Teleruptor-schakelrelais IP Matter';

  @override
  String get generalTextKidsRoom => 'Kinderkamerschakeling';

  @override
  String generalDescriptionKidsRoom(Object mode) {
    return 'Door iets langer op de drukknop te duwen ($mode), gaat de verlichting pas na ongeveer 1 seconde op zijn minimumwaarde oplichten en gaat, zo lang men blijft duwen, geleidelijk aan feller branden, zonder dat de eerder ingestelde lichtintensiteit veranderd wordt.';
  }

  @override
  String get generalTextSceneButton => 'Sfeerdrukknop';

  @override
  String get settingsEnOceanConfigHeader => 'EnOcean configuratie';

  @override
  String get enOceanConfigChangedSuccessfully =>
      'EnOcean configuratie werd succesvol gewijzigd.';

  @override
  String get activateEnOceanRepeater => 'EnOcean repeater activeren';

  @override
  String get enOceanRepeaterLevel => 'Repeaterniveau';

  @override
  String get enOceanRepeaterLevel1 => '1-niveau';

  @override
  String get enOceanRepeaterLevel2 => '2-niveau';

  @override
  String get enOceanRepeaterOffDescription =>
      'Er worden geen radiosignalen van sensoren herhaald.';

  @override
  String get enOceanRepeaterLevel1Description =>
      'Enkel de radiosignalen van sensoren worden ontvangen, gecontroleerd en met vol vermogen doorgestuurd. Radiosignalen van andere repeaters worden genegeerd, om de hoeveelheid data te beperken.';

  @override
  String get enOceanRepeaterLevel2Description =>
      'Niet enkel de radiosignalen van sensoren maar ook de radiosignalen van 1-niveau-repeaters worden verwerkt. Zodoende kan een radiosignaal maximaal 2 maal ontvangen en versterkt worden. Radiorepeaters dienen niet ingeleerd te worden. Ze ontvangen en versterken de radiosignalen van alle radiosensoren binnen hun ontvangstbereik.';

  @override
  String get settingsSensorHeader => 'Sensoren';

  @override
  String get sensorChangedSuccessfully => 'Sensoren werden succesvol gewijzigd';

  @override
  String get wiredButton => 'Bedrade drukknoppen';

  @override
  String get enOceanId => 'EnOcean-ID';

  @override
  String get enOceanAddManually => 'EnOcean-ID ingeven of scannen';

  @override
  String get enOceanIdInvalid => 'Ongeldig EnOcean-ID';

  @override
  String get enOceanAddAutomatically => 'Met EnOcean-telegrammen inleren';

  @override
  String get enOceanAddDescription =>
      'Met het EnOcean radio-protocol is het mogelijk om drukknoppen in te leren in uw actor en te bedienen.\nKies ofwel het automatisch inleren met EnOcean telegram, om drukknoppen door erop te drukken in te leren of kies de manuele variant, om de EnOcean-ID van uw drukknop in te scannen of in te typen.';

  @override
  String get enOceanTelegram => 'Telegram';

  @override
  String enOceanCodeScan(Object sensorType) {
    return 'Voer de EnOcean ID van uw $sensorType in of scan de EnOcean QR-code van uw $sensorType om deze toe te voegen.';
  }

  @override
  String get enOceanCode => 'EnOcean QR-code';

  @override
  String enOceanCodeScanDescription(Object sensorType) {
    return 'Zoek naar de EnOcean QR-code op uw $sensorType en scan die met uw camera.';
  }

  @override
  String get enOceanButton => 'EnOcean drukknop';

  @override
  String get enOceanBackpack => 'EnOcean adapter';

  @override
  String get sensorNotAvailable => 'Er werden nog geen sensoren ingeleerd';

  @override
  String get sensorAdd => 'Sensor toevoegen';

  @override
  String get sensorCancel => 'Inleren afbreken';

  @override
  String get sensorCancelDescription =>
      'Wenst u het inleerproces werkelijk te stoppen?';

  @override
  String get getEnOceanBackpack => 'Ga uw EnOcean-adapter halen';

  @override
  String get enOceanBackpackMissing =>
      'Om toegang te krijgen tot de fantastische wereld van de perfecte connectiviteit en communicatie, heeft u een EnOcean-adapter nodig. Klik hier, om meer informatie te bekomen.';

  @override
  String sensorEditChangedSuccessfully(Object sensorName) {
    return '$sensorName werd succesvol gewijzigd';
  }

  @override
  String sensorConnectedVia(Object deviceName) {
    return 'verbonden met $deviceName';
  }

  @override
  String get lastSeen => 'Laatst gezien';

  @override
  String get setButtonOrientation => 'Oriëntatie bevestigen';

  @override
  String get setButtonType => 'Type drukknop bevestigen';

  @override
  String get button1Way => '1-kanaals drukknop';

  @override
  String get button2Way => '2-kanaals drukknop';

  @override
  String get button4Way => '4-kanaals drukknop';

  @override
  String get buttonUnset => 'niet gebruikt';

  @override
  String get button => 'drukknop';

  @override
  String get sensor => 'sensor';

  @override
  String sensorsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count sensoren gevonden',
      one: '1 sensor gevonden',
      zero: 'geen sensoren gevonden',
    );
    return '$_temp0';
  }

  @override
  String get sensorSearch => 'Zoeken naar sensoren';

  @override
  String get searchAgain => 'Opnieuw zoeken';

  @override
  String sensorTeachInHeader(Object sensorType) {
    return '$sensorType inleren';
  }

  @override
  String sensorChooseHeader(Object sensorType) {
    return '$sensorType uitkiezen';
  }

  @override
  String get sensorChooseDescription =>
      'Kies een drukknop uit, die u wenst in te leren.';

  @override
  String get sensorCategoryDescription =>
      'Kies een categorie uit, die u wenst in te leren.';

  @override
  String get sensorName => 'Benaming drukknop';

  @override
  String get sensorNameFooter => 'Geef de drukknop een naam';

  @override
  String sensorAddedSuccessfully(Object sensorName) {
    return '$sensorName werd succesvol ingeleerd';
  }

  @override
  String sensorDelete(Object sensorType) {
    return '$sensorType wissen';
  }

  @override
  String sensorDeleteHint(Object sensorName, Object sensorType) {
    return 'Moet de $sensorType $sensorName werkelijk gewist worden?';
  }

  @override
  String sensorDeletedSuccessfully(Object sensorName) {
    return '$sensorName werd succesvol gewist';
  }

  @override
  String get buttonTapDescription =>
      'Druk op de drukknop die u wenst in te leren.';

  @override
  String get waitingForTelegram => 'De actor wacht op het telegram';

  @override
  String get copied => 'Gecopieerd';

  @override
  String pairingFailed(Object sensorType) {
    return '$sensorType reeds ingeleerd';
  }

  @override
  String get generalDescriptionUniversalbutton =>
      'Bij de universele drukknop volgt de richtingsomkeer door het kort loslaten van de drukknop. Korte stuurbevelen schakelen aan of uit. (toggelen)';

  @override
  String get generalDescriptionDirectionalbutton =>
      'Richtingsdrukknop is bovenaan \'inschakelen en opdimmen\' en onderaan \'uitschakelen en afdimmen\'.';

  @override
  String get matterForwardingDescription =>
      'De richtingsdruk wordt naar Matter doorgestuurd.';

  @override
  String get none => 'Geen';

  @override
  String get buttonNoneDescription => 'De drukknop heeft geen functie.';

  @override
  String get buttonUnsetDescription =>
      'Aan de knop is geen functie toegewezen.';

  @override
  String get sensorButtonTypeChangedSuccessfully =>
      'Het type drukknop werd succesvol gewijzigd.';

  @override
  String forExample(Object example) {
    return 'vb. $example';
  }

  @override
  String get enOceanQRCodeInvalidDescription =>
      'Pas vanaf productiedatum 44/20 mogelijk';

  @override
  String get input => 'Ingang';

  @override
  String get buttonSceneValueOverride => 'Waarde sfeerdrukknop overschrijven';

  @override
  String get buttonSceneValueOverrideDescription =>
      'De sfeerdrukknopwaarde wordt overschreven met de actuele dimwaarde door de drukknop ingedrukt te houden.';

  @override
  String get buttonSceneDescription =>
      'De sfeerdrukknop schakelt in met een vaste dimwaarde';

  @override
  String get buttonPress => 'Druk op de drukknop';

  @override
  String get triggerOn =>
      'door langer op de drukknop te duwen van de universele drukknop of richtingsdrukknop op de inschakelzijde';

  @override
  String get triggerOff =>
      'met een dubbele impuls op de universele drukknop of richtingsdrukknop op de uitschakelzijde';

  @override
  String get centralOn => 'centraal aan';

  @override
  String get centralOff => 'centraal uit';

  @override
  String get centralButton => 'centrale drukknop';

  @override
  String get enOceanAdapterNotFound => 'Geen EnOcean adapter gevonden';

  @override
  String get updateRequired => 'Update vereist';

  @override
  String get updateRequiredDescription =>
      'Gelieve uw app te updaten, om dit nieuw toestel te ondersteunen.';

  @override
  String get release32Header =>
      'De nieuwe reeks 64 met Matter en EnOcean alsook de nieuwe Bluetooth-inbouw-schakelklok SU62PF-BT/UC zijn nu verkrijgbaar!';

  @override
  String get release32EUD64Header =>
      'De nieuwe inbouwdimmer met Matter over Wi-Fi en tot 300W is beschikbaar!';

  @override
  String get release32EUD64Note1 =>
      'Configuratie van dimsnelheid, aan-/uitschakelsnelheid, kinderkamer-/sluimerschakeling en zo veel meer.';

  @override
  String get release32EUD64Note2 =>
      'De functie-omvang van de EUD64NPN-IPM kan per adapter, vb. de EnOcean-adapter EOA64, uitgebreid worden.';

  @override
  String get release32EUD64Note3 =>
      'Er kunnen max. 30 EnOcean-radiodrukknoppen direct met de EnOcean-adapter EOA64 gekoppeld worden aan de EUD64NPN-IPM en naar Matter doorgestuurd worden.';

  @override
  String get release32EUD64Note4 =>
      'Twee bedrade drukknopingangen kunnen rechtstreeks gekoppeld worden aan de EUD64NPN-IPM of naar Matter doorgestuurd worden.';

  @override
  String get release32ESR64Header =>
      'De nieuwe potentiaalvrije inbouw-1-kanaals-schakelactor met Matter via Wi-Fi en tot 16 A is beschikbaar!';

  @override
  String get release32ESR64Note1 =>
      'Configuratie van verschillende functies zoals teleruptor (ES), relaisfunctie (ER), NO (ER-inversie) en zo veel meer.';

  @override
  String get release32ESR64Note2 =>
      'Het aantal functies van de ESR64PF-IPM kan met de adapter, vb. de EnOcean-adapter EOA64, uitgebreid worden.';

  @override
  String get release32ESR64Note3 =>
      'Tot 30 EnOcean-radiodrukknoppen kunnen verbonden met de EnOcean-adapter EOA64 met de ESR64PF-IP direct gekoppeld en naar Matter doorgestuurd worden.';

  @override
  String get release32ESR64Note4 =>
      'Een bedrade drukknopingang kan met de ESR64PF-IPM direct verbonden of naar Matter doorgestuurd worden.';

  @override
  String buttonsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count drukknoppen gevonden',
      one: '1 drukknop gevonden',
      zero: 'geen drukknoppen gevonden',
    );
    return '$_temp0';
  }

  @override
  String get doubleImpuls => 'met een dubbele impuls';

  @override
  String get impulseDescription =>
      'Als het kanaal ingeschakeld is, wordt dit door een impuls uitgeschakeld.';

  @override
  String get locationServiceEnable => 'Locatie activeren';

  @override
  String get locationServiceDisabledDescription =>
      'Locatie is uitgeschakeld. De versie van uw besturingssysteem vereist de locatie om Bluetooth-apparaten te kunnen vinden.';

  @override
  String get locationPermissionDeniedNoPosition =>
      'Locatietoestemmingen zijn niet toegekend. De versie van uw besturingssysteem vereist locatieautorisatie om Bluetooth-apparaten te kunnen vinden. Sta de locatieautorisatie toe in de instellingen van uw apparaat.';

  @override
  String get interfaceStatePermanentDeniedDescriptionDevicesAround =>
      'De machtiging voor apparaten in de buurt is niet toegekend. Sta de machtiging voor apparaten in de buurt toe in de instellingen van uw apparaat.';

  @override
  String get permissionNearbyDevices => 'Apparaten in de buurt';

  @override
  String get release320Header =>
      'De nieuwe, krachtigere universele dimmer EUD12NPN-BT/600W-230V is er!';

  @override
  String get release320EUD600Header => 'Wat kan de nieuwe universele dimmer?';

  @override
  String get release320EUD600Note1 =>
      'Universele dimmer met een vermogen tot 600W';

  @override
  String get release320EUD600Note2 =>
      'Uitbreidbaar met een vermogenuitbreiding tot 3800W';

  @override
  String get release320EUD600Note3 =>
      'Lokale bediening met universele knop/richtingsknop';

  @override
  String get release320EUD600Note4 => 'Centrale sturing aan/uit';

  @override
  String get release320EUD600Note5 =>
      'Ingang bewegingsmelder voor meer comfort';

  @override
  String get release320EUD600Note6 =>
      'Geïntegreerde schakelklok met 10 schakelprogramma\'s';

  @override
  String get release320EUD600Note7 => 'Astrofunctie';

  @override
  String get release320EUD600Note8 => 'Individuele inschakelhelderheid';

  @override
  String get mqttClientCertificate => 'Klant certificaat';

  @override
  String get mqttClientCertificateHint => 'MQTT Klant certificaat';

  @override
  String get mqttClientKey => 'Klant sleutel';

  @override
  String get mqttClientKeyHint => 'MQTT Klant sleutel';

  @override
  String get mqttClientPassword => 'Klant paswoord';

  @override
  String get mqttClientPasswordHint => 'MQTT Klant paswoord';

  @override
  String get mqttEnableHomeAssistantDiscovery =>
      'HomeAssistant MQTT herkenning activeren';

  @override
  String get modbusTcp => 'Modbus TCP';

  @override
  String get enableInterface => 'Interface activeren';

  @override
  String get busAddress => 'Bus-adres';

  @override
  String busAddressWithAddress(Object index) {
    return 'Bus-adressen $index';
  }

  @override
  String get deviceType => 'Type toestel';

  @override
  String registerTable(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Registertabel',
      one: 'Registertabel',
    );
    return '$_temp0';
  }

  @override
  String get currentValues => 'Huidige waarde';

  @override
  String get requestRTU => 'RTU zoeken';

  @override
  String get requestPriority => 'Zoekprioriteit';

  @override
  String get mqttForwarding => 'MQTT doorsturen';

  @override
  String get historicData => 'Historische data';

  @override
  String get dataFormat => 'Dataformat';

  @override
  String get dataType => 'Datatype';

  @override
  String get description => 'Beschrijving';

  @override
  String get readWrite => 'Lezen/schrijven';

  @override
  String get unit => 'Eenheid';

  @override
  String get registerTableReset => 'Registertabel terugzetten';

  @override
  String get registerTableResetDescription =>
      'Moet de registertabel echt teruggezet worden?';

  @override
  String get notConfigured => 'Niet geconfigureerd';

  @override
  String get release330ZGW16Header => 'Omvangrijke update voor de ZGW16WL-IP';

  @override
  String get release330Header => 'De ZGW16WL-IP met tot 16 energietellers';

  @override
  String get release330ZGW16Note1 =>
      'Ondersteuning van tot 16 ELTAKO Modbus-energietellers';

  @override
  String get release330ZGW16Note2 => 'Ondersteuning van Modbus TCP';

  @override
  String get release330ZGW16Note3 => 'Ondersteuning van MQTT Discovery';

  @override
  String get screenshotButtonLivingRoom => 'Drukknoppen woonkamer';

  @override
  String get registerChangedSuccessfully =>
      'Register werd succesvol veranderd.';

  @override
  String get serverCertificateEmpty => 'Server certificaat mag niet leeg zijn.';

  @override
  String get registerTemplates => 'Register templates';

  @override
  String get registerTemplateChangedSuccessfully =>
      'Register template werd succesvol veranderd.';

  @override
  String get registerTemplateReset => 'Register template terugzetten';

  @override
  String get registerTemplateResetDescription =>
      'Moet het register template werkelijk teruggezet worden?';

  @override
  String get registerTemplateNotAvailable =>
      'Geen register templates beschikbaar';

  @override
  String get rename => 'Hernoemen';

  @override
  String get registerName => 'Registernaam';

  @override
  String get registerRenameDescription =>
      'Geef het register een door de gebruiker gedefinieerde naam';

  @override
  String get restart => 'Toestel opnieuw opstarten';

  @override
  String get restartDescription =>
      'Wenst u het toestel werkelijk opnieuw op te starten?';

  @override
  String get restartConfirmationDescription =>
      'Het toestel wordt nu opnieuw opgestart';

  @override
  String get deleteAllElectricityMeters => 'Alle energiemeters wissen';

  @override
  String get deleteAllElectricityMetersDescription =>
      'Wenst u werkelijk alle energiemeters te wissen?';

  @override
  String get deleteAllElectricityMetersConfirmationDescription =>
      'Alle energiemeters werden succesvol gewist';

  @override
  String get resetAllElectricityMeters =>
      'Alle energiemeterconfiguraties terugzetten';

  @override
  String get resetAllElectricityMetersDescription =>
      'Wenst u werkelijk alle energiemeterconfiguraties terug te zetten?';

  @override
  String get resetAllElectricityMetersConfirmationDescription =>
      'Alle energiemeterconfiguraties werden succesvol teruggezet';

  @override
  String get deleteElectricityMeterHistories => 'Alle energiemeterdata wissen';

  @override
  String get deleteElectricityMeterHistoriesDescription =>
      'Wenst u werkelijk alle energiemeterdata te wissen?';

  @override
  String get deleteElectricityMeterHistoriesConfirmationDescription =>
      'Alle energiemeterdata werden succesvol gewist';

  @override
  String get multipleElectricityMetersSupportMissing =>
      'Uw toestel ondersteunt momenteel slechts één energiemeter. Gelieve uw firmware te actualiseren.';

  @override
  String get consumptionWithUnit => 'Verbruik (kWh)';

  @override
  String get exportWithUnit => 'Levering (kWh)';

  @override
  String get importWithUnit => 'Afname (kWh)';

  @override
  String get resourceWarningHeader => 'Beperkingen in middelen';

  @override
  String mqttAndTcpResourceWarning(Object protocol) {
    return 'Het is niet mogelijk om MQTT en Modbus TCP tegelijkertijd te gebruiken vanwege beperkte systeembronnen. Deactiveer $protocol eerst.';
  }

  @override
  String get mqttEnabled => 'MQTT ingeschakeld';

  @override
  String get redirectMQTT => 'Ga naar MQTT-instellingen';

  @override
  String get redirectModbus => 'Ga naar Modbus-instellingen';

  @override
  String get unsupportedSettingDescription =>
      'Met uw huidige firmwareversie worden sommige apparaatinstellingen niet ondersteund. Update uw firmware om de nieuwe functies te gebruiken';

  @override
  String get updateNow => 'Nu bijwerken';

  @override
  String get zgw241Hint =>
      'Met deze update is Modbus TCP standaard ingeschakeld en MQTT uitgeschakeld. Dit kan worden gewijzigd in de instellingen. Met ondersteuning voor maximaal 16 tellers zijn er veel optimalisaties doorgevoerd; dit kan leiden tot wijzigingen in de instellingen van het apparaat. Herstart het apparaat na het aanpassen van de instellingen.';

  @override
  String get deviceConfigChangedSuccesfully =>
      'Looptijd werd succesvol gewijzigd.';

  @override
  String get deviceConfiguration => 'Apparaatconfiguratie';

  @override
  String get tiltModeToggle => 'Kantelmodus';

  @override
  String get tiltModeToggleFooter =>
      'Als het apparaat is ingesteld in Matter, moeten alle functies daar opnieuw worden geconfigureerd.';

  @override
  String get shaderMovementDirection => 'Achteruit Omhoog/Omlaag';

  @override
  String get shaderMovementDirectionDescription =>
      'Keer de richting om voor omhoog/omlaag-bewegingen van de motor';

  @override
  String get tiltTime => 'Kantel runtime';

  @override
  String changeTiltModeDialogTitle(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Enable',
      'false': 'Disable',
      'other': 'Change',
    });
    return '$_temp0 kantelfunctie';
  }

  @override
  String changeTiltModeDialogConfirmation(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Enable',
      'false': 'Disable',
      'other': 'Change',
    });
    return '$_temp0';
  }

  @override
  String get generalTextSlatSetting => 'Instelling lamellen';

  @override
  String get generalTextPosition => 'Positie';

  @override
  String get generalTextSlatPosition => 'Lamelpositie';

  @override
  String get slatSettingDescription => 'Beschrijving lamelleninstelling';

  @override
  String get scenePositionSliderDescription => 'Hoogte';

  @override
  String get sceneSlatPositionSliderDescription => 'Kantelen';

  @override
  String get referenceRun => 'Kalibratierun';

  @override
  String get slatAutoSettingHint =>
      'In deze modus is de stand van de zonwering niet van belang voordat de lamellen zich aanpassen aan de gewenste kantelstand.';

  @override
  String get slatReversalSettingHint =>
      'In deze modus sluit de zonwering volledig voordat de lamellen zich aanpassen aan de gewenste kantelpositie.';

  @override
  String get release340Header =>
      'De nieuwe ESB64NP-IPM-actuator voor inbouwzonwering is er!';

  @override
  String get release340ESB64Header => 'Wat kan de ESB64NP-IPM?';

  @override
  String get release340ESB64Note1 =>
      'Onze Matter Gateway-gecertificeerde zonweringactuator met optionele lamellenfunctie';

  @override
  String get release340ESB64Note2 =>
      'Twee bedrade knopingangen voor handmatig schakelen en doorsturen naar Matter';

  @override
  String get release340ESB64Note3 =>
      'Uitbreidbaar met EnOcean adapter (EOA64). Bijvoorbeeld met EnOcean draadloze drukknop F4T55';

  @override
  String get release340ESB64Note4 =>
      'Open voor integraties dankzij REST API gebaseerd op OpenAPI-standaard';

  @override
  String get activateTiltModeDialogText =>
      'Als de kantelfunctie is ingeschakeld, gaan alle instellingen verloren. Weet je zeker dat je de kantelfunctie wilt inschakelen?';

  @override
  String get deactivateTiltModeDialogText =>
      'Als je de kantelfunctie uitschakelt, gaan alle instellingen verloren. Weet je zeker dat je de kantelfunctie wilt uitschakelen?';
}
