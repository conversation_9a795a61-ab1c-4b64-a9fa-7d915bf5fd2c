{"appName": "ELTAKO Connect", "discoveryHint": "Aktivoi Bluetooth laitteessa yhteyden muodos<PERSON>i", "devicesFound": "{count, plural, =0 {<PERSON><PERSON><PERSON> ei lö<PERSON>yt} one {1 laitetta löytyi} other {{count} laitetta löytyi}}", "@devicesFound": {"description": "Specifies how many device are found in the discovery overview", "type": "text"}, "discoveryDemodeviceName": "{count, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {Demolaitteet}}", "discoverySu12Description": "2-<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>kin Bluetooth", "discoveryImprint": "<PERSON><PERSON><PERSON><PERSON>", "discoveryLegalnotice": "<PERSON><PERSON><PERSON><PERSON><PERSON> huo<PERSON>utus", "generalSave": "<PERSON><PERSON><PERSON>", "generalCancel": "Peruuta", "detailsHeaderHardwareversion": "Laitteistoversio", "detailsHeaderSoftwareversion": "Ohjelmiston versio", "detailsHeaderConnected": "<PERSON><PERSON><PERSON><PERSON>", "detailsHeaderDisconnected": "Irrotettu", "detailsTimersectionHeader": "<PERSON><PERSON><PERSON><PERSON>", "detailsTimersectionTimercount": "60 käytetystä <PERSON>", "detailsConfigurationsectionHeader": "Asetukset", "detailsConfigurationPin": "Laitteen PIN-koodi", "detailsConfigurationChannelsDescription": "Kanava 1: {channel1} | Kanava 2:  {channel2}", "settingsCentralHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> On/Off", "detailsConfigurationCentralDescription": "<PERSON><PERSON><PERSON> vain, jos kanava on asetettu AUTO-asentoon.", "detailsConfigurationDevicedisplaylock": "Lukitse laitteen näyttö", "timerOverviewHeader": "<PERSON><PERSON><PERSON><PERSON>", "timerOverviewTimersectionTimerinactivecount": "<PERSON><PERSON> to<PERSON>", "timerDetailsListsectionDays1": "<PERSON><PERSON><PERSON><PERSON>", "timerDetailsListsectionDays2": "Tiistai", "timerDetailsListsectionDays3": "Keskiviikko", "timerDetailsListsectionDays4": "Tor<PERSON><PERSON>", "timerDetailsListsectionDays5": "Perjan<PERSON>", "timerDetailsListsectionDays6": "<PERSON><PERSON><PERSON>", "timerDetailsListsectionDays7": "<PERSON><PERSON><PERSON><PERSON>", "timerDetailsHeader": "Ohjelma %@", "timerDetailsSunrise": "Auringonnous<PERSON>", "generalToggleOff": "OFF", "generalToggleOn": "ON", "timerDetailsImpuls": "<PERSON><PERSON><PERSON><PERSON>", "generalTextTime": "<PERSON><PERSON>", "generalTextAstro": "Astro", "generalTextAuto": "Auto", "timerDetailsOffset": "Aikaviive", "timerDetailsPlausibility": "Aktivoi uskottavuuden tarkistus", "timerDetailsPlausibilityDescription": "<PERSON><PERSON> p<PERSON> on asetettu aikaisemmaksi kuin päällekytkentä, kump<PERSON><PERSON><PERSON> ohjelmaa ei oteta huo<PERSON>, esim. kytkeytyminen päälle auringonnousun aikaan ja poiskytkeytyminen klo 6:00. On my<PERSON><PERSON>, jolloin tarkistusta ei haluta tehd<PERSON> ollen<PERSON>, esim. kytkeytyminen päälle auringonlaskun aikaan ja sammuminen klo 1:00 aamulla.", "generalDone": "Val<PERSON>", "generalDelete": "Poista", "timerDetailsImpulsDescription": "Muuta pulssikonfigu<PERSON>atiota", "settingsNameHeader": "Laitteen nimi", "settingsNameDescription": "Tämä nimi nä<PERSON>y myös hakemistossa.", "settingsFactoryresetHeader": "Tehdasasetukset", "settingsFactoryresetDescription": "<PERSON><PERSON><PERSON> si<PERSON> pala<PERSON>?", "settingsFactoryresetResetbluetooth": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "settingsFactoryresetResettime": "<PERSON><PERSON><PERSON><PERSON>", "settingsFactoryresetResetall": "<PERSON><PERSON><PERSON>", "settingsDeletetimerHeader": "<PERSON><PERSON><PERSON><PERSON> pois<PERSON>n", "settingsDeletetimerDescription": "<PERSON><PERSON><PERSON><PERSON> todella poistaa kaikki a<PERSON>?", "settingsDeletetimerAllchannels": "<PERSON><PERSON><PERSON> kanavat", "settingsImpulseHeader": "Impulssin <PERSON>", "settingsImpulseDescription": "Impulssin kytkentäaika määrittää impulssin keston.", "generalTextRandommode": "Satunnainen ON/OFF kytkentä", "settingsChannelsTimeoffsetHeader": "Päivänse<PERSON><PERSON><PERSON>", "settingsChannelsTimeoffsetDescription": "Kesä: {summerOffset} min | <PERSON><PERSON><PERSON>: {winterOffset}min", "settingsLocationHeader": "<PERSON><PERSON><PERSON><PERSON>", "settingsLocationDescription": "Aseta sijaintisi astrotoimintojen käyttöä varten.", "settingsLanguageHeader": "Laitteen kieli", "settingsLanguageSetlanguageautomatically": "Aseta kieli automaattisesti", "settingsLanguageDescription": "Valitse kieli {deviceType}", "settingsLanguageGerman": "<PERSON><PERSON><PERSON>", "settingsLanguageFrench": "Ranska", "settingsLanguageEnglish": "Eng<PERSON><PERSON>", "settingsLanguageItalian": "Italia", "settingsLanguageSpanish": "Espanja", "settingsDatetimeHeader": "Päivämäärä ja kellonaika", "settingsDatetimeSettimeautomatically": "Käytä puhelimen aikaa", "settingsDatetimeSettimezoneautomatically": "Aseta aikavyöhyke automaattisesti", "generalTextTimezone": "Ai<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsDatetime24Hformat": "24 tunnin formaatti", "settingsDatetimeSetsummerwintertimeautomatically": "Kesä-/talviaikaan automaattisesti", "settingsDatetimeWinter": "<PERSON><PERSON><PERSON>", "settingsDatetimeSummer": "<PERSON><PERSON><PERSON>", "settingsPasskeyHeader": "Laitteen nykyinen PIN-koodi", "settingsPasskeyDescription": "Syötä laitteen nykyinen PIN-koodi", "timerDetailsActiveprogram": "<PERSON><PERSON><PERSON>", "timerDetailsActivedays": "Aktiiviset päivät", "timerDetailsSuccessdialogHeader": "<PERSON><PERSON><PERSON>", "timerDetailsSuccessdialogDescription": "<PERSON><PERSON><PERSON>nist<PERSON>", "settingsRandommodeDescription": "Satunnaistila toimii vain kellonaikaan perustuvien ohjelmien kanssa, ei impulssi- tai astropohjaisten ohjelmien (auringonnousu/auringonlasku) kanssa.", "settingsSolsticeHeader": "Päivänse<PERSON><PERSON><PERSON>", "settingsSolsticeDescription": "Asetettu aika määrittelee aikaeron auringon liikkeiden aikaan nähden, ja se aina käännetään vastaa<PERSON>i.", "settingsSolsticeHint": "Esimerkki:\n<PERSON><PERSON><PERSON> kytkentä tapahtuu aina 30 minuuttia ennen auringonlaskua ja auringonnousun mukainen  kytkentä tapahtuu aina 30 minuuttia etukäteen.", "generalTextMinutesShort": "min", "settingsPinDescription": "PIN-koodia tarvitaan yhteyden muodostamiseen.", "settingsPinHeader": "<PERSON><PERSON><PERSON> la<PERSON>en PIN-koodi", "settingsPinNewpinDescription": "<PERSON><PERSON>-koodi", "settingsPinNewpinRepeat": "Toista uusi PIN-koodi", "detailsProductinfo": "Tuotetiedot", "settingsDatetimeSettimeautodescription": "<PERSON><PERSON><PERSON> ha<PERSON> aika", "minutes": "{count, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON>uuttia}}", "hours": "{count, plural, one {<PERSON><PERSON>} other {Tunnit}}", "seconds": "{count, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON>ku<PERSON><PERSON>}}", "generalTextChannel": "{count, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON>na<PERSON>}}", "generalLabelChannel": "<PERSON><PERSON><PERSON> {number}", "generalTextDate": "Päivämäärä", "settingsDatetime24HformatDescription": "<PERSON><PERSON><PERSON> ha<PERSON> muoto", "settingsDatetimeSetsummerwintertime": "Kesä-/talviaika", "settingsDatetime24HformatValue24": "24h", "settingsDatetime24HformatValue12": "AM/PM", "detailsEdittimer": "Muokkaa oh<PERSON>lmia", "settingsPinOldpinRepeat": "Toista nykyinen PIN-koodi", "settingsPinCheckpin": "PIN-k<PERSON><PERSON> ta<PERSON>", "detailsDevice": "{count, plural, one {<PERSON><PERSON>} other {<PERSON>tte<PERSON>}}", "detailsDisconnect": "<PERSON><PERSON><PERSON> y<PERSON>eys", "settingsCentralDescription": "Tuloliitin A1 ohjaa keskusoh<PERSON><PERSON>sen On/Off.\nPakotettu manuaalinen On/Off on käytössä vain, jos kanava on asetettu keskusohjaus On/Off -toimintoon.", "settingsCentralHint": "Esimerkki:\nKanava 1 = Keskusohjaus On/Off\nKanava 2 = Off\nA1 = Keskusohjaus On -> Vain C1 kytkeytyy päälle, C2 pysyy pois päältä", "settingsCentralToggleheader": "Keskustulokytkimet", "settingsCentralActivechannelsdescription": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> asetus on Keskusohjaus On/Off:", "settingsSolsticeSign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsDatetimeTimezoneDescription": "Keski-<PERSON><PERSON><PERSON> a<PERSON>", "generalButtonContinue": "Jatka", "settingsPinConfirmationDescription": "PIN-k<PERSON><PERSON> vai<PERSON>o on<PERSON>ui", "settingsPinFailDescription": "PIN-koodin vaihto ep<PERSON>", "settingsPinFailHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsPinFailShort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on oltava täsmälleen 6-numeroinen.", "settingsPinFailWrong": "PIN-koodi on väärä", "settingsPinFailMatch": "PIN-koodit eivät täsmää", "discoveryLostconnectionHeader": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "discoveryLostconnectionDescription": "Laitteen yhteys on katkaistu.", "settingsChannelConfigCentralDescription": "Toiminta kuten AUTO-tilassa ja seuraa my<PERSON> joh<PERSON><PERSON> keskusohjaustuloja", "settingsChannelConfigOnDescription": "Kytkee kanavan pysyvästi päälle ja ei huomioi oh<PERSON>lmoint<PERSON>a", "settingsChannelConfigOffDescription": "Kytkee kanavan pysyvästi pois päältä ja ei huomioi oh<PERSON>lmointeja", "settingsChannelConfigAutoDescription": "K<PERSON><PERSON>e aika- ja astro-oh<PERSON><PERSON><PERSON> mukaan", "bluetoothPermissionDescription": "Laitteiden konfigurointiin tarvitaan Bluetooth.", "timerListitemOn": "Kytke päälle", "timerListitemOff": "Kytke pois päältä", "timerListitemUnknown": "Tuntematon", "timerDetailsAstroHint": "<PERSON><PERSON><PERSON><PERSON> on as<PERSON><PERSON><PERSON> as<PERSON>, jotta astro-oh<PERSON><PERSON><PERSON> toimivat o<PERSON>.", "timerDetailsTrigger": "<PERSON><PERSON><PERSON>", "timerDetailsSunset": "Auringonlasku", "settingsLocationCoordinates": "Koordinaatit", "settingsLocationLatitude": "<PERSON><PERSON><PERSON><PERSON>", "settingsLocationLongitude": "Pituusaste", "timerOverviewEmptyday": "Ohjelmia ei tällä hetkellä käytetä {day}", "timerOverviewProgramloaded": "<PERSON><PERSON><PERSON><PERSON> lad<PERSON>", "timerOverviewProgramchanged": "<PERSON><PERSON><PERSON>", "settingsDatetimeProcessing": "Päivämäärä ja kellonaika on muutettu", "deviceNameEmpty": "Syöte ei saa olla tyhjä", "deviceNameHint": "Syötteessä ei saa olla enemmän kuin {count}-merkkiä.", "deviceNameChanged": "Laitteen nimi on muutettu", "deviceNameChangedSuccessfully": "Laitteen nimi vaih<PERSON><PERSON><PERSON> onnist<PERSON>i", "deviceNameChangedFailed": "Tapaht<PERSON> virhe.", "settingsPinConfirm": "Vahvista", "deviceShowInstructions": "1. Aktivoi kellokytkimen Bluetooth SET-näppäimellä\n2. <PERSON><PERSON><PERSON> haku napauttamalla ylhäällä olevaa painiketta.", "deviceNameNew": "Syötä uusi laitteen nimi", "settingsLanguageRetrieved": "<PERSON><PERSON>", "detailsProgramsShow": "Näytä <PERSON>", "generalTextProcessing": "Odota", "generalTextRetrieving": "ha<PERSON>an", "settingsLocationPermission": "Salli ELTAKO Connectin käyttää tämän laitteen sijaintia.", "timerOverviewChannelloaded": "Kanavat on ladattu", "generalTextRandommodeChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mu<PERSON>", "detailsConfigurationsectionChanged": "Konfiguraatiota muutetaan", "settingsSettimeFunctions": "Aikatoimin<PERSON>ja mu<PERSON>", "imprintContact": "<PERSON><PERSON>", "imprintPhone": "<PERSON><PERSON><PERSON>", "imprintMail": "Sähköposti", "imprintRegistrycourt": "Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imprintRegistrynumber": "Rekisteröintinumero", "imprintCeo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imprintTaxnumber": "Myyntiveron tunnistenumero", "settingsLocationCurrent": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "generalTextReset": "<PERSON><PERSON><PERSON>", "discoverySearchStart": "<PERSON><PERSON><PERSON> haku", "discoverySearchStop": "<PERSON><PERSON><PERSON> haku", "settingsImpulsSaved": "Impulssikytkentäaika on tallennettu", "settingsCentralNochannel": "<PERSON><PERSON> kana<PERSON>, j<PERSON>en as<PERSON> on keskusohjaus On/Off.", "settingsFactoryresetBluetoothConfirmationDescription": "Bluetooth-y<PERSON>eys no<PERSON>.", "settingsFactoryresetBluetoothFailDescription": "Bluetooth-yhteyksien nollaus epä<PERSON>.", "imprintPublisher": "Julkaisija", "discoveryDeviceConnecting": "Yhdistetään...", "discoveryDeviceRestarting": "Käynnistetään uude<PERSON>...", "generalTextConfigurationsaved": "<PERSON><PERSON><PERSON> k<PERSON> tall<PERSON>.\n", "timerOverviewChannelssaved": "<PERSON><PERSON><PERSON> kanavat", "timerOverviewSaved": "<PERSON><PERSON><PERSON>\n", "timerSectionList": "Listanäkymä\n", "timerSectionDayview": "Päivänäkymä", "generalTextChannelInstructions": "<PERSON><PERSON><PERSON>", "generalTextPublisher": "Julkaisija", "settingsDeletetimerDialog": "<PERSON><PERSON><PERSON><PERSON> todella poistaa kaik<PERSON>?", "settingsFactoryresetResetbluetoothDialog": "<PERSON><PERSON><PERSON><PERSON> todella nollata kaikki <PERSON>-as<PERSON><PERSON><PERSON>?", "settingsCentralTogglecentral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\nOn/Off", "generalTextConfirmation": "{serviceName} mu<PERSON>s onnist<PERSON>.", "generalTextFailed": "{serviceName} mu<PERSON><PERSON> ep<PERSON>.", "settingsChannelConfirmationDescription": "Kanavien vaihto on<PERSON>.", "timerDetailsSaveHeader": "<PERSON><PERSON><PERSON>", "timerDetailsDeleteHeader": "<PERSON><PERSON>", "timerDetailsSaveDescription": "<PERSON><PERSON><PERSON> tall<PERSON> on<PERSON>.", "timerDetailsDeleteDescription": "<PERSON><PERSON><PERSON> pois<PERSON>n on<PERSON>.", "timerDetailsAlertweekdays": "Oh<PERSON>lmaa ei voida tallentaa, koska mitään viikonpäiviä ei ole valittu aktiiviseksi.", "generalTextOk": "OK", "settingsDatetimeChangesuccesfull": "Päivämäärä ja kellonaika vaihdettiin onnistuneesti.", "discoveryConnectionFailed": "<PERSON><PERSON><PERSON><PERSON>", "discoveryDeviceResetrequired": "Yhteyttä laitteeseen ei saatu muodostettua. <PERSON><PERSON> ratkai<PERSON> ongelman poistamalla laitteen Bluetooth-asetuksista. <PERSON><PERSON> on<PERSON> j<PERSON>, ota yhteyttä tekniseen tukeemme.", "generalTextSearch": "<PERSON><PERSON><PERSON>", "generalTextOr": "tai", "settingsFactoryresetProgramsConfirmationDescription": "<PERSON><PERSON><PERSON> pois<PERSON><PERSON><PERSON> onnist<PERSON>.", "generalTextManualentry": "Manuaalinen syöttö", "settingsLocationSaved": "<PERSON><PERSON><PERSON><PERSON>u", "settingsLocationAutosearch": "Etsi sijainti automaattisesti", "imprintPhoneNumber": "045 7870 6795", "imprintMailAddress": "<EMAIL>", "settingsFactoryresetResetallDialog": "<PERSON><PERSON><PERSON><PERSON> todella palauttaa laitteen tehdas<PERSON>tuksiin?", "settingsFactoryresetFactoryConfirmationDescription": "Laite on onnistuneesti palautettu tehdasasetuksiin.", "settingsFactoryresetFactoryFailDescription": "Laitteen nollaus epäonnistui.", "imprintPhoneNumberIos": "04578706795", "mfzFunctionA2Title": "2-vaiheinen kytkentäviive (A2)", "mfzFunctionA2TitleShort": "2-vaiheinen kytkentäviive (A2)", "mfzFunctionA2Description": "<PERSON>n ohjausjänn<PERSON> kytket<PERSON><PERSON><PERSON>, alkaa aikajakso t1 0-60 sekunnin välillä. Tämän ajanjakson päätyttyä kosketin 1-2 sulkeutuu ja alkaa aikajakso t2 0-60 sekunnin välillä. Tämän aikajakson päätyttyä kosketin 3-4 sulkeutuu. Keskeytyksen jälkeen, aikajakso alkaa uudelleen t1:stä.", "mfzFunctionRvTitle": "Poiskytkentäviive (RV)", "mfzFunctionRvTitleShort": "RV | Poiskytkentäviive", "mfzFunctionRvDescription": "<PERSON>n ohjausjännite A1-<PERSON> kyt<PERSON><PERSON><PERSON>, releen kosketin vai<PERSON>a asennosta 15-16 asent<PERSON> 15-18. \n<PERSON>n ohjausj<PERSON>nn<PERSON> ka<PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON> t alkaa; a<PERSON><PERSON><PERSON> pä<PERSON><PERSON><PERSON>, releen kosketin palaa asentoon 15-16. \nJos A1-A2 kytketään uudelleen ennen kuin aikajakso on päättynyt, kulunut aika nolla<PERSON>.", "mfzFunctionTiTitle": "Tauko-/käyntiaika, käyntia<PERSON> alussa (TI)", "mfzFunctionTiTitleShort": "TI | Tauko-/käyntiaika, käyntiaika alussa", "mfzFunctionTiDescription": "Kun ohjausjännite A1-A2 kytket<PERSON><PERSON><PERSON>, ensimmäinen aikajakso t1 (käyntiaika) käynnistyy ja asetetun ajan j<PERSON>, releen koskettimen asento vaihtuu ja aikajakso t2 (taukoaika) käynnistyy. Aikajaksot jatkuvat niin pitk<PERSON>, kun<PERSON> ohjausjännite A1-A2 katkaistaan. <PERSON><PERSON> kosketin vaihtaa asentoon 15-18 heti kun ohjausjännite A1-A2 kytketään (käyntiaika käynnistyksen yhteydessä).", "mfzFunctionAvTitle": "Päällekytkentäviive (AV)", "mfzFunctionAvTitleShort": "AV | Päällekytkentäviive", "mfzFunctionAvDescription": "Kun ohjausjännite A1-A2 kyt<PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON> t alkaa; a<PERSON><PERSON><PERSON> päätyttyä, re<PERSON> kos<PERSON> v<PERSON> asentoon 15-18. <PERSON><PERSON> ohja<PERSON>jännite A1-A2 katkaistaan ennen kuin aikajakso on päättynyt, kulunut aika nolla<PERSON>an.", "mfzFunctionAvPlusTitle": "Päällekytkentäviive summaus (AV+)", "mfzFunctionAvPlusTitleShort": "AV+ | Päällekytkentäviive summaus", "mfzFunctionAvPlusDescription": "Toiminto muuten samanlainen kuin toiminnossa AV. Mutta kun ohjausjännite A1-A2 keskeytyy, nykyinen kulunut aika tallennetaan ja sitä käytetään jatkossa.", "mfzFunctionAwTitle": "<PERSON><PERSON><PERSON> p<PERSON>kennän yhteydessä (pyyhkäisy) (AW)", "mfzFunctionAwTitleShort": "AW | <PERSON><PERSON><PERSON> pois<PERSON>kennän yhteydessä", "mfzFunctionAwDescription": "<PERSON><PERSON> oh<PERSON>ännite A1-<PERSON> ka<PERSON>, releen kosketin vai<PERSON>a asentoon 15-18 ja samalla aika<PERSON>so t alkaa. Kun aikajakso t on kulu<PERSON> loppuun, releen kosketin palaa asentoon 15-16. \n<PERSON><PERSON>ite kytketään käynnissä olevan aika<PERSON>son aikana, kosketin palaa takaisin asentoon 15-16 ja aikajakso t nollata<PERSON>.", "mfzFunctionIfTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (IF)", "mfzFunctionIfTitleShort": "IF | Pulssisuodatin", "mfzFunctionIfDescription": "<PERSON><PERSON> oh<PERSON>jännite A1-A2 kytket<PERSON>n, <PERSON><PERSON> k<PERSON> v<PERSON><PERSON>ent<PERSON> 15-18 as<PERSON><PERSON> aika<PERSON>son t ajaksi. <PERSON><PERSON> asetetun aikajakson aikana saapuvat ohjausimpulssit ohite<PERSON>, kunnes asetettu aika on kulunut loppuun.", "mfzFunctionEwTitle": "P<PERSON>ssi päällekytkennän yhteydessä (pyyhkäisy) (EW)", "mfzFunctionEwTitleShort": "EW | Pulssi päällekytkennän yhteydessä", "mfzFunctionEwDescription": "<PERSON><PERSON> oh<PERSON><PERSON>nnite A1-<PERSON> kyt<PERSON><PERSON><PERSON>, releen kosketin vai<PERSON>a asentoon 15-18 ja samalla aikajakso t alkaa. Kun aikajakso t on kulu<PERSON> loppuun, releen kosketin palaa asentoon 15-16. \n<PERSON><PERSON> katkeaa käynnissä olevan a<PERSON> aikana, kosketin palaa takaisin asentoon 15-16 ja aikajakso t nollata<PERSON>.", "mfzFunctionEawTitle": "<PERSON><PERSON><PERSON> p<PERSON>- ja poiskytkenn<PERSON>n yhteydes<PERSON> (pyyhkäisy) (EAW)", "mfzFunctionEawTitleShort": "EAW | <PERSON><PERSON><PERSON> p<PERSON>- ja poiskytkennän yhteydes<PERSON>ä", "mfzFunctionEawDescription": "<PERSON>n ohja<PERSON>jännite A1-<PERSON> kyt<PERSON><PERSON><PERSON><PERSON>, releen kosketin vaihtaa asentoon 15-18 ja samalla aikajakso t1 alkaa. Kun aikajakso t1 on kulunut loppuun, releen kosketin palaa asentoon 15-16.\n<PERSON><PERSON> ohja<PERSON>jännite A1-<PERSON> ka<PERSON>, releen kosketin vaihtaa asentoon 15-18 ja samalla aikajakso t2 alkaa. Kun aikajakso t2 on kulunut loppuun, releen kosketin palaa asentoon 15-16.", "mfzFunctionTpTitle": "<PERSON><PERSON>-/k<PERSON><PERSON><PERSON><PERSON>, al<PERSON><PERSON> (vilkkurele) (TP)", "mfzFunctionTpTitleShort": "TP | Tauko-/käyn<PERSON><PERSON>, taukoaika alussa", "mfzFunctionTpDescription": "Kun ohjausjännite A1-A2 kytket<PERSON><PERSON><PERSON>, ensi<PERSON><PERSON>inen aikajakso t1 (taukoaika) käynnistyy ja as<PERSON>tun ajan j<PERSON>, releen koskettimen asento vaihtuu ja aikajakso t2 (käyntiaika) käynnistyy. Aikajaksot jatkuvat niin pitk<PERSON>, kun<PERSON> ohjausjännite A1-A2 katkaistaan. <PERSON>leen kosketin pysyy asennossa 15-16 kun ohjausjännite A1-A2 kytketään (taukoaika käynnistyksen yhteydessä).", "mfzFunctionIaTitle": "Impulssiohjattu päällekytkentäviive (esim. oven avaaja) (IA)", "mfzFunctionIaTitleShort": "IA | Impulssiohjattu päällekytkentäviive", "mfzFunctionIaDescription": "Aikajakso t1 alkaa, kun ohjausjännite A1-A2 kytketään. Kun aikajakso t1 on kulunut loppuun, releen kosketin vai<PERSON>a asentoon 15-18 ja tästä käynnistyy aikajakso t2. Kun aikajakso t2 on kulunut loppuun, releen kosketin palaa asentoon 15-16. \nJos t1 on asetettu minimiarvoon = 0,1 sekuntia, IA-toiminto toimii pulssisuodattimena t2 ajoituksen aikana.", "mfzFunctionArvTitle": "Päälle- ja pois<PERSON>tkentäviive (ARV)", "mfzFunctionArvTitleShort": "ARV | Päälle- ja poiskytkentäviive", "mfzFunctionArvDescription": "<PERSON>n ohjausjännite A1-A2 kytket<PERSON><PERSON>n, aikajakso t1 alkaa; a<PERSON><PERSON>son jälkeen releen kosketin vaihtaa asentoon 15-18. <PERSON><PERSON> ohja<PERSON>jännite A1-<PERSON> ka<PERSON>, aikajakso t2 alkaa ja aika<PERSON>son jälkeen releen kosketin palaa asentoon 15-16.\n<PERSON><PERSON> päällekytkentäviiveen aikajakso t1 keskeytetään, aika käynnistetään uudelleen alusta.", "mfzFunctionArvPlusTitle": "<PERSON><PERSON><PERSON><PERSON>- ja p<PERSON>, summaus (ARV+)", "mfzFunctionArvPlusTitleShort": "ARV+ | Päälle- ja poiskytkentäviive, summaus", "mfzFunctionArvPlusDescription": "<PERSON><PERSON><PERSON>o muuten sa<PERSON><PERSON>en kuin ARV, mutta sillä eroll<PERSON>, että jos ohjausjännite A1-A2 keskeytyy, nykyinen kulunut aika tallennetaan ja sitä käytetään jatkossa.", "mfzFunctionEsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ES)", "mfzFunctionEsTitleShort": "ES | Impulssirele", "mfzFunctionEsDescription": "Yli 50 ms:n pitui<PERSON><PERSON>, re<PERSON> k<PERSON>in kyt<PERSON>e päälle ja pois päältä.", "mfzFunctionEsvTitle": "Impulsrele poiskytkentäviiveellä ja poiskytkentävaroituksella (ESV)", "mfzFunctionEsvTitleShort": "ESV | Impulsrele poiskytkentäviiveellä ja poiskytkentävaroituksella", "mfzFunctionEsvDescription": "To<PERSON>into muuten samanlainen kuin toiminnossa SRV, mutta lisänä poiskytkentävaroitus: <PERSON>in 30 sekuntia ennen a<PERSON>son t umpeut<PERSON><PERSON>, re<PERSON> kos<PERSON> (ja esim. kytketty valaisin) ”vilkkuu” 3 kertaa asteittain lyhenevillä aikaväleillä.", "mfzFunctionErTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ER)", "mfzFunctionErTitleShort": "ER | Reletoiminto", "mfzFunctionErDescription": "<PERSON><PERSON>, kun ohja<PERSON>jännite A1-A2 on kytkettynä, releen kosketin pysyy asennossa 15-18. \n<PERSON><PERSON> ohjausjännite A1-<PERSON> ka<PERSON><PERSON><PERSON><PERSON>, releen kosketin palaa asent<PERSON> 15-16.", "mfzFunctionSrvTitle": "Impulssirele poiskytkentäviiveellä (SRV)", "mfzFunctionSrvTitleShort": "SRV | Impulssirele poiskytkentäviiveellä", "mfzFunctionSrvDescription": "Yli 50 ms:n pitui<PERSON><PERSON>, releen kosketin kytkee päälle ja pois päältä.\nKun releen koskettimen asento vaihtuu asennosta 15-18, a<PERSON><PERSON><PERSON> t alkaa automaattisesti. Kun aika<PERSON><PERSON> on kulu<PERSON> loppuun, releen kosketin palaa automaattisesti lähtöasentoon 15-16.", "detailsFunctionsHeader": "<PERSON><PERSON><PERSON><PERSON>", "mfzFunctionTimeHeader": "<PERSON><PERSON> (t{index})", "mfzFunctionOnDescription": "Pysyvästi päällä.", "mfzFunctionOffDescription": "Pysyvästi pois päältä.", "mfzFunctionMultiplier": "<PERSON><PERSON><PERSON>", "discoveryMfz12Description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mfzFunctionOnTitle": "Pysyvästi ON", "mfzFunctionOnTitleShort": "Pysyvästi ON", "mfzFunctionOffTitle": "Pysyvästi OFF", "mfzFunctionOffTitleShort": "Pysyvästi OFF", "mfzMultiplierSecondsFloatingpoint": "0.1 sekuntia", "mfzMultiplierMinutesFloatingpoint": "0.1 minuuttia", "mfzMultiplierHoursFloatingpoint": "0.1 tuntia", "mfzOverviewFunctionsloaded": "<PERSON><PERSON><PERSON><PERSON> lad<PERSON>", "mfzOverviewSaved": "<PERSON><PERSON><PERSON><PERSON>", "settingsBluetoothHeader": "Bluetooth", "bluetoothChangedSuccessfully": "Bluetooth-<PERSON><PERSON><PERSON><PERSON> muutt<PERSON>n onnist<PERSON>.", "settingsBluetoothInformation": "Huomio: <PERSON><PERSON> asetus on aktivoituna, laite on pysyvästi kaikkien nähtävissä Bluetoothin kautta!\nTällöin on suositeltavaa vaihtaa laitteen PIN-koodi.", "settingsBluetoothContinuousconnection": "Jatkuva näkyvyys", "settingsBluetoothContinuousconnectionDescription": "Aktivoimalla Bluetoothin jat<PERSON><PERSON>, Bluetooth pysyy jatkuvasti pä<PERSON><PERSON><PERSON> ({deviceType}), eikä sitä tarvitse aktivoida manuaalisesti ennen yhteyden muodostamista.", "settingsBluetoothTimeout": "Yhteyden aikakatkaisu", "settingsBluetoothPinlimit": "PIN-k<PERSON><PERSON> raja", "settingsBluetoothTimeoutDescription": "<PERSON><PERSON><PERSON><PERSON> {timeout} minuutin käyttämättömyyden jälkeen.", "settingsBluetoothPinlimitDescription": "Turvallisuussyist<PERSON> sinulla on enint<PERSON>än {attempts} yritystä syöttää PIN-koodi. Bluetooth poistetaan tämän jälkeen käytöstä ja se on aktivoitava uudelleen manuaalisesti uutta yhteyttä varten.", "settingsBluetoothPinAttempts": "yritykset", "settingsResetfunctionHeader": "<PERSON><PERSON><PERSON>", "settingsResetfunctionDialog": "<PERSON><PERSON><PERSON><PERSON> todella nollata kaikki to<PERSON>?", "settingsFactoryresetFunctionsConfirmationDescription": "<PERSON><PERSON><PERSON> to<PERSON> on onnistuneesti nollattu.", "mfzFunctionTime": "<PERSON><PERSON> (t)", "discoveryConnectionFailedInfo": "<PERSON><PERSON>-yhteytt<PERSON>", "detailsConfigurationDevicedisplaylockDialogtext": "<PERSON><PERSON> la<PERSON>en näytön lukitsemisen jälkeen Bluetooth deaktivoidaan ja se on aktivoitava uudelleen manuaalisesti uuden yhteyden muodostamiseksi.", "detailsConfigurationDevicedisplaylockDialogquestion": "<PERSON><PERSON><PERSON><PERSON><PERSON> var<PERSON>ti <PERSON>en n<PERSON>ö<PERSON>?", "settingsDemodevices": "Näytä demolaitteet", "generalTextSettings": "Asetukset", "discoveryWifi": "WiFi", "settingsInformations": "<PERSON><PERSON><PERSON>", "detailsConfigurationDimmingbehavior": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detailsConfigurationSwitchbehavior": "<PERSON><PERSON><PERSON><PERSON>", "detailsConfigurationBrightness": "<PERSON><PERSON><PERSON>", "detailsConfigurationMinimum": "<PERSON><PERSON><PERSON><PERSON>", "detailsConfigurationMaximum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detailsConfigurationSwitchesGr": "R<PERSON><PERSON><PERSON>rel<PERSON> (GR)", "detailsConfigurationSwitchesGs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (GS)", "detailsConfigurationSwitchesCloserer": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ER)", "detailsConfigurationSwitchesClosererDescription": "Painike vapautettuna -> Pois päältä. Pidä painettuna -> Päällä.", "detailsConfigurationSwitchesOpenerer": "<PERSON>ut<PERSON><PERSON> kosketin (käänteinen ER)", "detailsConfigurationSwitchesOpenererDescription": "Painike vapautettuna -> Päällä. Pidä painettuna -> <PERSON>is päältä.", "detailsConfigurationSwitchesSwitch": "<PERSON><PERSON><PERSON>", "detailsConfigurationSwitchesSwitchDescription": "Jokaisella kytkimen as<PERSON><PERSON>, valo kytketään päälle ja pois päältä.", "detailsConfigurationSwitchesImpulsswitch": "<PERSON><PERSON>", "detailsConfigurationSwitchesImpulsswitchDescription": "Lyhyt painikkeen painallus kytkee valon päälle ja pois päältä.", "detailsConfigurationSwitchesClosererDescription2": "Pidä painike painettuna. <PERSON><PERSON> vapautat pain<PERSON>, moot<PERSON>i pys<PERSON>.", "detailsConfigurationSwitchesImpulsswitchDescription2": "Paina painiketta lyhyesti käynnistääksesi moottorin ja paina uudelleen pysäyttääks<PERSON> sen.", "detailsConfigurationWifiloginScan": "Skannaa QR-koodi", "detailsConfigurationWifiloginScannotvalid": "<PERSON><PERSON><PERSON><PERSON> koodi ei ole voimassa", "detailsConfigurationWifiloginDescription": "Syötä koodi", "detailsConfigurationWifiloginPassword": "<PERSON><PERSON><PERSON>", "discoveryEsbipDescription": "Verho-ohjain IP", "discoveryEsripDescription": "Impulssirele IP", "discoveryEudipDescription": "Yleishimmennin IP", "generalTextLoad": "<PERSON><PERSON><PERSON>", "wifiBasicautomationsNotFound": "Automaatiota ei löytynyt.", "wifiCodeInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> koodi", "wifiCodeValid": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> koodi", "wifiAuthorizationLogin": "Yhdistä", "wifiAuthorizationLoginFailed": "<PERSON><PERSON><PERSON>ut<PERSON>nen e<PERSON>äonnistui", "wifiAuthorizationSerialnumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wifiAuthorizationProductiondate": "Tuotantopäivä", "wifiAuthorizationProofofpossession": "PoP", "generalTextWifipassword": "WiFi-<PERSON><PERSON><PERSON>", "generalTextUsername": "K<PERSON>yttäjät<PERSON>nus", "generalTextEnter": "TAI SYÖTÄ MANUAALISESTI", "wifiAuthorizationScan": "Skannaa ELTAKO-koodi.", "detailsConfigurationDevicesNofunctionshinttext": "Tämä laite ei tällä hetkellä tue muita asetuksia", "settingsUsedemodelay": "Käytä demo viivettä", "settingsImpulsLoad": "<PERSON><PERSON><PERSON> k<PERSON>ä<PERSON> on ladattu", "settingsBluetoothLoad": "Bluetooth-asetuksia ladataan.", "detailsConfigurationsectionLoad": "Määrityksiä ladataan", "generalTextLogin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextAuthentication": "To<PERSON><PERSON>", "wifiAuthorizationScanDescription": "Etsi ELTAKO-koodi WiFi-laitteesta tai sen mukana toimite<PERSON><PERSON> tuotekor<PERSON> ja kohdista se kameraan.", "wifiAuthorizationScanShort": "Skannaa ELTAKO-koodi", "detailsConfigurationEdgemode": "Himmennystapa", "detailsConfigurationEdgemodeLeadingedge": "<PERSON>useva reuna", "generalTextNetwork": "Verkko", "wifiAuthenticationSuccessful": "<PERSON><PERSON><PERSON>", "detailsConfigurationsectionSavechange": "Kon<PERSON>gu<PERSON><PERSON><PERSON> muuttunut", "discoveryWifiAdddevice": "Lisää WiFi-laite", "wifiAuthenticationDelay": "Tämä voi kestää jopa 1 minuutin", "generalTextRetry": "<PERSON><PERSON><PERSON>", "wifiAuthenticationCredentials": "<PERSON><PERSON>", "wifiAuthenticationSsid": "SSID", "wifiAuthenticationDelaylong": "Voi kestää jopa 1 minuutin, ennen kuin laite on valmis ja näkyy sovelluksessa", "wifiAuthenticationCredentialsShort": "<PERSON>", "wifiAuthenticationTeachin": "Opeta laite WiFi-verkkoon", "wifiAuthenticationEstablish": "<PERSON><PERSON><PERSON><PERSON> y<PERSON><PERSON>", "wifiAuthenticationEstablishLong": "<PERSON>te muodostaa yhteyden WiFi-verkkoon {ssid}", "wifiAuthenticationFailed": "<PERSON><PERSON><PERSON><PERSON> ep<PERSON>. Irrota laite virtalähteestä muutamaksi sekunniksi ja yritä liittää se uudelleen", "wifiAuthenticationReset": "<PERSON><PERSON><PERSON>", "wifiAuthenticationResetHint": "<PERSON><PERSON><PERSON> to<PERSON> pois<PERSON>.", "wifiAuthenticationInvaliddata": "Todennustiedot virheellisiä", "wifiAuthenticationReauthenticate": "<PERSON><PERSON><PERSON>", "wifiAddhkdeviceHeader": "Lisää laite", "wifiAddhkdeviceDescription": "Liitä uusi ELTAKO-laitteesi WiFi-verkkoon Apple Home -sovel<PERSON>sen kautta.", "wifiAddhkdeviceStep1": "1. Avaa Apple Home -sovellus", "wifiAddhkdeviceStep2": "2. <PERSON><PERSON><PERSON> so<PERSON> oikeassa yläkulmassa olevaa plus-merkkiä ja valitse **Lisää lisälaite**.", "wifiAddhkdeviceStep3": "3. <PERSON><PERSON><PERSON>.", "wifiAddhkdeviceStep4": "4. <PERSON><PERSON><PERSON> asetukset voidaan nyt määrittää ELTAKO Connect -sovelluksessa.", "detailsConfigurationRuntime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detailsConfigurationRuntimeMode": "Tila", "generalTextManually": "Man<PERSON><PERSON><PERSON><PERSON>", "detailsConfigurationRuntimeAutoDescription": "Kaihdinlaite määrittää itsenäisesti verhomoottorin käyntiajan jokaisen alimmasta pääteasennosta ylimpään pääteasentoon tapahtuvan matkan aikana (suositus).\nK<PERSON>yttöö<PERSON><PERSON><PERSON>, t<PERSON><PERSON>inen liike alhaalta yl<PERSON> on suoritettava ilman keskeytystä.", "detailsConfigurationRuntimeManuallyDescription": "Kaihdinmoottorin käyntiaika asetetaan manuaalisesti alla olevan keston avulla.\nVarmista, että määritetty käyntiaika vastaa kaihdinmoottorisi todellista käyntiaikaa.\nKäyttöö<PERSON><PERSON> j<PERSON><PERSON>, tä<PERSON>inen liike alhaalta <PERSON><PERSON><PERSON><PERSON><PERSON> on suoritettava keskeytyksettä.", "detailsConfigurationRuntimeDemoDescription": "Demo-tila on käytettävissä vain REST API:n kautta", "generalTextDemomodeActive": "Demo-tila aktiivinen", "detailsConfigurationRuntimeDuration": "<PERSON><PERSON>", "detailsConfigurationSwitchesGs4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (GS4)", "detailsConfigurationSwitchesGs4Description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jossa on peruutus toiminto kaihtimien ohja<PERSON>a varten", "screenshotSu12": "<PERSON><PERSON><PERSON><PERSON>", "screenshotS2U12": "<PERSON><PERSON><PERSON><PERSON>", "screenshotMfz12": "<PERSON><PERSON><PERSON>", "screenshotEsr62": "Lamppu", "screenshotEud62": "Kattovalaisin", "screenshotEsb62": "Parvekkeen kaihtimet", "detailsConfigurationEdgemodeLeadingedgeDescription": "LC1-LC3 ovat helppokäyttötoimintoja erilaisilla himmennyskäyrillä sellaisille himmennettäville 230V LED-lampuille ja valaisimille, joita ei voida himmentää tarpeeksi alas AUTO-tilassa niiden rakenteen vuoksi ja siksi ne on pakotettava vaihekulmaohjaukseen.", "detailsConfigurationEdgemodeAutoDescription": "AUTO-<PERSON><PERSON><PERSON>, kaikkien lamp<PERSON><PERSON><PERSON><PERSON>ien himmennys on ma<PERSON><PERSON><PERSON><PERSON>.", "detailsConfigurationEdgemodeTrailingedge": "<PERSON><PERSON><PERSON> reuna", "detailsConfigurationEdgemodeTrailingedgeDescription": "LC4-LC6 ovat <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> on erilaiset himmennyskäyrät himmennettäville 230V LED-lampuille ja valaisimille.", "updateHeader": "Laiteohjelmiston pä<PERSON>s", "updateTitleStepSearch": "Päivitystä etsitään", "updateTitleStepFound": "<PERSON><PERSON><PERSON><PERSON>", "updateTitleStepDownload": "<PERSON>ä<PERSON><PERSON><PERSON>", "updateTitleStepInstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateTitleStepSuccess": "<PERSON><PERSON><PERSON><PERSON>", "updateTitleStepUptodate": "Laite on ajan tasalla", "updateTitleStepFailed": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "updateButtonSearch": "Etsi p<PERSON>ivityksiä", "updateButtonInstall": "<PERSON><PERSON><PERSON>", "updateCurrentversion": "Nykyinen versio", "updateNewversion": "Uusi la<PERSON>ohjelmistopäivitys saatavilla", "updateHintPower": "Pä<PERSON>s kä<PERSON>ist<PERSON>y vasta sitten, kun laitteen lähtö ei ole aktiivinen. Laitetta ei saa irrottaa virtalähteestä eikä sovelluksesta saa poistua päivityksen aikana!", "updateButton": "Pä<PERSON><PERSON>", "updateHintCompatibility": "<PERSON><PERSON><PERSON><PERSON><PERSON>, sillä muuten jotkin sovelluksen toiminnoista ovat raj<PERSON>.", "generalTextDetails": "Yksityiskohdat", "updateMessageStepMetadata": "Ladataan päivitystietoja", "updateMessageStepPrepare": "<PERSON>ä<PERSON><PERSON><PERSON> val<PERSON>", "updateTitleStepUpdatesuccessful": "Päivitystä tark<PERSON>", "updateTextStepFailed": "Valitettavasti jokin meni pieleen p<PERSON><PERSON> a<PERSON>, yrit<PERSON> uudelleen muutaman minuutin kuluttua tai odota, kunnes la<PERSON> päivittyy automaattisesti (edellyttää internet yhteyttä).", "configurationsNotavailable": "Konfiguraatioita ei ole vielä saatavilla", "configurationsAddHint": "Luo uusia konfiguraatioita muodostamalla yhteys la<PERSON>en ja tallentamalla konfiguraatio.", "@configurationsAddHint": {"description": "Now available for all devices not only Bluetooth", "type": "text"}, "configurationsEdit": "Muokkaa konfigu<PERSON>atiota", "generalTextName": "<PERSON><PERSON>", "configurationsDelete": "Poista konfiguraatio", "configurationsDeleteHint": "<PERSON><PERSON><PERSON><PERSON> todella poistaa konfiguraation: {configName}?", "configurationsSave": "<PERSON><PERSON><PERSON>", "configurationsSaveHint": "Vie laitteesi nykyinen konfiguraatio tai lataa jokin olemassa olevista konfiguraatioista.", "configurationsImport": "<PERSON><PERSON> k<PERSON>o", "configurationsImportHint": "<PERSON><PERSON><PERSON><PERSON> todella siirtää konfiguraation {configName}?", "generalTextConfigurations": "{count, plural, one {Kon<PERSON><PERSON>raati<PERSON>} other {Kon<PERSON><PERSON>ra<PERSON><PERSON>}}", "configurationsStepPrepare": "Konfiguraatiota valmistellaan", "configurationsStepName": "<PERSON><PERSON><PERSON><PERSON> konfigu<PERSON> nimi", "configurationsStepSaving": "Konfiguraatiota tallenne<PERSON>", "configurationsStepSavedsuccessfully": "<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON> onnist<PERSON>i", "configurationsStepSavingfailed": "Konfiguraation tallentaminen epäonnistui", "configurationsStepChoose": "Valitse konfiguraatio", "configurationsStepImporting": "Konfiguraatiota tuodaan", "configurationsStepImportedsuccessfully": "Konfiguraatio on tuotu onnistuneesti", "configurationsStepImportingfailed": "Konfiguraation tuonti ep<PERSON>", "discoveryAssuDescription": "Bluetooth kellokytkin ulkopistorasiaan", "settingsDatetimeDevicetime": "Laitteen todellinen aika", "settingsDatetimeLoading": "Aika-<PERSON><PERSON><PERSON><PERSON> lad<PERSON>", "discoveryEud12Description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextOffdelay": "Poiskytkentäviive", "generalTextRemainingbrightness": "Jäljellä oleva kirkkaus", "generalTextSwitchonvalue": "Päällekytkennän arvo", "motionsensorTitleNoremainingbrightness": "<PERSON><PERSON> j<PERSON>nnöskirkkautta", "motionsensorTitleAlwaysremainingbrightness": "Jäännöskirkkaudella", "motionsensorTitleRemainingbrightnesswithprogram": "Jäännöskirkkaus kytkentäoh<PERSON>lman mukaan", "motionsensorTitleRemainingbrightnesswithprogramandzea": "Jäännöskirkkaus ZE:n ja ZA:n kautta", "motionsensorTitleNoremainingbrightnessauto": "<PERSON><PERSON> jäännöskirkkautta (puoliautomaattinen)", "generalTextMotionsensor": "Liiketunnistin", "generalTextLightclock": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextSnoozeclock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generalDescriptionLightclock": "Kun valo kytketään pä<PERSON> ({mode}), valo syttyy noin 1 sekunnin kuluttua pienimmällä kirkkaudella ja se himmennetään hitaasti siitä ylöspäin muuttamatta viimeksi tallennettua kirkkaustasoa.", "generalDescriptionSnoozeclock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ({mode}), vala<PERSON><PERSON> himmennetään nykyisestä tasosta minimikirkkauteen ja sammutetaan sen jälkeen. <PERSON><PERSON><PERSON> voidaan sammuttaa milloin tahansa himmennysprosessin aikana painamalla painiketta lyhyesti. Pitkä painallus himmennyksen aikana himmentää ja lopettaa torkkutoiminnon.", "generalTextImmediately": "<PERSON><PERSON>", "generalTextPercentage": "<PERSON><PERSON><PERSON>", "generalTextSwitchoffprewarning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalDescriptionSwitchoffprewarning": "<PERSON><PERSON> himmen<PERSON> mini<PERSON>rk<PERSON>", "generalDescriptionOffdelay": "<PERSON>te kyt<PERSON> pä<PERSON>, kun ohja<PERSON>j<PERSON>nnite kytketään. <PERSON><PERSON> oh<PERSON> kat<PERSON>, al<PERSON><PERSON> aikavi<PERSON>, jonka jälkeen laite sammuu. <PERSON>te voidaan kytkeä päälle aikaviiveen kuluessa.", "generalDescriptionBrightness": "<PERSON><PERSON><PERSON>, jolla himmennin kyt<PERSON>e valaisimen päälle.", "generalDescriptionRemainingbrightness": "<PERSON><PERSON><PERSON><PERSON><PERSON>, johon lamppu himmenee liiketunnistimen sammutt<PERSON>ssa.", "generalDescriptionRuntime": "Valoherätystoiminnon käyntiaika minimikirkkaudesta maksimikirkkauteen.", "generalTextUniversalbutton": "Yleispainike", "generalTextDirectionalbutton": "Suuntapainike", "eud12DescriptionAuto": "Automaattinen UT/RT tunnistus (RTD suunta-anturi diodilla)", "eud12DescriptionRt": "jossa on RTD suunta-anturi diodi", "generalTextProgram": "<PERSON><PERSON><PERSON>", "eud12MotionsensorOff": "Liiketunnistimen ollessa pois päältä", "eud12ClockmodeTitleProgramze": "<PERSON><PERSON><PERSON> ja keskusohjaus ON", "eud12ClockmodeTitleProgramza": "Oh<PERSON>lma ja keskusohjaus OFF", "eud12ClockmodeTitleProgrambuttonon": "Ohjelma ja UT/RT ON", "eud12ClockmodeTitleProgrambuttonoff": "Ohjelma ja UT/RT OFF", "eud12TiImpulseTitle": "Pulssiaika ON (t1)", "eud12TiImpulseHeader": "Himmennysarvo Pulssiaika ON", "eud12TiImpulseDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, johon vala<PERSON>us s<PERSON>detään ON-puls<PERSON> a<PERSON>na.", "eud12TiOffTitle": "Pulssiaika OFF (t2)", "eud12TiOffHeader": "Himmennysarvo Pulssiaika OFF", "eud12TiOffDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, johon vala<PERSON>us säädetään OFF-pulssin aikana.", "generalTextButtonpermanentlight": "<PERSON><PERSON><PERSON><PERSON> vala<PERSON><PERSON>", "generalDescriptionButtonpermanentlight": "Jatkuvan valon painikkeella asettaminen välillä 0 - 10 tuntia 0,5 tunnin välein. Aktivointi tapahtuu painamalla painiketta yli 1 sekunnin ajan (1x välähdys), deaktivointi painamalla painiketta yli 2 sekuntia.", "generalTextNobuttonpermanentlight": "<PERSON><PERSON> k<PERSON>", "generalTextBasicsettings": "Perusasetukset", "generalTextInputswitch": "<PERSON><PERSON><PERSON><PERSON> (A1)", "generalTextOperationmode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextDimvalue": "Käyttäytyminen päälle kytkettäessä", "eud12TitleUsememory": "Käytä muist<PERSON>a", "eud12DescriptionUsememory": "Muistiarvo vastaa aina viimeisimmäksi asetettua himmennysar<PERSON>a. <PERSON><PERSON> m<PERSON> on pois käytöstä, himmen<PERSON>s asetetaan aina päällekytkentäarvoon.", "generalTextStartup": "Päällekytkentäkirkkaus", "generalDescriptionSwitchonvalue": "Päällekytkentäarvo on säädettävä kirkkausarvo, joka takaa turvallisen päällekytkennän.", "generalTitleSwitchontime": "Päällekytkentäaika", "generalDescriptionSwitchontime": "Kun päällekytkentäaika on kulunut loppuun, lamppu himmenee päällekytkentäarvosta muistiarvoon.", "generalDescriptionStartup": "Jotkin LED-lamput vaativat suuremman käynnistysvirran kytkeytyäkseen aina luotettavasti päälle. Lamppu kytkeytyy päälle tällä kytkentäarvolla ja himmenee muistiin jäävään arvoon heti kytkentäajan jälkeen.", "eud12ClockmodeSubtitleProgramze": "Lyhyt klikkaus Keskusohjaus ON", "eud12ClockmodeSubtitleProgramza": "Lyhyt klikkaus Keskusohjaus OFF", "eud12ClockmodeSubtitleProgrambuttonon": "Tuplapainallus yleispainikkeen/suuntapainikkeen ON puolelta", "eud12ClockmodeSubtitleProgrambuttonoff": "Tuplapainallus y<PERSON>painikkeen/suuntapainikkeen OFF puolelta", "eud12FunctionStairlighttimeswitchTitleShort": "TLZ | Porrasvaloautomaatti", "eud12FunctionMinTitleShort": "MIN", "eud12FunctionMmxTitleShort": "MMX", "eud12FunctionTiDescription": "<PERSON><PERSON><PERSON>, j<PERSON> on säädettävä päällekytkentä ja poiskytkentäsaika 0,5 sekunnista 9,9 minuuttiin. <PERSON><PERSON><PERSON><PERSON> voidaan säätää minimikirkkaudesta maksimikirkkauteen.", "eud12FunctionAutoDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jossa on as<PERSON><PERSON><PERSON> l<PERSON><PERSON>, valo<PERSON><PERSON><PERSON>- ja torkku<PERSON>.", "eud12FunctionErDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kirk<PERSON><PERSON>aan as<PERSON>a minimikirk<PERSON>udesta maksimikirkkauteen.", "eud12FunctionEsvDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON> <PERSON><PERSON> as<PERSON>a poiskytkentäviive 1-120 minuuttiin. Poiskytkennän varoitus ajan loppuessa himmentämäll<PERSON> al<PERSON>, jonka aika säädettävissä 1-3 minuuttiin. Molemmat keskusohjaustulot ovat aktiivisia.", "eud12FunctionTlzDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON><PERSON> keston asettaminen 0-10 tunnin välillä, 0,5 tunnin askelin. Aktivointi painamalla painiketta yli 1 sekunnin ajan (1x välähdys), deaktivointi painamalla painiketta yli 2 sekunnin ajan.", "eud12FunctionMinDescription": "Yleishimmennin joka kytkeytyy päälle asetettuun minimikirkkauteen, kun ohjausjännite kytketään. Valo kirkastuu maksimikirkkauteen asetetun himmenny<PERSON>jan (1-120 minuuttia) kuluessa. Kun ohjausj<PERSON><PERSON><PERSON> ka<PERSON>, valo sammuu v<PERSON>, my<PERSON><PERSON> asetetun himmennysajan aikana. Molemmat keskusohjaustulot ovat aktiivisia.", "eud12FunctionMmxDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON> kytkeytyy asetettuun minimikirkkauteen, kun ohjausjännite kytketään. Valo kirkastuu maksimikirkkauteen asetetun himmenny<PERSON> (1-120 minuuttia) kuluessa. <PERSON>n ohjausj<PERSON>nn<PERSON> kat<PERSON>, himmennin himmenee asetettuun minimikirkkauteen. Sen jälkeen se kytkeytyy pois päältä. Molemmat keskusohjaustulot ovat aktiivisia.", "motionsensorSubtitleNoremainingbrightness": "Liiketunnistimen ollessa pois päältä", "motionsensorSubtitleAlwaysremainingbrightness": "Liiketunnistimen ollessa pois päältä", "motionsensorSubtitleRemainingbrightnesswithprogram": "Kytkentäohjelma aktivoitu ja deaktivoitu BWM:n ollessa pois päältä", "motionsensorSubtitleRemainingbrightnesswithprogramandzea": "Keskusohjaus ON aktivoi liiketunnistimen, Keskusohjaus OFF deaktivoi liiketunnistimen, samoin kuin k<PERSON>äohjelmissa", "motionsensorSubtitleNoremainingbrightnessauto": "Liiketunnistin kytkee vain pois päältä", "detailsDimsectionHeader": "<PERSON><PERSON><PERSON><PERSON>", "generalTextFast": "<PERSON>a", "generalTextSlow": "<PERSON><PERSON>", "eud12TextDimspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> nopeus", "eud12TextSwitchonspeed": "Päällekytkennän nopeus", "eud12TextSwitchoffspeed": "Poiskytkennän nopeus", "eud12DescriptionDimspeed": "Him<PERSON><PERSON>sno<PERSON>us on nopeus, jolla himmennin himmentää nykyisestä kirkkaustasosta tavoitekirkkauteen.", "eud12DescriptionSwitchonspeed": "Päällekytkentänopeus on nopeus, jonka himmennin tarvitsee kytkeytyäkseen kokonaan päälle.", "eud12DescriptionSwitchoffspeed": "Poiskytkentänopeus on nopeus, jonka himmennin tarvitsee sammukseen kokonaan.", "settingsFactoryresetResetdimHeader": "Himmennysa<PERSON>ust<PERSON>", "settingsFactoryresetResetdimDescription": "<PERSON><PERSON><PERSON><PERSON> todella nollata kaikki him<PERSON>?", "settingsFactoryresetResetdimConfirmationDescription": "Himmennysasetukset on nyt onnistuneesti nollattu", "eud12TextSwitchonoffspeed": "ON/OFF-nopeus", "eud12DescriptionSwitchonoffspeed": "Päälle/poiskytkentänopeus on nopeus, jolla himmennin kytkee valaistuksen päälle tai pois päältä kokonaan.", "timerDetailsDimtoval": "Päälle himmennysarvolla %", "timerDetailsDimtovalDescription": "Him<PERSON>nin kytkeytyy aina päälle kiinteällä himmennysarvolla (%).", "timerDetailsDimtovalSubtitle": "K<PERSON><PERSON> päälle {brightness}%:lla", "timerDetailsDimtomem": "<PERSON><PERSON><PERSON><PERSON>", "timerDetailsDimtomemSubtitle": "Päällekytkentä muistiarvolla", "timerDetailsMotionsensorwithremainingbrightness": "Jäännöskirkkaus (BWM) ON", "timerDetailsMotionsensornoremainingbrightness": "Jäännöskirkkaus (BWM) OFF", "settingsRandommodeHint": "Kun satunnaistila on aktivoituna, kaikkien tämän kanavan ohjelmien kytkentää siirretään satunnaisesti enintään 15 minuuttilla (läsnäolon simulointi). ON-ohjelmat siirretään asetettua aikaisemmaksi, OFF-ohjelmat myöhemmäksi.", "runtimeOffsetDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ylitys, kun aika on kulunut loppuun", "loadingTextDimvalue": "Himmennysarvo on ladattu", "discoveryEudipmDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IP, Matter", "generalTextOffset": "Ylitys", "eud12DimvalueTestText": "<PERSON><PERSON><PERSON> k<PERSON>", "eud12DimvalueTestDescription": "Testauksessa otetaan huomioon myös tällä hetkellä asetettu himmennysnopeus.", "eud12DimvalueLoadText": "Lataa kirkkausarvo", "settingsDatetimeNotime": "Päivämäärän ja kello<PERSON><PERSON> as<PERSON> on luettava laitteen näytöstä.", "generalMatterText": "Matter", "generalMatterMessage": "Opeta Matter-la<PERSON><PERSON> k<PERSON>täen jotakin seuraavista sovel<PERSON>.", "generalMatterOpengooglehome": "Avaa Google Home", "generalMatterOpenamazonalexa": "Avaa Amazon Alexa", "generalMatterOpensmartthings": "<PERSON>a SmartThings", "generalLabelProgram": "<PERSON><PERSON><PERSON> {number}", "generalTextDone": "Val<PERSON>", "settingsRandommodeDescriptionShort": "Kun satunnaistila on aktivoituna, kaikkien tämän kanavan ohjelmien kytkentää siirretään satunnaisesti enintään 15 minuuttilla (läsnäolon simulointi). ON-ohjelmat siirretään asetettua aikaisemmaksi, OFF-ohjelmat myöhemmäksi.", "all": "<PERSON><PERSON><PERSON>", "discoveryBluetooth": "Bluetooth", "success": "<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON>", "timeProgramAdd": "Lisä<PERSON> a<PERSON>", "noConnection": "<PERSON>i <PERSON>", "timeProgramOnlyActive": "Määritetyt ohjelmat", "timeProgramAll": "<PERSON><PERSON><PERSON>", "active": "Aktiivinen", "inactive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeProgramSaved": "<PERSON><PERSON><PERSON><PERSON><PERSON> {number} tallennettu", "deviceLanguageSaved": "Laitteen kieli tallennettu", "generalTextTimeShort": "{time}", "programDeleteHint": "<PERSON><PERSON><PERSON><PERSON><PERSON> ohjelma {index} todella poistaa?", "milliseconds": "{count, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON>kunti<PERSON>}}", "millisecondsWithValue": "{count, plural, one {{count} <PERSON><PERSON><PERSON><PERSON>} other {{count} <PERSON><PERSON><PERSON><PERSON><PERSON>}}", "secondsWithValue": "{count, plural, one {{count} <PERSON><PERSON><PERSON>} other {{count} <PERSON><PERSON><PERSON><PERSON>}}", "minutesWithValue": "{count, plural, one {{count} <PERSON><PERSON><PERSON>i} other {{count} Minu<PERSON><PERSON>}}", "hoursWithValue": "{count, plural, one {{count} <PERSON><PERSON>} other {{count} Tunnit}}", "settingsPinFailEmpty": "PIN-koodi ei saa olla tyhjä", "detailsConfigurationWifiloginScanNoMatch": "<PERSON><PERSON><PERSON><PERSON> koodi ei vastaa laitetta", "wifiAuthorizationPopIsEmpty": "PoP ei voi olla tyhjä", "wifiAuthenticationCredentialsHint": "Koska sovellus ei pääse käsiksi yksityiseen Wi-Fi-salasanaasi, ei ole mahdollista tarkistaa syötteen oikeellisuutta. Jos yhteyttä ei muodostu, tarkista salasana ja syötä se uudelleen.", "generalMatterOpenApplehome": "Avaa Apple Home", "timeProgramNoActive": "Ei m<PERSON>ä<PERSON>", "timeProgramNoEmpty": "Vapaata aikaoh<PERSON>lmaa ei ole sa<PERSON>", "nameOfConfiguration": "Konfiguraation nimi", "currentDevice": "Nykyinen laite", "export": "Vie", "import": "<PERSON><PERSON>", "savedConfigurations": "Tallennetut konfigu<PERSON>", "importableServicesLabel": "<PERSON><PERSON><PERSON><PERSON> as<PERSON> voidaan tuoda:", "notImportableServicesLabel": "Yhteensopimattomat asetukset", "deviceCategoryMeterGateway": "<PERSON><PERSON><PERSON>äytäv<PERSON>", "deviceCategory2ChannelTimeSwitch": "2-<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "devicategoryOutdoorTimeSwitchBluetooth": "Bluetooth kellokytkin ulkokäyttöön", "settingsModbusHeader": "Modbus", "settingsModbusDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, parite<PERSON><PERSON> ja a<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON>, virheiden havaitsemisen ja odotusajan määrittämiseksi.", "settingsModbusRTU": "Modbus RTU", "settingsModbusBaudrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsModbusParity": "<PERSON><PERSON><PERSON>", "settingsModbusTimeout": "Modbus-aikakatkaisu", "locationServiceDisabled": "<PERSON><PERSON><PERSON>i poistettu käytöstä", "locationPermissionDenied": "<PERSON> sijainnille lupa pyytää nykyistä sijaintiasi.", "locationPermissionDeniedPermanently": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on estetty pysyvästi, salli sijaintilupa laitteesi asetuksissa nykyisen sijaintisi määrittämistä varten.", "lastSync": "<PERSON><PERSON><PERSON><PERSON>", "dhcpActive": "DHCP aktiivinen", "ipAddress": "IP", "subnetMask": "Aliverkon peite", "standardGateway": "Oletusyhdyskäytävä", "dns": "DNS", "alternateDNS": "Vaihtoehtoinen DNS", "errorNoNetworksFound": "WiFi-verkkoa ei löydy", "availableNetworks": "Käytettävissä olevat verkot", "enableWifiInterface": "Ota WiFi-liitäntä k<PERSON>yttöön", "enableLANInterface": "Ota LAN-liitäntä käyttöön", "hintDontDisableAllInterfaces": "Varmista, että kaikkia liitäntöjä ei ole poistettu käytöstä. Viimeksi aktivoitu käyttöliittymä on etusijalla.", "ssid": "SSID", "searchNetworks": "Etsi verkkoja", "errorNoNetworkEnabled": "Vähin<PERSON><PERSON><PERSON><PERSON> y<PERSON>den aseman on oltava aktiivinen", "errorActiveNetworkInvalid": "Kaikki aktiiviset asemat eivät ole kelvollisia", "invalidNetworkConfiguration": "Virheellinen verkkomääritys", "generalDefault": "<PERSON><PERSON>", "mqttHeader": "MQTT", "mqttConnected": "Yhdistetty MQTT-välittäjään", "mqttDisconnected": "Ei yhteyttä MQTT-välittäjään", "mqttBrokerURI": "Välittäjän URI", "mqttBrokerURIHint": "MQTT-välittäjän URI", "mqttPort": "<PERSON><PERSON>", "mqttPortHint": "MQTT-portti", "mqttClientId": "Asiakas-ID", "mqttClientIdHint": "MQTT Asiakas-ID", "mqttUsername": "K<PERSON>yttäjät<PERSON>nus", "mqttUsernameHint": "MQTT käyttäjätunnus", "mqttPassword": "<PERSON><PERSON><PERSON>", "mqttPasswordHint": "MQTT salasana", "mqttCertificate": "Ser<PERSON><PERSON><PERSON><PERSON><PERSON>", "mqttCertificateHint": "MQTT sertifikaatti", "mqttTopic": "<PERSON><PERSON>", "mqttTopicHint": "MQTT aihe", "electricityMeter": "Sähkömittari", "electricityMeterCurrent": "<PERSON><PERSON><PERSON><PERSON>", "electricityMeterHistory": "Historia", "electricityMeterReading": "<PERSON><PERSON><PERSON> lukema", "connectivity": "Yhdistettävyys", "electricMeter": "{count, plural, one {Energiamittari} other {Energiamittarit}}", "discoveryZGW16Description": "Modbus-Energia-Mittarit-MQTT-Gateway", "bluetoothConnectionLost": "Bluetooth-<PERSON><PERSON><PERSON><PERSON> ka<PERSON>i", "bluetoothConnectionLostDescription": "Bluetooth-yht<PERSON><PERSON> laitte<PERSON>en on katkennut. Tarkista yhteys laitteeseen.", "openBluetoothSettings": "<PERSON><PERSON> Bluetooth-<PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "setInitialPassword": "<PERSON>eta alkuperäinen salasana", "initialPasswordMinimumLength": "Salasanan on oltava vähintään {length} merkkiä pitkä", "repeatPassword": "<PERSON><PERSON> sa<PERSON>", "passwordsDoNotMatch": "Salasanat eivät täsmää", "savePassword": "<PERSON><PERSON><PERSON>", "savePasswordHint": "<PERSON><PERSON><PERSON> tall<PERSON>taan laitteen tulevia yhteyksiä varten.", "retrieveNtpServer": "Hae aika NTP-palvelimelta", "retrieveNtpServerFailed": "Yhteyttä NTP-palvelimeen ei saatu muodostettua.", "retrieveNtpServerSuccess": "Yhteys NTP-palvelimeen onnistui.", "settingsPasswordNewPasswordDescription": "<PERSON>", "settingsPasswordConfirmationDescription": "Salasanan vaihto on<PERSON>ui", "dhcpRangeStart": "DHCP-al<PERSON>en alku", "dhcpRangeEnd": "DHCP-al<PERSON><PERSON> loppu", "forwardOnMQTT": "Välitä eteenpäin MQTT:hen", "showAll": "Näytä kaikki", "hide": "<PERSON><PERSON><PERSON>", "changeToAPMode": "Vaihda AP-tilaan", "changeToAPModeDescription": "<PERSON><PERSON>mässä laitetta WiFi-verk<PERSON>on, jolloin yhteys laitteeseen katkeaa ja sinun on muodostettava yhteys laitteeseen uudelleen määritetyn verkon kautta.", "consumption": "<PERSON><PERSON><PERSON>", "currentDay": "Nykyinen päivä", "twoWeeks": "2 viikkoa", "oneYear": "1 vuosi", "threeYears": "3 vuotta", "passwordMinLength": "Salasanan on oltava vähintään {length} merkkiä.", "passwordNeedsLetter": "Salasanan on sisällettävä kirjain.", "passwordNeedsNumber": "Salasanan on sisällettävä numero.", "portEmpty": "<PERSON>ti ei voi olla tyhjä", "portInvalid": "Väärä portti", "portOutOfRange": "Portin on oltava välillä {rangeStart} ja {rangeEnd}", "ipAddressEmpty": "IP-osoite ei voi olla tyhjä", "ipAddressInvalid": "Virheellinen IP-osoite", "subnetMaskEmpty": "Aliverkon peite ei voi olla tyhjä", "subnetMaskInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> alive<PERSON>on peite", "gatewayEmpty": "Gateway ei voi olla tyhjä", "gatewayInvalid": "Virheellinen yhdyskäytävä", "dnsEmpty": "DNS ei voi olla tyhjä", "dnsInvalid": "Virheellinen DNS", "uriEmpty": "URI ei voi olla tyhjä", "uriInvalid": "Virheellinen URI", "electricityMeterChangedSuccessfully": "Sähkömittari vaihdettu onnistuneesti", "networkChangedSuccessfully": "Verkkoasetusten muuttaminen on<PERSON>", "mqttChangedSuccessfully": "MQTT-<PERSON><PERSON><PERSON> m<PERSON> on<PERSON>", "modbusChangedSuccessfully": "Modbus-<PERSON><PERSON><PERSON> mu<PERSON> on<PERSON>", "loginData": "Poista kirjautumistiedot", "valueConfigured": "Määritetty", "electricityMeterHistoryNoData": "Tietoja ei ole sa<PERSON>", "locationChangedSuccessfully": "<PERSON><PERSON><PERSON>i muutettu onnistuneesti", "settingsNameFailEmpty": "<PERSON>mi ei voi olla tyhjä", "settingsNameFailLength": "<PERSON>mi ei saa olla pitdempi kuin {length} merkkiä", "solsticeChangedSuccesfully": "Päivänse<PERSON><PERSON> as<PERSON><PERSON> muutt<PERSON>n on<PERSON>ui", "relayFunctionChangedSuccesfully": "<PERSON><PERSON>-to<PERSON>into muutettu onnistuneesti", "relayFunctionHeader": "<PERSON><PERSON>oi<PERSON><PERSON>", "dimmerValueChangedSuccesfully": "Käyttäytyminen päälle kytkettäessä muutettu onnistuneesti", "dimmerBehaviourChangedSuccesfully": "Himmennystoiminnot muutettu onnistuneesti", "dimmerBrightnessDescription": "<PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON> as<PERSON> vaikuttaa kaikkiin himmentimen säädettäviin kirk<PERSON>.", "dimmerSettingsChangedSuccesfully": "Perusasetukset muutettu onnistuneesti", "liveUpdateEnabled": "Live-testi k<PERSON>", "liveUpdateDisabled": "Live-testi pois kä<PERSON>", "liveUpdateDescription": "Viimeksi muutettu liukusäätimen arvo lähetetään laitteeseen.", "demoDevices": "Demolaitteet", "showDemoDevices": "Näytä demolaitteet", "deviceCategoryTimeSwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deviceCategoryMultifunctionalRelay": "Monitoimirele", "deviceCategoryDimmer": "<PERSON><PERSON><PERSON>", "deviceCategoryShutter": "Kaiht<PERSON>t", "deviceCategoryRelay": "<PERSON><PERSON>", "search": "Etsi", "configurationsHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "configurationsDescription": "Hallitse konfiguraatioitasi täällä.", "configurationsNameFailEmpty": "Konfiguraation nimi ei voi olla tyhjä", "configurationDeleted": "Konfigu<PERSON>ati<PERSON> pois<PERSON>u", "codeFound": "{codeType} k<PERSON>i l<PERSON>y", "errorCameraPermission": "<PERSON> ka<PERSON> lupa ELTAKO-k<PERSON><PERSON> s<PERSON>.", "authorizationSuccessful": "Laitteen valtuutus on<PERSON>ui", "wifiAuthenticationResetConfirmationDescription": "Laite on nyt valmis uutta valtuutusta varten.", "settingsResetConnectionHeader": "Resetoi yhteys", "settingsResetConnectionDescription": "<PERSON><PERSON><PERSON><PERSON> todella resetoida yhteyden?", "settingsResetConnectionConfirmationDescription": "<PERSON>hteys on onnistuneesti resetoitu.", "wiredInputChangedSuccesfully": "Kytkentä käyttäytiminen muutettu onnistuneesti", "runtimeChangedSuccesfully": "Käyttöai<PERSON> toiminta muutettu onnistuneesti", "expertModeActivated": "Expert-tila k<PERSON>", "expertModeDeactivated": "Expert-tila poistettu käytöstä", "license": "<PERSON><PERSON><PERSON>", "retry": "Uude<PERSON><PERSON>yr<PERSON><PERSON>", "provisioningConnectingHint": "Laiteyhteyttä muodostetaan. Tämä voi kestää 1 minuutin.", "serialnumberEmpty": "Sarjanumero ei voi olla tyhjä", "interfaceStateInactiveDescriptionBLE": "Bluetooth ei ole k<PERSON>, ota se käyttöön Bluetooth-laitteiden löytämiseksi.", "interfaceStateDeniedDescriptionBLE": "Bluetooth-käyttöoikeuksia ei ole annettu.", "interfaceStatePermanentDeniedDescriptionBLE": "Bluetooth-käyttöoikeuksia ei ole annettu. Ota ne käyttöön laitteen asetuksissa.", "requestPermission": "Pyydä lupaa", "goToSettings": "<PERSON><PERSON><PERSON>", "enableBluetooth": "Ota bluetooth käyttöön", "installed": "Asennett<PERSON>", "teachInDialogDescription": "<PERSON><PERSON><PERSON><PERSON> op<PERSON> la<PERSON> {type}?", "useMatter": "Käytä Matteria", "relayMode": "Aktivoi rele-tila", "whatsNew": "Uutta tässä versiossa", "migrationHint": "Uusien ominaisuuksien käyttäminen edellyttää siirtymistä.", "migrationHeader": "<PERSON><PERSON><PERSON><PERSON>", "migrationProgress": "<PERSON>irt<PERSON>inen käynnissä...", "letsGo": "Mennään!", "noDevicesFound": "Laitteita ei löytynyt. Tarkista, onko laite paritustilassa.", "interfaceStateEmpty": "Laitteita ei lö<PERSON>", "ssidEmpty": "SSID ei voi olla tyhjä", "passwordEmpty": "Salasana ei voi olla tyhjä", "settingsDeleteSettingsHeader": "<PERSON><PERSON><PERSON> no<PERSON>us", "settingsDeleteSettingsDescription": "<PERSON><PERSON><PERSON><PERSON> varmasti nollata kaikki as<PERSON>?", "settingsDeleteSettingsConfirmationDescription": "<PERSON><PERSON><PERSON> on onnistuneesti nollattu.", "locationNotFound": "Sijaintia ei lö<PERSON>y", "timerProgramEmptySaveHint": "<PERSON><PERSON><PERSON><PERSON><PERSON> on tyhjä. <PERSON><PERSON>tko peruuttaa muokkauksen?", "timerProgramDaysEmptySaveHint": "Viikonpäiviä ei ole valittu. Haluat<PERSON> kuitenkin tallentaa a<PERSON>?", "timeProgramNoDays": "Vähintään yksi viikonpäivä on aktivoitava", "timeProgramColliding": "<PERSON><PERSON><PERSON><PERSON><PERSON> on r<PERSON><PERSON><PERSON><PERSON> {program}", "timeProgramDuplicated": "<PERSON><PERSON><PERSON><PERSON><PERSON> on kopio ohjelmasta {program}", "screenshotZgw16": "Omakotitalo", "interfaceStateUnknown": "Laitteita ei lö<PERSON>", "settingsPinChange": "Vaihda PIN-koodi", "timeProgrammOneTime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeProgrammRepeating": "<PERSON><PERSON><PERSON><PERSON>", "generalIgnore": "<PERSON><PERSON><PERSON> huo<PERSON>", "timeProgramChooseDay": "Valitse päivä", "generalToday": "Tänää<PERSON>", "generalTomorrow": "<PERSON><PERSON><PERSON>", "bluetoothAndPINChangedSuccessfully": "Bluetooth ja PIN-koodi vai<PERSON><PERSON><PERSON> onnistuneesti", "generalTextDimTime": "Himmennysaika", "discoverySu62Description": "1-<PERSON><PERSON><PERSON>inen Bluetooth kellokytkin", "bluetoothAlwaysOnTitle": "<PERSON><PERSON>", "bluetoothAlwaysOnDescription": "Bluetooth on jat<PERSON>vasti käytössä.", "bluetoothAlwaysOnHint": "Huomio: <PERSON><PERSON> asetus on aktivoituna, laite on jat<PERSON><PERSON><PERSON> kaikkien nähtävissä Bluetoothin kautta! Tällöin on suositeltavaa vaihtaa oletus PIN-koodi.", "bluetoothManualStartupOnTitle": "Väliaikaisesti päälle", "bluetoothManualStartupOnDescription": "<PERSON><PERSON> virt<PERSON>, Bluetooth aktivoituu 3 minuutiksi.", "bluetoothManualStartupOnHint": "Huomio: <PERSON><PERSON><PERSON><PERSON><PERSON> valmiustila aktivoituu 3 minuutiksi ja sammuu sen jälkeen. <PERSON><PERSON> halut<PERSON> muodos<PERSON>a uusi yhtey<PERSON>, pain<PERSON><PERSON> on pidettävä painettuna noin 5 sekuntia.", "bluetoothManualStartupOffTitle": "<PERSON><PERSON><PERSON><PERSON>", "bluetoothManualStartupOffDescription": "Bluetooth aktivoidaan manuaalisesti painiketuloon kytketyllä pain<PERSON>.", "bluetoothManualStartupOffHint": "Huomio: <PERSON><PERSON><PERSON> aktivo<PERSON>, painike<PERSON>loon kytkettyä painiketta tulee pitää painettuna noin 5 sekunnin ajan.", "timeProgrammOneTimeRepeatingDescription": "<PERSON><PERSON><PERSON><PERSON> voidaan joko suorittaa toistuvasti suorittamalla kytkentä aina määritettyinä päivinä ja kellonai<PERSON>, tai ne voidaan suorittaa vain kertaalleen määritettynä kytkentäaikana.", "versionHeader": "Versio {version}", "releaseNotesHeader": "Julkaisutiedot", "release30Header": "Uusi ELTAKO Connect -appi on nyt täällä!", "release30FeatureDesignHeader": "<PERSON><PERSON><PERSON>", "release30FeatureDesignDescription": "<PERSON><PERSON><PERSON> on tä<PERSON>in uudi<PERSON>ttu ja se on saanut uuden ulko<PERSON>un. <PERSON> on nyt entistä helpompaa ja intuitiivisempaa.", "release30FeaturePerformanceHeader": "Parannett<PERSON>", "release30FeaturePerformanceDescription": "Nauti sujuvammasta käyttökokemuksesta ja lyhentyneistä latausajoista - parempaa käyttökokemusta ajatellen.", "release30FeatureConfigurationHeader": "Laitteiden väliset kokoonpanot", "release30FeatureConfigurationDescription": "<PERSON><PERSON><PERSON> la<PERSON>ot ja siirrä ne muihin laitteisiin. Laitteiden ei tarvitse olla sa<PERSON>, vaan voit esimerkiksi siirtää S2U12DBT1+1-UC-laitteen kokoonpanon ja asetukset ASSU-BT-laitteeseen tai päinvastoin.", "release31Header": "Uusi uppoasennettava 1-ka<PERSON>vainen Bluetooth astro-k<PERSON>kytkin on nyt täällä!", "release31Description": "Mitä SU62PF-BT/UC:llä voi tehdä?", "release31SU62Name": "SU62PF-BT/UC", "release31DeviceNote1": "Jopa 60 eri a<PERSON>.", "release31DeviceNote2": "Astro-toiminto: <PERSON><PERSON><PERSON><PERSON><PERSON> kytkee haluamasi laitteet aina auringon<PERSON>un ja -laskun mukaan.", "release31DeviceNote3": "Satunnaistila: Kytkentäikoja muutetaan satunnaisesti 15 minuutilla, simuloiden läsnäoloa.", "release31DeviceNote4": "Kesä-/talviajan vaihto: Kellokytkin siirtyy aina automaattisesti kesä- tai talviaikaan.", "release31DeviceNote5": "<PERSON><PERSON><PERSON>- ja ohjausjännite 12-230V UC.", "release31DeviceNote6": "<PERSON><PERSON><PERSON><PERSON> my<PERSON> manuaalista kytkentää varten.", "release31DeviceNote7": "1 sulkeutuva (NO) potentiaalivapaa kosketin 10 A/250 V AC.", "release31DeviceNote8": "Aikaohjelmien kertaluonteinen suorittaminen.", "generalNew": "<PERSON>us<PERSON>", "yearsAgo": "{count, plural, one {Viime vuonna} other {{count} vuotta sitten}}", "monthsAgo": "{count, plural, one {<PERSON><PERSON><PERSON> ku<PERSON>a} other {{count} kuukautta sitten}}", "weeksAgo": "{count, plural, one {Vii<PERSON> viikolla} other {{count} viikkoa sitten}}", "daysAgo": "{count, plural, one {<PERSON><PERSON><PERSON>} other {{count} pä<PERSON><PERSON><PERSON> sitten}}", "minutesAgo": "{count, plural, one {Minuutti sitten} other {{count} minuuttia sitten}}", "hoursAgo": "{count, plural, one {<PERSON><PERSON> sitten} other {{count} tuntia sitten}}", "secondsAgo": "{count, plural, one {sekunti sitten} other {{count} sekuntia sitten}}", "justNow": "Tällä he<PERSON>kellä", "discoveryEsripmDescription": "Impulssirele IP Matter", "generalTextKidsRoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generalDescriptionKidsRoom": "<PERSON><PERSON> valo sytytet<PERSON><PERSON>n pitäen painiketta painettuna pitkään ({mode}), valaistus syttyy minimitasolla noin 1 sekunnin kuluttua ja kirkastuu hitaasti niin kauan kuin painiketta pidetään painettuna ilman, että viimeksi tallentunut valaistustaso muuttuu.", "generalTextSceneButton": "Tilannepainike", "settingsEnOceanConfigHeader": "EnOcean-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enOceanConfigChangedSuccessfully": "EnOcean-konfiguraatio muutettu onnistuneesti", "activateEnOceanRepeater": "Aktivoi EnOcean-vahvistin", "enOceanRepeaterLevel": "<PERSON>ah<PERSON><PERSON><PERSON> to<PERSON>", "enOceanRepeaterLevel1": "1-<PERSON><PERSON><PERSON>", "enOceanRepeaterLevel2": "2-<PERSON><PERSON><PERSON>", "enOceanRepeaterOffDescription": "Langattomia signaaleja antureilta ei vastaanoteta.", "enOceanRepeaterLevel1Description": "Vain signaalit kaikilta langattomilta painikkeilta/antureilta <PERSON>, tarkistetaan ja lähetetään eteenpäin täydellä lähetysteholla. Muiden vahvistimen toistamat langattomat signaalit jätetään huomio<PERSON>, tiedon määrän vähentämiseksi.", "enOceanRepeaterLevel2Description": "Langattomien painikkeiden/anturien signaal<PERSON> l<PERSON>, my<PERSON>s 1-tasoisen vahvistimen langattomat signaalit huomioidaan ja vahvistetaan. Vahvistin pystyy vastaanottamaan ja vahvistamaan signaalin siis enintään kaksi kertaa, 1-<PERSON>son vahvistimelta 2-tason vahvistimelle. \nLangattomia vahvistimia ei tarvitse erikseen opettaa. Ne aina vastaanottavat ja vahvistavat kaikkien langattomien painikkeiden/antureiden lähettämiä langattomia signaaleja kantama alueensa sisällä.", "settingsSensorHeader": "Anturit ja painik<PERSON>et", "sensorChangedSuccessfully": "Anturit vai<PERSON>det<PERSON> onnistuneesti", "wiredButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> painike", "enOceanId": "EnOcean-ID", "enOceanAddManually": "Syötä tai skannaa EnOcean-ID", "enOceanIdInvalid": "Virheellinen EnOcean-ID", "enOceanAddAutomatically": "Opeta EnOcean viestillä", "enOceanAddDescription": "Langaton EnOcean-protok<PERSON>a mahdollistaa langattomien painikkeiden opettamisen ja niiden käytön toimilaitteessasi.\n\nValitse joko automa<PERSON> opettaminen, jolloin voit opettaa painikkeet vain painikkeen painall<PERSON>, tai valitse manuaalinen vaiht<PERSON>, jolloin voit skannata tai kirjoittaa painikkeen EnOcean-ID tunnuksen.", "enOceanTelegram": "<PERSON><PERSON><PERSON>", "enOceanCodeScan": "Syötä {sensorType} EnOcean-ID -koodi tai skannaa {sensorType} EnOcean-QR -koodi lisätäksesi sen.", "enOceanCode": "EnOcean QR-koodi", "enOceanCodeScanDescription": "<PERSON>tsi EnOcean-koodi {sensorType}-anturistasi ja skannaa se kamerall<PERSON>.", "enOceanButton": "EnOcean-painike", "enOceanBackpack": "EnOcean-adapteri", "sensorNotAvailable": "Antureita ei ole vielä paritettu", "sensorAdd": "Lisää antureita", "sensorCancel": "Peruuta opettaminen", "sensorCancelDescription": "Haluatteko todella peruuttaa painikkeen opetuksen?", "getEnOceanBackpack": "<PERSON><PERSON><PERSON> langaton EnOcean plug-in adapteri", "enOceanBackpackMissing": "Jotta pä<PERSON>set täydellisen liitettävyyden ja kommunikaation fantastiseen ma<PERSON>an, tarvitset EnOcean-sovittimen.\nKlikkaa tästä saadaksesi lisätietoa", "sensorEditChangedSuccessfully": "{sensorName} muutettu onnistuneesti", "sensorConnectedVia": "yhdistetty {deviceName} kautta", "lastSeen": "<PERSON><PERSON>me<PERSON><PERSON> hava<PERSON>u", "setButtonOrientation": "<PERSON><PERSON> suunta", "setButtonType": "Aseta <PERSON>", "button1Way": "1-<PERSON><PERSON><PERSON><PERSON> painike", "button2Way": "2-<PERSON><PERSON><PERSON><PERSON> painike", "button4Way": "4-<PERSON><PERSON><PERSON><PERSON> painike", "buttonUnset": "ei asetettu", "button": "<PERSON><PERSON>", "sensor": "<PERSON><PERSON><PERSON>", "sensorsFound": "{count, plural, =0 {<PERSON><PERSON><PERSON> ei löydetty} one {1 anturi löydetty} other {{count} anturia löydetty}}", "sensorSearch": "Etsi antureita", "searchAgain": "<PERSON><PERSON><PERSON>", "sensorTeachInHeader": "Opeta {sensorType}", "sensorChooseHeader": "Valitse {sensorType}", "sensorChooseDescription": "<PERSON><PERSON><PERSON>, jonka haluat op<PERSON>a", "sensorCategoryDescription": "Valitse lisättävän anturin kategoria.", "sensorName": "<PERSON><PERSON><PERSON><PERSON> nimi", "sensorNameFooter": "<PERSON><PERSON><PERSON>", "sensorAddedSuccessfully": "{sensorName} l<PERSON><PERSON><PERSON><PERSON>nist<PERSON>", "sensorDelete": "Poista {sensorType}", "sensorDeleteHint": "<PERSON><PERSON><PERSON><PERSON> todella poistaa {sensorType} {sensorName}?", "sensorDeletedSuccessfully": "{sensorName} pois<PERSON><PERSON><PERSON> on<PERSON><PERSON>", "buttonTapDescription": "<PERSON><PERSON><PERSON>, jonka haluat l<PERSON>.", "waitingForTelegram": "Toimilaite odottaa signaalia.", "copied": "Ko<PERSON><PERSON><PERSON>", "pairingFailed": "{sensorType} on jo paritettu", "generalDescriptionUniversalbutton": "Yleispainikkeella himmennyksen suunnan vaihto tapahtuu vapauttamalla painike hetkeksi. Päälle/poiskytkentä tapahtuu lyhyellä painalluksella.", "generalDescriptionDirectionalbutton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on vivun yläreunassa \"päällekytkentä ja ylös himmennys\" ja alareunassa \"poiskytkentä ja alas himmennys\".", "matterForwardingDescription": "Matter viestien välittäminen eteenpäin", "none": "<PERSON><PERSON>", "buttonNoneDescription": "Painikkeella ei ole toim<PERSON>", "buttonUnsetDescription": "Painikkeelle ei ole määritelty käyttäytymistä", "sensorButtonTypeChangedSuccessfully": "Painikkeen tyyppi on vaihdettu onnistuneesti", "forExample": "esim. {example}}", "enOceanQRCodeInvalidDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> vain tuotantoviikosta 44/20 alkaen", "input": "<PERSON><PERSON>", "buttonSceneValueOverride": "<PERSON><PERSON><PERSON>inik<PERSON>en arvo", "buttonSceneValueOverrideDescription": "Tilannepainikkeen arvo korvataan nykyisellä himmennysarvolla painikkeen pitkällä painalluksella.", "buttonSceneDescription": "Tilannepainike kytkee päälle tietyllä himmennysarvolla.", "buttonPress": "<PERSON><PERSON>", "triggerOn": "Yleispainike tai suuntapainike painettuna päällekytkentä puolelta.", "triggerOff": "Yleispainike tai suuntapainike painettuna poiskytkentä puolelta.", "centralOn": "Keskusohjaus ON", "centralOff": "Keskusohjaus OFF", "centralButton": "Keskusohjauspainike", "enOceanAdapterNotFound": "EnOcean-adapteria ei löydetty", "updateRequired": "<PERSON><PERSON><PERSON><PERSON>", "updateRequiredDescription": "Sovelluksesi tarvitsee p<PERSON><PERSON>, jotta se tukee tätä uutta laitetta.", "release32Header": "Uusi 64-<PERSON><PERSON><PERSON> j<PERSON> on Matter ja EnO<PERSON>n, <PERSON><PERSON><PERSON> uus<PERSON> 62-sa<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tooth-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ovat nyt saatavilla!", "release32EUD64Header": "Uusi uppoasennettava 1-kanavainen jopa 300W <PERSON><PERSON><PERSON>, jossa on Matter Wi-Fi:n kautta, on nyt täällä!", "release32EUD64Note1": "<PERSON><PERSON>, pä<PERSON><PERSON>-/poiskytkentä<PERSON>uden, lasten<PERSON><PERSON>-/torkkutoim<PERSON>on ja monien muiden toimintojen asetuksia.", "release32EUD64Note2": "EUD64NPN-IPM:n toimintoja voidaan laajentaa plug-in adapterien, kuten langattoman EnOcean plug-in adapterin, EOA64 avulla.", "release32EUD64Note3": "Jopa 30 eri langatonta EnOcean-painiketta voidaan yhdistää suoraan EUD64NPN-IPM:ään EnOcean-adapterin EOA64 avulla ja sen avulla ne voidaan myös välittää eteenpäin Matteriin.", "release32EUD64Note4": "Laitteen kaksi langallista painiketuloa voidaan yhdistää suoraan EUD64NPN-IPM:ään tai ne voidaan myös välittää eteenpäin Matteriin.", "release32ESR64Header": "Uusi uppoasennettava 1-kanavainen jopa 16A potentiaalivapaa yleisrele, jossa on Matter Wi-Fi:n kautta, on nyt täällä!", "release32ESR64Note1": "Erilaisten toim<PERSON><PERSON><PERSON><PERSON> k<PERSON>, kute<PERSON> (ES), <PERSON><PERSON><PERSON><PERSON><PERSON> (ER), välirele avautu<PERSON><PERSON> (ER, NO) ja paljon muuta.", "release32ESR64Note2": "ESR64PF-IPM:n toimintoja voidaan laajentaa adapterien, kuten langattoman EnOcean plug-in adapterin EOA64, avulla.", "release32ESR64Note3": "Jopa 30 eri langatonta EnOcean-painiketta voidaan yhdistää suoraan ESR64PF-IPM:ään langattoman EnOcean-adapterin EOA64 avulla ja sen avulla ne voidaan myös välittää eteenpäin Matteriin.", "release32ESR64Note4": "Laitteen yksi langallinen painiketulo voidaan yhdistää suoraan ESR64PF-IPM:ään tai välittää eteenpäin Matteriin.", "buttonsFound": "{count, plural, =0 {<PERSON><PERSON><PERSON><PERSON> ei löydetty} one {1 painike löydetty} other {{count} painiketta löydetty}}", "doubleImpuls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "impulseDescription": "<PERSON><PERSON> ka<PERSON> on p<PERSON><PERSON><PERSON><PERSON>, se sammutetaan impulssilla.", "locationServiceEnable": "Aktivoi sijainti", "locationServiceDisabledDescription": "<PERSON><PERSON><PERSON><PERSON> on poistettu k<PERSON>ytöstä. Käyttöjärjestelmäsi tarvitsee sijainnin löytääkseen Bluetooth-laitteet.", "locationPermissionDeniedNoPosition": "Sijaintilupia ei ole my<PERSON>tty. Käyttöjärjestelmäsi edellyttää sijaintioikeuksia, jotta <PERSON>-laitteet voidaan löytää. <PERSON>li sijaintioikeus laitteesi asetuksissa.", "interfaceStatePermanentDeniedDescriptionDevicesAround": "Lähellä oleville laitteille ei myö<PERSON>tty lupaa. <PERSON>ta lupa käyttöön laitteen asetuksissa.", "permissionNearbyDevices": "Lähellä olevat laitteet", "release320Header": "Uusi entistä tehokkaampi yleishimmennin EUD12NPN-BT/600W-230V on nyt saatavissa!", "release320EUD600Header": "Mit<PERSON> uudella EUD12NPN-BT/600W voi tehdä?", "release320EUD600Note1": "Yleishimmennin jopa 600W teholla", "release320EUD600Note2": "Laajennettavissa LUD12-tehoyksiköllä jopa 3800W:iin asti.", "release320EUD600Note3": "Paikallinen käyttö yleispainikkeella tai suuntapainikkeella", "release320EUD600Note4": "Keskusohjaustoiminnot Päällä / Pois p<PERSON>ltä", "release320EUD600Note5": "Liiketunnistintulo lis<PERSON> mukavuutta", "release320EUD600Note6": "Integroitu k<PERSON>, jossa on 10 kytkentäohjelmaa", "release320EUD600Note7": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "release320EUD600Note8": "Yksilöllinen päällekytkentäkirkkaus", "mqttClientCertificate": "Asiakassertifikaatti", "mqttClientCertificateHint": "MQTT-asiakasvarmenne", "mqttClientKey": "Asiakasavain", "mqttClientKeyHint": "MQTT-asiakasavain", "mqttClientPassword": "Asiakas salasana", "mqttClientPasswordHint": "MQTT-asiakas sa<PERSON>ana", "mqttEnableHomeAssistantDiscovery": "Ota HomeAssistant MQTT Discovery käyttöön", "modbusTcp": "Modbus TCP", "enableInterface": "<PERSON><PERSON> k<PERSON>öliittym<PERSON> kä<PERSON>töön", "busAddress": "Väyläosoite", "busAddressWithAddress": "Väyläosoite {index}", "deviceType": "Laitteen tyyppi", "registerTable": "{count, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "currentValues": "Nyk<PERSON><PERSON> a<PERSON>vo<PERSON>", "requestRTU": "Pyydä RTU", "requestPriority": "<PERSON><PERSON><PERSON><PERSON><PERSON> prioriteetti", "mqttForwarding": "Edelleenlähetys MQTT:hen", "historicData": "Historiatiedot", "dataFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataType": "Tietotyyppi", "description": "<PERSON><PERSON><PERSON>", "readWrite": "<PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>", "unit": "Yksikkö", "registerTableReset": "Rekisteritaulukon nollaus", "registerTableResetDescription": "<PERSON><PERSON><PERSON><PERSON> todella nollata rekisteritaulukon?", "notConfigured": "<PERSON><PERSON>", "release330ZGW16Header": "Merkittävä päivitys ZGW16WL-IP:lle", "release330Header": "ZGW16WL-IP yhdessä 16 eri energiamittarin kanssa", "release330ZGW16Note1": "Tukee jopa 16 ELTAKO Modbus -energiamittaria.", "release330ZGW16Note2": "Modbus TCP -tuki", "release330ZGW16Note3": "MQTT Discovery -tuki", "screenshotButtonLivingRoom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registerChangedSuccessfully": "Rekisteri on vaihdettu onnistuneesti", "serverCertificateEmpty": "Palvelimen varmenne ei voi olla tyhjä", "registerTemplates": "Rekisterimallit", "registerTemplateChangedSuccessfully": "Rekisterimalli muutettu onnistuneesti", "registerTemplateReset": "Rekisterimallin nollaus", "registerTemplateResetDescription": "<PERSON><PERSON><PERSON><PERSON> todella nollata re<PERSON>?", "registerTemplateNotAvailable": "Rekisterimalleja ei saatavilla", "rename": "<PERSON><PERSON><PERSON>", "registerName": "<PERSON><PERSON><PERSON><PERSON> nimi", "registerRenameDescription": "<PERSON> oma nimi", "restart": "Käynnistä laite uudelleen", "restartDescription": "<PERSON><PERSON><PERSON><PERSON> todella käynnistää laitteen uudelleen?", "restartConfirmationDescription": "<PERSON><PERSON> k<PERSON> nyt uudelleen", "deleteAllElectricityMeters": "Poistetaan kaikki energiamittarit", "deleteAllElectricityMetersDescription": "<PERSON><PERSON><PERSON><PERSON> todella poistaa kaikki energiamittarit?", "deleteAllElectricityMetersConfirmationDescription": "<PERSON><PERSON><PERSON> energiamittarit on onnistuneesti poistettu", "resetAllElectricityMeters": "<PERSON><PERSON><PERSON> kaikki energia<PERSON><PERSON>i k<PERSON>", "resetAllElectricityMetersDescription": "<PERSON><PERSON><PERSON><PERSON> todella nollata kaikki energiamittarin asetuk<PERSON>?", "resetAllElectricityMetersConfirmationDescription": "<PERSON><PERSON><PERSON> ener<PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON> on onnistuneesti nollattu.", "deleteElectricityMeterHistories": "Poista kaikki energiamittari historiatiedot", "deleteElectricityMeterHistoriesDescription": "<PERSON><PERSON><PERSON><PERSON> todella poistaa kaikki energiamittari historiatiedot?", "deleteElectricityMeterHistoriesConfirmationDescription": "<PERSON><PERSON><PERSON> energiamittari historiatiedot on onnistuneesti poistettu", "multipleElectricityMetersSupportMissing": "Laitteesi tukee tällä hetkellä vain yhtä energiamittaria. Päivitä laiteohjelmisto.", "consumptionWithUnit": "Käyttö (kWh)", "exportWithUnit": "<PERSON><PERSON><PERSON> (kWh)", "importWithUnit": "<PERSON><PERSON><PERSON> (kWh)", "resourceWarningHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "mqttAndTcpResourceWarning": "MQTT:n ja Modbus TCP:n samanaikainen käyttö ei ole mahdollista järjestelmän rajallisten resurssien vuoksi. <PERSON><PERSON> {protocol} ensin pois käytöstä.", "mqttEnabled": "MQTT käytössä", "redirectMQTT": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "redirectModbus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unsupportedSettingDescription": "Jotkin laiteasetukset eivät ole tuettuja nykyisessä laiteohjelmistoversiossa. Päivitä laiteohjelmistosi, jotta voit käyttää uusia ominaisuuksia.", "updateNow": "Päivitä nyt", "zgw241Hint": "Tämän päivityksen myötä Modbus TCP on oletusarvoisesti käytössä ja MQTT on poistettu käytöstä. Tämä voidaan muuttaa asetuksissa. Jopa 16 laskurin tuen myötä on tehty monia optimointeja; tämä voi johtaa muutoksiin laitteen asetuksissa. Käynnistä laite uudelleen asetusten säätämisen jälkeen.", "deviceConfigChangedSuccesfully": "Käyttöai<PERSON> toiminta muutettu onnistuneesti", "deviceConfiguration": "Laitteen konfigurointi", "tiltModeToggle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tiltModeToggleFooter": "<PERSON><PERSON> la<PERSON><PERSON><PERSON><PERSON>, kaik<PERSON> on konfiguroitava siellä uude<PERSON>en.", "shaderMovementDirection": "<PERSON>utus y<PERSON>/alas", "shaderMovementDirectionDescription": "Moottorin ylö<PERSON>/alas -liikkeen suunnan kääntäminen päinvastaiseksi", "tiltTime": "Kallistuksen k<PERSON>töaika", "changeTiltModeDialogTitle": "{target, select, true {Enable} false {Disable} other {Change}} tilt function", "changeTiltModeDialogConfirmation": "{target, select, true {Enable} false {Disable} other {Change}}", "generalTextSlatSetting": "<PERSON><PERSON><PERSON>", "generalTextPosition": "<PERSON><PERSON>", "generalTextSlatPosition": "<PERSON><PERSON><PERSON>", "slatSettingDescription": "Slat Setting Kuvaus", "scenePositionSliderDescription": "<PERSON><PERSON><PERSON>", "sceneSlatPositionSliderDescription": "<PERSON><PERSON><PERSON>", "referenceRun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slatAutoSettingHint": "Tässä tilassa sävyjen asennolla ei ole merkitystä ennen kuin säleet säätyvät haluttuun kallistus<PERSON>nto<PERSON>.", "slatReversalSettingHint": "Tässä tilassa sälekaihtimet sulkeutuvat kokonaan ennen kuin säleet säätyvät haluttuun kallistusasentoon.", "release340Header": "Uusi uppoasennettava ESB64NP-IPM-varjostustoimilaite on täällä!", "release340ESB64Header": "<PERSON><PERSON>SB64NP-IPM pystyy?", "release340ESB64Note1": "Matter Gateway -sertif<PERSON><PERSON><PERSON>, jossa on vali<PERSON><PERSON> lamellitoiminto.", "release340ESB64Note2": "<PERSON><PERSON><PERSON> la<PERSON><PERSON>ta painiketuloa manuaalista kytkentää ja Matteriin välittämistä varten.", "release340ESB64Note3": "Laajennettavissa EnOcean-<PERSON><PERSON><PERSON><PERSON><PERSON> (EOA64). Esim. langattoman EnOcean-painikkeen F4T55 kanssa.", "release340ESB64Note4": "Avoin integraatioille OpenAPI-standardiin perustuvan REST API:n ansiosta.", "activateTiltModeDialogText": "<PERSON><PERSON> on k<PERSON>yt<PERSON>ssä, ka<PERSON><PERSON> as<PERSON> menete<PERSON>. <PERSON><PERSON><PERSON><PERSON> varmasti ottaa kallistus<PERSON><PERSON> käyttöön?", "deactivateTiltModeDialogText": "<PERSON><PERSON><PERSON><PERSON> pois<PERSON> k<PERSON>, ka<PERSON><PERSON> men<PERSON>. <PERSON><PERSON><PERSON><PERSON> varmasti poistaa kallistus<PERSON><PERSON> käytöstä?"}