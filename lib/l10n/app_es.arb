{"appName": "ELTAKO Connect", "discoveryHint": "Activa el Bluetooth en el dispositivo para conectarlo", "devicesFound": "{count, plural, =0 {No se encontraron dispositivos} one {1 dispositivo encontrado} other {{count} dispositivos encontrados}}", "@devicesFound": {"description": "Specifies how many device are found in the discovery overview", "type": "text"}, "discoveryDemodeviceName": "{count, plural, one {Dispositivo Demo} other {Dispositivos Demo}}", "discoverySu12Description": "Interruptor horario Bluetooth de 2 canales", "discoveryImprint": "Pie de imprenta", "discoveryLegalnotice": "Aviso legal", "generalSave": "Guardar", "generalCancel": "<PERSON><PERSON><PERSON>", "detailsHeaderHardwareversion": "Versión de hardware", "detailsHeaderSoftwareversion": "Versión del software", "detailsHeaderConnected": "Conectado", "detailsHeaderDisconnected": "Desconectado", "detailsTimersectionHeader": "Programas", "detailsTimersectionTimercount": "de 60 programas utilizados", "detailsConfigurationsectionHeader": "Configuración", "detailsConfigurationPin": "PIN del dispositivo", "detailsConfigurationChannelsDescription": "Canal 1: {channel1}| Canal 2:  {channel2}", "settingsCentralHeader": "Central On/Off", "detailsConfigurationCentralDescription": "Sólo se aplica si el canal está configurado en AUTO", "detailsConfigurationDevicedisplaylock": "Bloquear la pantalla del dispositivo", "timerOverviewHeader": "Programas", "timerOverviewTimersectionTimerinactivecount": "inactivo", "timerDetailsListsectionDays1": "<PERSON><PERSON>", "timerDetailsListsectionDays2": "<PERSON><PERSON>", "timerDetailsListsectionDays3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timerDetailsListsectionDays4": "<PERSON><PERSON>", "timerDetailsListsectionDays5": "Viernes", "timerDetailsListsectionDays6": "Sábado", "timerDetailsListsectionDays7": "Domingo", "timerDetailsHeader": "Programa", "timerDetailsSunrise": "<PERSON><PERSON><PERSON>", "generalToggleOff": "Off", "generalToggleOn": "On", "timerDetailsImpuls": "<PERSON>mpul<PERSON>", "generalTextTime": "<PERSON><PERSON>", "generalTextAstro": "Astro", "generalTextAuto": "Auto", "timerDetailsOffset": "Desplazamiento horario", "timerDetailsPlausibility": "Activar la comprobación de plausibilidad", "timerDetailsPlausibilityDescription": "Si la hora de « Off “ es anterior a la hora de ” On », se ignoran ambas horas, por ejemplo, encender al amanecer y apagar a las 6:00 de la mañana. También hay constelaciones en las que la comprobación no es intencionada, por ejemplo, encender al atardecer y apagar a la 1:00 de la madrugada.", "generalDone": "Listo", "generalDelete": "Bo<PERSON>r", "timerDetailsImpulsDescription": "Cambiar la configuración global de impulso", "settingsNameHeader": "Nombre del dispositivo", "settingsNameDescription": "Este nombre se utiliza para identificar el dispositivo.", "settingsFactoryresetHeader": "Ajustes de fábrica", "settingsFactoryresetDescription": "¿Qué contenidos deben restablecerse?", "settingsFactoryresetResetbluetooth": "Restablecer la configuración de Bluetooth", "settingsFactoryresetResettime": "Restablecer la configuración de la hora", "settingsFactoryresetResetall": "Restablecer valores de fábrica", "settingsDeletetimerHeader": "Borrar programas", "settingsDeletetimerDescription": "¿Deben borrarse realmente todos los programas?", "settingsDeletetimerAllchannels": "Todos los canales", "settingsImpulseHeader": "Tiempo del impulso", "settingsImpulseDescription": "El tiempo del impulso determina la duración del impulso.", "generalTextRandommode": "Modo aleatorio", "settingsChannelsTimeoffsetHeader": "Desplazamiento del solsticio", "settingsChannelsTimeoffsetDescription": "Verano: {summerOffset}min | Invierno: {winterOffset}min", "settingsLocationHeader": "Ubicación", "settingsLocationDescription": "Configura tu ubicación para utilizar las funciones Astro.", "settingsLanguageHeader": "Idioma del dispositivo", "settingsLanguageSetlanguageautomatically": "Establecer el idioma automáticamente", "settingsLanguageDescription": "Elija el idioma para el {deviceType}", "settingsLanguageGerman": "Alemán", "settingsLanguageFrench": "<PERSON><PERSON><PERSON><PERSON>", "settingsLanguageEnglish": "Inglés", "settingsLanguageItalian": "Italiano", "settingsLanguageSpanish": "Español", "settingsDatetimeHeader": "<PERSON><PERSON> y hora", "settingsDatetimeSettimeautomatically": "Aplicar el tiempo del sistema", "settingsDatetimeSettimezoneautomatically": "Establecer automáticamente la zona horaria", "generalTextTimezone": "Zona horaria", "settingsDatetime24Hformat": "Formato de 24 horas", "settingsDatetimeSetsummerwintertimeautomatically": "Tiempo verano/invierno automáticamente", "settingsDatetimeWinter": "Invierno", "settingsDatetimeSummer": "Verano", "settingsPasskeyHeader": "PIN del dispositivo actual", "settingsPasskeyDescription": "Introduzca el PIN actual del dispositivo", "timerDetailsActiveprogram": "Programa activo", "timerDetailsActivedays": "Días activos", "timerDetailsSuccessdialogHeader": "Exitoso", "timerDetailsSuccessdialogDescription": "Programa añadido con éxito", "settingsRandommodeDescription": "El modo aleatorio sólo funciona con programas horarios, no con programas de impulso o astro (salida o puesta del sol).", "settingsSolsticeHeader": "Desplazamiento hora del solsticio", "settingsSolsticeDescription": "La hora indica el desplazamiento con respecto a la puesta de sol. La salida del sol se invierte en consecuencia.", "settingsSolsticeHint": "Ejemplo: \nEn invierno, la conmutación se realiza 30 minutos antes de la puesta de sol, lo que significa que la conmutación también se realiza 30 minutos después de la salida del sol.", "generalTextMinutesShort": "min", "settingsPinDescription": "El PIN es necesario para la conexión.", "settingsPinHeader": "Nuevo PIN del dispositivo", "settingsPinNewpinDescription": "Introduzca un nuevo PIN", "settingsPinNewpinRepeat": "Repita el nuevo PIN", "detailsProductinfo": "Información sobre el producto", "settingsDatetimeSettimeautodescription": "Elija la hora preferida", "minutes": "{count, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "hours": "{count, plural, one {<PERSON><PERSON>} other {<PERSON>ras}}", "seconds": "{count, plural, one {segundo} other {segundos}}", "generalTextChannel": "{count, plural, one {Canal} other {Canales}}", "generalLabelChannel": "Canal {number}", "generalTextDate": "<PERSON><PERSON>", "settingsDatetime24HformatDescription": "Elija el formato preferido", "settingsDatetimeSetsummerwintertime": "Tiempo verano/invierno", "settingsDatetime24HformatValue24": "24-horas", "settingsDatetime24HformatValue12": "12-horas", "detailsEdittimer": "Editar programas", "settingsPinOldpinRepeat": "Por favor, repita el PIN actual", "settingsPinCheckpin": "Comprobación del PIN", "detailsDevice": "{count, plural, one {Dispositivo} other {Dispositivos}}", "detailsDisconnect": "Desconectar", "settingsCentralDescription": "La entrada A1 controla el central On/Off.\nEl central ON/OFF sólo se aplica si el canal está configurado como ON/OFF centralizado.", "settingsCentralHint": "Ejemplo:\nCanal 1 = Central On/Off\nCanal 2 = Off\nA1 = Central On -> Sólo C1 conmuta a On, C2 permanece en Off", "settingsCentralToggleheader": "Conmuta la entrada central", "settingsCentralActivechannelsdescription": "Canales actuales con el ajuste Central On/Off:", "settingsSolsticeSign": "Firme", "settingsDatetimeTimezoneDescription": "Hora de Europa central", "generalButtonContinue": "<PERSON><PERSON><PERSON><PERSON>", "settingsPinConfirmationDescription": "Cambio de PIN exitoso", "settingsPinFailDescription": "El cambio de PIN ha fallado.", "settingsPinFailHeader": "Fallido", "settingsPinFailShort": "El PIN debe tener exactamente 6 dígitos", "settingsPinFailWrong": "El PIN actual es incorrecto", "settingsPinFailMatch": "Los PINs no coinciden", "discoveryLostconnectionHeader": "Conexión perdida", "discoveryLostconnectionDescription": "Se ha desconectado la conexión con el dispositivo.", "settingsChannelConfigCentralDescription": "Se comporta como AUTO y también reacciona a las entradas de control cableados.", "settingsChannelConfigOnDescription": "Cambia el canal a permanentemente ON e ignora los programas", "settingsChannelConfigOffDescription": "Cambia el canal a permanentemente OFF e ignora los programas", "settingsChannelConfigAutoDescription": "Conmuta en función de los programas horarios y astronómicos.", "bluetoothPermissionDescription": "Para la configuración de los dispositivos se requiere Bluetooth.", "timerListitemOn": "Encender", "timerListitemOff": "<PERSON><PERSON><PERSON>", "timerListitemUnknown": "Desconocido", "timerDetailsAstroHint": "Para que los programas astronómicos funcionen correctamente, es necesario establecer la ubicación en los ajustes.", "timerDetailsTrigger": "Disparador", "timerDetailsSunset": "Puesta del sol", "settingsLocationCoordinates": "<PERSON><PERSON><PERSON><PERSON>", "settingsLocationLatitude": "Latitud", "settingsLocationLongitude": "<PERSON><PERSON><PERSON>", "timerOverviewEmptyday": "Actualmente no se utilizan programas para {day}", "timerOverviewProgramloaded": "Los programas se cargan", "timerOverviewProgramchanged": "El programa fue modificado", "settingsDatetimeProcessing": "Se cambia la fecha y la hora", "deviceNameEmpty": "La entrada no debe estar vacía", "deviceNameHint": "La entrada no debe contener más de {count} caracteres.", "deviceNameChanged": "Se cambia el nombre del dispositivo", "deviceNameChangedSuccessfully": "El nombre del dispositivo se ha cambiado con éxito.", "deviceNameChangedFailed": "Se ha producido un error.", "settingsPinConfirm": "Confirmar", "deviceShowInstructions": "1. Activar el Bluetooth del reloj con SET\n2. Pulse el botón de la parte superior para iniciar la búsqueda.", "deviceNameNew": "Introduzca el nuevo nombre del dispositivo", "settingsLanguageRetrieved": "Se recupera la lengua", "detailsProgramsShow": "Mostrar programas", "generalTextProcessing": "Por favor, espere", "generalTextRetrieving": "se solicitan", "settingsLocationPermission": "Permitir que ELTAKO Connect acceda a la ubicación de este dispositivo", "timerOverviewChannelloaded": "Los canales se cargan", "generalTextRandommodeChanged": "Se cambia el modo aleatorio", "detailsConfigurationsectionChanged": "Cambio de la configuración", "settingsSettimeFunctions": "Se modifican las funciones horarias", "imprintContact": "Contacto", "imprintPhone": "Teléfono", "imprintMail": "Correo eléctronico", "imprintRegistrycourt": "Registro mercantil", "imprintRegistrynumber": "Número de registro", "imprintCeo": "Director General", "imprintTaxnumber": "Número VAT", "settingsLocationCurrent": "Ubicación actual", "generalTextReset": "Reiniciar", "discoverySearchStart": "Iniciar la <PERSON>ús<PERSON>", "discoverySearchStop": "Para la búsqueda", "settingsImpulsSaved": "Se guarda el tiempo de impulsos", "settingsCentralNochannel": "No hay canales con el ajuste ON/OFF centralizado", "settingsFactoryresetBluetoothConfirmationDescription": "La conexión Bluetooth se ha restablecido con éxito.", "settingsFactoryresetBluetoothFailDescription": "Fallo en el restablecimiento de las conexiones Bluetooth.", "imprintPublisher": "Editorial", "discoveryDeviceConnecting": "Se establece la conexión", "discoveryDeviceRestarting": "Reiniciando...", "generalTextConfigurationsaved": "Configuración del canal guardada.\n", "timerOverviewChannelssaved": "Guardar canales", "timerOverviewSaved": "Temporizador guardado", "timerSectionList": "Vista de lista", "timerSectionDayview": "Vista del día", "generalTextChannelInstructions": "Ajustes del canal", "generalTextPublisher": "Editorial", "settingsDeletetimerDialog": "¿De verdad quiere borrar todos los programas?", "settingsFactoryresetResetbluetoothDialog": "¿De verdad quiere resetear todos los ajustes de Bluetooth?", "settingsCentralTogglecentral": "Central\nOn/Off", "generalTextConfirmation": "{serviceName} se ha modificado con éxito.", "generalTextFailed": "{serviceName} no se podía cambiar.", "settingsChannelConfirmationDescription": "Los canales fueron cambiados con éxito.", "timerDetailsSaveHeader": "Guardar el programa", "timerDetailsDeleteHeader": "Eliminar el programa", "timerDetailsSaveDescription": "El programa se ha guardado con éxito.", "timerDetailsDeleteDescription": "El programa se ha eliminado con éxito.", "timerDetailsAlertweekdays": "El programa no se puede guardar, porque no hay días de la semana seleccionados.", "generalTextOk": "OK", "settingsDatetimeChangesuccesfull": "La fecha y la hora se han modificado correctamente.", "discoveryConnectionFailed": "Fallo de conexión", "discoveryDeviceResetrequired": "No se ha podido establecer la conexión con el dispositivo. Para solucionar este problema, elimina el dispositivo de tu configuración Bluetooth. Si el problema persiste, póngase en contacto con nuestro servicio técnico.", "generalTextSearch": "Búsqueda de dispositivos", "generalTextOr": "o", "settingsFactoryresetProgramsConfirmationDescription": "Todos los programas se han eliminado con éxito.", "generalTextManualentry": "Introducción manual", "settingsLocationSaved": "Ubicación guardada", "settingsLocationAutosearch": "Búsqueda automática de ubicación", "imprintPhoneNumber": "+49 711 / 9435 0000", "imprintMailAddress": "<EMAIL>", "settingsFactoryresetResetallDialog": "¿Realmente se debe restablecer la configuración de fábrica del dispositivo?", "settingsFactoryresetFactoryConfirmationDescription": "El dispositivo se ha restablecido con éxito a la configuración de fábrica.", "settingsFactoryresetFactoryFailDescription": "El reinicio del dispositivo ha fallado.", "imprintPhoneNumberIos": "+49711/94350000", "mfzFunctionA2Title": "Retardo de conexión en 2 etapas (A2)", "mfzFunctionA2TitleShort": "Retardo de conexión en 2 etapas (A2)", "mfzFunctionA2Description": "Cuando se aplica la tensión de control, comienza el tiempo de retardo T1 entre 0 y 60 segundos. Al final, el contacto 1-2 se cierra y comienza el tiempo de retardo T2 entre 0 y 60 segundos. Al finalizar, se cierra el contacto 3-4. Tras una interrupción, el temporizador vuelve a empezar con T1.", "mfzFunctionRvTitle": "Retardo a la desconexión (RV)", "mfzFunctionRvTitleShort": "RV| Retardo a la desconexión", "mfzFunctionRvDescription": "Cuando se aplica la tensión de control, el contacto cambia a 15-18. \nCuando se interrumpe la tensión de control, comienza el intervalo de tiempo, al final del cual el contacto vuelve a la posición de reposo. Se puede prolongar el tiempo durante el intervalo de tiempo.", "mfzFunctionTiTitle": "Intermitencia empezando con impulso (TI; rele intermitente)", "mfzFunctionTiTitleShort": "TI | Intermitencia empezando con impulso", "mfzFunctionTiDescription": "Mientras la tensión de control está aplicada, el contacto  se cierra y se abre. El tiempo de conmutación en ambas direcciones puede ajustarse por separado. Cuando se aplica la tensión de control, el contacto cambia inmediatamente a 15-18.", "mfzFunctionAvTitle": "Retardo a la conexión (AV)", "mfzFunctionAvTitleShort": "AV | Retardo a la conexión", "mfzFunctionAvDescription": "Cuando se aplica la tensión de control, se inicia el periodo de temporización; cuando se acaba el tiempo, el contacto del relé cambia a 15-18. Tras una interrupción, el periodo de temporización se reinicia.", "mfzFunctionAvPlusTitle": "Retardo a la conexión, conteo regresivo (AV+)", "mfzFunctionAvPlusTitleShort": "AV+ | Retardo a la conexión, conteo regresivo", "mfzFunctionAvPlusDescription": "Funciona como AV, pero después de una interrupción se guarda el tiempo restante.", "mfzFunctionAwTitle": "Comienzo del Intervalo a la desconexión (AW)", "mfzFunctionAwTitleShort": "AW | Comienzo del intervalo a la desconexión", "mfzFunctionAwDescription": "Si se corta la tensión de control, el contacto cambia a 15-18 y vuelve una vez transcurrido el tiempo ajustado. Si se aplica la tensión de control durante el intervalo, el contacto vuelve inmediatamente a la posición de reposo y se borra el tiempo restante.", "mfzFunctionIfTitle": "<PERSON><PERSON><PERSON><PERSON> <PERSON> (IF)", "mfzFunctionIfTitleShort": "IF | Modulador de impulsos", "mfzFunctionIfDescription": "Cuando se aplica la tensión de control, el contacto cambia a 15-18 por el tiempo ajustado. Las siguientes activaciones sólo se evalúan una vez transcurrido el tiempo ajustado.", "mfzFunctionEwTitle": "Cominezo de intervalo a la conexión(EW)l", "mfzFunctionEwTitleShort": "EW | Comienzo del intervalo a la conexión", "mfzFunctionEwDescription": "Cuando se aplica la tensión de control, el contacto cambia a 15-18 y vuelve a la posición de reposo una vez transcurrido el tiempo ajustado. Si se corta la tensión de control durante el tiempo ajustado, el contacto vuelve inmediatamente a la posición de reposo y se borra el tiempo restante.", "mfzFunctionEawTitle": "Comienzo de Intervalo a la conexión y desconexión (EAW)\n", "mfzFunctionEawTitleShort": "EAW | Intervalo a la conexión y desconexión", "mfzFunctionEawDescription": "Cuando se aplica o interrumpe la tensión de control, el contacto del relé cambia a 15-18 y vuelve a invertirse después del tiempo de conmutación ajustado.", "mfzFunctionTpTitle": "Intermitente empezando con pausa (TP)", "mfzFunctionTpTitleShort": "TP | Intermitente empezando con pausa", "mfzFunctionTpDescription": "Descripcion de funcionamiento como TI, pero cuando se aplica la tensión de control el contacto no cambia a 15-18, sino que inicialmente permanece en 15-16 o abierto.", "mfzFunctionIaTitle": "Retardo de desconexión controlado por impulsos (por ejemplo, abrepuertas automático) (IA)", "mfzFunctionIaTitleShort": "IA | Retardo de desconexión controlado por impulsos y modelador de impulsos", "mfzFunctionIaDescription": "Con el inicio de un impulso de control a partir de 20 ms, comienza el intervalo de tiempo T1, al final del cual el contacto cambia por el tiempo T2 a 15-18 (p.ej. para abrepuertas automáticos). Si T1 se ajusta al tiempo más corto 0,1 s, IA funciona como un formador de impulsos en el que T2 expira, independientemente de la longitud de la señal de control (mín. 150 ms).", "mfzFunctionArvTitle": "Retardo a la conexión y desconexión (ARV)", "mfzFunctionArvTitleShort": "ARV | Retardo a la conexión y desconexión", "mfzFunctionArvDescription": "Cuando se aplica la tensión de control, comienza el intervalo de tiempo, al final del cual el contacto cambia a 15 -18. Si se interrumpe la tensión de control, comienza otro intervalo de tiempo, al final del cual el contacto vuelve a la posición de reposo. \nTras una interrupción del retardo de reacción, el intervalo de tiempo vuelve a empezar.\n", "mfzFunctionArvPlusTitle": "Retardo a la conexión y desconexión Retardo a la conexión y desconexión conteo regresivo (ARV+)", "mfzFunctionArvPlusTitleShort": "ARV+ | Retardo a la conexión y desconexión conteo regresivo", "mfzFunctionArvPlusDescription": "Misma función que ARV, pero tras una interrupción del funcionamiento se almacena el tiempo transcurrido.", "mfzFunctionEsTitle": "Telerruptor (ES)", "mfzFunctionEsTitleShort": "ES | Telerruptor", "mfzFunctionEsDescription": "El contacto de cierre cambia de un lado a otro con impulsos de control mayor de 50 ms.", "mfzFunctionEsvTitle": "Telerruptor con retardo a la desconexión con avisador (ESV)", "mfzFunctionEsvTitleShort": "ESV | Telerruptor con retardo a la desconexión con avisador", "mfzFunctionEsvDescription": "Función como SRV. Además, con advertencia de apagado: aproximadamente 30 segundos antes del final del tiempo, la iluminación parpadea 3 veces en intervalos cada vez más cortos.", "mfzFunctionErTitle": "<PERSON><PERSON> (ER)", "mfzFunctionErTitleShort": "ER | Relé", "mfzFunctionErDescription": "Mientras el contacto de control esté cerrado, el contacto  pasa de 15-16 a 15-18.", "mfzFunctionSrvTitle": "Telerruptor con retardo a la desconexión (SRV)", "mfzFunctionSrvTitleShort": "SRV | Telerruptor con retardo a la desconexión", "mfzFunctionSrvDescription": "Con impulsos de control a partir de 50 ms, el contacto cambia permanente su posición. En la posición de contacto 15-18, el dispositivo cambia automáticamente a la posición de reposo 15-16 después de que transcurra el tiempo de retardo.", "detailsFunctionsHeader": "Funciones", "mfzFunctionTimeHeader": "Tiempo (t{index})", "mfzFunctionOnDescription": "Permanente ON", "mfzFunctionOffDescription": "Permanente OFF", "mfzFunctionMultiplier": "Factor", "discoveryMfz12Description": "Relé temporizado multifunción Bluetooth", "mfzFunctionOnTitle": "ON", "mfzFunctionOnTitleShort": "ON", "mfzFunctionOffTitle": "OFF", "mfzFunctionOffTitleShort": "OFF", "mfzMultiplierSecondsFloatingpoint": "0.1 segundos", "mfzMultiplierMinutesFloatingpoint": "0.1 minutos", "mfzMultiplierHoursFloatingpoint": "0.1 horas", "mfzOverviewFunctionsloaded": "Las funciones están cargados", "mfzOverviewSaved": "Función guardada", "settingsBluetoothHeader": "Bluetooth", "bluetoothChangedSuccessfully": "La configuración de Bluetooth se ha cambiado con éxito.", "settingsBluetoothInformation": "Nota: Si este ajuste está activado, el dispositivo estará permanentemente visible para todo el mundo a través de Bluetooth. Se recomienda cambiar el PIN del dispositivo.", "settingsBluetoothContinuousconnection": "Visibilidad permanente", "settingsBluetoothContinuousconnectionDescription": "Al activar la visibilidad permanente, el Bluetooth permanece activo en el dispositivo ({deviceType}) y no es necesario activarlo manualmente antes de establecer una conexión.", "settingsBluetoothTimeout": "Tiempo de espera de la conexión", "settingsBluetoothPinlimit": "Límite del PIN", "settingsBluetoothTimeoutDescription": "La conexión se desconecta tras {timeout} minutos de inactividad.", "settingsBluetoothPinlimitDescription": "Por razones de seguridad, tiene un máximo de {attempts} intentos \npara introducir el PIN. El Bluetooth se desactiva y debe ser reactivado manualmente para una nueva conexión.", "settingsBluetoothPinAttempts": "<PERSON><PERSON>", "settingsResetfunctionHeader": "Restablecer las funciones", "settingsResetfunctionDialog": "Realmente quieres restablecer todas las funciones?", "settingsFactoryresetFunctionsConfirmationDescription": "Todas las funciones se han restablecido con éxito.", "mfzFunctionTime": "<PERSON><PERSON><PERSON> (t)", "discoveryConnectionFailedInfo": "No hay conexión Bluetooth", "detailsConfigurationDevicedisplaylockDialogtext": "Si bloqueas la pantalla del dispositivo, Bluetooth se desactiva y debe volver a activarse manualmente para una nueva conexión.", "detailsConfigurationDevicedisplaylockDialogquestion": "¿Realmente quieres bloquear la pantalla del dispositivo?", "settingsDemodevices": "Mostrar unidades de demostración", "generalTextSettings": "<PERSON><PERSON><PERSON><PERSON>", "discoveryWifi": "WiFi", "settingsInformations": "Información", "detailsConfigurationDimmingbehavior": "Comportamiento de regulación", "detailsConfigurationSwitchbehavior": "Comportamiento de la entrada de control", "detailsConfigurationBrightness": "Luminosidad", "detailsConfigurationMinimum": "<PERSON><PERSON><PERSON>", "detailsConfigurationMaximum": "Máximo", "detailsConfigurationSwitchesGr": "Relé en grupo (GR)", "detailsConfigurationSwitchesGs": "Interruptor de grupo (GS)", "detailsConfigurationSwitchesCloserer": "Relé NA", "detailsConfigurationSwitchesClosererDescription": "OFF -> <PERSON><PERSON><PERSON><PERSON> (On) -> Soltar (Off)", "detailsConfigurationSwitchesOpenerer": "Relé NC", "detailsConfigurationSwitchesOpenererDescription": "On -> <PERSON><PERSON><PERSON><PERSON> (Off) -> Soltar (On)", "detailsConfigurationSwitchesSwitch": "<PERSON><PERSON><PERSON><PERSON>", "detailsConfigurationSwitchesSwitchDescription": "Cada vez que se conmuta, la luz se enciende y se apaga.", "detailsConfigurationSwitchesImpulsswitch": "Telerruptor", "detailsConfigurationSwitchesImpulsswitchDescription": "La pulsación del pulsador enciende y apaga la luz", "detailsConfigurationSwitchesClosererDescription2": "Pulse el pulsador. Al soltarlo, el motor se para", "detailsConfigurationSwitchesImpulsswitchDescription2": "El pulsador se pulsa brevemente para arrancar el motor y se pulsa brevemente para volver a pararlo", "detailsConfigurationWifiloginScan": "Escanear el código QR", "detailsConfigurationWifiloginScannotvalid": "El código escaneado no es válido", "detailsConfigurationWifiloginDescription": "Introduzca el código", "detailsConfigurationWifiloginPassword": "Contraseña", "discoveryEsbipDescription": "Actuador de persianas IP", "discoveryEsripDescription": "Telerruptor/Relé IP", "discoveryEudipDescription": "Regulador universal IP", "generalTextLoad": "Cargando", "wifiBasicautomationsNotFound": "No se ha encontrado ninguna automatización.", "wifiCodeInvalid": "<PERSON><PERSON><PERSON>", "wifiCodeValid": "<PERSON><PERSON><PERSON> v<PERSON>", "wifiAuthorizationLogin": "Conectar", "wifiAuthorizationLoginFailed": "Fallo en el inicio de sesión", "wifiAuthorizationSerialnumber": "Número de serie", "wifiAuthorizationProductiondate": "Fecha de producción", "wifiAuthorizationProofofpossession": "PoP", "generalTextWifipassword": "Contraseña WiFi", "generalTextUsername": "Nombre de usuario", "generalTextEnter": "O INTRODUZCA MANUALMENTE", "wifiAuthorizationScan": "Escanea el código ELTAKO.", "detailsConfigurationDevicesNofunctionshinttext": "Este dispositivo no admite actualmente ninguna otra configuración más", "settingsUsedemodelay": "Utilizar el retardo de demostración", "settingsImpulsLoad": "Se carga el tiempo de impulsos.", "settingsBluetoothLoad": "Se carga la configuración de Bluetooth.", "detailsConfigurationsectionLoad": "Se carga las configuraciones", "generalTextLogin": "Log in\n", "generalTextAuthentication": "Autentificar", "wifiAuthorizationScanDescription": "Busque el código ELTAKO en el dispositivo-WiFi o en la hoja de información entregada y escanearlo con tu cámara.", "wifiAuthorizationScanShort": "Escanear el código ELTAKO", "detailsConfigurationEdgemode": "Curvas de regulación", "detailsConfigurationEdgemodeLeadingedge": "Principio de fase", "generalTextNetwork": "Red", "wifiAuthenticationSuccessful": "Autenticación exitosa", "detailsConfigurationsectionSavechange": "Configuración modificada", "discoveryWifiAdddevice": "Añadir dispositivo Wi-Fi", "wifiAuthenticationDelay": "Esto puede durar hasta 1 minuto.", "generalTextRetry": "Reintentar", "wifiAuthenticationCredentials": "Por favor, introduzca el password de su WiFi", "wifiAuthenticationSsid": "SSID", "wifiAuthenticationDelaylong": "<PERSON><PERSON><PERSON> durar hasta 1 minuto hasta que el dispositivo esté listo y aparezca en la aplicación", "wifiAuthenticationCredentialsShort": "introduzca los datos de WiFi", "wifiAuthenticationTeachin": "Enlaza el dispositivo con el WiFi", "wifiAuthenticationEstablish": "Establecer cone<PERSON> con el dispositivo", "wifiAuthenticationEstablishLong": "El dispositivo se conecta con WiFi {ssid}", "wifiAuthenticationFailed": "Error de conexión. Desconecte el aparato de la red eléctrica durante unos segundos y vuelva a conectarlo.", "wifiAuthenticationReset": "Restablecer la autenticación", "wifiAuthenticationResetHint": "Se eliminan todos los datos de autenticación.", "wifiAuthenticationInvaliddata": "Los datos de autenticación no son válidos", "wifiAuthenticationReauthenticate": "Autenticar de nuevo", "wifiAddhkdeviceHeader": "<PERSON><PERSON>dir dispositivo", "wifiAddhkdeviceDescription": "Conecta tu nuevo dispositivo ELTAKO a tu WLAN a través de la App Apple Home.", "wifiAddhkdeviceStep1": "1. Abre la App Apple Home.", "wifiAddhkdeviceStep2": "2. <PERSON>z clic en el signo más de la esquina superior derecha de la App y selecciona **Añadir dispositivo**.", "wifiAddhkdeviceStep3": "3. Sigue las instrucciones de la App.", "wifiAddhkdeviceStep4": "Ahora puedes configurar tu dispositivo en la App ELTAKO Connect.", "detailsConfigurationRuntime": "Tiempo de funcionamiento", "detailsConfigurationRuntimeMode": "Modo", "generalTextManually": "Manualmente", "detailsConfigurationRuntimeAutoDescription": "El actuador de persianas determina automáticamente el tiempo de funcionamiento del motor de la persiana para cada trayecto de la posición final inferior a la superior (recomendado).\nTras la puesta en marcha o modificaciones, debe realizarse un movimiento de abajo hacia arriba sin interrupción.", "detailsConfigurationRuntimeManuallyDescription": "Die Laufzeit des Rollladenmotors wird manuell mit Hilfe der unten angegebenen Dauer eingestellt.\nAcht<PERSON>, dass die eingestellte Laufzeit mit der tatsächlichen Laufzeit Ihres Rollladenmotors übereinstimmt.\nNach Inbetriebnahme oder Änderungen muss ein Lauf von unten nach oben ohne Unterbrechung durchgeführt werden.", "detailsConfigurationRuntimeDemoDescription": "El modo del display LCD sólo está disponible a través de la API REST", "generalTextDemomodeActive": "Modo demo activo", "detailsConfigurationRuntimeDuration": "Duración", "detailsConfigurationSwitchesGs4": "Interruptor de grupo con función inversión de marcha TIpp (GS4)", "detailsConfigurationSwitchesGs4Description": "Interruptor de grupo con función de inversión de marcha para controlar las persianas", "screenshotSu12": "Luz exterior", "screenshotS2U12": "Luz exterior", "screenshotMfz12": "Bomba", "screenshotEsr62": "Lámpara", "screenshotEud62": "Luz de techo", "screenshotEsb62": "Persianas balcón", "detailsConfigurationEdgemodeLeadingedgeDescription": "LC1-LC3 son posiciones de confort con diferentes curvas de regulación para lámparas LED regulables de 230 V, que no pueden regularse lo suficiente en AUTO debido a su diseño y, por lo tanto, deben forzarse al control de final de fase.", "detailsConfigurationEdgemodeAutoDescription": "AUTO permite la regulación de todos los tipos de lámparas.", "detailsConfigurationEdgemodeTrailingedge": "Final de fase", "detailsConfigurationEdgemodeTrailingedgeDescription": "LC4-LC6 son posiciones comfort con diferentes curvas de regulación para lámparas LED regulables de 230 V.", "updateHeader": "Actualización del firmware", "updateTitleStepSearch": "Buscando actualización", "updateTitleStepFound": "Se ha encontrado una actualización", "updateTitleStepDownload": "Descarga de la actualización", "updateTitleStepInstall": "Instalación de la actualización", "updateTitleStepSuccess": "Actualización exitosa", "updateTitleStepUptodate": "Ya está actualizado", "updateTitleStepFailed": "Actualización fallida", "updateButtonSearch": "Buscar actualizaciones", "updateButtonInstall": "Instalar actualización", "updateCurrentversion": "Versión actual", "updateNewversion": "Nueva actualización de firmware disponible", "updateHintPower": "La actualización sólo se inicia cuando la salida del dispositivo no está activa. El dispositivo no debe desconectarse de la alimentación y no se debe salir de la App durante la actualización.", "updateButton": "Actualización", "updateHintCompatibility": "Se recomienda una actualización, de lo contrario algunas funciones de la App se verán limitadas.", "generalTextDetails": "Detalles", "updateMessageStepMetadata": "Cargando información de actualización", "updateMessageStepPrepare": "La actualización está en preparación", "updateTitleStepUpdatesuccessful": "La actualización se está comprobando", "updateTextStepFailed": "Sentimos que algo haya ido mal durante la actualización, inténtalo de nuevo en unos minutos o espera a que tu dispositivo se actualice automáticamente (requiere conexión a Internet).", "configurationsNotavailable": "Aún no hay configuraciones disponibles", "configurationsAddHint": "Crea nuevas configuraciones al conectarte a un dispositivo y guardar una configuración.", "@configurationsAddHint": {"description": "Now available for all devices not only Bluetooth", "type": "text"}, "configurationsEdit": "Editar configuración", "generalTextName": "Nombre", "configurationsDelete": "Borrar configuración", "configurationsDeleteHint": "¿Debe borrarse realmente la configuración: {configName}?", "configurationsSave": "Guardar configuración", "configurationsSaveHint": "Aquí puedes guardar la configuración de tu dispositivo actual o cargar una configuración guardada anteriormente.", "configurationsImport": "Importar configuración", "configurationsImportHint": "¿Debes transferirse realmente la configuración {configName}?", "generalTextConfigurations": "{count, plural, one {Configuración} other {Configuracions}}", "configurationsStepPrepare": "Preparando la configuración", "configurationsStepName": "Introduzca un nombre para la configuración", "configurationsStepSaving": "La configuración se guarda", "configurationsStepSavedsuccessfully": "La configuración se ha guardado correctamente", "configurationsStepSavingfailed": "Error al guardar la configuración", "configurationsStepChoose": "Seleccione una configuración", "configurationsStepImporting": "Se importa la configuración", "configurationsStepImportedsuccessfully": "La configuración se ha importado correctamente", "configurationsStepImportingfailed": "Error al importar la configuración", "discoveryAssuDescription": "Enchufe interruptor horario para el exterior Bluetooth 230V", "settingsDatetimeDevicetime": "Tiempo actual del dispositivo", "settingsDatetimeLoading": "Se cargan los ajustes de tiempo", "discoveryEud12Description": "Regulador de intensidad universal con Bluetooth", "generalTextOffdelay": "Retardo a la desconexión", "generalTextRemainingbrightness": "Luminosidad restante", "generalTextSwitchonvalue": "Valor de encendido", "motionsensorTitleNoremainingbrightness": "Sin luminosidad restante", "motionsensorTitleAlwaysremainingbrightness": "Con luminosidad restante", "motionsensorTitleRemainingbrightnesswithprogram": "Luminosidad restante mediante el programa de conmutación", "motionsensorTitleRemainingbrightnesswithprogramandzea": "Luminosidad residual a través de ZE y ZA", "motionsensorTitleNoremainingbrightnessauto": "Sin luminosidad restante (semiautomático)", "generalTextMotionsensor": "Detector de movimiento", "generalTextLightclock": "Despertador de luminosidad", "generalTextSnoozeclock": "Función atenuación automática", "generalDescriptionLightclock": "Al encender ({mode}), la luz se enciende después de aprox. 1 segundo con la luminosidad más baja y se atenúa lentamente sin modificar el último valor de luminosidad guardado.", "generalDescriptionSnoozeclock": "Al apagar ({mode}), la iluminación se atenúa desde el valor de atenuación actual hasta la intensidad mínima y se apaga. Es posible apagar en cualquier momento durante el proceso de atenuación pulsando brevemente el pulsador. Una pulsación larga durante el proceso de atenuación pone fin a la función de atenuación automática. ", "generalTextImmediately": "Inmediatamente", "generalTextPercentage": "Po<PERSON>entaj<PERSON>", "generalTextSwitchoffprewarning": "Preaviso de desconexión", "generalDescriptionSwitchoffprewarning": "Atenuación lenta hasta la luminosidad mínima", "generalDescriptionOffdelay": "El dispositivo se enciende cuando se aplica la tensión de control. Si se interrumpe la tensión de control, comienza la temporización, tras la cual el dispositivo se apaga. El dispositivo puede encender de nuevo durente la temporización.", "generalDescriptionBrightness": "La luminosidad a la que el regulador enciende la lámpara.", "generalDescriptionRemainingbrightness": "Valor de atenuación en porcentaje al que se atenúa la lámpara tras la desconexión del detector de movimiento.", "generalDescriptionRuntime": "Tiempo de funcionamiento de la función despertador luminoso desde la intensidad mínima hasta la máxima.", "generalTextUniversalbutton": "Pulsador universal", "generalTextDirectionalbutton": "Pulsador direcci<PERSON>", "eud12DescriptionAuto": "Detección automática UT/RT (con diodo de pulsador direccional RTD)", "eud12DescriptionRt": "con diodo de pulsador direccional RTD", "generalTextProgram": "Programa", "eud12MotionsensorOff": "Con el detector de movimiento en Off", "eud12ClockmodeTitleProgramze": "Programa y Central On", "eud12ClockmodeTitleProgramza": "Programa y Central Off", "eud12ClockmodeTitleProgrambuttonon": "Programa y UT/RT On", "eud12ClockmodeTitleProgrambuttonoff": "Programa y UT/RT Off", "eud12TiImpulseTitle": "Tiempo de impulso On (t1)", "eud12TiImpulseHeader": "Valor de regulación tiempo de impulso ON", "eud12TiImpulseDescription": "El valor de regulación en porcentaje al que se atenúa la lámpara con el tiempo de impulso ON.", "eud12TiOffTitle": "Tiempo de impulso OFF (t2)", "eud12TiOffHeader": "Valor de regulación tiempo de impulso Off", "eud12TiOffDescription": "El valor de atenuación en porcentaje al que se atenúa la lámpara con el tiempo de impulso OFF.", "generalTextButtonpermanentlight": "Iluminación permanente con el pulsador", "generalDescriptionButtonpermanentlight": "Ajuste de la luz permanente mediante pulsador de 0 a 10 horas en pasos de 0,5 horas. Activación pulsando el pulsador durante más de 1 segundo (1 parpadeo), desactivación pulsando el botón durante más de 2 segundos.", "generalTextNobuttonpermanentlight": "No LPP", "generalTextBasicsettings": "Ajustes básicos", "generalTextInputswitch": "Entrada de pulsador local (A1)", "generalTextOperationmode": "Modo de funcionamiento", "generalTextDimvalue": "Comportamiento de encender", "eud12TitleUsememory": "Utilizar valor de memoria", "eud12DescriptionUsememory": "El valor de memoria corresponde al último valor de regulación ajustado. Si la memorización del valor está desactivada, la regulación se ajusta siempre al valor de conexión.", "generalTextStartup": "Luminosidad de encendido", "generalDescriptionSwitchonvalue": "El valor de encendido es un valor de luminosidad ajustable que garantiza un encendido seguro.", "generalTitleSwitchontime": "Tiempo de conexión", "generalDescriptionSwitchontime": "Una vez transcurrido el tiempo de encendido, la lámpara se atenúa desde el valor de encendido hasta el valor de memoria.", "generalDescriptionStartup": "Algunas lámparas LED necesitan una corriente de arranque mayor para encenderse de forma fiable. La lámpara se enciende con este valor de encendido y se atenúa al valor de la memoria una vez transcurrido el tiempo de encendido.", "eud12ClockmodeSubtitleProgramze": "Breve clic en encender centralizado", "eud12ClockmodeSubtitleProgramza": "Breve clic en apagado centralizado", "eud12ClockmodeSubtitleProgrambuttonon": "Doble clic en el pulsador universal/pulsador direccional lado ON", "eud12ClockmodeSubtitleProgrambuttonoff": "Doble clic en el pulsador universal/pulsador direccional lado OFF", "eud12FunctionStairlighttimeswitchTitleShort": "TLZ | Minutero de escalera", "eud12FunctionMinTitleShort": "MIN", "eud12FunctionMmxTitleShort": "MMX", "eud12FunctionTiDescription": "Temporizador con tiempo de encendido y apagado ajustable de 0,5 segundos a 9,9 minutos. La luminosidad puede ajustarse desde la luminosidad mínima hasta la luminosidad máxima.", "eud12FunctionAutoDescription": "Dimmer universal con ajuste para detector de movimiento, despertador de luz y atenuación automática", "eud12FunctionErDescription": "Relé de conmutación, la luminosidad se puede ajustar desde la luminosidad mínima hasta la luminosidad máxima.", "eud12FunctionEsvDescription": "Regulador de intensidad universal con retardo de desconexión de 1 a 120 minutos. El preaviso de desconexión al final de la regulación puede ajustarse de 1 a 3 minutos. Ambas entradas de control centralizado activos.", "eud12FunctionTlzDescription": "Ajuste de la duración de la luz permanente mediante pulsador de 0 a 10 horas en pasos de 0,5 horas. Activación pulsando el pulsador durante más de 1 segundo (1 parpadeo), desactivación pulsando el pulsador durante más de 2 segundos.", "eud12FunctionMinDescription": "Dimmer universal, conmuta a la luminosidad mínima ajustada cuando se aplica la tensión de control. La luz se regula hasta la luminosidad máxima dentro del tiempo de regulación ajustado de 1 a 120 minutos. Cuando se desconecta la tensión de control, la luz se apaga inmediatamente, incluso durante el tiempo de regulación. Ambas entradas de control centralizado activos.", "eud12FunctionMmxDescription": "Dimmer universal, conmuta a la luminosidad mínima ajustada cuando se aplica la tensión de control. Durante el tiempo de atenuación ajustado de 1 a 120 minutos, la luz se atenúa hasta la luminosidad máxima. Sin embargo, cuando se desconecta la tensión de control, el regulador se atenúa hasta la luminosidad mínima ajustada. A continuación, se apaga. Ambas entradas de control centralizado activos.", "motionsensorSubtitleNoremainingbrightness": "Con el detector de movimiento en Off", "motionsensorSubtitleAlwaysremainingbrightness": "Con el detector de movimiento en Off", "motionsensorSubtitleRemainingbrightnesswithprogram": "Programa de conmutación activado y desactivado con BM OFF", "motionsensorSubtitleRemainingbrightnesswithprogramandzea": "Central On activa el sensor de movimiento, central Off desactiva el sensor de movimiento, así como un programa de conmutación", "motionsensorSubtitleNoremainingbrightnessauto": "El detector de movimiento sólo se apaga", "detailsDimsectionHeader": "regular", "generalTextFast": "<PERSON><PERSON><PERSON><PERSON>", "generalTextSlow": "Lentamente", "eud12TextDimspeed": "Velocidad de regulación", "eud12TextSwitchonspeed": "Velocidad de conexión", "eud12TextSwitchoffspeed": "Velocidad de desconexión", "eud12DescriptionDimspeed": "La velocidad de atenuación es la velocidad a la que el atenuador pasa de la luminosidad actual a la luminosidad objetivo.", "eud12DescriptionSwitchonspeed": "La velocidad de encendido es la velocidad que necesita el regulador para encenderse completamente.", "eud12DescriptionSwitchoffspeed": "La velocidad de desconexión es la velocidad que necesita el regulador para desconectarse completamente.", "settingsFactoryresetResetdimHeader": "Restablecer ajustes de regulación", "settingsFactoryresetResetdimDescription": "¿Deben restablecerse realmente todos los ajustes de regulación?", "settingsFactoryresetResetdimConfirmationDescription": "Los ajustes de regulación se han restablecido correctamente", "eud12TextSwitchonoffspeed": "Velocidad de encendido/apagado", "eud12DescriptionSwitchonoffspeed": "La velocidad de encendido/apagado es la velocidad que necesita el regulador para encenderse o apagarse completamente.", "timerDetailsDimtoval": "Encendido con valor de regulación en %.", "timerDetailsDimtovalDescription": "El regulador se enciende siempre con el valor de regulación fijo en %.", "timerDetailsDimtovalSubtitle": "Encender con {brightness}%", "timerDetailsDimtomem": "Encendido con la intensidad memorizada", "timerDetailsDimtomemSubtitle": "Encendido con la intensidad memorizada", "timerDetailsMotionsensorwithremainingbrightness": "Luminosidad residual (BM) ON", "timerDetailsMotionsensornoremainingbrightness": "Luz residual (BM) OFF", "settingsRandommodeHint": "Cuando se activa el modo aleatorio, todos los tiempos de conmutación del canal se desplazan aleatoriamente. Para horas de conexión hasta 15 minutos antes y horas de desconexión hasta 15 minutos después.", "runtimeOffsetDescription": "Rebasamiento adicional, una vez transcurrido el tiempo de recorrido. Puede utilizarse para garantizar que se alcanza la posición final.", "loadingTextDimvalue": "Se carga el valor de regulación", "discoveryEudipmDescription": "Regulador universal IP Matter", "generalTextOffset": "Rebasamiento", "eud12DimvalueTestText": "Enviar luminosidad", "eud12DimvalueTestDescription": "Durante la comprobación se tiene en cuenta la velocidad de regulación ajustada actualmente.", "eud12DimvalueLoadText": "Carga la luminosidad", "settingsDatetimeNotime": "Los ajustes de fecha y hora deben leerse a través de la pantalla del dispositivo.", "generalMatterText": "Matter", "generalMatterMessage": "Por favor, enlace sus dispositivos Matter utilizando una de las siguientes aplicaciones.", "generalMatterOpengooglehome": "Abrir Google Home", "generalMatterOpenamazonalexa": "Abrir Amazon Alexa", "generalMatterOpensmartthings": "<PERSON><PERSON><PERSON> SmartThings", "generalLabelProgram": "Programa {number}", "generalTextDone": "<PERSON><PERSON>", "settingsRandommodeDescriptionShort": "Con el modo aleatorio activado, todos los programas de este canal se desplazan aleatoriamente hasta 15 minutos. Los programas On se adelantan, los Off se retrasan.", "all": "Todo", "discoveryBluetooth": "Bluetooth", "success": "Con éxito", "error": "Error", "timeProgramAdd": "Añadir programa de tiempo", "noConnection": "Sin conexión", "timeProgramOnlyActive": "Programas configurados", "timeProgramAll": "Todos los programas", "active": "activo", "inactive": "Inactivo", "timeProgramSaved": "Programa {number} guardado", "deviceLanguageSaved": "Idioma del dispositivo guardado", "generalTextTimeShort": "{time} reloj", "programDeleteHint": "¿Debe eliminarse realmente el programa {index}? ", "milliseconds": "{count, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON>} other{<PERSON><PERSON>egund<PERSON>}}", "millisecondsWithValue": "{count, plural, one {{count} milisegundo} other {{count} milisegundos}}", "secondsWithValue": "{count, plural, one {{count} segundo} other {{count} segundos}}", "minutesWithValue": "{count, plural, one {{count} minuto} other {{count} minutos}}", "hoursWithValue": "{count, plural, one {{count} hora} other {{count} horas}}", "settingsPinFailEmpty": "El PIN no debe estar vacío", "detailsConfigurationWifiloginScanNoMatch": "El código escaneado no coincide con el dispositivo", "wifiAuthorizationPopIsEmpty": "El PdP no puede estar vacío", "wifiAuthenticationCredentialsHint": "Como la aplicación no puede acceder a su contraseña Wi-Fi privada, no es posible comprobar si la entrada es correcta. Si no se establece la conexión, comprueba la contraseña e introdúcela de nuevo.", "generalMatterOpenApplehome": "Abrir Apple Home", "timeProgramNoActive": "Sin programas configurados", "timeProgramNoEmpty": "No hay ningún programa de tiempo libre disponible.", "nameOfConfiguration": "Nombre de la configuración", "currentDevice": "Dispositivo actual", "export": "Exportar", "import": "Importar", "savedConfigurations": "Configuraciones guardadas", "importableServicesLabel": "Se pueden importar los siguientes ajustes:", "notImportableServicesLabel": "Ajustes incompatibles", "deviceCategoryMeterGateway": "Pasarela de contadores", "deviceCategory2ChannelTimeSwitch": "Interruptor horario de 2 canales", "devicategoryOutdoorTimeSwitchBluetooth": "Interruptor horario exterior Bluetooth", "settingsModbusHeader": "Modbus", "settingsModbusDescription": "Ajuste la velocidad en baudios, la paridad y el tiempo de espera para configurar la velocidad de transmisión, la detección de errores y el tiempo de espera.", "settingsModbusRTU": "Modbus RTU", "settingsModbusBaudrate": "Velocidad de transmisión", "settingsModbusParity": "Paridad", "settingsModbusTimeout": "Tiempo de espera Modbus", "locationServiceDisabled": "Ubicación desactivada", "locationPermissionDenied": "Por favor, permita la autorización de localización para recuperar su posición actual.", "locationPermissionDeniedPermanently": "La autorización de localización está denegada permanentemente. Permite la autorización de ubicación en la configuración de tu dispositivo para recuperar tu posición actual.", "lastSync": "Última sincronización", "dhcpActive": "DHCP activo", "ipAddress": "IP", "subnetMask": "Máscara de subred", "standardGateway": "<PERSON><PERSON><PERSON> de enlace predeterminada", "dns": "DNS", "alternateDNS": "DNS alternativo", "errorNoNetworksFound": "No se ha encontrado ninguna red wifi", "availableNetworks": "Redes disponibles", "enableWifiInterface": "Habilitar interfaz WiFi", "enableLANInterface": "Habilitar interfaz LAN", "hintDontDisableAllInterfaces": "Asegúrese de que no todas las interfaces están desactivadas. La última interfaz activada tiene prioridad.", "ssid": "SSID", "searchNetworks": "Buscar redes WiFi", "errorNoNetworkEnabled": "Al menos un interfaz debe estar activado.", "errorActiveNetworkInvalid": "No todas las estaciones activas son válidas", "invalidNetworkConfiguration": "Configuración de red no válida", "generalDefault": "E<PERSON>ar", "mqttHeader": "MQTT", "mqttConnected": "Conectado al broker MQTT", "mqttDisconnected": "No hay conexión con el broker MQTT", "mqttBrokerURI": "Broker <PERSON>", "mqttBrokerURIHint": "Broker-MQTT URI", "mqttPort": "Puerto", "mqttPortHint": "Puerto MQTT", "mqttClientId": "ID de cliente", "mqttClientIdHint": "MQTT Cliente-ID", "mqttUsername": "Nombre de usuario", "mqttUsernameHint": "Nombre de usuario MQTT", "mqttPassword": "Contraseña", "mqttPasswordHint": "Contraseña MQTT", "mqttCertificate": "Certificado (opcional)", "mqttCertificateHint": "Certificado MQTT", "mqttTopic": "<PERSON><PERSON>", "mqttTopicHint": "Tema MQTT", "electricityMeter": "Con<PERSON><PERSON> corriente", "electricityMeterCurrent": "Actual", "electricityMeterHistory": "Historia", "electricityMeterReading": "Lectura del contador", "connectivity": "Conectividad", "electricMeter": "{count, plural, one {contador} other{contadores}} ", "discoveryZGW16Description": "Modbus-Energy-Meters-MQTT-Gateway", "bluetoothConnectionLost": "Conexión Bluetooth perdida", "bluetoothConnectionLostDescription": "La conexión Bluetooth con el dispositivo se ha interrumpido. Asegúrese de que el dispositivo está dentro del alcance.", "openBluetoothSettings": "Abrir la configuración", "password": "Contraseña", "setInitialPassword": "<PERSON><PERSON><PERSON> contraseña inicial", "initialPasswordMinimumLength": "La contraseña debe tener al menos {length} caracteres", "repeatPassword": "<PERSON><PERSON>r con<PERSON>", "passwordsDoNotMatch": "Las contraseñas no coinciden", "savePassword": "Guardar contraseña", "savePasswordHint": "La contraseña se guarda para futuras conexiones en tu dispositivo.", "retrieveNtpServer": "Recuperar la hora del servidor NTP", "retrieveNtpServerFailed": "No se ha podido establecer la conexión con el servidor NTP.", "retrieveNtpServerSuccess": "La conexión con el servidor NTP se ha realizado correctamente.", "settingsPasswordNewPasswordDescription": "Introducir nueva contraseña", "settingsPasswordConfirmationDescription": "Cambio de contraseña correcto", "dhcpRangeStart": "Rango de Inicio DHCP", "dhcpRangeEnd": "Rango final DHCP", "forwardOnMQTT": "Reenviar a MQTT", "showAll": "<PERSON><PERSON> todo", "hide": "Ocultar", "changeToAPMode": "Cambiar a modo AP", "changeToAPModeDescription": "Está a punto de conectar su dispositivo a una red WiFi, en este caso la conexión con el dispositivo se desconecta y debe volver a conectarse a su dispositivo a través de la red configurada.", "consumption": "Consu<PERSON>", "currentDay": "Día actual", "twoWeeks": "2 semanas", "oneYear": "1 año", "threeYears": "3 años", "passwordMinLength": "La contraseña debe tener al menos {length} caracteres. ", "passwordNeedsLetter": "La contraseña debe contener una letra.", "passwordNeedsNumber": "La contraseña debe contener un número.", "portEmpty": "El puerto no puede estar vacío", "portInvalid": "Puerto no válido", "portOutOfRange": "El puerto debe estar entre {rangeStart} y {rangeEnd}. ", "ipAddressEmpty": "La dirección IP no puede estar vacía", "ipAddressInvalid": "Dirección IP no válida", "subnetMaskEmpty": "La máscara de subred no puede estar vacía", "subnetMaskInvalid": "Máscara de subred no válida", "gatewayEmpty": "La pasarela no puede estar vacía", "gatewayInvalid": "Pasarela no válida", "dnsEmpty": "DNS no puede estar vacío", "dnsInvalid": "DNS no válido", "uriEmpty": "El URI no puede estar vacío", "uriInvalid": "URI no válido", "electricityMeterChangedSuccessfully": "Contador de corriente cambiado con éxito", "networkChangedSuccessfully": "Configuración de red modificada correctamente", "mqttChangedSuccessfully": "Configuración MQTT modificada correctamente", "modbusChangedSuccessfully": "Configuración Modbus modificada correctamente", "loginData": "Bo<PERSON>r datos de acceso", "valueConfigured": "<PERSON><PERSON><PERSON><PERSON>", "electricityMeterHistoryNoData": "No hay datos disponibles", "locationChangedSuccessfully": "Ubicación modificada correctamente", "settingsNameFailEmpty": "El nombre no puede estar vacío", "settingsNameFailLength": "El nombre no debe tener más de {length} caracteres.", "solsticeChangedSuccesfully": "Ajustes de solsticio modificados correctamente", "relayFunctionChangedSuccesfully": "Relé-Función cambiada con éxito", "relayFunctionHeader": "Función relé", "dimmerValueChangedSuccesfully": "El comportamiento de encendido se ha modificado correctamente", "dimmerBehaviourChangedSuccesfully": "Se ha modificado correctamente el comportamiento de regulación", "dimmerBrightnessDescription": "La luminosidad mínima y máxima afecta a todas las luminosidades regulables del regulador.", "dimmerSettingsChangedSuccesfully": "Configuración básica modificada correctamente", "liveUpdateEnabled": "Prueba en directo habilitada", "liveUpdateDisabled": "Prueba en vivo desactivada", "liveUpdateDescription": "Se envía al dispositivo el último valor modificado del deslizador.", "demoDevices": "Dispositivos de demostración", "showDemoDevices": "Mostrar dispositivos de demostración", "deviceCategoryTimeSwitch": "Interruptor horario", "deviceCategoryMultifunctionalRelay": "Temporizador con multifunción", "deviceCategoryDimmer": "Regulador de intensidad", "deviceCategoryShutter": "Actuador de persianas y toldos", "deviceCategoryRelay": "<PERSON><PERSON>", "search": "Buscar", "configurationsHeader": "Configuraciones", "configurationsDescription": "Gestione aquí sus configuraciones.", "configurationsNameFailEmpty": "El nombre de la configuración no puede estar vacío", "configurationDeleted": "Configuración eliminada", "codeFound": "{codeType} Código reconocido", "errorCameraPermission": "Por favor, permita el acceso a la cámara para escanear el código ELTAKO.", "authorizationSuccessful": "Autorizado con éxito en el dispositivo", "wifiAuthenticationResetConfirmationDescription": "La autenticación se ha restablecido correctamente.", "settingsResetConnectionHeader": "Restablecer cone<PERSON>", "settingsResetConnectionDescription": "¿De verdad quieres restablecer la conexión?", "settingsResetConnectionConfirmationDescription": "La conexión se ha restablecido correctamente.", "wiredInputChangedSuccesfully": "Se ha modificado correctamente el comportamiento del pulsador", "runtimeChangedSuccesfully": "Se ha modificado correctamente el comportamiento en tiempo de ejecución", "expertModeActivated": "Modo experto activado", "expertModeDeactivated": "Modo experto desactivado", "license": "Licencias", "retry": "Reintentar", "provisioningConnectingHint": "Se está estableciendo la conexión con el dispositivo. Esto puede tardar hasta 1 minuto.", "serialnumberEmpty": "El número de serie no puede estar vacío", "interfaceStateInactiveDescriptionBLE": "Bluetooth está desactivado, por favor, actívelo para encontrar dispositivos Bluetooth.", "interfaceStateDeniedDescriptionBLE": "No se concedieron los permisos Bluetooth.", "interfaceStatePermanentDeniedDescriptionBLE": "No se han concedido los permisos de Bluetooth. Por favor, habilítalos en la configuración de tu dispositivo.", "requestPermission": "Solicitar permiso", "goToSettings": "Ir a la configuración", "enableBluetooth": "Activar bluetooth", "installed": "Instalado", "teachInDialogDescription": "¿Le gustaría enseñar en su dispositivo a través de {type}?", "useMatter": "Utilizar Matter", "relayMode": "Activar modo relé", "whatsNew": "Novedades de esta versión", "migrationHint": "Es necesaria una migración de tus datos para utilizar las nuevas funciones.", "migrationHeader": "Migración", "migrationProgress": "Migración en curso...", "letsGo": "¡Vamos!", "noDevicesFound": "No se han encontrado dispositivos. Comprueba si tu dispositivo está en modo de emparejamiento.", "interfaceStateEmpty": "No se han encontrado dispositivos", "ssidEmpty": "SSID no puede estar vacío", "passwordEmpty": "La contraseña no puede estar vacía", "settingsDeleteSettingsHeader": "Restable<PERSON> ajustes", "settingsDeleteSettingsDescription": "¿De verdad quieres restablecer todos los ajustes?", "settingsDeleteSettingsConfirmationDescription": "Todos los ajustes se han restablecido correctamente.", "locationNotFound": "Ubicación no encontrada", "timerProgramEmptySaveHint": "El programa de tiempo está vacío y no se puede guardar. ¿ Finalizar la edición ?", "timerProgramDaysEmptySaveHint": "No hay días seleccionados. ¿Quieres guardar el programa de tiempo de todos modos?", "timeProgramNoDays": "Un programa sin días activos no puede activarse.", "timeProgramColliding": "El programa de tiempo colisiona con el programa {program}.", "timeProgramDuplicated": "El programa de tiempo es un duplicado del programa {program}.", "screenshotZgw16": "Casa unifamiliar", "interfaceStateUnknown": "No se han encontrado dispositivos", "settingsPinChange": "Cambiar PIN", "timeProgrammOneTime": "una sola vez", "timeProgrammRepeating": "repitiendo", "generalIgnore": "Ignore", "timeProgramChooseDay": "Elija el día", "generalToday": "Hoy", "generalTomorrow": "<PERSON><PERSON><PERSON>", "bluetoothAndPINChangedSuccessfully": "Bluetooth y PIN han sido cambiados con éxito.", "generalTextDimTime": "Tiempo de regulación", "discoverySu62Description": "Interruptor horario Bluetooth de 1 canal", "bluetoothAlwaysOnTitle": "ON permanente", "bluetoothAlwaysOnDescription": "Bluetooth está permanentemente activado.", "bluetoothAlwaysOnHint": "Nota: ¡Si este ajuste está activado, el dispositivo estará permanentemente visible para todo el mundo a través de Bluetooth! Se recomienda cambiar el PIN predeterminado.", "bluetoothManualStartupOnTitle": "Temporal on", "bluetoothManualStartupOnDescription": "Después de conectar la alimentación, el Bluetooth se activa durante 3 minutos.", "bluetoothManualStartupOnHint": "Nota: El modo de acoplamiento está activado durante 3 minutos y luego se apaga. Si se desea establecer una nueva conexión, debe pulsarse el pulsador durante aprox. 5 segundos.", "bluetoothManualStartupOffTitle": "Manualmente ON", "bluetoothManualStartupOffDescription": "El Bluetooth se activa manualmente mediante la entrada de pulsador.", "bluetoothManualStartupOffHint": "Nota: Para activar el Bluetooth, un pulsador conectado en la entrada de pulsadores tiene que estar pulsado durante aprox. 5 segundos.", "timeProgrammOneTimeRepeatingDescription": "Los programas pueden ejecutarse repetidamente realizando siempre una conmutación en los días y horas configurados, o pueden ejecutarse una sola vez al momento de conmutación configurada.", "versionHeader": "Versión {version}", "releaseNotesHeader": "Notas de publicación", "release30Header": "Ya está aquí la nueva App Eltako Connect.", "release30FeatureDesignHeader": "Nuevo diseño", "release30FeatureDesignDescription": "La App ha sido completamente revisada y tiene un nuevo diseño. Ahora es aún más fácil e intuitiva de usar.", "release30FeaturePerformanceHeader": "Rendimiento mejorado", "release30FeaturePerformanceDescription": "Disfrute de una experiencia más fluida y de tiempos de carga reducidos, para una experiencia de usuario óptima.", "release30FeatureConfigurationHeader": "Configuraciones entre dispositivos", "release30FeatureConfigurationDescription": "Guarde las configuraciones de los dispositivos y transfiéralas a otros dispositivos. Aunque no tengan el mismo hardware, puede, por ejemplo, transferir la configuración de su S2U12DBT1+1-UC a un ASSU-BT o viceversa.", "release31Header": "¡Ya está aquí el nuevo interruptor horario empotrable de 1 canal con Bluetooth!", "release31Description": "¿Qué puede hacer el SU62PF-BT/UC?", "release31SU62Name": "SU62PF-BT/UC", "release31DeviceNote1": "Hasta 60 programas de conmutación", "release31DeviceNote2": "El reloj puede conmutar los dispositivos según horas fijas o mediante la función astro basada en la salida y la puesta del sol.", "release31DeviceNote3": "Modo aleatorio: Los tiempos de conmutación pueden desplazarse aleatoriamente hasta 15 minutos.", "release31DeviceNote4": "Cambio de horario de verano/invierno: El reloj cambia automáticamente al horario de verano o invierno.", "release31DeviceNote5": "Tensión universal de alimentación y control de 12-230V UC.", "release31DeviceNote6": "Entrada de pulsador para una conmutación manual.", "release31DeviceNote7": "1 contacto NA libre de potencial 10 A/250 V AC.", "release31DeviceNote8": "Ejecutar programas horarios una vez.", "generalNew": "Novedades", "yearsAgo": "{count, plural, one {<PERSON><PERSON><PERSON> a<PERSON>} other {{count} anos atras}}", "monthsAgo": "{count, plural, one {<PERSON><PERSON><PERSON> me<PERSON>} other {{count} meses atrás}}", "weeksAgo": "{count, plural, one {Última semana} other {{count} semanas atrás}}", "daysAgo": "{count, plural, one {ayer} other {{count} dias atrás}}", "minutesAgo": "{count, plural, one {hace un minuto} other {{count} minutos}}", "hoursAgo": "{count, plural, one {Hace una hora} other {{count} horas}}", "secondsAgo": "{count, plural, one {hace un segundo} other {{count} segundos}}", "justNow": "<PERSON><PERSON> mismo", "discoveryEsripmDescription": "Telerruptor-Relé IP Matter", "generalTextKidsRoom": "Función de atenuación automática", "generalDescriptionKidsRoom": "Al encender ({mode}), la luz se enciende con el valor de luminosidad más bajo después de aprox. 1 segundo y se va atenuando lentamente mientras se siga pulsando el pulsador, sin modificar el último valor de intensidad memorizado.", "generalTextSceneButton": "Pulsador de escenas", "settingsEnOceanConfigHeader": "Configuración EnOcean", "enOceanConfigChangedSuccessfully": "La configuración de EnOcean se ha modificado correctamente", "activateEnOceanRepeater": "Activar repetidor EnOcean", "enOceanRepeaterLevel": "Nivel del repetidor", "enOceanRepeaterLevel1": "Modo-1", "enOceanRepeaterLevel2": "Modo-2", "enOceanRepeaterOffDescription": "No se repiten señales inalámbricas de los sensores.", "enOceanRepeaterLevel1Description": "Sólo las señales inalámbricas de los sensores se reciben, comprueban y reenvían a plena potencia de transmisión. Las señales inalámbricas de otros repetidores se ignoran para reducir la cantidad de datos.", "enOceanRepeaterLevel2Description": "Además de las señales inalámbricas de los sensores, también se procesan las señales inalámbricas de los repetidores de modo-1. Así, una señal inalámbrica puede recibirse y amplificarse un máximo de dos veces. Los repetidores inalámbricos no necesitan ser programados. Reciben y amplifican las señales inalámbricas de todos los sensores inalámbricos en su alcance de recepción.", "settingsSensorHeader": "Sen<PERSON><PERSON>", "sensorChangedSuccessfully": "Sensores cambiados con éxito", "wiredButton": "Pulsador cableado", "enOceanId": "EnOcean-ID", "enOceanAddManually": "Introducir o escanear la EnOcean-ID", "enOceanIdInvalid": "EnOcean-ID no válido", "enOceanAddAutomatically": "Enlace con telegrama EnOcean", "enOceanAddDescription": "El protocolo inalámbrico EnOcean permite enlazar y utilizar pulsadores en tu actuador.\n\nElija entre el enlazamiento automático con un telgramma EnOcean para enlazar pulsadores con solo una pulsación o seleccione la opción manual para escanear o escribir el ID EnOcean de tu pulsador.", "enOceanTelegram": "Telegrama", "enOceanCodeScan": "tIntroduzca el EnOcean-ID de su {sensorType} o escanee el EnOcean-QR-Code de tu {sensorType}, para añadirlo", "enOceanCode": "Código QR EnOcean", "enOceanCodeScanDescription": "Busca el código EnOcean en tu {sensorType} y escanéalo con tu cámara.", "enOceanButton": "<PERSON><PERSON><PERSON><PERSON>", "enOceanBackpack": "Adaptador EnOcean", "sensorNotAvailable": "Aún no se ha enlazado ningún sensor", "sensorAdd": "<PERSON><PERSON><PERSON>", "sensorCancel": "Cancelar el enlazamiento", "sensorCancelDescription": "¿De verdad quieres cancelar el enlazamiento?", "getEnOceanBackpack": "Pida su adaptador EnOcean", "enOceanBackpackMissing": "Para entrar en el fantástico mundo de la conectividad y la comunicación perfectas, necesita un adaptador EnOcean.\nHaga clic aquí para obtener más información", "sensorEditChangedSuccessfully": "{sensorName} cambiado con éxito", "sensorConnectedVia": "conectado a través de {deviceName}", "lastSeen": "Visto por última vez", "setButtonOrientation": "Determinar la orientación", "setButtonType": "Determinar el tipo del pulsador", "button1Way": "Pulsador de 1 canal", "button2Way": "Pulsador de 2 canales", "button4Way": "Pulsador de 4 canales", "buttonUnset": "no ocupado", "button": "<PERSON><PERSON><PERSON><PERSON>", "sensor": "Sensor", "sensorsFound": "{count, plural, =0 {No se encontraron sensores} one {1 sensor encontrado} other {{count} sensores encontrados}}", "sensorSearch": "Búsqueda de sensores", "searchAgain": "Buscar de nuevo", "sensorTeachInHeader": "<PERSON><PERSON><PERSON> en {sensorType}", "sensorChooseHeader": "Seleccione {sensorType}", "sensorChooseDescription": "Elija un pulsador para enlazar en", "sensorCategoryDescription": "Seleccione la categoría del sensor que desea añadir.", "sensorName": "Nombre del pulsador", "sensorNameFooter": "Ponga nombre a su pulsador", "sensorAddedSuccessfully": "{sensorName} se ha añadido correctamente", "sensorDelete": "Borrar {sensorType}", "sensorDeleteHint": "¿Realmente desea eliminar el {sensorType}? {sensorName}?", "sensorDeletedSuccessfully": "{sensorName} se ha eliminado correctamente", "buttonTapDescription": "Pulse el pulsador que desea enlazar.", "waitingForTelegram": "El actuador espera el telegrama", "copied": "Copiado", "pairingFailed": "{sensorType} ya enlazado", "generalDescriptionUniversalbutton": "En caso de pulsador universal, la dirección se invierte soltando brevemente el botón. Los comandos de control cortos encienden o apagan.", "generalDescriptionDirectionalbutton": "En caso de pulsador direccional es \"encender y atenuar\" en la parte superior y \"apagar y atenuar\" en la parte inferior.", "matterForwardingDescription": "La pulsación del pulsador se reenvía a Matter.", "none": "<PERSON><PERSON><PERSON>", "buttonNoneDescription": "El pulsador no tiene ninguna funcionalidad", "buttonUnsetDescription": "El pulsador no tiene ningún comportamiento establecido", "sensorButtonTypeChangedSuccessfully": "Tipo de pulsador cambiado correctamente", "forExample": "p.e. {example}}", "enOceanQRCodeInvalidDescription": "Sólo posible a partir de la fecha de fabricación 44/20", "input": "Entrada", "buttonSceneValueOverride": "sobrescribir valor del pulsador de escena", "buttonSceneValueOverrideDescription": "El valor del pulsador de escena se sobrescribirá con el valor de atenuación actual con una pulsación larga del botón", "buttonSceneDescription": "El pulsador de escena se enciende con un valor de atenuación específico", "buttonPress": "Pulsación", "triggerOn": "con accionamiento prolongado del pulsador universal o del pulsador direccional en el lado de encendido", "triggerOff": "con doble pulsación en el pulsador universal o en el pulsador direccional del lado de desconexión", "centralOn": "Central On", "centralOff": "Central Off", "centralButton": "pulsador de control central", "enOceanAdapterNotFound": "No se ha encontrado ningún adaptador EnOcean", "updateRequired": "Actualización necesaria", "updateRequiredDescription": "Tu APP necesita una actualización para ser compatible con este nuevo dispositivo.", "release32Header": "Ya está disponible la nueva serie 64 con Matter y EnOcean y el nuevo interruptor horario empotrable Bluetooth SU62PF-BT/UC.", "release32EUD64Header": "¡Ya está aquí el nuevo dimmer empotrable de 1 canal con Matter sobre Wi-Fi y hasta 300W!", "release32EUD64Note1": "Configuración de la velocidad de atenuación, velocidad de encendido/apagado, encender con intensidad mínima/atenuación automática y mucho más.", "release32EUD64Note2": "La funcionalidad del EUD64NPN-IPM puede ampliarse mediante adaptadores, como el adaptador EnOcean EOA64.", "release32EUD64Note3": "En combinación con el adaptador EnOcean EOA64, se pueden enlazarse directamente hasta 30 pulsadores inalámbricos EnOcean al EUD64NPN-IPM y reenviarlos a Matter.", "release32EUD64Note4": "Las dos entradas para pulsadores cableadas pueden enlazarse directamente con el EUD64NPN-IPM o reenviarse a Matter.", "release32ESR64Header": "Ya está aquí el nuevo actuador empotrado de conmutación con 1 canal, libre de potencial, con Matter vía Wi-Fi y hasta 16 A.", "release32ESR64Note1": "Configuración de varias funciones, como telerruptor (ES), función de relé (ER), NC (ER-Inverse), y mucho más.", "release32ESR64Note2": "La funcionalidad del ESR64PF-IPM puede ampliarse mediante adaptadores, como el adaptador EnOcean EOA64.", "release32ESR64Note3": "Se pueden conectar directamente hasta 30 pulsadores inalámbricos EnOcean al ESR64PF-IPM en combinación con el adaptador EnOcean EOA64 y reenviarlos a Matter.", "release32ESR64Note4": "Una entrada para pulsadores cableadas pueden enlazarse directamente con el EUD64NPN-IPM o reenviarse a Matter.", "buttonsFound": "{count, plural, =0 {Ningún pulsador encontrado} one {1 pulsador encontrado} other {{count} pulsadores encontrados}}", "doubleImpuls": "con un impulso doble", "impulseDescription": "Si el canal está encendido, se apaga mediante un impulso.", "locationServiceEnable": "Activar ubicación", "locationServiceDisabledDescription": "La localización está desactivada. La versión de tu sistema operativo necesita la ubicación para poder encontrar dispositivos Bluetooth.", "locationPermissionDeniedNoPosition": "No se han concedido permisos de localización. La versión de tu sistema operativo requiere permisos de localización para poder encontrar dispositivos Bluetooth. Por favor, permite el permiso de localización en los ajustes de tu dispositivo.", "interfaceStatePermanentDeniedDescriptionDevicesAround": "No se ha concedido el permiso de dispositivos cercanos. Por favor, habilita el permiso en los ajustes de tu dispositivo.", "permissionNearbyDevices": "Dispositivos cercanos", "release320Header": "Ya está aquí el nuevo y más potente dimmer universal EUD12NPN-BT/600W-230V.", "release320EUD600Header": "¿Qué puede hacer el nuevo dimmer universal?", "release320EUD600Note1": "Regulador de intensidad universal con una potencia de hasta 600 W", "release320EUD600Note2": "Ampliable con la extensión de potencia LUD12 hasta 3800 W", "release320EUD600Note3": "Accionamiento local con pulsador universal o direccional", "release320EUD600Note4": "Funciones centrales On / Off", "release320EUD600Note5": "Entrada de detector de movimiento para mayor comodidad", "release320EUD600Note6": "Temporizador integrado con 10 programas de conmutación", "release320EUD600Note7": "Función astronómica", "release320EUD600Note8": "Luminosidad de encendido individual", "mqttClientCertificate": "Certificado de cliente", "mqttClientCertificateHint": "Certificado de cliente MQTT", "mqttClientKey": "Clave de cliente", "mqttClientKeyHint": "Clave de cliente MQTT", "mqttClientPassword": "Contraseña de cliente", "mqttClientPasswordHint": "Contraseña de cliente MQTT", "mqttEnableHomeAssistantDiscovery": "Activar la detección MQTT de HomeAssistant", "modbusTcp": "Modbus TCP", "enableInterface": "Habilitar interfaz", "busAddress": "Dirección del autobús", "busAddressWithAddress": "Dirección de bus {index}", "deviceType": "Tipo de dispositivo", "registerTable": "{count, plural, one {Tabla de registro} other {Tabla de registro}}", "currentValues": "Valores actuales", "requestRTU": "Solicitar RTU", "requestPriority": "Solicitar prioridad", "mqttForwarding": "Reenvío a MQTT", "historicData": "<PERSON><PERSON> his<PERSON>", "dataFormat": "Formato de los datos", "dataType": "Tipo de <PERSON>", "description": "Descripción", "readWrite": "Lectura/Escritura", "unit": "Unidad", "registerTableReset": "Restablecer tabla de registros", "registerTableResetDescription": "¿Debe restablecerse realmente la tabla de registros?", "notConfigured": "No configurado", "release330ZGW16Header": "Actualización importante para el ZGW16WL-IP", "release330Header": "El ZGW16WL-IP con hasta 16 contadores de electricidad", "release330ZGW16Note1": "Admite hasta 16 contadores de electricidad ELTAKO Modbus", "release330ZGW16Note2": "Compatibilidad con Modbus TCP", "release330ZGW16Note3": "Compatibilidad con MQTT Discovery", "screenshotButtonLivingRoom": "<PERSON>ul<PERSON><PERSON> de <PERSON>ón", "registerChangedSuccessfully": "Registro modificado correctamente", "serverCertificateEmpty": "El certificado del servidor no puede estar vacío", "registerTemplates": "Plantillas de registro", "registerTemplateChangedSuccessfully": "Plantilla de registro modificada correctamente", "registerTemplateReset": "Restablecer plantilla de registro", "registerTemplateResetDescription": "¿Debe restablecerse realmente la plantilla de registro?", "registerTemplateNotAvailable": "No hay plantillas de registro disponibles", "rename": "Cambie el nombre de", "registerName": "Nombre de registro", "registerRenameDescription": "Introduzca un nombre personalizado para el registro", "restart": "Reiniciar el dispositivo", "restartDescription": "¿Realmente quieres reiniciar el dispositivo?", "restartConfirmationDescription": "El dispositivo se está reiniciando", "deleteAllElectricityMeters": "Borrar todos los contadores de electricidad", "deleteAllElectricityMetersDescription": "¿De verdad quiere borrar todos los contadores de electricidad?", "deleteAllElectricityMetersConfirmationDescription": "Todos los contadores de electricidad se han eliminado correctamente", "resetAllElectricityMeters": "Restablecer todas las configuraciones de los contadores de electricidad", "resetAllElectricityMetersDescription": "¿De verdad quieres restablecer todas las configuraciones de los contadores de electricidad?", "resetAllElectricityMetersConfirmationDescription": "Se han restablecido correctamente todas las configuraciones de los contadores de electricidad", "deleteElectricityMeterHistories": "Borrar todos los historiales de los contadores de electricidad", "deleteElectricityMeterHistoriesDescription": "¿De verdad quieres borrar todos los historiales de los contadores de la luz?", "deleteElectricityMeterHistoriesConfirmationDescription": "Se han borrado correctamente todos los historiales de los contadores de electricidad", "multipleElectricityMetersSupportMissing": "Su dispositivo sólo admite actualmente un contador de electricidad. Por favor, actualice su firmware.", "consumptionWithUnit": "Consumo (kWh)", "exportWithUnit": "Entrega (kWh)", "importWithUnit": "Consumo (kWh)", "resourceWarningHeader": "Limitación de recursos", "mqttAndTcpResourceWarning": "Operar MQTT y Modbus TCP al mismo tiempo no es posible debido a los recursos limitados del sistema. Desactive {protocol} en primer lugar.", "mqttEnabled": "MQTT habilitado", "redirectMQTT": "Ir a Configuración MQTT", "redirectModbus": "Ir a Configuración Modbus", "unsupportedSettingDescription": "Con la versión actual del firmware, algunos de los ajustes del dispositivo no son compatibles. Actualiza tu firmware para utilizar las nuevas funciones.", "updateNow": "<PERSON><PERSON><PERSON><PERSON>ora", "zgw241Hint": "Con esta actualización, Modbus TCP está activado por defecto y MQTT está desactivado. Esto se puede cambiar en la configuración. Con soporte para hasta 16 contadores, se han realizado muchas optimizaciones; esto puede provocar cambios en la configuración del dispositivo. Por favor, reinicie el dispositivo después de ajustar la configuración.", "deviceConfigChangedSuccesfully": "Se ha modificado correctamente el comportamiento en tiempo de ejecución", "deviceConfiguration": "Configuración del dispositivo", "tiltModeToggle": "Modo de inclinación", "tiltModeToggleFooter": "Si el dispositivo se configura en Matter, todas las funciones deben reconfigurarse allí.", "shaderMovementDirection": "<PERSON><PERSON> atrás Arriba/Abajo", "shaderMovementDirectionDescription": "Invertir la dirección del movimiento arriba/abajo del motor", "tiltTime": "Tiempo de ejecución de la inclinación", "changeTiltModeDialogTitle": "{target, select, true {Activar} false {Desactivar} other {Cambiar}} función de inclinación", "changeTiltModeDialogConfirmation": "{target, select, true {Activar} false {Desactivar} other {Cambiar}}", "generalTextSlatSetting": "Ajuste de las lamas", "generalTextPosition": "Posición", "generalTextSlatPosition": "Posición de las lamas", "slatSettingDescription": "Descripción del ajuste de las lamas", "scenePositionSliderDescription": "Altura", "sceneSlatPositionSliderDescription": "Inclinación", "referenceRun": "Calibración", "slatAutoSettingHint": "En este modo, la posición de las persianas no importa antes de que las lamas se ajusten a la posición de inclinación deseada.", "slatReversalSettingHint": "En este modo, las persianas se cerrarán completamente antes de que las lamas se ajusten a la posición de inclinación deseada.", "release340Header": "Ya está aquí el nuevo actuador de sombreado de materia empotrable ESB64NP-IPM.", "release340ESB64Header": "¿De qué es capaz el ESB64NP-IPM?", "release340ESB64Note1": "Nuestro actuador de sombreado certificado Matter Gateway con función opcional de lamas", "release340ESB64Note2": "Dos entradas de botón con cable para conmutación manual y reenvío a Matter", "release340ESB64Note3": "Ampliable con adaptador EnOcean (EOA64). <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, con el pulsador inalámbrico EnOcean F4T55.", "release340ESB64Note4": "Abierto a integraciones gracias a la API REST basada en el estándar OpenAPI", "activateTiltModeDialogText": "Si se activa la función de inclinación, se perderán todos los ajustes. Está seguro de que desea activar la función de inclinación?", "deactivateTiltModeDialogText": "Si se desactiva la función de inclinación, se perderán todos los ajustes. Está seguro de que desea desactivar la función de inclinación?"}