{"appName": "ELTAKO Connect", "discoveryHint": "Attivare il Bluetooth sul dispositivo per connettersi", "devicesFound": "{count, plural, =0 {nessun dispositivo trovato} one {1 dispositivo trovato} other {{count} dispositivi trovati}}.", "@devicesFound": {"description": "Specifies how many device are found in the discovery overview", "type": "text"}, "discoveryDemodeviceName": "{count, plural, one {dispositivo demo} other {dispositivi demo}}", "discoverySu12Description": "Interruttore orario Bluetooth a 2 canali", "discoveryImprint": "Impronta", "discoveryLegalnotice": "Avviso legale", "generalSave": "<PERSON><PERSON><PERSON>", "generalCancel": "<PERSON><PERSON><PERSON>", "detailsHeaderHardwareversion": "Versione hardware", "detailsHeaderSoftwareversion": "Versione software", "detailsHeaderConnected": "<PERSON><PERSON><PERSON>", "detailsHeaderDisconnected": "Disconnesso", "detailsTimersectionHeader": "<PERSON><PERSON>", "detailsTimersectionTimercount": "di 60 programmi utilizzati", "detailsConfigurationsectionHeader": "Configurazione", "detailsConfigurationPin": "PIN del dispositivo", "detailsConfigurationChannelsDescription": "Canale 1: {channel1}| Canale 2:  {channel2}", "settingsCentralHeader": "Central On/Off", "detailsConfigurationCentralDescription": "Si applica solo se il canale è impostato su AUTO", "detailsConfigurationDevicedisplaylock": "Blocco del display del dispositivo", "timerOverviewHeader": "<PERSON><PERSON>", "timerOverviewTimersectionTimerinactivecount": "inattivo", "timerDetailsListsectionDays1": "Lunedì", "timerDetailsListsectionDays2": "Martedì", "timerDetailsListsectionDays3": "Mercoledì", "timerDetailsListsectionDays4": "<PERSON><PERSON><PERSON><PERSON>", "timerDetailsListsectionDays5": "<PERSON>ener<PERSON><PERSON>", "timerDetailsListsectionDays6": "Sabato", "timerDetailsListsectionDays7": "Domenica", "timerDetailsHeader": "Programma %@", "timerDetailsSunrise": "Alba", "generalToggleOff": "Off", "generalToggleOn": "On", "timerDetailsImpuls": "<PERSON>mpul<PERSON>", "generalTextTime": "Tempo", "generalTextAstro": "Astro", "generalTextAuto": "Auto", "timerDetailsOffset": "Time Offset", "timerDetailsPlausibility": "Attivare il controllo di plausibilità", "timerDetailsPlausibilityDescription": "Se l'ora di spegnimento è impostata su un'ora precedente a quella di accensione, entrambi i programmi vengono ignorati, ad esempio l'accensione all'alba e lo spegnimento alle 6:00 del mattino. Ci potrebbero essere programmazioni non volute come l'accensione al tramonto e lo spegnimento alle 01:00 del mattino", "generalDone": "Pronto", "generalDelete": "Cancellare", "timerDetailsImpulsDescription": "Per modifiche, accedere alla configurazione del dispositivo", "settingsNameHeader": "Nome del dispositivo", "settingsNameDescription": "Il nome è visibile quando si utilizza il Bluetooth.", "settingsFactoryresetHeader": "Impostazioni di fabbrica", "settingsFactoryresetDescription": "Quali contenuti devono essere reimpostati?", "settingsFactoryresetResetbluetooth": "Ripristino delle impostazioni Bluetooth", "settingsFactoryresetResettime": "Ripristino delle impostazioni dell'ora", "settingsFactoryresetResetall": "Impostare le impostazioni di fabbrica", "settingsDeletetimerHeader": "Cancellare i programmi", "settingsDeletetimerDescription": "Scegliere i canali da eliminare.", "settingsDeletetimerAllchannels": "Tutti i canali", "settingsImpulseHeader": "Tempo di commutazione degli impulsi", "settingsImpulseDescription": "Il tempo di commutazione dell'impulso imposta la durata dell'impulso.", "generalTextRandommode": "Modalità casuale", "settingsChannelsTimeoffsetHeader": "Sfalsamento dell'ora del solstizio", "settingsChannelsTimeoffsetDescription": "Estate: {summerOffset}min | Inverno: {winterOffset}min", "settingsLocationHeader": "Posizione", "settingsLocationDescription": "Impostare la posizione per utilizzare le funzioni astro.", "settingsLanguageHeader": "Lingua del dispositivo", "settingsLanguageSetlanguageautomatically": "Impostazione automatica della lingua", "settingsLanguageDescription": "Scegliere la lingua per il {deviceType}", "settingsLanguageGerman": "Tedesco", "settingsLanguageFrench": "<PERSON><PERSON>", "settingsLanguageEnglish": "<PERSON><PERSON><PERSON>", "settingsLanguageItalian": "Italiano", "settingsLanguageSpanish": "<PERSON><PERSON><PERSON>", "settingsDatetimeHeader": "Data e ora", "settingsDatetimeSettimeautomatically": "Applicare il tempo di sistema", "settingsDatetimeSettimezoneautomatically": "Impostazione automatica del fuso orario", "generalTextTimezone": "<PERSON><PERSON> orario", "settingsDatetime24Hformat": "Formato 24 ore", "settingsDatetimeSetsummerwintertimeautomatically": "Estate/inverno automaticamente", "settingsDatetimeWinter": "Inverno", "settingsDatetimeSummer": "Estate", "settingsPasskeyHeader": "PIN attuale del dispositivo", "settingsPasskeyDescription": "Inserire il PIN attuale del dispositivo", "timerDetailsActiveprogram": "Programma attivo", "timerDetailsActivedays": "<PERSON><PERSON><PERSON>", "timerDetailsSuccessdialogHeader": "Successo", "timerDetailsSuccessdialogDescription": "Programma aggiunto con successo", "settingsRandommodeDescription": "La modalità casuale funziona solo con i programmi basati su timer, ma non con quelli basati su impulsi o su astro (alba/tramonto). Con la modalità casuale attivata, tutti i programmi di questo canale vengono sfalsati in modo casuale fino a 15 minuti. I programmi on-timers sono sfalsati in anticipo, quelli off-timers sono ritardati.", "settingsSolsticeHeader": "Sfalsamento dell'ora del solstizio", "settingsSolsticeDescription": "L'ora impostata definisce l'offset temporale rispetto al solstizio. Il solstizio viene rispettivamente invertito.", "settingsSolsticeHint": "Esempio:\nIn inverno la commutazione avviene 30 minuti prima del tramonto, in risposta la commutazione all'alba avviene anch'essa con 30 minuti di anticipo.", "generalTextMinutesShort": "min", "settingsPinDescription": "Il PIN è necessario per la connessione.", "settingsPinHeader": "PIN del nuovo dispositivo", "settingsPinNewpinDescription": "Inserire un nuovo PIN", "settingsPinNewpinRepeat": "Ripetere il nuovo PIN", "detailsProductinfo": "Informazioni sul prodotto", "settingsDatetimeSettimeautodescription": "Scegliere l'orario preferito", "minutes": "{count, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON>}}", "hours": "{count, plural, one {<PERSON><PERSON>} other {Ore}}", "seconds": "{count, plural, one {secondo} other {secondi}}.", "generalTextChannel": "{count, plural, one {canale} other {canali}}", "generalLabelChannel": "Canale {number}", "generalTextDate": "Data", "settingsDatetime24HformatDescription": "Scegliere il formato preferito", "settingsDatetimeSetsummerwintertime": "Estate/inverno", "settingsDatetime24HformatValue24": "24h", "settingsDatetime24HformatValue12": "AM/PM", "detailsEdittimer": "Modifica dei programmi", "settingsPinOldpinRepeat": "Ripetere il PIN attuale", "settingsPinCheckpin": "Verifica del PIN", "detailsDevice": "{count, plural, one {dispositivo} other {dispositivo}}", "detailsDisconnect": "Disconnessione", "settingsCentralDescription": "L'ingresso A1 controlla l'On/Off centrale.\nL'On/Off centrale si applica solo se il canale è impostato su On/Off centrale.", "settingsCentralHint": "Esempio:\nCanale 1 = On/Off centrale\nCanale 2 = Spento\nA1 = On centrale -> Solo C1 passa a On, C2 rimane Off", "settingsCentralToggleheader": "Interruttori di ingresso centrale", "settingsCentralActivechannelsdescription": "Canali attuali con l'impostazione Central On/Off:", "settingsSolsticeSign": "Se<PERSON>", "settingsDatetimeTimezoneDescription": "Ora dell'Europa centrale", "generalButtonContinue": "Continua", "settingsPinConfirmationDescription": "Modifica del PIN riuscita", "settingsPinFailDescription": "Modifica del PIN non riuscita", "settingsPinFailHeader": "Errore", "settingsPinFailShort": "Il PIN deve essere composto esattamente da 6 cifre.", "settingsPinFailWrong": "Il PIN non è corretto", "settingsPinFailMatch": "I PIN non corrispondono", "discoveryLostconnectionHeader": "Connessione persa", "discoveryLostconnectionDescription": "Il dispositivo è stato scollegato.", "settingsChannelConfigCentralDescription": "Si comporta come AUTO e ascolta anche gli ingressi della centrale cablata", "settingsChannelConfigOnDescription": "Commuta il canale in modo permanente su ON e ignora i programmi", "settingsChannelConfigOffDescription": "Commuta il canale in modo permanente su OFF e ignora i programmi", "settingsChannelConfigAutoDescription": "Commuta in relazione all'ora e ai programmi astronomici", "bluetoothPermissionDescription": "Per la configurazione dei dispositivi è necessario il Bluetooth.", "timerListitemOn": "A<PERSON><PERSON>e", "timerListitemOff": "<PERSON><PERSON><PERSON><PERSON>", "timerListitemUnknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timerDetailsAstroHint": "La posizione deve essere impostata nelle impostazioni affinché i programmi astro funzionino correttamente.", "timerDetailsTrigger": "Aziona", "timerDetailsSunset": "Tramonto", "settingsLocationCoordinates": "Coordinate", "settingsLocationLatitude": "Latitudine", "settingsLocationLongitude": "<PERSON><PERSON><PERSON><PERSON>", "timerOverviewEmptyday": "Al momento non vengono utilizzati programmi per {day}", "timerOverviewProgramloaded": "I programmi vengono caricati", "timerOverviewProgramchanged": "Il programma è stato modificato", "settingsDatetimeProcessing": "La data e l'ora sono state modificate", "deviceNameEmpty": "L'input non deve essere vuoto", "deviceNameHint": "L'input non deve contenere più di {count} caratteri.", "deviceNameChanged": "Il nome del dispositivo è stato modificato", "deviceNameChangedSuccessfully": "Il nome del dispositivo è stato modificato con successo.", "deviceNameChangedFailed": "Si è verificato un errore.", "settingsPinConfirm": "<PERSON><PERSON><PERSON>", "deviceShowInstructions": "1. <PERSON><PERSON><PERSON><PERSON> il Bluetooth dell'orologio con SET\n2. <PERSON><PERSON><PERSON> il pulsante in alto per avviare la ricerca.", "deviceNameNew": "Inserire il nuovo nome del dispositivo", "settingsLanguageRetrieved": "La lingua viene recuperata", "detailsProgramsShow": "Mostra i programmi", "generalTextProcessing": "Attendere prego", "generalTextRetrieving": "vengono recuperati", "settingsLocationPermission": "Consentire a ELTAKO Connect di accedere alla posizione di questo dispositivo", "timerOverviewChannelloaded": "I canali sono caricati", "generalTextRandommodeChanged": "La modalità casuale viene modificata", "detailsConfigurationsectionChanged": "La configurazione viene modificata", "settingsSettimeFunctions": "Le funzioni temporali vengono modificate", "imprintContact": "Contat<PERSON>", "imprintPhone": "Telefono", "imprintMail": "Mail", "imprintRegistrycourt": "Registro del tribunale", "imprintRegistrynumber": "Numero di registrazione", "imprintCeo": "Direttore generale", "imprintTaxnumber": "Numero di identificazione dell'imposta sulle vendite", "settingsLocationCurrent": "Posizione attuale", "generalTextReset": "Reset", "discoverySearchStart": "Avviare la ricerca", "discoverySearchStop": "Interrompere la ricerca", "settingsImpulsSaved": "Il tempo di commutazione degli impulsi viene memorizzato", "settingsCentralNochannel": "Non ci sono canali con l'impostazione On/Off centrale.", "settingsFactoryresetBluetoothConfirmationDescription": "La connessione Bluetooth è stata ripristinata con successo.", "settingsFactoryresetBluetoothFailDescription": "Ripristino delle connessioni Bluetooth non riuscito.", "imprintPublisher": "Editore", "discoveryDeviceConnecting": "Connessione riuscita", "discoveryDeviceRestarting": "Riavvio...", "generalTextConfigurationsaved": "Configurazione del canale salvata.\n", "timerOverviewChannelssaved": "Salva i canali", "timerOverviewSaved": "<PERSON><PERSON> salvato\n", "timerSectionList": "Vista elenco\n", "timerSectionDayview": "vista giorno", "generalTextChannelInstructions": "Impostazioni del canale", "generalTextPublisher": "Editore", "settingsDeletetimerDialog": "Volete davvero cancellare tutti i programmi?", "settingsFactoryresetResetbluetoothDialog": "Volete davvero ripristinare tutte le impostazioni Bluetooth?", "settingsCentralTogglecentral": "Central\nOn/Off", "generalTextConfirmation": "{serviceName} modifica riuscita.", "generalTextFailed": "{serviceName} modifica fallita.", "settingsChannelConfirmationDescription": "I canali sono stati cambiati con successo.", "timerDetailsSaveHeader": "<PERSON><PERSON><PERSON> il programma", "timerDetailsDeleteHeader": "Cancellare il programma", "timerDetailsSaveDescription": "Salvataggio del programma riuscito.", "timerDetailsDeleteDescription": "L'eliminazione del programma è riuscita.", "timerDetailsAlertweekdays": "Il programma non può essere salvato perché non è stato selezionato alcun giorno della settimana.", "generalTextOk": "OK", "settingsDatetimeChangesuccesfull": "L'ora è stata modificata con successo.", "discoveryConnectionFailed": "Connessione fallita", "discoveryDeviceResetrequired": "Non è stato possibile stabilire una connessione con il dispositivo. Per risolvere il problema, eliminare il dispositivo dalle impostazioni Bluetooth. Se il problema persiste, contattare il nostro supporto tecnico.", "generalTextSearch": "Dispositivi di ricerca", "generalTextOr": "o", "settingsFactoryresetProgramsConfirmationDescription": "<PERSON>tti i programmi sono stati eliminati con successo.", "generalTextManualentry": "Inserimento manuale", "settingsLocationSaved": "Posizione salvata", "settingsLocationAutosearch": "Ricerca automatica della posizione", "imprintPhoneNumber": "+49 711 / 9435 0000", "imprintMailAddress": "<EMAIL>", "settingsFactoryresetResetallDialog": "Volete davvero ripristinare le impostazioni di fabbrica del dispositivo?", "settingsFactoryresetFactoryConfirmationDescription": "Il dispositivo è stato ripristinato con successo alle impostazioni di fabbrica.", "settingsFactoryresetFactoryFailDescription": "Il reset dell'unità non è riuscito.", "imprintPhoneNumberIos": "+49711/94350000", "mfzFunctionA2Title": "Ritardo di risposta a 2 stadi (A2)", "mfzFunctionA2TitleShort": "Ritardo di risposta a 2 stadi (A2)", "mfzFunctionA2Description": "Quando viene applicata la tensione di controllo, inizia il tempo T1 compreso tra 0 e 60 secondi. Al termine di questo periodo, il contatto 1-2 si chiude e inizia il lasso di tempo t2 compreso tra 0 e 60 secondi. Al termine di questo periodo, il contatto 3-4 si chiude. Dopo un'interruzione, la sequenza temporale ricomincia con t1.", "mfzFunctionRvTitle": "<PERSON><PERSON> di spegnimento (RV)", "mfzFunctionRvTitleShort": "RV | Ritardo di spegnimento", "mfzFunctionRvDescription": "Quando viene applicata la tensione di controllo, il contatto passa cambia a 15-18. \nQuando la tensione di controllo viene interrotta, inizia l'intervallo di tempo, al termine del quale il contatto di lavoro torna in posizione di riposo. Può essere attivato durante il timeout.", "mfzFunctionTiTitle": "Intermittenza inizio ON (TI)", "mfzFunctionTiTitleShort": "TI | Intermittenza inizio ON ", "mfzFunctionTiDescription": "Finché la tensione di controllo è applicata, il contatto di comando si chiude e si apre. Il tempo di commutazione in entrambe le direzioni può essere impostato separatamente. Quando viene applicata la tensione di controllo, il contatto di lavoro passa immediatamente a 15-18.", "mfzFunctionAvTitle": "<PERSON><PERSON> di risposta (AV)", "mfzFunctionAvTitleShort": "AV | Ritardo di risposta", "mfzFunctionAvDescription": "Quando viene applicata la tensione di controllo, inizia la sequenza temporale, al termine della quale il contatto di lavoro passa a 15-18. <PERSON><PERSON> un'interruzione, la sequenza temporale ricomincia.", "mfzFunctionAvPlusTitle": "Ritardo di risposta additivo (AV+; ritardo di accensione)", "mfzFunctionAvPlusTitleShort": "AV+ | Ritardo di risposta additivo", "mfzFunctionAvPlusDescription": "Funziona come AV, ma dopo un'interruzione il tempo già trascorso rimane memorizzato.", "mfzFunctionAwTitle": "Contatto ad impulso alla diseccitazione (AW)", "mfzFunctionAwTitleShort": "AW | contatto ad impulso alla diseccitazione", "mfzFunctionAwDescription": "Quando viene interrotta la tensione di comando il contatto passa da 15-16 a 15-18 e ritorna automaticamente a 15-16 allo scadere del tempo impostato. Se durante il tempo impostato viene applicata la tensione di comando, il contatto ritorna immediatamente a 15-16 e il tempo rimanente viene cancellato.", "mfzFunctionIfTitle": "<PERSON><PERSON><PERSON><PERSON> di impulsi (IF; solo MFZ12.1)", "mfzFunctionIfTitleShort": "IF | Generatore di impulsi", "mfzFunctionIfDescription": "Quando viene applicata la tensione di comando, il contatto passa a 15-18 per il tempo impostato. Le ulteriori attivazioni vengono valutate solo allo scadere del tempo impostato.", "mfzFunctionEwTitle": "Contatto ad impulso all'eccitazione (EW)", "mfzFunctionEwTitleShort": "EW | Contatto ad impulso all'eccitazione", "mfzFunctionEwDescription": "All'applicazione della tensione di comando il contatto passa da 15-16 a 15-18 e ritorna automaticamente a 15-16 allo scadere del tempo impostato. Se durante il tempo impostato viene interrotta la tensione di comando il contatto ritorna immediatamente a 15-16 ed il tempo rimanente viene cancellato.", "mfzFunctionEawTitle": "Contatto ad impulso all'eccitazione ed alla diseccitazione (EAW)", "mfzFunctionEawTitleShort": "EAW | Contatto ad impulso all'eccitazione ed alla diseccitazione", "mfzFunctionEawDescription": "Quando la tensione di controllo viene applicata o interrotta, il contatto di lavoro passa a 15-18 e ritorna dopo il tempo di pulizia impostato.", "mfzFunctionTpTitle": "Intermittenza inizio con pausa (TP;)", "mfzFunctionTpTitleShort": "TP | Intermittenza inizio con pausa", "mfzFunctionTpDescription": "Descrizioni funzionali come TI, ma quando viene applicata la tensione di controllo, il contatto non passa a 15-18, ma rimane inizialmente a 15-16 o aperto.", "mfzFunctionIaTitle": "Ritardato all'eccitazione comandato da impulso (es. apertura porte automatiche)", "mfzFunctionIaTitleShort": "IA | Ritardato all'eccitazione comandato da impulso", "mfzFunctionIaDescription": "All'applicazione di un impulso di comando di almeno 50 ms inizia il periodo t1. A<PERSON>na trascorso il contatto passa a 15-18 per il periodo t2 (MFZ12DX = 1 secondo). Per es. apriporte automatico. Se t1 viene impostato al tempo minimo di 0,1 secondi, agisce come generatore di impulso, impulso generato con il periodo t2 indipendentemente dalla lunghezza del segnale di comando (min. 150ms.)", "mfzFunctionArvTitle": "<PERSON><PERSON> nella risposta e nel rilascio (ARV)", "mfzFunctionArvTitleShort": "ARV | Ritardo nella risposta e nel rilascio", "mfzFunctionArvDescription": "Quando viene applicata la tensione di controllo, inizia il timeout, al termine del quale il contatto di lavoro passa alla posizione 15 -18. Se la tensione di controllo viene interrotta, inizia un nuovo timeout, al termine del quale il contatto di comando torna in posizione di riposo.\nDopo un'interruzione del ritardo di risposta, il timeout inizia nuovamente.", "mfzFunctionArvPlusTitle": "Risposta additiva e ritardo di rilascio (ARV+)", "mfzFunctionArvPlusTitleShort": "ARV+ | Risposta additiva e ritardo di rilascio", "mfzFunctionArvPlusDescription": "Funziona come ARV, ma dopo un'interruzione del ritardo di risposta, il tempo già trascorso rimane memorizzato.", "mfzFunctionEsTitle": "Interru<PERSON>re a impulsi (ES)", "mfzFunctionEsTitleShort": "ES | Interruttore a impulsi", "mfzFunctionEsDescription": "Il contatto di lavoro si muove avanti e indietro con impulsi di controllo da 50 ms.", "mfzFunctionEsvTitle": "Interruttore a impulsi con ritardo di spegnimento e avviso di spegnimento anticipato (ESV)", "mfzFunctionEsvTitleShort": "ESV | Interruttore a impulsi con ritardo di spegnimento", "mfzFunctionEsvDescription": "Funzione come SRV. Inoltre, con preallarme di spegnimento: a partire da circa 30 secondi prima dello scadere del tempo, l'illuminazione lampeggia 3 volte a intervalli di tempo decrescenti.", "mfzFunctionErTitle": "Funzione relè monostabile (ER)", "mfzFunctionErTitleShort": "ER | Funzione relè monostabile", "mfzFunctionErDescription": "<PERSON><PERSON> il contatto di comando è chiuso, il contatto di lavoro commuta da 15-16 a 15-18.", "mfzFunctionSrvTitle": "Interruttore a impulsi con ritardo di spegnimento (SRV)", "mfzFunctionSrvTitleShort": "SRV | Interruttore a impulsi con ritardo di spegnimento", "mfzFunctionSrvDescription": "Il contatto di lavoro commuta avanti e indietro con impulsi di comando a partire da 50ms. Nella posizione di contatto 15-18, il dispositivo torna automaticamente alla posizione di riposo 15-16 allo scadere del tempo di ritardo.", "detailsFunctionsHeader": "Funzioni", "mfzFunctionTimeHeader": "Tempo (t{index})", "mfzFunctionOnDescription": "acceso permanente", "mfzFunctionOffDescription": "spento permanente", "mfzFunctionMultiplier": "Fattore", "discoveryMfz12Description": "Relè orario multifunzione Bluetooth", "mfzFunctionOnTitle": "acceso permanente", "mfzFunctionOnTitleShort": "acceso permanente", "mfzFunctionOffTitle": "spento permanente", "mfzFunctionOffTitleShort": "spento permenente", "mfzMultiplierSecondsFloatingpoint": "0.1 secondi", "mfzMultiplierMinutesFloatingpoint": "0.1 minuti", "mfzMultiplierHoursFloatingpoint": "0.1 ore", "mfzOverviewFunctionsloaded": "Le funzioni sono caricate", "mfzOverviewSaved": "Funzione salvata", "settingsBluetoothHeader": "Bluetooth", "bluetoothChangedSuccessfully": "L'impostazione Bluetooth è stata modificata con successo.", "settingsBluetoothInformation": "Nota: se questa impostazione è attivata, il dispositivo è permanentemente visibile a tutti tramite Bluetooth!", "settingsBluetoothContinuousconnection": "Visibilità permanente", "settingsBluetoothContinuousconnectionDescription": "Attivando la visibilità permanente, il Bluetooth rimane attivo al dispositivo ({deviceType}) e non deve essere attivato manualmente prima di stabilire una connessione.", "settingsBluetoothTimeout": "Timeout della connessione", "settingsBluetoothPinlimit": "Limite del PIN", "settingsBluetoothTimeoutDescription": "La connessione viene scollegata dopo {timeout} minuti di inattività.", "settingsBluetoothPinlimitDescription": "Per motivi di sicurezza, è possibile effettuare un massimo di {attempts} tentativi per l'inserimento del PIN. Il Bluetooth viene quindi disattivato e deve essere riattivato manualmente per una nuova connessione. manualmente per una nuova connessione.", "settingsBluetoothPinAttempts": "tentativi", "settingsResetfunctionHeader": "Funzioni di reset", "settingsResetfunctionDialog": "Vuoi davvero ripristinare tutte le funzioni?", "settingsFactoryresetFunctionsConfirmationDescription": "Tutte le funzioni sono state ripristinate con successo.", "mfzFunctionTime": "Tempo (t)", "discoveryConnectionFailedInfo": "Nessuna connessione Bluetooth", "detailsConfigurationDevicedisplaylockDialogtext": "Subito dopo aver bloccato il display del dispositivo, il Bluetooth viene disattivato e deve essere riattivato manualmente per stabilire una nuova connessione.", "detailsConfigurationDevicedisplaylockDialogquestion": "Sei sicuro di bloccare il display del dispositivo?", "settingsDemodevices": "Mostra unità demo", "generalTextSettings": "Impostazioni", "discoveryWifi": "WiFi", "settingsInformations": "Informazioni", "detailsConfigurationDimmingbehavior": "funzionamento dimmer", "detailsConfigurationSwitchbehavior": "funzionamento impulso", "detailsConfigurationBrightness": "Luminosità", "detailsConfigurationMinimum": "Minimo", "detailsConfigurationMaximum": "<PERSON><PERSON>", "detailsConfigurationSwitchesGr": "Gruppo relè (GR)", "detailsConfigurationSwitchesGs": "Interruttore di gruppo (GS)", "detailsConfigurationSwitchesCloserer": "Interru<PERSON><PERSON> di ch<PERSON> (ER)", "detailsConfigurationSwitchesClosererDescription": "Off -> <PERSON><PERSON> pre<PERSON> (On) -> <PERSON><PERSON><PERSON><PERSON><PERSON> (Off)", "detailsConfigurationSwitchesOpenerer": "Interruttore di apertura (ER-Invers)", "detailsConfigurationSwitchesOpenererDescription": "On -> <PERSON><PERSON> pre<PERSON> (Off) -> <PERSON><PERSON><PERSON><PERSON><PERSON> (On)", "detailsConfigurationSwitchesSwitch": "Interruttore", "detailsConfigurationSwitchesSwitchDescription": "Ad ogni impulso, la luce si accende e si spegne", "detailsConfigurationSwitchesImpulsswitch": "<PERSON><PERSON><PERSON> passo passo", "detailsConfigurationSwitchesImpulsswitchDescription": "L'interruttore viene premuto e rilasciato brevemente per accendere o spegnere la luce.", "detailsConfigurationSwitchesClosererDescription2": "Tenere premuto l'interruttore. Quando viene rilasciato, il motore si ferma", "detailsConfigurationSwitchesImpulsswitchDescription2": "L'interruttore viene premuto brevemente per avviare il motore e brevemente per arrestarlo di nuovo.", "detailsConfigurationWifiloginScan": "Scansione del codice QR", "detailsConfigurationWifiloginScannotvalid": "Il codice scansionato non è valido", "detailsConfigurationWifiloginDescription": "Inserire il codice", "detailsConfigurationWifiloginPassword": "Password", "discoveryEsbipDescription": "Attuatore per tapparelle e tende IP", "discoveryEsripDescription": "<PERSON><PERSON><PERSON> passo passo IP", "discoveryEudipDescription": "Dimmer universale IP", "generalTextLoad": "Caricamento", "wifiBasicautomationsNotFound": "Non è stata trovata alcuna automazione.", "wifiCodeInvalid": "Codice non valido", "wifiCodeValid": "Codice valido", "wifiAuthorizationLogin": "Collegare", "wifiAuthorizationLoginFailed": "Accesso non riuscito", "wifiAuthorizationSerialnumber": "Numero di serie", "wifiAuthorizationProductiondate": "Data di produzione", "wifiAuthorizationProofofpossession": "PoP", "generalTextWifipassword": "Password del WiFi", "generalTextUsername": "Nome utente", "generalTextEnter": "O INSERIRE MANUALMENTE", "wifiAuthorizationScan": "Scansionare il codice ELTAKO.", "detailsConfigurationDevicesNofunctionshinttext": "Questo dispositivo non supporta attualmente altre impostazioni", "settingsUsedemodelay": "Utilizzare il ritardo demo", "settingsImpulsLoad": "Il tempo di commutazione degli impulsi è caricato", "settingsBluetoothLoad": "L'impostazione Bluetooth è in fase di caricamento.", "detailsConfigurationsectionLoad": "Le configurazioni sono caricate", "generalTextLogin": "Accedi", "generalTextAuthentication": "Autenticare", "wifiAuthorizationScanDescription": "Cerca il codice ELTAKO sul dispositivo o sulla scheda informativa inclusa e allinealo nella cornice della fotocamera in alto.", "wifiAuthorizationScanShort": "Scansione del codice ELTAKO", "detailsConfigurationEdgemode": "Modalità Edge", "detailsConfigurationEdgemodeLeadingedge": "Leading edge", "generalTextNetwork": "Rete", "wifiAuthenticationSuccessful": "Autenticazione riuscita", "detailsConfigurationsectionSavechange": "Configurazione modificata", "discoveryWifiAdddevice": "Aggiungi dispositivo Wi-Fi", "wifiAuthenticationDelay": "Questo può durare fino a 1 minuto", "generalTextRetry": "<PERSON><PERSON><PERSON><PERSON>", "wifiAuthenticationCredentials": "Inserisci le credenziali del tuo WiFi", "wifiAuthenticationSsid": "SSID", "wifiAuthenticationDelaylong": "P<PERSON>ò durare fino a 1 minuto finché il dispositivo non è pronto\ne viene visualizzato nell'app", "wifiAuthenticationCredentialsShort": "Inserisci le credenziali Wi-Fi", "wifiAuthenticationTeachin": "Insegna il dispositivo al WiFi", "wifiAuthenticationEstablish": "Stabilire la connessione al dispositivo", "wifiAuthenticationEstablishLong": "Il dispositivo si connette al Wi-Fi {ssid}", "wifiAuthenticationFailed": "Connessione fallita. Scollegare il dispositivo dall'alimentazione per alcuni secondi e riprovare a collegarlo", "wifiAuthenticationReset": "Reimposta l'autenticazione", "wifiAuthenticationResetHint": "Il codice ELTAKO deve quindi essere nuovamente scansionato", "wifiAuthenticationInvaliddata": "Dati di autenticazione non validi", "wifiAuthenticationReauthenticate": "Autenticare di nuovo", "wifiAddhkdeviceHeader": "Aggiungi dispositivo", "wifiAddhkdeviceDescription": "Collegate il nuovo dispositivo ELTAKO alla vostra WLAN tramite l'app Apple Home.", "wifiAddhkdeviceStep1": "Aprite l'app Apple Home.", "wifiAddhkdeviceStep2": "Fare clic sul più nell'angolo superiore destro dell'applicazione e selezionare **Aggiungi dispositivo**.", "wifiAddhkdeviceStep3": "Seguire le istruzioni dell'applicazione.", "wifiAddhkdeviceStep4": "Ora il dispositivo può essere configurato nell'app ELTAKO Connect.", "detailsConfigurationRuntime": "Tempo di esecuzione", "detailsConfigurationRuntimeMode": "Modalità", "generalTextManually": "Manualmente", "detailsConfigurationRuntimeAutoDescription": "L'attuatore di ombreggiatura determina autonomamente il tempo di funzionamento durante ogni corsa dalla posizione finale inferiore a quella superiore (consigliata).\n<PERSON><PERSON> la messa in funzione, tale movimento\ndovrebbe essere effettuato dal basso verso l'alto senza interruzioni.\n", "detailsConfigurationRuntimeManuallyDescription": "Il tempo di funzionamento del motore di ombreggiatura viene impostato manualmente tramite la durata sottostante.\nAssicurati che il tempo di funzionamento configurato corrisponda al tempo di funzionamento effettivo del tuo motore di schermatura.\nDopo l'avvio iniziale o le modifiche, tale movimento dovrebbe essere eseguito dal basso verso l'alto senza interruzioni.", "detailsConfigurationRuntimeDemoDescription": "La modalità demo è disponibile solo tramite API REST", "generalTextDemomodeActive": "Modalità demo attiva", "detailsConfigurationRuntimeDuration": "<PERSON><PERSON>", "detailsConfigurationSwitchesGs4": "Interruttore di gruppo (GS4)", "detailsConfigurationSwitchesGs4Description": "Interruttore di gruppo con funzione di inversione jog per il comando delle tende", "screenshotSu12": "Faro esterno", "screenshotS2U12": "Faro esterno", "screenshotMfz12": "Pompa", "screenshotEsr62": "<PERSON><PERSON><PERSON>", "screenshotEud62": "Plafonier<PERSON>", "screenshotEsb62": "Persiane balcone", "detailsConfigurationEdgemodeLeadingedgeDescription": "LC1-LC3 sono posizioni di comfort con diverse curve di regolazione per le lampade LED dimmerabili a 230 V, che a causa della loro struttura non possono essere regolate a sufficienza su AUTO e devono quindi essere forzate al controllo dell'angolo di fase.", "detailsConfigurationEdgemodeAutoDescription": "AUTO consente di regolare la luminosità di tutti i tipi di lampade.", "detailsConfigurationEdgemodeTrailingedge": "<PERSON><PERSON>'<PERSON>", "detailsConfigurationEdgemodeTrailingedgeDescription": "LC4-LC6 sono posizioni Comfort con diverse curve di dimmerazione per lampade LED dimmerabili a 230 V.", "updateHeader": "Aggiornamento del firmware", "updateTitleStepSearch": "Ricerca di un aggiornamento", "updateTitleStepFound": "È stato trovato un aggiornamento", "updateTitleStepDownload": "Download dell'aggiornamento", "updateTitleStepInstall": "Installazione dell'aggiornamento", "updateTitleStepSuccess": "Aggiornamento riuscito", "updateTitleStepUptodate": "Già a<PERSON>rnato", "updateTitleStepFailed": "Aggiornamento fallito", "updateButtonSearch": "Ricerca di aggiornamenti", "updateButtonInstall": "Installare l'aggiornamento", "updateCurrentversion": "Versione attuale", "updateNewversion": "Disponibile un nuovo aggiornamento del firmware", "updateHintPower": "L'utente deve essere spento e il dispositivo non deve essere scollegato dall'alimentazione.", "updateButton": "Aggiornamento", "updateHintCompatibility": "Si consiglia di effettuare un aggiornamento, altrimenti alcune funzioni dell'app saranno limitate.", "generalTextDetails": "<PERSON><PERSON><PERSON>", "updateMessageStepMetadata": "Informazioni sull'aggiornamento Lade", "updateMessageStepPrepare": "L'aggiornamento è stato autorizzato", "updateTitleStepUpdatesuccessful": "L'aggiornamento è stato effettuato", "updateTextStepFailed": "Tuttavia, l'aggiornamento è molto complicato, ma è possibile provarlo in un paio di minuti o verificare se l'apparecchio è stato aggiornato automaticamente (collegamento a Internet).", "configurationsNotavailable": "Non ci sono ancora configurazioni disponibili", "configurationsAddHint": "Creare nuove configurazioni collegandosi a un dispositivo e salvando una configurazione.", "@configurationsAddHint": {"description": "Now available for all devices not only Bluetooth", "type": "text"}, "configurationsEdit": "Modifica della configurazione", "generalTextName": "Nome", "configurationsDelete": "Cancellare la configurazione", "configurationsDeleteHint": "La configurazione: {configName} deve essere cancellata?", "configurationsSave": "Salvare la configurazione", "configurationsSaveHint": "Qui è possibile salvare la configurazione del dispositivo attuale o caricare una configurazione precedentemente salvata.", "configurationsImport": "Importazione della configurazione", "configurationsImportHint": "La configurazione {configName} deve essere trasferita?", "generalTextConfigurations": "{count, plural, one {Configurazione} other {Configurazioni}}", "configurationsStepPrepare": "La configurazione è in fase di preparazione", "configurationsStepName": "Inserire un nome per la configurazione", "configurationsStepSaving": "La configurazione viene salvata", "configurationsStepSavedsuccessfully": "La configurazione è stata salvata con successo", "configurationsStepSavingfailed": "Il salvataggio della configurazione non è riuscito", "configurationsStepChoose": "Selezionare una configurazione", "configurationsStepImporting": "La configurazione viene importata", "configurationsStepImportedsuccessfully": "La configurazione è stata importata con successo", "configurationsStepImportingfailed": "Importazione della configurazione fallita", "discoveryAssuDescription": "Interruttore orario per prese da esterno Bluetooth", "settingsDatetimeDevicetime": "Tempo effettivo del dispositivo", "settingsDatetimeLoading": "Le impostazioni dell'ora sono caricate", "discoveryEud12Description": "Interruttore dimmer universale Bluetooth", "generalTextOffdelay": "<PERSON><PERSON> di spegnimento", "generalTextRemainingbrightness": "Luminosità residua", "generalTextSwitchonvalue": "Valore di accensione", "motionsensorTitleNoremainingbrightness": "Nessuna luminosità residua", "motionsensorTitleAlwaysremainingbrightness": "Con luminosità residua", "motionsensorTitleRemainingbrightnesswithprogram": "Luminosità residua tramite programma di commutazione", "motionsensorTitleRemainingbrightnesswithprogramandzea": "Luminosità residua tramite ZE e ZA", "motionsensorTitleNoremainingbrightnessauto": "Nessuna luminosità residua (semiautomatico)", "generalTextMotionsensor": "Rilevatore di movimento", "generalTextLightclock": "Sveglia luminosa", "generalTextSnoozeclock": "Funzione Autospegnimento", "generalDescriptionLightclock": "All'accensione ({mode}), la luce si accende dopo circa 1 secondo alla luminosità minima e si attenua lentamente senza modificare l'ultimo livello di luminosità salvato.", "generalDescriptionSnoozeclock": "Quando si spegne ({mode}), l'illuminazione viene abbassata dalla posizione di regolazione attuale alla luminosità minima e si spegne. L'illuminazione può essere spenta in qualsiasi momento durante il processo di regolazione della luminosità premendo brevemente il pulsante. Una pressione prolungata durante il processo di regolazione della luminosità fa cessare la funzione autospegnimento.", "generalTextImmediately": "Immediatamente", "generalTextPercentage": "Percent<PERSON><PERSON>", "generalTextSwitchoffprewarning": "Preavviso di spegnimento", "generalDescriptionSwitchoffprewarning": "Dimmerazione lenta fino alla luminosità minima", "generalDescriptionOffdelay": "Il dispositivo si accende quando viene applicata la tensione di controllo. Se la tensione di controllo viene interrotta, inizia un intervallo di tempo, al termine del quale l'apparecchio si spegne. L'apparecchio può essere acceso a valle durante il lasso di tempo.", "generalDescriptionBrightness": "La luminosità con cui la lampada viene accesa dal dimmer.", "generalDescriptionRemainingbrightness": "Il valore di regolazione in percentuale a cui la lampada viene regolata dopo lo spegnimento del rilevatore di movimento.", "generalDescriptionRuntime": "Tempo di funzionamento della funzione di sveglia luminosa dalla luminosità minima alla luminosità massima.", "generalTextUniversalbutton": "Pulsante universale", "generalTextDirectionalbutton": "Pulsante di direzione", "eud12DescriptionAuto": "Rilevamento automatico UT/RT (con sensore direzionale a diodo RTD)", "eud12DescriptionRt": "con diodo di rilevamento direzionale RTD", "generalTextProgram": "Programma", "eud12MotionsensorOff": "Con il rilevatore di movimento impostato su Off", "eud12ClockmodeTitleProgramze": "Programma e Central On", "eud12ClockmodeTitleProgramza": "Programma e Central Off", "eud12ClockmodeTitleProgrambuttonon": "Programma e UT/RT On", "eud12ClockmodeTitleProgrambuttonoff": "Programma e UT/RT Off", "eud12TiImpulseTitle": "Tempo di impulso On (t1)", "eud12TiImpulseHeader": "Valore di regolazione Tempo di impulso On", "eud12TiImpulseDescription": "Il valore di regolazione in percentuale a cui la lampada viene regolata al tempo di impulso ON.", "eud12TiOffTitle": "Tempo di impulso Off (t2)", "eud12TiOffHeader": "Valore di regolazione Tempo di impulso Off", "eud12TiOffDescription": "Il valore di regolazione in percentuale a cui la lampada viene regolata al tempo di impulso OFF.", "generalTextButtonpermanentlight": "Luce permanente a pulsante", "generalDescriptionButtonpermanentlight": "Impostazione della luce continua a pulsante da 0 a 10 ore con incrementi di 0,5 ore. Attivazione premendo il pulsante per più di 1 secondo (1x sfarfallio), disattivazione premendo il pulsante per più di 2 secondi.", "generalTextNobuttonpermanentlight": "No TSP", "generalTextBasicsettings": "Impostazioni di base", "generalTextInputswitch": "Ingresso pulsante locale (A1)", "generalTextOperationmode": "Modalità operativa", "generalTextDimvalue": "Comportamento acceso", "eud12TitleUsememory": "<PERSON><PERSON><PERSON><PERSON><PERSON> il valore della memoria", "eud12DescriptionUsememory": "Il valore di memoria corrisponde all'ultimo valore di regolazione impostato. Se il valore di memoria è disattivato, la dimmerazione è sempre impostata sul valore di accensione.", "generalTextStartup": "Luminosità all'accensione", "generalDescriptionSwitchonvalue": "Il valore di accensione è un valore di luminosità regolabile che garantisce un'accensione sicura.", "generalTitleSwitchontime": "Tempo di accensione", "generalDescriptionSwitchontime": "Al termine del tempo di accensione impostato, la lampada viene dimmerata dal valore di accensione al valore di memoria.", "generalDescriptionStartup": "Alcune lampade a LED richiedono una corrente di spunto maggiore per accendersi in modo affidabile. La lampada viene accesa a questo valore di accensione e poi dimmerata al valore di memoria dopo il tempo di accensione.", "eud12ClockmodeSubtitleProgramze": "Cliccare brevemente su Central On", "eud12ClockmodeSubtitleProgramza": "Breve clic su central off", "eud12ClockmodeSubtitleProgrambuttonon": "Doppio clic sul pulsante universale/pulsante di direzione On", "eud12ClockmodeSubtitleProgrambuttonoff": "Doppio clic sul pulsante universale/pulsante di direzione Off", "eud12FunctionStairlighttimeswitchTitleShort": "TLZ | Timer per l'illuminazione delle scale", "eud12FunctionMinTitleShort": "MIN", "eud12FunctionMmxTitleShort": "MMX", "eud12FunctionTiDescription": "Timer con tempo di accensione e spegnimento regolabile da 0,5 secondi a 9,9 minuti. La luminosità può essere impostata da una luminosità minima a una massima.", "eud12FunctionAutoDescription": "Interruttore dimmer universale con impostazione per rilevatore di movimento, sveglia luminosa e funzione autospegnimento", "eud12FunctionErDescription": "Commutando il relè, è possibile impostare la luminosità da minima a massima.", "eud12FunctionEsvDescription": "Interruttore dimmer universale con impostazione di un ritardo di spegnimento da 1 a 120 minuti. Preavviso di spegnimento al termine del dimmeraggio selezionabile e regolabile da 1 a 3 minuti. Entrambi gli ingressi centrali sono attivi.", "eud12FunctionTlzDescription": "Impostazione della durata della luce del pulsante da 0 a 10 ore con incrementi di 0,5 ore. Attivazione premendo il pulsante per più di 1 secondo (1x sfarfallio), disattivazione premendo il pulsante per più di 2 secondi.", "eud12FunctionMinDescription": "Interruttore dimmer universale, passa alla luminosità minima impostata quando viene applicata la tensione di controllo. La luce viene dimmerata fino alla massima luminosità entro il tempo di dimmerazione impostato, compreso tra 1 e 120 minuti. Quando si toglie la tensione di controllo, la luce si spegne immediatamente, anche durante il tempo di regolazione. Entrambi gli ingressi centrali sono attivi.", "eud12FunctionMmxDescription": "Interruttore dimmer universale, passa alla luminosità minima impostata quando viene applicata la tensione di controllo. Durante il tempo di regolazione impostato, da 1 a 120 minuti, la luce viene regolata alla massima luminosità. Tuttavia, quando la tensione di controllo viene rimossa, il dimmer scende alla luminosità minima impostata. Quindi si spegne. Entrambi gli ingressi centrali sono attivi.", "motionsensorSubtitleNoremainingbrightness": "Con il rilevatore di movimento impostato su Off", "motionsensorSubtitleAlwaysremainingbrightness": "Con il rilevatore di movimento impostato su Off", "motionsensorSubtitleRemainingbrightnesswithprogram": "Programma di commutazione attivato e disattivato con BWM spento", "motionsensorSubtitleRemainingbrightnesswithprogramandzea": "Central On attiva il sensore di movimento, Central Off disattiva il sensore di movimento, così come il programma di commutazione.", "motionsensorSubtitleNoremainingbrightnessauto": "Il rilevatore di movimento si spegne solo", "detailsDimsectionHeader": "Dimmerazione", "generalTextFast": "Veloce", "generalTextSlow": "Lentamente", "eud12TextDimspeed": "Velocità di oscuramento", "eud12TextSwitchonspeed": "Velocità di accensione", "eud12TextSwitchoffspeed": "Velocità di spegnimento", "eud12DescriptionDimspeed": "La velocità di regolazione è la velocità con cui il dimmer passa dalla luminosità attuale alla luminosità desiderata.", "eud12DescriptionSwitchonspeed": "La velocità di accensione è la velocità che il dimmer richiede per accendersi completamente.", "eud12DescriptionSwitchoffspeed": "La velocità di spegnimento è la velocità necessaria al dimmer per spegnersi completamente.", "settingsFactoryresetResetdimHeader": "Ripristino delle impostazioni di regolazione della luminosità", "settingsFactoryresetResetdimDescription": "Tutte le impostazioni di regolazione della luminosità devono essere ripristinate?", "settingsFactoryresetResetdimConfirmationDescription": "Le impostazioni di regolazione della luminosità sono state ripristinate con successo", "eud12TextSwitchonoffspeed": "Velocità di accensione/spegnimento", "eud12DescriptionSwitchonoffspeed": "La velocità di accensione/spegnimento è la velocità che il dimmer richiede per accendersi o spegnersi completamente.", "timerDetailsDimtoval": "Acceso con valore di regolazione in %", "timerDetailsDimtovalDescription": "Il dimmer si accende sempre con il valore di regolazione fisso in %.", "timerDetailsDimtovalSubtitle": "Accendere con {brightness}%", "timerDetailsDimtomem": "Acceso con valore di memoria", "timerDetailsDimtomemSubtitle": "Accensione con valore di memoria", "timerDetailsMotionsensorwithremainingbrightness": "Luminosità residua (BWM) On", "timerDetailsMotionsensornoremainingbrightness": "Luminosità residua (BWM) Off", "settingsRandommodeHint": "Con la modalità casuale attivata, tutti i programmi di questo canale vengono sfalsati in modo casuale fino a 15 minuti. I programmi on-timers sono sfalsati in anticipo, quelli off-timers sono ritardati.", "runtimeOffsetDescription": "Tempo addizionale, dopo la scadenza del tempo impostato", "loadingTextDimvalue": "Il valore di regolazione è caricato", "discoveryEudipmDescription": "Dimmer universale IP Matter", "generalTextOffset": "Superamento", "eud12DimvalueTestText": "Invia luminosità", "eud12DimvalueTestDescription": "Durante il test si tiene conto della velocità di oscuramento attualmente impostata.", "eud12DimvalueLoadText": "Livello di luminosità", "settingsDatetimeNotime": "Le impostazioni di data e ora devono essere lette sul display del dispositivo.", "generalMatterText": "Matter", "generalMatterMessage": "Configurare il dispositivo Matter utilizzando l'app Google Home, Amazon Alexa o Samsung SmartThings.", "generalMatterOpengooglehome": "Aprire Google Home", "generalMatterOpenamazonalexa": "Aprire Amazon Alexa", "generalMatterOpensmartthings": "Aprire SmartThings", "generalLabelProgram": "Programma {number}", "generalTextDone": "<PERSON><PERSON>", "settingsRandommodeDescriptionShort": "Con la modalità casuale attivata, tutti i programmi di questo canale vengono sfalsati in modo casuale fino a 15 minuti. I programmi on-timers sono sfalsati in anticipo, quelli off-timers sono ritardati.", "all": "<PERSON><PERSON>", "discoveryBluetooth": "Bluetooth", "success": "Successo", "error": "Errore", "timeProgramAdd": "Aggiungi programma orario", "noConnection": "Nessuna connessione", "timeProgramOnlyActive": "<PERSON>mi configurati", "timeProgramAll": "<PERSON><PERSON> i <PERSON>mi", "active": "Attivo", "inactive": "Inattivo", "timeProgramSaved": "Tempo di salvataggio del programma {number}", "deviceLanguageSaved": "Lingua del dispositivo salvata", "generalTextTimeShort": "orologio {time}", "programDeleteHint": "Il programma {index} deve essere davvero cancellato?", "milliseconds": "{count, plural, one {Millisecondo} other {Millisecondi}}", "millisecondsWithValue": "{count, plural, one {{count} Millisecond} other {{count} Milliseconds}}", "secondsWithValue": "{count, plural, one {{count} Second} other {{count} Seconds}}", "minutesWithValue": "{count, plural, one {{count} Minute} other {{count} Minutes}}", "hoursWithValue": "{count, plural, one {{count} Hour} other {{count} Hours}}", "settingsPinFailEmpty": "Il PIN non deve essere vuoto", "detailsConfigurationWifiloginScanNoMatch": "Il codice scansionato non corrisponde al dispositivo", "wifiAuthorizationPopIsEmpty": "Il PoP non può essere vuoto", "wifiAuthenticationCredentialsHint": "Poiché l'applicazione non può accedere alla password Wi-Fi privata, non è possibile verificare la correttezza dell'inserimento. Se non viene stabilita alcuna connessione, controllare la password e inserirla nuovamente.", "generalMatterOpenApplehome": "Aprire casa Apple", "timeProgramNoActive": "Nessun programma configurato", "timeProgramNoEmpty": "Non è disponibile un programma per il tempo libero", "nameOfConfiguration": "Nome della configurazione", "currentDevice": "Dispositivo attuale", "export": "Esportazione", "import": "Importazione", "savedConfigurations": "Configurazioni salvate", "importableServicesLabel": "È possibile importare le seguenti impostazioni:", "notImportableServicesLabel": "Impostazioni incompatibili", "deviceCategoryMeterGateway": "Gateway del contatore", "deviceCategory2ChannelTimeSwitch": "Interruttore orario a 2 canali", "devicategoryOutdoorTimeSwitchBluetooth": "Interruttore orario esterno Bluetooth", "settingsModbusHeader": "Modbus", "settingsModbusDescription": "Regolare il baud rate, la parità e il timeout per configurare la velocità di trasmissione, il rilevamento degli errori e il tempo di attesa.", "settingsModbusRTU": "Modbus RTU", "settingsModbusBaudrate": "Baudrate", "settingsModbusParity": "Parità", "settingsModbusTimeout": "Timeout Modbus", "locationServiceDisabled": "La posizione è disabilitata", "locationPermissionDenied": "Consentite alla localizzazione di richiedere la vostra posizione attuale.", "locationPermissionDeniedPermanently": "I permessi di localizzazione sono permanentemente negati, si prega di consentire il permesso di localizzazione nelle impostazioni del dispositivo per richiedere la posizione corrente.", "lastSync": "Ultima sincronizzazione", "dhcpActive": "DHCP attivo", "ipAddress": "IP", "subnetMask": "Maschera di sottorete", "standardGateway": "Gateway predefinito", "dns": "DNS", "alternateDNS": "DNS alternativo", "errorNoNetworksFound": "Nessuna rete wifi trovata", "availableNetworks": "Reti disponibili", "enableWifiInterface": "Abilitare l'interfaccia WiFi", "enableLANInterface": "Abilitazione dell'interfaccia LAN", "hintDontDisableAllInterfaces": "Assicurarsi che non tutte le interfacce siano disattivate. L'ultima interfaccia attivata ha la priorità.", "ssid": "SSID", "searchNetworks": "Reti di ricerca", "errorNoNetworkEnabled": "Almeno una stazione deve essere attiva", "errorActiveNetworkInvalid": "Non tutte le stazioni attive sono valide", "invalidNetworkConfiguration": "Configurazione di rete non valida", "generalDefault": "Predefinito", "mqttHeader": "MQTT", "mqttConnected": "Collegato al broker MQTT", "mqttDisconnected": "Nessuna connessione al broker MQTT", "mqttBrokerURI": "URI del broker", "mqttBrokerURIHint": "URI del broker MQTT", "mqttPort": "Porto", "mqttPortHint": "Porta MQTT", "mqttClientId": "ID cliente", "mqttClientIdHint": "ID cliente MQTT", "mqttUsername": "Nome utente", "mqttUsernameHint": "Nome utente MQTT", "mqttPassword": "Password", "mqttPasswordHint": "Password MQTT", "mqttCertificate": "Certificato", "mqttCertificateHint": "Certificato MQTT", "mqttTopic": "Argomento", "mqttTopicHint": "Argomento MQTT", "electricityMeter": "Contatore elettrico", "electricityMeterCurrent": "Attuale", "electricityMeterHistory": "La storia", "electricityMeterReading": "Lettura del contatore", "connectivity": "Connettività", "electricMeter": "{count, plural, one  {contatore elettrico} other {contatori elettrici}}", "discoveryZGW16Description": "Contatori di energia Modbus-MQTT-Gateway", "bluetoothConnectionLost": "Perdita della connessione Bluetooth", "bluetoothConnectionLostDescription": "La connessione Bluetooth al dispositivo è stata interrotta. Controllare la connessione al dispositivo.", "openBluetoothSettings": "Aprire le impostazioni Bluetooth", "password": "Password", "setInitialPassword": "Impostazione della password iniziale", "initialPasswordMinimumLength": "La password deve essere lunga almeno {length} caratteri", "repeatPassword": "Ripetere la password", "passwordsDoNotMatch": "Le password non corrispondono", "savePassword": "<PERSON><PERSON> la <PERSON>", "savePasswordHint": "La password viene salvata per le connessioni future sul dispositivo.", "retrieveNtpServer": "Recuperare l'ora dal server NTP", "retrieveNtpServerFailed": "Non è stato possibile stabilire la connessione al server NTP.", "retrieveNtpServerSuccess": "La connessione al server NTP è riuscita.", "settingsPasswordNewPasswordDescription": "Inserire la nuova password", "settingsPasswordConfirmationDescription": "Modifica della password riuscita", "dhcpRangeStart": "Inizio intervallo DHCP", "dhcpRangeEnd": "Fine intervallo DHCP", "forwardOnMQTT": "Inoltro a MQTT", "showAll": "<PERSON><PERSON> tutti", "hide": "Nascondere", "changeToAPMode": "Passare alla modalità AP", "changeToAPModeDescription": "Si sta per collegare il dispositivo a una rete WiFi; in questo caso la connessione al dispositivo viene interrotta e occorre ricollegarsi al dispositivo tramite la rete configurata.", "consumption": "Consu<PERSON>", "currentDay": "<PERSON><PERSON><PERSON>", "twoWeeks": "2 settimane", "oneYear": "1 anno", "threeYears": "3 anni", "passwordMinLength": "Password needs at least {length} characters.", "passwordNeedsLetter": "La password deve contenere una lettera.", "passwordNeedsNumber": "La password deve contenere un numero.", "portEmpty": "La porta non può essere vuota", "portInvalid": "Porta non valida", "portOutOfRange": "La porta deve essere compresa tra {rangeStart} e {rangeEnd}.", "ipAddressEmpty": "L'indirizzo IP non può essere vuoto", "ipAddressInvalid": "Indirizzo IP non valido", "subnetMaskEmpty": "La maschera di sottorete non può essere vuota", "subnetMaskInvalid": "Maschera di sottorete non valida", "gatewayEmpty": "Il gateway non può essere vuoto", "gatewayInvalid": "Gateway non valido", "dnsEmpty": "Il DNS non può essere vuoto", "dnsInvalid": "DNS non valido", "uriEmpty": "L'URI non può essere vuoto", "uriInvalid": "URI non valido", "electricityMeterChangedSuccessfully": "Cambio del contatore elettrico r<PERSON>to", "networkChangedSuccessfully": "La configurazione di rete è stata modificata con successo", "mqttChangedSuccessfully": "La configurazione di MQTT è stata modificata con successo", "modbusChangedSuccessfully": "Le impostazioni Modbus sono state modificate con successo", "loginData": "Cancellare i dati di accesso", "valueConfigured": "Configurato", "electricityMeterHistoryNoData": "<PERSON><PERSON><PERSON> dato disponibile", "locationChangedSuccessfully": "La posizione è stata modificata con successo", "settingsNameFailEmpty": "Il nome non può essere vuoto", "settingsNameFailLength": "Name must not be longer than {length} characters", "solsticeChangedSuccesfully": "Le impostazioni del Solstizio sono state modificate con successo", "relayFunctionChangedSuccesfully": "Relè-Funzione modificata con successo", "relayFunctionHeader": "Funzione relè", "dimmerValueChangedSuccesfully": "Il comportamento all'accensione è stato modificato con successo", "dimmerBehaviourChangedSuccesfully": "Il comportamento di oscuramento è stato modificato con successo", "dimmerBrightnessDescription": "La luminosità minima e massima riguarda tutte le luminosità regolabili del dimmer.", "dimmerSettingsChangedSuccesfully": "Le impostazioni di base sono state modificate con successo", "liveUpdateEnabled": "Test dal vivo abilitato", "liveUpdateDisabled": "Test dal vivo disattivato", "liveUpdateDescription": "Il valore dell'ultimo cursore modificato verrà inviato al dispositivo.", "demoDevices": "Dispositivi demo", "showDemoDevices": "Mostra i dispositivi demo", "deviceCategoryTimeSwitch": "Interruttore orario", "deviceCategoryMultifunctionalRelay": "Relè multifunzione", "deviceCategoryDimmer": "<PERSON><PERSON>", "deviceCategoryShutter": "Attuatore per tapparelle e ombreggianti\n", "deviceCategoryRelay": "<PERSON><PERSON><PERSON>", "search": "Ricerca", "configurationsHeader": "Configurazioni", "configurationsDescription": "Gestite le vostre configurazioni qui.", "configurationsNameFailEmpty": "Il nome della configurazione non può essere vuoto", "configurationDeleted": "Configurazione eliminata", "codeFound": "Codice {codeType} trovato", "errorCameraPermission": "Consentire alla telecamera di eseguire la scansione del codice ELTAKO.", "authorizationSuccessful": "Autorizzato con successo sul dispositivo", "wifiAuthenticationResetConfirmationDescription": "Il dispositivo è ora pronto per una nuova autorizzazione.", "settingsResetConnectionHeader": "Ripristino della connessione", "settingsResetConnectionDescription": "Vuoi davvero ripristinare la connessione?", "settingsResetConnectionConfirmationDescription": "La connessione è stata ripristinata con successo.", "wiredInputChangedSuccesfully": "Il comportamento dell'interruttore è stato modificato con successo", "runtimeChangedSuccesfully": "Il comportamento del runtime è stato modificato con successo", "expertModeActivated": "Modalità esperto attivata", "expertModeDeactivated": "Modalità esperto disattivata", "license": "Licenza", "retry": "<PERSON><PERSON><PERSON><PERSON>", "provisioningConnectingHint": "È in corso la connessione del dispositivo. L'operazione può richiedere fino a 1 minuto.", "serialnumberEmpty": "Il numero di serie non può essere vuoto", "interfaceStateInactiveDescriptionBLE": "Il Bluetooth è disattivato; attivarlo per scoprire i dispositivi Bluetooth.", "interfaceStateDeniedDescriptionBLE": "Le autorizzazioni Bluetooth non sono state concesse.", "interfaceStatePermanentDeniedDescriptionBLE": "Le autorizzazioni Bluetooth non sono state concesse. Abilitarli nelle impostazioni del dispositivo.", "requestPermission": "Richiesta di autorizzazione", "goToSettings": "Vai alle impostazioni", "enableBluetooth": "Abilitare il bluetooth", "installed": "Installato", "teachInDialogDescription": "Would you like to teach in your device with {type}?", "useMatter": "Utilizzare la materia", "relayMode": "Attivare la modalità relè", "whatsNew": "Novità di questa versione", "migrationHint": "Per utilizzare le nuove funzionalità è necessaria una migrazione.", "migrationHeader": "Migrazione", "migrationProgress": "Migrazione in corso...", "letsGo": "Andiamo!", "noDevicesFound": "Nessun dispositivo trovato", "interfaceStateEmpty": "Non sono stati trovati dispositivi", "ssidEmpty": "L'SSID non può essere vuoto", "passwordEmpty": "La password non può essere vuota", "settingsDeleteSettingsHeader": "Ripristino delle impostazioni", "settingsDeleteSettingsDescription": "Volete davvero ripristinare tutte le impostazioni?", "settingsDeleteSettingsConfirmationDescription": "Tutte le impostazioni sono state ripristinate con successo.", "locationNotFound": "Posizione non trovata", "timerProgramEmptySaveHint": "Il programma orario è vuoto. Si desidera annullare la modifica?", "timerProgramDaysEmptySaveHint": "Non è stato selezionato alcun giorno. Volete comunque salvare il programma orario?", "timeProgramNoDays": "Deve essere attivato almeno un giorno", "timeProgramColliding": "Il programma orario si scontra con il programma {program}", "timeProgramDuplicated": "Il programma orario è un duplicato del programma {program}", "screenshotZgw16": "Casa indipendente", "interfaceStateUnknown": "Non sono stati trovati dispositivi", "settingsPinChange": "Modifica del PIN", "timeProgrammOneTime": "una tantum", "timeProgrammRepeating": "ripetizione", "generalIgnore": "<PERSON><PERSON><PERSON><PERSON>", "timeProgramChooseDay": "Scegliere il giorno", "generalToday": "<PERSON><PERSON><PERSON>", "generalTomorrow": "<PERSON><PERSON>", "bluetoothAndPINChangedSuccessfully": "Bluetooth e PIN modificati con successo", "generalTextDimTime": "Tempo di oscuramento", "discoverySu62Description": "Interruttore orario a 1 canale Bluetooth", "bluetoothAlwaysOnTitle": "Sempre acceso", "bluetoothAlwaysOnDescription": "Il Bluetooth è permanentemente attivato.", "bluetoothAlwaysOnHint": "Nota: se questa impostazione è attivata, il dispositivo è permanentemente visibile a tutti tramite Bluetooth! Si consiglia di modificare il PIN predefinito.", "bluetoothManualStartupOnTitle": "Temporaneo su", "bluetoothManualStartupOnDescription": "Dopo l'alimentazione, il Bluetooth si attiva per 3 minuti.", "bluetoothManualStartupOnHint": "Nota: lo standby di accoppiamento si attiva per 3 minuti e poi si spegne. Per stabilire una nuova connessione, è necessario tenere premuto il pulsante per circa 5 secondi.", "bluetoothManualStartupOffTitle": "Avvio manuale", "bluetoothManualStartupOffDescription": "Il Bluetooth viene attivato manualmente tramite l'ingresso a pulsante.", "bluetoothManualStartupOffHint": "Nota: per attivare il Bluetooth, è necessario tenere premuto il pulsante dell'input per circa 5 secondi.", "timeProgrammOneTimeRepeatingDescription": "I programmi possono essere eseguiti ripetutamente, eseguendo sempre un'operazione di commutazione nei giorni e negli orari configurati, oppure possono essere eseguiti una sola volta all'ora di commutazione configurata.", "versionHeader": "Versione {version}", "releaseNotesHeader": "Note di rilascio", "release30Header": "È arrivata la nuova app Eltako Connect!", "release30FeatureDesignHeader": "Nuovo design", "release30FeatureDesignDescription": "L'app è stata completamente rivista e ha un nuovo design. Ora è ancora più facile e intuitiva da usare.", "release30FeaturePerformanceHeader": "Prestazioni migliorate", "release30FeaturePerformanceDescription": "<PERSON><PERSON>vi un'esperienza più fluida e tempi di caricamento ridotti, per un'esperienza utente senza problemi.", "release30FeatureConfigurationHeader": "Configurazioni cross-device", "release30FeatureConfigurationDescription": "Salvare le configurazioni dei dispositivi e trasferirle ad altri dispositivi. Anche se non hanno lo stesso hardware, è possibile, ad esempio, trasferire la configurazione del dispositivo S2U12DBT1+1-UC a un ASSU-BT o viceversa.", "release31Header": "È arrivato il nuovo interruttore orario a 1 canale da incasso con Bluetooth!", "release31Description": "Cosa può fare il SU62PF-BT/UC?", "release31SU62Name": "SU62PF-BT/UC", "release31DeviceNote1": "Fino a 60 programmi orari.", "release31DeviceNote2": "Funzione Astro: L'orologio commuta i dispositivi in base all'alba e al tramonto.", "release31DeviceNote3": "Modalità casuale: gli orari di commutazione possono essere spostati in modo casuale fino a 15 minuti.", "release31DeviceNote4": "Cambio dell'ora legale/invernale: L'orologio passa automaticamente all'ora legale o a quella invernale.", "release31DeviceNote5": "Tensione di alimentazione e controllo universale 12-230V UC.", "release31DeviceNote6": "Ingresso a pulsante per la commutazione manuale.", "release31DeviceNote7": "1 contatto NA a potenziale zero 10 A/250 V CA.", "release31DeviceNote8": "Esecuzione una tantum di programmi a tempo.", "generalNew": "Novità", "yearsAgo": "{count, plural, one {ultimo anno} other {{count} anni fa}}.", "monthsAgo": "{count, plural, one {ultimo mese} other {{count} mesi fa}}", "weeksAgo": "{count, plural, one {ultima settimana} other {{count} settimane fa}}.", "daysAgo": "{count, plural, one {<PERSON><PERSON>} other {{count} giorni fa}}.", "minutesAgo": "{count, plural, one {Un minuto fa} other {{count} minuti fa}}.", "hoursAgo": "{count, plural, one {un'ora fa} other {{count} ore fa}}.", "secondsAgo": "{count, plural, one {Un secondo fa} other {{count} secondi fa}}.", "justNow": "Solo ora", "discoveryEsripmDescription": "Relè di commutazione a impulsi IP Matter", "generalTextKidsRoom": "Funzione luce notturna", "generalDescriptionKidsRoom": "Quando si accende con un'azione prolungata del pulsante ({mode}), la luce si accende al livello di luminosità più basso dopo circa 1 secondo e si attenua lentamente finché si tiene premuto il pulsante, senza modificare l'ultimo livello di luminosità salvato.", "generalTextSceneButton": "Pulsante di scena", "settingsEnOceanConfigHeader": "Configurazione EnOcean", "enOceanConfigChangedSuccessfully": "La configurazione EnOcean è stata modificata con successo", "activateEnOceanRepeater": "Attivare il ripetitore EnOcean", "enOceanRepeaterLevel": "Livello del ripetitore", "enOceanRepeaterLevel1": "Livello 1", "enOceanRepeaterLevel2": "Livello 2", "enOceanRepeaterOffDescription": "I sensori non ricevono segnali wireless.", "enOceanRepeaterLevel1Description": "Solo i segnali wireless provenienti dai sensori vengono ricevuti, controllati e inoltrati alla massima potenza di trasmissione. I segnali wireless provenienti da altri ripetitori vengono ignorati per ridurre la quantità di dati.", "enOceanRepeaterLevel2Description": "Oltre ai segnali wireless dei sensori, vengono elaborati anche i segnali wireless dei ripetitori a livello 1. Un segnale wireless può quindi essere ricevuto e amplificato al massimo due volte. I ripetitori wireless non hanno bisogno di essere appresi. Ricevono e amplificano i segnali wireless di tutti i sensori wireless nella loro area di ricezione.", "settingsSensorHeader": "<PERSON><PERSON><PERSON>", "sensorChangedSuccessfully": "I sensori sono stati sostituiti con successo", "wiredButton": "Pulsante cablato", "enOceanId": "ID EnOcean", "enOceanAddManually": "Immettere o scansionare l'ID EnOcean", "enOceanIdInvalid": "ID EnOcean non valido", "enOceanAddAutomatically": "Teach-in con Telegramma EnOcean", "enOceanAddDescription": "Il protocollo wireless EnOcean consente di accoppiare i pulsanti con l'attuatore.\n\nScegliete l'autoapprendimento automatico con il Telegramma EnOcean per apprendere i pulsanti premendo un solo tasto o selezionate l'opzione manuale per scansionare o digitare l'ID EnOcean del vostro pulsante.", "enOceanTelegram": "Telegramma", "enOceanCodeScan": "Inserire l'ID EnOcean del {sensorType} o scansionare il codice EnOcean-QR del {sensorType} per aggiungerlo.", "enOceanCode": "Codice QR EnOcean", "enOceanCodeScanDescription": "Cercare il codice EnOcean sul {sensorType} e scansionarlo con la fotocamera.", "enOceanButton": "Pulsante EnOcean", "enOceanBackpack": "<PERSON><PERSON><PERSON>", "sensorNotAvailable": "Nessun sensore è stato ancora accoppiato", "sensorAdd": "Aggiungere sensori", "sensorCancel": "<PERSON><PERSON><PERSON> il teach-in", "sensorCancelDescription": "Volete davvero cancellare il processo di insegnamento?", "getEnOceanBackpack": "Ordinate il vostro adattatore EnOcean", "enOceanBackpackMissing": "Per entrare nel fantastico mondo della connettività e della comunicazione perfetta, è necessario un adattatore EnOcean.\nFare clic qui per ulteriori informazioni", "sensorEditChangedSuccessfully": "{sensorName} modificato con successo", "sensorConnectedVia": "connesso tramite {deviceName}", "lastSeen": "<PERSON>lt<PERSON> visto", "setButtonOrientation": "Impostare l'orientamento", "setButtonType": "Impostare il tipo di pulsante", "button1Way": "Pulsante a 1 via", "button2Way": "Pulsante a 2 vie", "button4Way": "Pulsante a 4 vie", "buttonUnset": "non impostato", "button": "Pulsante", "sensor": "Sensore", "sensorsFound": "{count, plural, =0 {nessun sensore trovato} one {1 sensore trovato} other {{count} sensori trovati}}.", "sensorSearch": "Ricerca di sensori", "searchAgain": "Cerca di nuovo", "sensorTeachInHeader": "Insegna in {sensorType}", "sensorChooseHeader": "Scegliere {sensorType}", "sensorChooseDescription": "Scegliere un pulsante per l'insegnamento", "sensorCategoryDescription": "Selezionare la categoria del sensore che si desidera aggiungere.", "sensorName": "Nome del pulsante", "sensorNameFooter": "Assegnate un nome al vostro pulsante", "sensorAddedSuccessfully": "{sensorName} è stato aggiunto con successo", "sensorDelete": "Cancellare {sensorType}", "sensorDeleteHint": "Si vuole veramente cancellare il {sensorType} {sensorName}?", "sensorDeletedSuccessfully": "{sensorName} è stato cancellato con successo.", "buttonTapDescription": "Toccare il pulsante che si desidera aggiungere.", "waitingForTelegram": "L'attuatore attende il telegramma", "copied": "Copiato", "pairingFailed": "{sensorType} già accoppiato", "generalDescriptionUniversalbutton": "Con il pulsante universale, la direzione viene invertita rilasciando brevemente il pulsante. I comandi brevi accendono o spengono il sistema.", "generalDescriptionDirectionalbutton": "Il pulsante di direzione è \"accendi e oscura\" in alto e \"spegni e oscura\" in basso.", "matterForwardingDescription": "Inoltro dei telegrammi Matter", "none": "<PERSON><PERSON><PERSON>", "buttonNoneDescription": "Il pulsante non ha alcuna funzionalità", "buttonUnsetDescription": "Il pulsante non ha un comportamento impostato", "sensorButtonTypeChangedSuccessfully": "Il tipo di pulsante è stato modificato con successo", "forExample": "ad esempio {example}}", "enOceanQRCodeInvalidDescription": "Possibile solo a partire dalla data di produzione 44/20", "input": "Ingresso", "buttonSceneValueOverride": "Sovrascrivere il valore del pulsante di scena", "buttonSceneValueOverrideDescription": "Il valore del pulsante di scena verrà sovrascritto con il valore attuale della luminosità attraverso una pressione prolungata del pulsante", "buttonSceneDescription": "Il pulsante di scena si accende a un valore di luminosità specifico.", "buttonPress": "Pulsante premuto", "triggerOn": "Pulsante universale o pulsante di direzione premuto sul lato di accensione", "triggerOff": "Pulsante universale o pulsante di direzione premuto sul lato di spegnimento", "centralOn": "Centrale On", "centralOff": "Centrale Off", "centralButton": "Pulsante centrale", "enOceanAdapterNotFound": "<PERSON><PERSON><PERSON> adattatore EnOcean trovato", "updateRequired": "Aggiornamento richiesto", "updateRequiredDescription": "La vostra applicazione richiede un aggiornamento per supportare questo nuovo dispositivo.", "release32Header": "Il nuovo BR64 con Matter ed EnOcean e il nuovo interruttore orario da incasso Bluetooth SU62PF-BT/UC sono ora disponibili!", "release32EUD64Header": "È arrivato il nuovo dimmer a 1 canale da incasso con Matter over Wi-Fi e fino a 300W!", "release32EUD64Note1": "Configurazione della velocità di oscuramento, della velocità di accensione/spegnimento, della modalità luce notturna/autospegnimento e molto altro ancora.", "release32EUD64Note2": "Le funzionalità dell'EUD64NPN-IPM possono essere ampliate tramite adattatori, come l'adattatore EnOcean EOA64.", "release32EUD64Note3": "Fino a 30 interruttori wireless EnOcean possono essere collegati direttamente all'EUD64NPN-IPM in combinazione con l'adattatore EnOcean EOA64 e inoltrati a Matter.", "release32EUD64Note4": "Due ingressi a pulsante cablati possono essere collegati direttamente all'EUD64NPN-IPM o inoltrati a Matter.", "release32ESR64Header": "È arrivato il nuovo attuatore da incasso a 1 canale a potenziale zero con Matter over Wi-Fi e fino a 16A!", "release32ESR64Note1": "Configurazione di varie funzioni come interruttore a impulsi (ES), funzione relè (ER), normalmente chiusa (ER-Inversa) e molto altro ancora.", "release32ESR64Note2": "Le funzionalità dell'ESR64PF-IPM possono essere ampliate tramite adattatori, come l'adattatore EnOcean EOA64.", "release32ESR64Note3": "Fino a 30 interruttori wireless EnOcean possono essere collegati direttamente all'ESR64PF-IPM in combinazione con l'adattatore EnOcean EOA64 e inoltrati a Matter.", "release32ESR64Note4": "Un ingresso a pulsante cablato può essere collegato direttamente al ESR64PF-IPM o inoltrato a Matter.", "buttonsFound": "{count, plural, =0 {<PERSON>essun pulsante trovato} one {1 pulsante trovato} other {{count} pulsanti trovati}}", "doubleImpuls": "con un doppio impulso", "impulseDescription": "Se il canale è acceso, viene spento da un impulso.", "locationServiceEnable": "Attivare la posizione", "locationServiceDisabledDescription": "La posizione è disattivata. La versione del sistema operativo ha bisogno della posizione per poter trovare i dispositivi Bluetooth.", "locationPermissionDeniedNoPosition": "Le autorizzazioni di localizzazione non sono state concesse. La versione del sistema operativo in uso richiede i permessi di localizzazione per poter trovare i dispositivi Bluetooth. Consentire l'autorizzazione alla localizzazione nelle impostazioni del dispositivo.", "interfaceStatePermanentDeniedDescriptionDevicesAround": "L'autorizzazione per i dispositivi vicini non è stata concessa. Attivare l'autorizzazione nelle impostazioni del dispositivo.", "permissionNearbyDevices": "Dispositivi vicini", "release320Header": "È arrivato il nuovo e più potente dimmer universale EUD12NPN-BT/600W-230V!", "release320EUD600Header": "Cosa può fare il nuovo dimmer universale?", "release320EUD600Note1": "Dimmer universale con potenza fino a 600W", "release320EUD600Note2": "Espandibile con l'estensione di potenza LUD12 fino a 3800W", "release320EUD600Note3": "Funzionamento locale con pulsante universale o direzionale", "release320EUD600Note4": "Funzioni centrali On / Off", "release320EUD600Note5": "Ingresso per rilevatore di movimento per una maggiore comodità", "release320EUD600Note6": "Timer integrato con 10 programmi di commutazione", "release320EUD600Note7": "Funzione astronomica", "release320EUD600Note8": "Luminosità di accensione individuale", "mqttClientCertificate": "Certificato del cliente", "mqttClientCertificateHint": "Certificato cliente MQTT", "mqttClientKey": "Chiave cliente", "mqttClientKeyHint": "Chiave cliente MQTT", "mqttClientPassword": "Password Cliente", "mqttClientPasswordHint": "Password cliente MQTT", "mqttEnableHomeAssistantDiscovery": "Attivare il rilevamento MQTT di HomeAssistant", "modbusTcp": "Modbus TCP", "enableInterface": "Attivare l'interfaccia", "busAddress": "Indirizzo Bus", "busAddressWithAddress": "Indirizzo Bus indice", "deviceType": "Tipo di dispositivo", "registerTable": "{count, plural, one {<PERSON><PERSON> dei registri} other {<PERSON><PERSON> dei registri}}", "currentValues": "Valori attuali", "requestRTU": "Interrogazione RTU", "requestPriority": "Priorità della query", "mqttForwarding": "Inoltro MQTT", "historicData": "<PERSON><PERSON> storici", "dataFormat": "Formato dei dati", "dataType": "Tipo di dati", "description": "Descrizione", "readWrite": "Lettura/Scrittura", "unit": "Unità", "registerTableReset": "Tabella dei registri di reset", "registerTableResetDescription": "La tabella dei registri deve essere davvero azzerata?", "notConfigured": "Non configurato", "release330ZGW16Header": "Aggiornamento importante per il modello ZGW16WL-IP", "release330Header": "ZGW16WL-IP con un massimo di 16 contatori elettrici", "release330ZGW16Note1": "Supporta fino a 16 contatori elettrici ELTAKO Modbus", "release330ZGW16Note2": "Supporto Modbus TCP", "release330ZGW16Note3": "Supporto MQTT Discovery", "screenshotButtonLivingRoom": "Pulsante del soggiorno", "registerChangedSuccessfully": "Registro modificato con successo", "serverCertificateEmpty": "Il certificato del server non può essere vuoto", "registerTemplates": "Modelli di registro", "registerTemplateChangedSuccessfully": "Il modello di registro è stato modificato con successo", "registerTemplateReset": "Azzeramento del modello di registro", "registerTemplateResetDescription": "Il modello di registro deve essere davvero azzerato?", "registerTemplateNotAvailable": "Nessun modello di registro disponibile", "rename": "Rinominare", "registerName": "Nome del registro", "registerRenameDescription": "Inserire un nome personalizzato per il registro", "restart": "Riavviare il dispositivo", "restartDescription": "Volete davvero riavviare il dispositivo?", "restartConfirmationDescription": "Il dispositivo si sta riavviando", "deleteAllElectricityMeters": "Cancellare tutti i contatori elettrici", "deleteAllElectricityMetersDescription": "Volete davvero cancellare tutti i contatori elettrici?", "deleteAllElectricityMetersConfirmationDescription": "Tutti i contatori elettrici sono stati cancellati con successo", "resetAllElectricityMeters": "Azzeramento di tutte le configurazioni dei contatori elettrici", "resetAllElectricityMetersDescription": "Volete davvero resettare tutte le configurazioni dei contatori elettrici?", "resetAllElectricityMetersConfirmationDescription": "Tutte le configurazioni dei contatori elettrici sono state ripristinate con successo.", "deleteElectricityMeterHistories": "Cancellare tutte le cronologie dei contatori elettrici", "deleteElectricityMeterHistoriesDescription": "Volete davvero cancellare tutte le cronologie dei contatori elettrici?", "deleteElectricityMeterHistoriesConfirmationDescription": "Tutte le cronologie dei contatori elettrici sono state cancellate con successo", "multipleElectricityMetersSupportMissing": "Il dispositivo supporta attualmente un solo contatore elettrico. Aggiornare il firmware.", "consumptionWithUnit": "<PERSON><PERSON><PERSON><PERSON> (kWh)", "exportWithUnit": "Consegna (kWh)", "importWithUnit": "Consumo (kWh)", "resourceWarningHeader": "Limiti delle risorse", "mqttAndTcpResourceWarning": "Il funzionamento contemporaneo di MQTT e Modbus TCP non è possibile a causa delle risorse di sistema limitate. Disattivare prima {protocol}.", "mqttEnabled": "MQTT abilitato", "redirectMQTT": "Andare alle Impostazioni MQTT", "redirectModbus": "Andare alle impostazioni Modbus", "unsupportedSettingDescription": "Con la versione attuale del firmware, alcune impostazioni del dispositivo non sono supportate. Aggiornare il firmware per utilizzare le nuove funzioni", "updateNow": "Aggiornare ora", "zgw241Hint": "Con questo aggiornamento, Modbus TCP è abilitato per impostazione predefinita e MQTT è disabilitato. Questo può essere modificato nelle impostazioni. Con il supporto di un massimo di 16 contatori, sono state apportate molte ottimizzazioni; ciò può comportare modifiche alle impostazioni del dispositivo. Si prega di riavviare il dispositivo dopo aver regolato le impostazioni.", "deviceConfigChangedSuccesfully": "Il comportamento del runtime è stato modificato con successo", "deviceConfiguration": "Configurazione del dispositivo", "tiltModeToggle": "Modalità di inclinazione", "tiltModeToggleFooter": "Se il dispositivo è stato configurato in Matter, tutte le funzioni devono essere riconfigurate in quella sede.", "shaderMovementDirection": "Inversione su/giù", "shaderMovementDirectionDescription": "Invertire la direzione del movimento del motore verso l'alto o verso il basso.", "tiltTime": "Tempo di esecuzione dell'inclinazione", "changeTiltModeDialogTitle": "{target, select, true {Enable} false {Disable} other {Change}} funzione di inclinazione", "changeTiltModeDialogConfirmation": "{target, select, true {Enable} false {Disable} other {Change}}", "generalTextSlatSetting": "Impostazione delle lamelle", "generalTextPosition": "Posizione", "generalTextSlatPosition": "Posizione delle lamelle", "slatSettingDescription": "Descrizione dell'impostazione delle lamelle", "scenePositionSliderDescription": "Altezza", "sceneSlatPositionSliderDescription": "Inclinazione", "referenceRun": "Esecuzione della calibrazione", "slatAutoSettingHint": "In questa modalità, la posizione delle tende non ha importanza prima che le lamelle si regolino nella posizione di inclinazione desiderata.", "slatReversalSettingHint": "In questa modalità, le tende si chiudono completamente prima che le lamelle si regolino nella posizione di inclinazione desiderata.", "release340Header": "È arrivato il nuovo attuatore per ombreggiatura ad incasso ESB64NP-IPM!", "release340ESB64Header": "Di cosa è capace l'ESB64NP-IPM?", "release340ESB64Note1": "Il nostro attuatore per ombreggiature certificato Matter Gateway con funzione lamellare opzionale", "release340ESB64Note2": "Due ingressi a pulsante cablati per la commutazione manuale e l'inoltro a Matter", "release340ESB64Note3": "Espandibile con l'adattatore EnOcean (EOA64). Ad esempio con il pulsante EnOcean wireless F4T55", "release340ESB64Note4": "Aperto alle integrazioni grazie all'API REST basata sullo standard OpenAPI", "activateTiltModeDialogText": "Se si attiva la funzione di inclinazione, tutte le impostazioni andranno perse. Siete sicuri di voler abilitare la funzione di inclinazione?", "deactivateTiltModeDialogText": "Se la funzione di inclinazione viene disattivata, tutte le impostazioni andranno perse. Siete sicuri di voler disabilitare la funzione di inclinazione?"}