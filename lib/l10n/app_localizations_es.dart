// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appName => 'ELTAKO Connect';

  @override
  String get discoveryHint =>
      'Activa el Bluetooth en el dispositivo para conectarlo';

  @override
  String devicesFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count dispositivos encontrados',
      one: '1 dispositivo encontrado',
      zero: 'No se encontraron dispositivos',
    );
    return '$_temp0';
  }

  @override
  String discoveryDemodeviceName(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Dispositivos Demo',
      one: 'Dispositivo Demo',
    );
    return '$_temp0';
  }

  @override
  String get discoverySu12Description =>
      'Interruptor horario Bluetooth de 2 canales';

  @override
  String get discoveryImprint => 'Pie de imprenta';

  @override
  String get discoveryLegalnotice => 'Aviso legal';

  @override
  String get generalSave => 'Guardar';

  @override
  String get generalCancel => 'Cancelar';

  @override
  String get detailsHeaderHardwareversion => 'Versión de hardware';

  @override
  String get detailsHeaderSoftwareversion => 'Versión del software';

  @override
  String get detailsHeaderConnected => 'Conectado';

  @override
  String get detailsHeaderDisconnected => 'Desconectado';

  @override
  String get detailsTimersectionHeader => 'Programas';

  @override
  String get detailsTimersectionTimercount => 'de 60 programas utilizados';

  @override
  String get detailsConfigurationsectionHeader => 'Configuración';

  @override
  String get detailsConfigurationPin => 'PIN del dispositivo';

  @override
  String detailsConfigurationChannelsDescription(
    Object channel1,
    Object channel2,
  ) {
    return 'Canal 1: $channel1| Canal 2:  $channel2';
  }

  @override
  String get settingsCentralHeader => 'Central On/Off';

  @override
  String get detailsConfigurationCentralDescription =>
      'Sólo se aplica si el canal está configurado en AUTO';

  @override
  String get detailsConfigurationDevicedisplaylock =>
      'Bloquear la pantalla del dispositivo';

  @override
  String get timerOverviewHeader => 'Programas';

  @override
  String get timerOverviewTimersectionTimerinactivecount => 'inactivo';

  @override
  String get timerDetailsListsectionDays1 => 'Lunes';

  @override
  String get timerDetailsListsectionDays2 => 'Martes';

  @override
  String get timerDetailsListsectionDays3 => 'Miércoles';

  @override
  String get timerDetailsListsectionDays4 => 'Jueves';

  @override
  String get timerDetailsListsectionDays5 => 'Viernes';

  @override
  String get timerDetailsListsectionDays6 => 'Sábado';

  @override
  String get timerDetailsListsectionDays7 => 'Domingo';

  @override
  String get timerDetailsHeader => 'Programa';

  @override
  String get timerDetailsSunrise => 'Amanecer';

  @override
  String get generalToggleOff => 'Off';

  @override
  String get generalToggleOn => 'On';

  @override
  String get timerDetailsImpuls => 'Impulso';

  @override
  String get generalTextTime => 'Hora';

  @override
  String get generalTextAstro => 'Astro';

  @override
  String get generalTextAuto => 'Auto';

  @override
  String get timerDetailsOffset => 'Desplazamiento horario';

  @override
  String get timerDetailsPlausibility =>
      'Activar la comprobación de plausibilidad';

  @override
  String get timerDetailsPlausibilityDescription =>
      'Si la hora de « Off “ es anterior a la hora de ” On », se ignoran ambas horas, por ejemplo, encender al amanecer y apagar a las 6:00 de la mañana. También hay constelaciones en las que la comprobación no es intencionada, por ejemplo, encender al atardecer y apagar a la 1:00 de la madrugada.';

  @override
  String get generalDone => 'Listo';

  @override
  String get generalDelete => 'Borrar';

  @override
  String get timerDetailsImpulsDescription =>
      'Cambiar la configuración global de impulso';

  @override
  String get settingsNameHeader => 'Nombre del dispositivo';

  @override
  String get settingsNameDescription =>
      'Este nombre se utiliza para identificar el dispositivo.';

  @override
  String get settingsFactoryresetHeader => 'Ajustes de fábrica';

  @override
  String get settingsFactoryresetDescription =>
      '¿Qué contenidos deben restablecerse?';

  @override
  String get settingsFactoryresetResetbluetooth =>
      'Restablecer la configuración de Bluetooth';

  @override
  String get settingsFactoryresetResettime =>
      'Restablecer la configuración de la hora';

  @override
  String get settingsFactoryresetResetall => 'Restablecer valores de fábrica';

  @override
  String get settingsDeletetimerHeader => 'Borrar programas';

  @override
  String get settingsDeletetimerDescription =>
      '¿Deben borrarse realmente todos los programas?';

  @override
  String get settingsDeletetimerAllchannels => 'Todos los canales';

  @override
  String get settingsImpulseHeader => 'Tiempo del impulso';

  @override
  String get settingsImpulseDescription =>
      'El tiempo del impulso determina la duración del impulso.';

  @override
  String get generalTextRandommode => 'Modo aleatorio';

  @override
  String get settingsChannelsTimeoffsetHeader => 'Desplazamiento del solsticio';

  @override
  String settingsChannelsTimeoffsetDescription(
    Object summerOffset,
    Object winterOffset,
  ) {
    return 'Verano: ${summerOffset}min | Invierno: ${winterOffset}min';
  }

  @override
  String get settingsLocationHeader => 'Ubicación';

  @override
  String get settingsLocationDescription =>
      'Configura tu ubicación para utilizar las funciones Astro.';

  @override
  String get settingsLanguageHeader => 'Idioma del dispositivo';

  @override
  String get settingsLanguageSetlanguageautomatically =>
      'Establecer el idioma automáticamente';

  @override
  String settingsLanguageDescription(Object deviceType) {
    return 'Elija el idioma para el $deviceType';
  }

  @override
  String get settingsLanguageGerman => 'Alemán';

  @override
  String get settingsLanguageFrench => 'Francés';

  @override
  String get settingsLanguageEnglish => 'Inglés';

  @override
  String get settingsLanguageItalian => 'Italiano';

  @override
  String get settingsLanguageSpanish => 'Español';

  @override
  String get settingsDatetimeHeader => 'Fecha y hora';

  @override
  String get settingsDatetimeSettimeautomatically =>
      'Aplicar el tiempo del sistema';

  @override
  String get settingsDatetimeSettimezoneautomatically =>
      'Establecer automáticamente la zona horaria';

  @override
  String get generalTextTimezone => 'Zona horaria';

  @override
  String get settingsDatetime24Hformat => 'Formato de 24 horas';

  @override
  String get settingsDatetimeSetsummerwintertimeautomatically =>
      'Tiempo verano/invierno automáticamente';

  @override
  String get settingsDatetimeWinter => 'Invierno';

  @override
  String get settingsDatetimeSummer => 'Verano';

  @override
  String get settingsPasskeyHeader => 'PIN del dispositivo actual';

  @override
  String get settingsPasskeyDescription =>
      'Introduzca el PIN actual del dispositivo';

  @override
  String get timerDetailsActiveprogram => 'Programa activo';

  @override
  String get timerDetailsActivedays => 'Días activos';

  @override
  String get timerDetailsSuccessdialogHeader => 'Exitoso';

  @override
  String get timerDetailsSuccessdialogDescription =>
      'Programa añadido con éxito';

  @override
  String get settingsRandommodeDescription =>
      'El modo aleatorio sólo funciona con programas horarios, no con programas de impulso o astro (salida o puesta del sol).';

  @override
  String get settingsSolsticeHeader => 'Desplazamiento hora del solsticio';

  @override
  String get settingsSolsticeDescription =>
      'La hora indica el desplazamiento con respecto a la puesta de sol. La salida del sol se invierte en consecuencia.';

  @override
  String get settingsSolsticeHint =>
      'Ejemplo: \nEn invierno, la conmutación se realiza 30 minutos antes de la puesta de sol, lo que significa que la conmutación también se realiza 30 minutos después de la salida del sol.';

  @override
  String get generalTextMinutesShort => 'min';

  @override
  String get settingsPinDescription => 'El PIN es necesario para la conexión.';

  @override
  String get settingsPinHeader => 'Nuevo PIN del dispositivo';

  @override
  String get settingsPinNewpinDescription => 'Introduzca un nuevo PIN';

  @override
  String get settingsPinNewpinRepeat => 'Repita el nuevo PIN';

  @override
  String get detailsProductinfo => 'Información sobre el producto';

  @override
  String get settingsDatetimeSettimeautodescription =>
      'Elija la hora preferida';

  @override
  String minutes(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Minutos',
      one: 'Minuto',
    );
    return '$_temp0';
  }

  @override
  String hours(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Horas',
      one: 'Hora',
    );
    return '$_temp0';
  }

  @override
  String seconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'segundos',
      one: 'segundo',
    );
    return '$_temp0';
  }

  @override
  String generalTextChannel(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Canales',
      one: 'Canal',
    );
    return '$_temp0';
  }

  @override
  String generalLabelChannel(Object number) {
    return 'Canal $number';
  }

  @override
  String get generalTextDate => 'Fecha';

  @override
  String get settingsDatetime24HformatDescription =>
      'Elija el formato preferido';

  @override
  String get settingsDatetimeSetsummerwintertime => 'Tiempo verano/invierno';

  @override
  String get settingsDatetime24HformatValue24 => '24-horas';

  @override
  String get settingsDatetime24HformatValue12 => '12-horas';

  @override
  String get detailsEdittimer => 'Editar programas';

  @override
  String get settingsPinOldpinRepeat => 'Por favor, repita el PIN actual';

  @override
  String get settingsPinCheckpin => 'Comprobación del PIN';

  @override
  String detailsDevice(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Dispositivos',
      one: 'Dispositivo',
    );
    return '$_temp0';
  }

  @override
  String get detailsDisconnect => 'Desconectar';

  @override
  String get settingsCentralDescription =>
      'La entrada A1 controla el central On/Off.\nEl central ON/OFF sólo se aplica si el canal está configurado como ON/OFF centralizado.';

  @override
  String get settingsCentralHint =>
      'Ejemplo:\nCanal 1 = Central On/Off\nCanal 2 = Off\nA1 = Central On -> Sólo C1 conmuta a On, C2 permanece en Off';

  @override
  String get settingsCentralToggleheader => 'Conmuta la entrada central';

  @override
  String get settingsCentralActivechannelsdescription =>
      'Canales actuales con el ajuste Central On/Off:';

  @override
  String get settingsSolsticeSign => 'Firme';

  @override
  String get settingsDatetimeTimezoneDescription => 'Hora de Europa central';

  @override
  String get generalButtonContinue => 'Continuar';

  @override
  String get settingsPinConfirmationDescription => 'Cambio de PIN exitoso';

  @override
  String get settingsPinFailDescription => 'El cambio de PIN ha fallado.';

  @override
  String get settingsPinFailHeader => 'Fallido';

  @override
  String get settingsPinFailShort => 'El PIN debe tener exactamente 6 dígitos';

  @override
  String get settingsPinFailWrong => 'El PIN actual es incorrecto';

  @override
  String get settingsPinFailMatch => 'Los PINs no coinciden';

  @override
  String get discoveryLostconnectionHeader => 'Conexión perdida';

  @override
  String get discoveryLostconnectionDescription =>
      'Se ha desconectado la conexión con el dispositivo.';

  @override
  String get settingsChannelConfigCentralDescription =>
      'Se comporta como AUTO y también reacciona a las entradas de control cableados.';

  @override
  String get settingsChannelConfigOnDescription =>
      'Cambia el canal a permanentemente ON e ignora los programas';

  @override
  String get settingsChannelConfigOffDescription =>
      'Cambia el canal a permanentemente OFF e ignora los programas';

  @override
  String get settingsChannelConfigAutoDescription =>
      'Conmuta en función de los programas horarios y astronómicos.';

  @override
  String get bluetoothPermissionDescription =>
      'Para la configuración de los dispositivos se requiere Bluetooth.';

  @override
  String get timerListitemOn => 'Encender';

  @override
  String get timerListitemOff => 'Apagar';

  @override
  String get timerListitemUnknown => 'Desconocido';

  @override
  String get timerDetailsAstroHint =>
      'Para que los programas astronómicos funcionen correctamente, es necesario establecer la ubicación en los ajustes.';

  @override
  String get timerDetailsTrigger => 'Disparador';

  @override
  String get timerDetailsSunset => 'Puesta del sol';

  @override
  String get settingsLocationCoordinates => 'Coordenadas';

  @override
  String get settingsLocationLatitude => 'Latitud';

  @override
  String get settingsLocationLongitude => 'Longitud';

  @override
  String timerOverviewEmptyday(Object day) {
    return 'Actualmente no se utilizan programas para $day';
  }

  @override
  String get timerOverviewProgramloaded => 'Los programas se cargan';

  @override
  String get timerOverviewProgramchanged => 'El programa fue modificado';

  @override
  String get settingsDatetimeProcessing => 'Se cambia la fecha y la hora';

  @override
  String get deviceNameEmpty => 'La entrada no debe estar vacía';

  @override
  String deviceNameHint(Object count) {
    return 'La entrada no debe contener más de $count caracteres.';
  }

  @override
  String get deviceNameChanged => 'Se cambia el nombre del dispositivo';

  @override
  String get deviceNameChangedSuccessfully =>
      'El nombre del dispositivo se ha cambiado con éxito.';

  @override
  String get deviceNameChangedFailed => 'Se ha producido un error.';

  @override
  String get settingsPinConfirm => 'Confirmar';

  @override
  String get deviceShowInstructions =>
      '1. Activar el Bluetooth del reloj con SET\n2. Pulse el botón de la parte superior para iniciar la búsqueda.';

  @override
  String get deviceNameNew => 'Introduzca el nuevo nombre del dispositivo';

  @override
  String get settingsLanguageRetrieved => 'Se recupera la lengua';

  @override
  String get detailsProgramsShow => 'Mostrar programas';

  @override
  String get generalTextProcessing => 'Por favor, espere';

  @override
  String get generalTextRetrieving => 'se solicitan';

  @override
  String get settingsLocationPermission =>
      'Permitir que ELTAKO Connect acceda a la ubicación de este dispositivo';

  @override
  String get timerOverviewChannelloaded => 'Los canales se cargan';

  @override
  String get generalTextRandommodeChanged => 'Se cambia el modo aleatorio';

  @override
  String get detailsConfigurationsectionChanged => 'Cambio de la configuración';

  @override
  String get settingsSettimeFunctions => 'Se modifican las funciones horarias';

  @override
  String get imprintContact => 'Contacto';

  @override
  String get imprintPhone => 'Teléfono';

  @override
  String get imprintMail => 'Correo eléctronico';

  @override
  String get imprintRegistrycourt => 'Registro mercantil';

  @override
  String get imprintRegistrynumber => 'Número de registro';

  @override
  String get imprintCeo => 'Director General';

  @override
  String get imprintTaxnumber => 'Número VAT';

  @override
  String get settingsLocationCurrent => 'Ubicación actual';

  @override
  String get generalTextReset => 'Reiniciar';

  @override
  String get discoverySearchStart => 'Iniciar la búsqueda';

  @override
  String get discoverySearchStop => 'Para la búsqueda';

  @override
  String get settingsImpulsSaved => 'Se guarda el tiempo de impulsos';

  @override
  String get settingsCentralNochannel =>
      'No hay canales con el ajuste ON/OFF centralizado';

  @override
  String get settingsFactoryresetBluetoothConfirmationDescription =>
      'La conexión Bluetooth se ha restablecido con éxito.';

  @override
  String get settingsFactoryresetBluetoothFailDescription =>
      'Fallo en el restablecimiento de las conexiones Bluetooth.';

  @override
  String get imprintPublisher => 'Editorial';

  @override
  String get discoveryDeviceConnecting => 'Se establece la conexión';

  @override
  String get discoveryDeviceRestarting => 'Reiniciando...';

  @override
  String get generalTextConfigurationsaved =>
      'Configuración del canal guardada.\n';

  @override
  String get timerOverviewChannelssaved => 'Guardar canales';

  @override
  String get timerOverviewSaved => 'Temporizador guardado';

  @override
  String get timerSectionList => 'Vista de lista';

  @override
  String get timerSectionDayview => 'Vista del día';

  @override
  String get generalTextChannelInstructions => 'Ajustes del canal';

  @override
  String get generalTextPublisher => 'Editorial';

  @override
  String get settingsDeletetimerDialog =>
      '¿De verdad quiere borrar todos los programas?';

  @override
  String get settingsFactoryresetResetbluetoothDialog =>
      '¿De verdad quiere resetear todos los ajustes de Bluetooth?';

  @override
  String get settingsCentralTogglecentral => 'Central\nOn/Off';

  @override
  String generalTextConfirmation(Object serviceName) {
    return '$serviceName se ha modificado con éxito.';
  }

  @override
  String generalTextFailed(Object serviceName) {
    return '$serviceName no se podía cambiar.';
  }

  @override
  String get settingsChannelConfirmationDescription =>
      'Los canales fueron cambiados con éxito.';

  @override
  String get timerDetailsSaveHeader => 'Guardar el programa';

  @override
  String get timerDetailsDeleteHeader => 'Eliminar el programa';

  @override
  String get timerDetailsSaveDescription =>
      'El programa se ha guardado con éxito.';

  @override
  String get timerDetailsDeleteDescription =>
      'El programa se ha eliminado con éxito.';

  @override
  String get timerDetailsAlertweekdays =>
      'El programa no se puede guardar, porque no hay días de la semana seleccionados.';

  @override
  String get generalTextOk => 'OK';

  @override
  String get settingsDatetimeChangesuccesfull =>
      'La fecha y la hora se han modificado correctamente.';

  @override
  String get discoveryConnectionFailed => 'Fallo de conexión';

  @override
  String get discoveryDeviceResetrequired =>
      'No se ha podido establecer la conexión con el dispositivo. Para solucionar este problema, elimina el dispositivo de tu configuración Bluetooth. Si el problema persiste, póngase en contacto con nuestro servicio técnico.';

  @override
  String get generalTextSearch => 'Búsqueda de dispositivos';

  @override
  String get generalTextOr => 'o';

  @override
  String get settingsFactoryresetProgramsConfirmationDescription =>
      'Todos los programas se han eliminado con éxito.';

  @override
  String get generalTextManualentry => 'Introducción manual';

  @override
  String get settingsLocationSaved => 'Ubicación guardada';

  @override
  String get settingsLocationAutosearch => 'Búsqueda automática de ubicación';

  @override
  String get imprintPhoneNumber => '+49 711 / 9435 0000';

  @override
  String get imprintMailAddress => '<EMAIL>';

  @override
  String get settingsFactoryresetResetallDialog =>
      '¿Realmente se debe restablecer la configuración de fábrica del dispositivo?';

  @override
  String get settingsFactoryresetFactoryConfirmationDescription =>
      'El dispositivo se ha restablecido con éxito a la configuración de fábrica.';

  @override
  String get settingsFactoryresetFactoryFailDescription =>
      'El reinicio del dispositivo ha fallado.';

  @override
  String get imprintPhoneNumberIos => '+49711/94350000';

  @override
  String get mfzFunctionA2Title => 'Retardo de conexión en 2 etapas (A2)';

  @override
  String get mfzFunctionA2TitleShort => 'Retardo de conexión en 2 etapas (A2)';

  @override
  String get mfzFunctionA2Description =>
      'Cuando se aplica la tensión de control, comienza el tiempo de retardo T1 entre 0 y 60 segundos. Al final, el contacto 1-2 se cierra y comienza el tiempo de retardo T2 entre 0 y 60 segundos. Al finalizar, se cierra el contacto 3-4. Tras una interrupción, el temporizador vuelve a empezar con T1.';

  @override
  String get mfzFunctionRvTitle => 'Retardo a la desconexión (RV)';

  @override
  String get mfzFunctionRvTitleShort => 'RV| Retardo a la desconexión';

  @override
  String get mfzFunctionRvDescription =>
      'Cuando se aplica la tensión de control, el contacto cambia a 15-18. \nCuando se interrumpe la tensión de control, comienza el intervalo de tiempo, al final del cual el contacto vuelve a la posición de reposo. Se puede prolongar el tiempo durante el intervalo de tiempo.';

  @override
  String get mfzFunctionTiTitle =>
      'Intermitencia empezando con impulso (TI; rele intermitente)';

  @override
  String get mfzFunctionTiTitleShort =>
      'TI | Intermitencia empezando con impulso';

  @override
  String get mfzFunctionTiDescription =>
      'Mientras la tensión de control está aplicada, el contacto  se cierra y se abre. El tiempo de conmutación en ambas direcciones puede ajustarse por separado. Cuando se aplica la tensión de control, el contacto cambia inmediatamente a 15-18.';

  @override
  String get mfzFunctionAvTitle => 'Retardo a la conexión (AV)';

  @override
  String get mfzFunctionAvTitleShort => 'AV | Retardo a la conexión';

  @override
  String get mfzFunctionAvDescription =>
      'Cuando se aplica la tensión de control, se inicia el periodo de temporización; cuando se acaba el tiempo, el contacto del relé cambia a 15-18. Tras una interrupción, el periodo de temporización se reinicia.';

  @override
  String get mfzFunctionAvPlusTitle =>
      'Retardo a la conexión, conteo regresivo (AV+)';

  @override
  String get mfzFunctionAvPlusTitleShort =>
      'AV+ | Retardo a la conexión, conteo regresivo';

  @override
  String get mfzFunctionAvPlusDescription =>
      'Funciona como AV, pero después de una interrupción se guarda el tiempo restante.';

  @override
  String get mfzFunctionAwTitle =>
      'Comienzo del Intervalo a la desconexión (AW)';

  @override
  String get mfzFunctionAwTitleShort =>
      'AW | Comienzo del intervalo a la desconexión';

  @override
  String get mfzFunctionAwDescription =>
      'Si se corta la tensión de control, el contacto cambia a 15-18 y vuelve una vez transcurrido el tiempo ajustado. Si se aplica la tensión de control durante el intervalo, el contacto vuelve inmediatamente a la posición de reposo y se borra el tiempo restante.';

  @override
  String get mfzFunctionIfTitle => 'Modulador de impulsos (IF)';

  @override
  String get mfzFunctionIfTitleShort => 'IF | Modulador de impulsos';

  @override
  String get mfzFunctionIfDescription =>
      'Cuando se aplica la tensión de control, el contacto cambia a 15-18 por el tiempo ajustado. Las siguientes activaciones sólo se evalúan una vez transcurrido el tiempo ajustado.';

  @override
  String get mfzFunctionEwTitle => 'Cominezo de intervalo a la conexión(EW)l';

  @override
  String get mfzFunctionEwTitleShort =>
      'EW | Comienzo del intervalo a la conexión';

  @override
  String get mfzFunctionEwDescription =>
      'Cuando se aplica la tensión de control, el contacto cambia a 15-18 y vuelve a la posición de reposo una vez transcurrido el tiempo ajustado. Si se corta la tensión de control durante el tiempo ajustado, el contacto vuelve inmediatamente a la posición de reposo y se borra el tiempo restante.';

  @override
  String get mfzFunctionEawTitle =>
      'Comienzo de Intervalo a la conexión y desconexión (EAW)\n';

  @override
  String get mfzFunctionEawTitleShort =>
      'EAW | Intervalo a la conexión y desconexión';

  @override
  String get mfzFunctionEawDescription =>
      'Cuando se aplica o interrumpe la tensión de control, el contacto del relé cambia a 15-18 y vuelve a invertirse después del tiempo de conmutación ajustado.';

  @override
  String get mfzFunctionTpTitle => 'Intermitente empezando con pausa (TP)';

  @override
  String get mfzFunctionTpTitleShort => 'TP | Intermitente empezando con pausa';

  @override
  String get mfzFunctionTpDescription =>
      'Descripcion de funcionamiento como TI, pero cuando se aplica la tensión de control el contacto no cambia a 15-18, sino que inicialmente permanece en 15-16 o abierto.';

  @override
  String get mfzFunctionIaTitle =>
      'Retardo de desconexión controlado por impulsos (por ejemplo, abrepuertas automático) (IA)';

  @override
  String get mfzFunctionIaTitleShort =>
      'IA | Retardo de desconexión controlado por impulsos y modelador de impulsos';

  @override
  String get mfzFunctionIaDescription =>
      'Con el inicio de un impulso de control a partir de 20 ms, comienza el intervalo de tiempo T1, al final del cual el contacto cambia por el tiempo T2 a 15-18 (p.ej. para abrepuertas automáticos). Si T1 se ajusta al tiempo más corto 0,1 s, IA funciona como un formador de impulsos en el que T2 expira, independientemente de la longitud de la señal de control (mín. 150 ms).';

  @override
  String get mfzFunctionArvTitle => 'Retardo a la conexión y desconexión (ARV)';

  @override
  String get mfzFunctionArvTitleShort =>
      'ARV | Retardo a la conexión y desconexión';

  @override
  String get mfzFunctionArvDescription =>
      'Cuando se aplica la tensión de control, comienza el intervalo de tiempo, al final del cual el contacto cambia a 15 -18. Si se interrumpe la tensión de control, comienza otro intervalo de tiempo, al final del cual el contacto vuelve a la posición de reposo. \nTras una interrupción del retardo de reacción, el intervalo de tiempo vuelve a empezar.\n';

  @override
  String get mfzFunctionArvPlusTitle =>
      'Retardo a la conexión y desconexión Retardo a la conexión y desconexión conteo regresivo (ARV+)';

  @override
  String get mfzFunctionArvPlusTitleShort =>
      'ARV+ | Retardo a la conexión y desconexión conteo regresivo';

  @override
  String get mfzFunctionArvPlusDescription =>
      'Misma función que ARV, pero tras una interrupción del funcionamiento se almacena el tiempo transcurrido.';

  @override
  String get mfzFunctionEsTitle => 'Telerruptor (ES)';

  @override
  String get mfzFunctionEsTitleShort => 'ES | Telerruptor';

  @override
  String get mfzFunctionEsDescription =>
      'El contacto de cierre cambia de un lado a otro con impulsos de control mayor de 50 ms.';

  @override
  String get mfzFunctionEsvTitle =>
      'Telerruptor con retardo a la desconexión con avisador (ESV)';

  @override
  String get mfzFunctionEsvTitleShort =>
      'ESV | Telerruptor con retardo a la desconexión con avisador';

  @override
  String get mfzFunctionEsvDescription =>
      'Función como SRV. Además, con advertencia de apagado: aproximadamente 30 segundos antes del final del tiempo, la iluminación parpadea 3 veces en intervalos cada vez más cortos.';

  @override
  String get mfzFunctionErTitle => 'Relé (ER)';

  @override
  String get mfzFunctionErTitleShort => 'ER | Relé';

  @override
  String get mfzFunctionErDescription =>
      'Mientras el contacto de control esté cerrado, el contacto  pasa de 15-16 a 15-18.';

  @override
  String get mfzFunctionSrvTitle =>
      'Telerruptor con retardo a la desconexión (SRV)';

  @override
  String get mfzFunctionSrvTitleShort =>
      'SRV | Telerruptor con retardo a la desconexión';

  @override
  String get mfzFunctionSrvDescription =>
      'Con impulsos de control a partir de 50 ms, el contacto cambia permanente su posición. En la posición de contacto 15-18, el dispositivo cambia automáticamente a la posición de reposo 15-16 después de que transcurra el tiempo de retardo.';

  @override
  String get detailsFunctionsHeader => 'Funciones';

  @override
  String mfzFunctionTimeHeader(Object index) {
    return 'Tiempo (t$index)';
  }

  @override
  String get mfzFunctionOnDescription => 'Permanente ON';

  @override
  String get mfzFunctionOffDescription => 'Permanente OFF';

  @override
  String get mfzFunctionMultiplier => 'Factor';

  @override
  String get discoveryMfz12Description =>
      'Relé temporizado multifunción Bluetooth';

  @override
  String get mfzFunctionOnTitle => 'ON';

  @override
  String get mfzFunctionOnTitleShort => 'ON';

  @override
  String get mfzFunctionOffTitle => 'OFF';

  @override
  String get mfzFunctionOffTitleShort => 'OFF';

  @override
  String get mfzMultiplierSecondsFloatingpoint => '0.1 segundos';

  @override
  String get mfzMultiplierMinutesFloatingpoint => '0.1 minutos';

  @override
  String get mfzMultiplierHoursFloatingpoint => '0.1 horas';

  @override
  String get mfzOverviewFunctionsloaded => 'Las funciones están cargados';

  @override
  String get mfzOverviewSaved => 'Función guardada';

  @override
  String get settingsBluetoothHeader => 'Bluetooth';

  @override
  String get bluetoothChangedSuccessfully =>
      'La configuración de Bluetooth se ha cambiado con éxito.';

  @override
  String get settingsBluetoothInformation =>
      'Nota: Si este ajuste está activado, el dispositivo estará permanentemente visible para todo el mundo a través de Bluetooth. Se recomienda cambiar el PIN del dispositivo.';

  @override
  String get settingsBluetoothContinuousconnection => 'Visibilidad permanente';

  @override
  String settingsBluetoothContinuousconnectionDescription(Object deviceType) {
    return 'Al activar la visibilidad permanente, el Bluetooth permanece activo en el dispositivo ($deviceType) y no es necesario activarlo manualmente antes de establecer una conexión.';
  }

  @override
  String get settingsBluetoothTimeout => 'Tiempo de espera de la conexión';

  @override
  String get settingsBluetoothPinlimit => 'Límite del PIN';

  @override
  String settingsBluetoothTimeoutDescription(Object timeout) {
    return 'La conexión se desconecta tras $timeout minutos de inactividad.';
  }

  @override
  String settingsBluetoothPinlimitDescription(Object attempts) {
    return 'Por razones de seguridad, tiene un máximo de $attempts intentos \npara introducir el PIN. El Bluetooth se desactiva y debe ser reactivado manualmente para una nueva conexión.';
  }

  @override
  String get settingsBluetoothPinAttempts => 'intentos';

  @override
  String get settingsResetfunctionHeader => 'Restablecer las funciones';

  @override
  String get settingsResetfunctionDialog =>
      'Realmente quieres restablecer todas las funciones?';

  @override
  String get settingsFactoryresetFunctionsConfirmationDescription =>
      'Todas las funciones se han restablecido con éxito.';

  @override
  String get mfzFunctionTime => 'Tiempo (t)';

  @override
  String get discoveryConnectionFailedInfo => 'No hay conexión Bluetooth';

  @override
  String get detailsConfigurationDevicedisplaylockDialogtext =>
      'Si bloqueas la pantalla del dispositivo, Bluetooth se desactiva y debe volver a activarse manualmente para una nueva conexión.';

  @override
  String get detailsConfigurationDevicedisplaylockDialogquestion =>
      '¿Realmente quieres bloquear la pantalla del dispositivo?';

  @override
  String get settingsDemodevices => 'Mostrar unidades de demostración';

  @override
  String get generalTextSettings => 'Ajustes';

  @override
  String get discoveryWifi => 'WiFi';

  @override
  String get settingsInformations => 'Información';

  @override
  String get detailsConfigurationDimmingbehavior =>
      'Comportamiento de regulación';

  @override
  String get detailsConfigurationSwitchbehavior =>
      'Comportamiento de la entrada de control';

  @override
  String get detailsConfigurationBrightness => 'Luminosidad';

  @override
  String get detailsConfigurationMinimum => 'Mínimo';

  @override
  String get detailsConfigurationMaximum => 'Máximo';

  @override
  String get detailsConfigurationSwitchesGr => 'Relé en grupo (GR)';

  @override
  String get detailsConfigurationSwitchesGs => 'Interruptor de grupo (GS)';

  @override
  String get detailsConfigurationSwitchesCloserer => 'Relé NA';

  @override
  String get detailsConfigurationSwitchesClosererDescription =>
      'OFF -> Pulsando (On) -> Soltar (Off)';

  @override
  String get detailsConfigurationSwitchesOpenerer => 'Relé NC';

  @override
  String get detailsConfigurationSwitchesOpenererDescription =>
      'On -> Pulsando (Off) -> Soltar (On)';

  @override
  String get detailsConfigurationSwitchesSwitch => 'Conmutador';

  @override
  String get detailsConfigurationSwitchesSwitchDescription =>
      'Cada vez que se conmuta, la luz se enciende y se apaga.';

  @override
  String get detailsConfigurationSwitchesImpulsswitch => 'Telerruptor';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription =>
      'La pulsación del pulsador enciende y apaga la luz';

  @override
  String get detailsConfigurationSwitchesClosererDescription2 =>
      'Pulse el pulsador. Al soltarlo, el motor se para';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription2 =>
      'El pulsador se pulsa brevemente para arrancar el motor y se pulsa brevemente para volver a pararlo';

  @override
  String get detailsConfigurationWifiloginScan => 'Escanear el código QR';

  @override
  String get detailsConfigurationWifiloginScannotvalid =>
      'El código escaneado no es válido';

  @override
  String get detailsConfigurationWifiloginDescription => 'Introduzca el código';

  @override
  String get detailsConfigurationWifiloginPassword => 'Contraseña';

  @override
  String get discoveryEsbipDescription => 'Actuador de persianas IP';

  @override
  String get discoveryEsripDescription => 'Telerruptor/Relé IP';

  @override
  String get discoveryEudipDescription => 'Regulador universal IP';

  @override
  String get generalTextLoad => 'Cargando';

  @override
  String get wifiBasicautomationsNotFound =>
      'No se ha encontrado ninguna automatización.';

  @override
  String get wifiCodeInvalid => 'Código inválido';

  @override
  String get wifiCodeValid => 'Código válido';

  @override
  String get wifiAuthorizationLogin => 'Conectar';

  @override
  String get wifiAuthorizationLoginFailed => 'Fallo en el inicio de sesión';

  @override
  String get wifiAuthorizationSerialnumber => 'Número de serie';

  @override
  String get wifiAuthorizationProductiondate => 'Fecha de producción';

  @override
  String get wifiAuthorizationProofofpossession => 'PoP';

  @override
  String get generalTextWifipassword => 'Contraseña WiFi';

  @override
  String get generalTextUsername => 'Nombre de usuario';

  @override
  String get generalTextEnter => 'O INTRODUZCA MANUALMENTE';

  @override
  String get wifiAuthorizationScan => 'Escanea el código ELTAKO.';

  @override
  String get detailsConfigurationDevicesNofunctionshinttext =>
      'Este dispositivo no admite actualmente ninguna otra configuración más';

  @override
  String get settingsUsedemodelay => 'Utilizar el retardo de demostración';

  @override
  String get settingsImpulsLoad => 'Se carga el tiempo de impulsos.';

  @override
  String get settingsBluetoothLoad => 'Se carga la configuración de Bluetooth.';

  @override
  String get detailsConfigurationsectionLoad => 'Se carga las configuraciones';

  @override
  String get generalTextLogin => 'Log in\n';

  @override
  String get generalTextAuthentication => 'Autentificar';

  @override
  String get wifiAuthorizationScanDescription =>
      'Busque el código ELTAKO en el dispositivo-WiFi o en la hoja de información entregada y escanearlo con tu cámara.';

  @override
  String get wifiAuthorizationScanShort => 'Escanear el código ELTAKO';

  @override
  String get detailsConfigurationEdgemode => 'Curvas de regulación';

  @override
  String get detailsConfigurationEdgemodeLeadingedge => 'Principio de fase';

  @override
  String get generalTextNetwork => 'Red';

  @override
  String get wifiAuthenticationSuccessful => 'Autenticación exitosa';

  @override
  String get detailsConfigurationsectionSavechange =>
      'Configuración modificada';

  @override
  String get discoveryWifiAdddevice => 'Añadir dispositivo Wi-Fi';

  @override
  String get wifiAuthenticationDelay => 'Esto puede durar hasta 1 minuto.';

  @override
  String get generalTextRetry => 'Reintentar';

  @override
  String get wifiAuthenticationCredentials =>
      'Por favor, introduzca el password de su WiFi';

  @override
  String get wifiAuthenticationSsid => 'SSID';

  @override
  String get wifiAuthenticationDelaylong =>
      'Puede durar hasta 1 minuto hasta que el dispositivo esté listo y aparezca en la aplicación';

  @override
  String get wifiAuthenticationCredentialsShort =>
      'introduzca los datos de WiFi';

  @override
  String get wifiAuthenticationTeachin => 'Enlaza el dispositivo con el WiFi';

  @override
  String get wifiAuthenticationEstablish =>
      'Establecer conexión con el dispositivo';

  @override
  String wifiAuthenticationEstablishLong(Object ssid) {
    return 'El dispositivo se conecta con WiFi $ssid';
  }

  @override
  String get wifiAuthenticationFailed =>
      'Error de conexión. Desconecte el aparato de la red eléctrica durante unos segundos y vuelva a conectarlo.';

  @override
  String get wifiAuthenticationReset => 'Restablecer la autenticación';

  @override
  String get wifiAuthenticationResetHint =>
      'Se eliminan todos los datos de autenticación.';

  @override
  String get wifiAuthenticationInvaliddata =>
      'Los datos de autenticación no son válidos';

  @override
  String get wifiAuthenticationReauthenticate => 'Autenticar de nuevo';

  @override
  String get wifiAddhkdeviceHeader => 'Añadir dispositivo';

  @override
  String get wifiAddhkdeviceDescription =>
      'Conecta tu nuevo dispositivo ELTAKO a tu WLAN a través de la App Apple Home.';

  @override
  String get wifiAddhkdeviceStep1 => '1. Abre la App Apple Home.';

  @override
  String get wifiAddhkdeviceStep2 =>
      '2. Haz clic en el signo más de la esquina superior derecha de la App y selecciona **Añadir dispositivo**.';

  @override
  String get wifiAddhkdeviceStep3 => '3. Sigue las instrucciones de la App.';

  @override
  String get wifiAddhkdeviceStep4 =>
      'Ahora puedes configurar tu dispositivo en la App ELTAKO Connect.';

  @override
  String get detailsConfigurationRuntime => 'Tiempo de funcionamiento';

  @override
  String get detailsConfigurationRuntimeMode => 'Modo';

  @override
  String get generalTextManually => 'Manualmente';

  @override
  String get detailsConfigurationRuntimeAutoDescription =>
      'El actuador de persianas determina automáticamente el tiempo de funcionamiento del motor de la persiana para cada trayecto de la posición final inferior a la superior (recomendado).\nTras la puesta en marcha o modificaciones, debe realizarse un movimiento de abajo hacia arriba sin interrupción.';

  @override
  String get detailsConfigurationRuntimeManuallyDescription =>
      'Die Laufzeit des Rollladenmotors wird manuell mit Hilfe der unten angegebenen Dauer eingestellt.\nAchten Sie darauf, dass die eingestellte Laufzeit mit der tatsächlichen Laufzeit Ihres Rollladenmotors übereinstimmt.\nNach Inbetriebnahme oder Änderungen muss ein Lauf von unten nach oben ohne Unterbrechung durchgeführt werden.';

  @override
  String get detailsConfigurationRuntimeDemoDescription =>
      'El modo del display LCD sólo está disponible a través de la API REST';

  @override
  String get generalTextDemomodeActive => 'Modo demo activo';

  @override
  String get detailsConfigurationRuntimeDuration => 'Duración';

  @override
  String get detailsConfigurationSwitchesGs4 =>
      'Interruptor de grupo con función inversión de marcha TIpp (GS4)';

  @override
  String get detailsConfigurationSwitchesGs4Description =>
      'Interruptor de grupo con función de inversión de marcha para controlar las persianas';

  @override
  String get screenshotSu12 => 'Luz exterior';

  @override
  String get screenshotS2U12 => 'Luz exterior';

  @override
  String get screenshotMfz12 => 'Bomba';

  @override
  String get screenshotEsr62 => 'Lámpara';

  @override
  String get screenshotEud62 => 'Luz de techo';

  @override
  String get screenshotEsb62 => 'Persianas balcón';

  @override
  String get detailsConfigurationEdgemodeLeadingedgeDescription =>
      'LC1-LC3 son posiciones de confort con diferentes curvas de regulación para lámparas LED regulables de 230 V, que no pueden regularse lo suficiente en AUTO debido a su diseño y, por lo tanto, deben forzarse al control de final de fase.';

  @override
  String get detailsConfigurationEdgemodeAutoDescription =>
      'AUTO permite la regulación de todos los tipos de lámparas.';

  @override
  String get detailsConfigurationEdgemodeTrailingedge => 'Final de fase';

  @override
  String get detailsConfigurationEdgemodeTrailingedgeDescription =>
      'LC4-LC6 son posiciones comfort con diferentes curvas de regulación para lámparas LED regulables de 230 V.';

  @override
  String get updateHeader => 'Actualización del firmware';

  @override
  String get updateTitleStepSearch => 'Buscando actualización';

  @override
  String get updateTitleStepFound => 'Se ha encontrado una actualización';

  @override
  String get updateTitleStepDownload => 'Descarga de la actualización';

  @override
  String get updateTitleStepInstall => 'Instalación de la actualización';

  @override
  String get updateTitleStepSuccess => 'Actualización exitosa';

  @override
  String get updateTitleStepUptodate => 'Ya está actualizado';

  @override
  String get updateTitleStepFailed => 'Actualización fallida';

  @override
  String get updateButtonSearch => 'Buscar actualizaciones';

  @override
  String get updateButtonInstall => 'Instalar actualización';

  @override
  String get updateCurrentversion => 'Versión actual';

  @override
  String get updateNewversion => 'Nueva actualización de firmware disponible';

  @override
  String get updateHintPower =>
      'La actualización sólo se inicia cuando la salida del dispositivo no está activa. El dispositivo no debe desconectarse de la alimentación y no se debe salir de la App durante la actualización.';

  @override
  String get updateButton => 'Actualización';

  @override
  String get updateHintCompatibility =>
      'Se recomienda una actualización, de lo contrario algunas funciones de la App se verán limitadas.';

  @override
  String get generalTextDetails => 'Detalles';

  @override
  String get updateMessageStepMetadata =>
      'Cargando información de actualización';

  @override
  String get updateMessageStepPrepare => 'La actualización está en preparación';

  @override
  String get updateTitleStepUpdatesuccessful =>
      'La actualización se está comprobando';

  @override
  String get updateTextStepFailed =>
      'Sentimos que algo haya ido mal durante la actualización, inténtalo de nuevo en unos minutos o espera a que tu dispositivo se actualice automáticamente (requiere conexión a Internet).';

  @override
  String get configurationsNotavailable =>
      'Aún no hay configuraciones disponibles';

  @override
  String get configurationsAddHint =>
      'Crea nuevas configuraciones al conectarte a un dispositivo y guardar una configuración.';

  @override
  String get configurationsEdit => 'Editar configuración';

  @override
  String get generalTextName => 'Nombre';

  @override
  String get configurationsDelete => 'Borrar configuración';

  @override
  String configurationsDeleteHint(Object configName) {
    return '¿Debe borrarse realmente la configuración: $configName?';
  }

  @override
  String get configurationsSave => 'Guardar configuración';

  @override
  String get configurationsSaveHint =>
      'Aquí puedes guardar la configuración de tu dispositivo actual o cargar una configuración guardada anteriormente.';

  @override
  String get configurationsImport => 'Importar configuración';

  @override
  String configurationsImportHint(Object configName) {
    return '¿Debes transferirse realmente la configuración $configName?';
  }

  @override
  String generalTextConfigurations(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Configuracions',
      one: 'Configuración',
    );
    return '$_temp0';
  }

  @override
  String get configurationsStepPrepare => 'Preparando la configuración';

  @override
  String get configurationsStepName =>
      'Introduzca un nombre para la configuración';

  @override
  String get configurationsStepSaving => 'La configuración se guarda';

  @override
  String get configurationsStepSavedsuccessfully =>
      'La configuración se ha guardado correctamente';

  @override
  String get configurationsStepSavingfailed =>
      'Error al guardar la configuración';

  @override
  String get configurationsStepChoose => 'Seleccione una configuración';

  @override
  String get configurationsStepImporting => 'Se importa la configuración';

  @override
  String get configurationsStepImportedsuccessfully =>
      'La configuración se ha importado correctamente';

  @override
  String get configurationsStepImportingfailed =>
      'Error al importar la configuración';

  @override
  String get discoveryAssuDescription =>
      'Enchufe interruptor horario para el exterior Bluetooth 230V';

  @override
  String get settingsDatetimeDevicetime => 'Tiempo actual del dispositivo';

  @override
  String get settingsDatetimeLoading => 'Se cargan los ajustes de tiempo';

  @override
  String get discoveryEud12Description =>
      'Regulador de intensidad universal con Bluetooth';

  @override
  String get generalTextOffdelay => 'Retardo a la desconexión';

  @override
  String get generalTextRemainingbrightness => 'Luminosidad restante';

  @override
  String get generalTextSwitchonvalue => 'Valor de encendido';

  @override
  String get motionsensorTitleNoremainingbrightness =>
      'Sin luminosidad restante';

  @override
  String get motionsensorTitleAlwaysremainingbrightness =>
      'Con luminosidad restante';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogram =>
      'Luminosidad restante mediante el programa de conmutación';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogramandzea =>
      'Luminosidad residual a través de ZE y ZA';

  @override
  String get motionsensorTitleNoremainingbrightnessauto =>
      'Sin luminosidad restante (semiautomático)';

  @override
  String get generalTextMotionsensor => 'Detector de movimiento';

  @override
  String get generalTextLightclock => 'Despertador de luminosidad';

  @override
  String get generalTextSnoozeclock => 'Función atenuación automática';

  @override
  String generalDescriptionLightclock(Object mode) {
    return 'Al encender ($mode), la luz se enciende después de aprox. 1 segundo con la luminosidad más baja y se atenúa lentamente sin modificar el último valor de luminosidad guardado.';
  }

  @override
  String generalDescriptionSnoozeclock(Object mode) {
    return 'Al apagar ($mode), la iluminación se atenúa desde el valor de atenuación actual hasta la intensidad mínima y se apaga. Es posible apagar en cualquier momento durante el proceso de atenuación pulsando brevemente el pulsador. Una pulsación larga durante el proceso de atenuación pone fin a la función de atenuación automática. ';
  }

  @override
  String get generalTextImmediately => 'Inmediatamente';

  @override
  String get generalTextPercentage => 'Porcentaje';

  @override
  String get generalTextSwitchoffprewarning => 'Preaviso de desconexión';

  @override
  String get generalDescriptionSwitchoffprewarning =>
      'Atenuación lenta hasta la luminosidad mínima';

  @override
  String get generalDescriptionOffdelay =>
      'El dispositivo se enciende cuando se aplica la tensión de control. Si se interrumpe la tensión de control, comienza la temporización, tras la cual el dispositivo se apaga. El dispositivo puede encender de nuevo durente la temporización.';

  @override
  String get generalDescriptionBrightness =>
      'La luminosidad a la que el regulador enciende la lámpara.';

  @override
  String get generalDescriptionRemainingbrightness =>
      'Valor de atenuación en porcentaje al que se atenúa la lámpara tras la desconexión del detector de movimiento.';

  @override
  String get generalDescriptionRuntime =>
      'Tiempo de funcionamiento de la función despertador luminoso desde la intensidad mínima hasta la máxima.';

  @override
  String get generalTextUniversalbutton => 'Pulsador universal';

  @override
  String get generalTextDirectionalbutton => 'Pulsador direccional';

  @override
  String get eud12DescriptionAuto =>
      'Detección automática UT/RT (con diodo de pulsador direccional RTD)';

  @override
  String get eud12DescriptionRt => 'con diodo de pulsador direccional RTD';

  @override
  String get generalTextProgram => 'Programa';

  @override
  String get eud12MotionsensorOff => 'Con el detector de movimiento en Off';

  @override
  String get eud12ClockmodeTitleProgramze => 'Programa y Central On';

  @override
  String get eud12ClockmodeTitleProgramza => 'Programa y Central Off';

  @override
  String get eud12ClockmodeTitleProgrambuttonon => 'Programa y UT/RT On';

  @override
  String get eud12ClockmodeTitleProgrambuttonoff => 'Programa y UT/RT Off';

  @override
  String get eud12TiImpulseTitle => 'Tiempo de impulso On (t1)';

  @override
  String get eud12TiImpulseHeader => 'Valor de regulación tiempo de impulso ON';

  @override
  String get eud12TiImpulseDescription =>
      'El valor de regulación en porcentaje al que se atenúa la lámpara con el tiempo de impulso ON.';

  @override
  String get eud12TiOffTitle => 'Tiempo de impulso OFF (t2)';

  @override
  String get eud12TiOffHeader => 'Valor de regulación tiempo de impulso Off';

  @override
  String get eud12TiOffDescription =>
      'El valor de atenuación en porcentaje al que se atenúa la lámpara con el tiempo de impulso OFF.';

  @override
  String get generalTextButtonpermanentlight =>
      'Iluminación permanente con el pulsador';

  @override
  String get generalDescriptionButtonpermanentlight =>
      'Ajuste de la luz permanente mediante pulsador de 0 a 10 horas en pasos de 0,5 horas. Activación pulsando el pulsador durante más de 1 segundo (1 parpadeo), desactivación pulsando el botón durante más de 2 segundos.';

  @override
  String get generalTextNobuttonpermanentlight => 'No LPP';

  @override
  String get generalTextBasicsettings => 'Ajustes básicos';

  @override
  String get generalTextInputswitch => 'Entrada de pulsador local (A1)';

  @override
  String get generalTextOperationmode => 'Modo de funcionamiento';

  @override
  String get generalTextDimvalue => 'Comportamiento de encender';

  @override
  String get eud12TitleUsememory => 'Utilizar valor de memoria';

  @override
  String get eud12DescriptionUsememory =>
      'El valor de memoria corresponde al último valor de regulación ajustado. Si la memorización del valor está desactivada, la regulación se ajusta siempre al valor de conexión.';

  @override
  String get generalTextStartup => 'Luminosidad de encendido';

  @override
  String get generalDescriptionSwitchonvalue =>
      'El valor de encendido es un valor de luminosidad ajustable que garantiza un encendido seguro.';

  @override
  String get generalTitleSwitchontime => 'Tiempo de conexión';

  @override
  String get generalDescriptionSwitchontime =>
      'Una vez transcurrido el tiempo de encendido, la lámpara se atenúa desde el valor de encendido hasta el valor de memoria.';

  @override
  String get generalDescriptionStartup =>
      'Algunas lámparas LED necesitan una corriente de arranque mayor para encenderse de forma fiable. La lámpara se enciende con este valor de encendido y se atenúa al valor de la memoria una vez transcurrido el tiempo de encendido.';

  @override
  String get eud12ClockmodeSubtitleProgramze =>
      'Breve clic en encender centralizado';

  @override
  String get eud12ClockmodeSubtitleProgramza =>
      'Breve clic en apagado centralizado';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonon =>
      'Doble clic en el pulsador universal/pulsador direccional lado ON';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonoff =>
      'Doble clic en el pulsador universal/pulsador direccional lado OFF';

  @override
  String get eud12FunctionStairlighttimeswitchTitleShort =>
      'TLZ | Minutero de escalera';

  @override
  String get eud12FunctionMinTitleShort => 'MIN';

  @override
  String get eud12FunctionMmxTitleShort => 'MMX';

  @override
  String get eud12FunctionTiDescription =>
      'Temporizador con tiempo de encendido y apagado ajustable de 0,5 segundos a 9,9 minutos. La luminosidad puede ajustarse desde la luminosidad mínima hasta la luminosidad máxima.';

  @override
  String get eud12FunctionAutoDescription =>
      'Dimmer universal con ajuste para detector de movimiento, despertador de luz y atenuación automática';

  @override
  String get eud12FunctionErDescription =>
      'Relé de conmutación, la luminosidad se puede ajustar desde la luminosidad mínima hasta la luminosidad máxima.';

  @override
  String get eud12FunctionEsvDescription =>
      'Regulador de intensidad universal con retardo de desconexión de 1 a 120 minutos. El preaviso de desconexión al final de la regulación puede ajustarse de 1 a 3 minutos. Ambas entradas de control centralizado activos.';

  @override
  String get eud12FunctionTlzDescription =>
      'Ajuste de la duración de la luz permanente mediante pulsador de 0 a 10 horas en pasos de 0,5 horas. Activación pulsando el pulsador durante más de 1 segundo (1 parpadeo), desactivación pulsando el pulsador durante más de 2 segundos.';

  @override
  String get eud12FunctionMinDescription =>
      'Dimmer universal, conmuta a la luminosidad mínima ajustada cuando se aplica la tensión de control. La luz se regula hasta la luminosidad máxima dentro del tiempo de regulación ajustado de 1 a 120 minutos. Cuando se desconecta la tensión de control, la luz se apaga inmediatamente, incluso durante el tiempo de regulación. Ambas entradas de control centralizado activos.';

  @override
  String get eud12FunctionMmxDescription =>
      'Dimmer universal, conmuta a la luminosidad mínima ajustada cuando se aplica la tensión de control. Durante el tiempo de atenuación ajustado de 1 a 120 minutos, la luz se atenúa hasta la luminosidad máxima. Sin embargo, cuando se desconecta la tensión de control, el regulador se atenúa hasta la luminosidad mínima ajustada. A continuación, se apaga. Ambas entradas de control centralizado activos.';

  @override
  String get motionsensorSubtitleNoremainingbrightness =>
      'Con el detector de movimiento en Off';

  @override
  String get motionsensorSubtitleAlwaysremainingbrightness =>
      'Con el detector de movimiento en Off';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogram =>
      'Programa de conmutación activado y desactivado con BM OFF';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogramandzea =>
      'Central On activa el sensor de movimiento, central Off desactiva el sensor de movimiento, así como un programa de conmutación';

  @override
  String get motionsensorSubtitleNoremainingbrightnessauto =>
      'El detector de movimiento sólo se apaga';

  @override
  String get detailsDimsectionHeader => 'regular';

  @override
  String get generalTextFast => 'Rápido';

  @override
  String get generalTextSlow => 'Lentamente';

  @override
  String get eud12TextDimspeed => 'Velocidad de regulación';

  @override
  String get eud12TextSwitchonspeed => 'Velocidad de conexión';

  @override
  String get eud12TextSwitchoffspeed => 'Velocidad de desconexión';

  @override
  String get eud12DescriptionDimspeed =>
      'La velocidad de atenuación es la velocidad a la que el atenuador pasa de la luminosidad actual a la luminosidad objetivo.';

  @override
  String get eud12DescriptionSwitchonspeed =>
      'La velocidad de encendido es la velocidad que necesita el regulador para encenderse completamente.';

  @override
  String get eud12DescriptionSwitchoffspeed =>
      'La velocidad de desconexión es la velocidad que necesita el regulador para desconectarse completamente.';

  @override
  String get settingsFactoryresetResetdimHeader =>
      'Restablecer ajustes de regulación';

  @override
  String get settingsFactoryresetResetdimDescription =>
      '¿Deben restablecerse realmente todos los ajustes de regulación?';

  @override
  String get settingsFactoryresetResetdimConfirmationDescription =>
      'Los ajustes de regulación se han restablecido correctamente';

  @override
  String get eud12TextSwitchonoffspeed => 'Velocidad de encendido/apagado';

  @override
  String get eud12DescriptionSwitchonoffspeed =>
      'La velocidad de encendido/apagado es la velocidad que necesita el regulador para encenderse o apagarse completamente.';

  @override
  String get timerDetailsDimtoval => 'Encendido con valor de regulación en %.';

  @override
  String get timerDetailsDimtovalDescription =>
      'El regulador se enciende siempre con el valor de regulación fijo en %.';

  @override
  String timerDetailsDimtovalSubtitle(Object brightness) {
    return 'Encender con $brightness%';
  }

  @override
  String get timerDetailsDimtomem => 'Encendido con la intensidad memorizada';

  @override
  String get timerDetailsDimtomemSubtitle =>
      'Encendido con la intensidad memorizada';

  @override
  String get timerDetailsMotionsensorwithremainingbrightness =>
      'Luminosidad residual (BM) ON';

  @override
  String get timerDetailsMotionsensornoremainingbrightness =>
      'Luz residual (BM) OFF';

  @override
  String get settingsRandommodeHint =>
      'Cuando se activa el modo aleatorio, todos los tiempos de conmutación del canal se desplazan aleatoriamente. Para horas de conexión hasta 15 minutos antes y horas de desconexión hasta 15 minutos después.';

  @override
  String get runtimeOffsetDescription =>
      'Rebasamiento adicional, una vez transcurrido el tiempo de recorrido. Puede utilizarse para garantizar que se alcanza la posición final.';

  @override
  String get loadingTextDimvalue => 'Se carga el valor de regulación';

  @override
  String get discoveryEudipmDescription => 'Regulador universal IP Matter';

  @override
  String get generalTextOffset => 'Rebasamiento';

  @override
  String get eud12DimvalueTestText => 'Enviar luminosidad';

  @override
  String get eud12DimvalueTestDescription =>
      'Durante la comprobación se tiene en cuenta la velocidad de regulación ajustada actualmente.';

  @override
  String get eud12DimvalueLoadText => 'Carga la luminosidad';

  @override
  String get settingsDatetimeNotime =>
      'Los ajustes de fecha y hora deben leerse a través de la pantalla del dispositivo.';

  @override
  String get generalMatterText => 'Matter';

  @override
  String get generalMatterMessage =>
      'Por favor, enlace sus dispositivos Matter utilizando una de las siguientes aplicaciones.';

  @override
  String get generalMatterOpengooglehome => 'Abrir Google Home';

  @override
  String get generalMatterOpenamazonalexa => 'Abrir Amazon Alexa';

  @override
  String get generalMatterOpensmartthings => 'Abrir SmartThings';

  @override
  String generalLabelProgram(Object number) {
    return 'Programa $number';
  }

  @override
  String get generalTextDone => 'Hecho';

  @override
  String get settingsRandommodeDescriptionShort =>
      'Con el modo aleatorio activado, todos los programas de este canal se desplazan aleatoriamente hasta 15 minutos. Los programas On se adelantan, los Off se retrasan.';

  @override
  String get all => 'Todo';

  @override
  String get discoveryBluetooth => 'Bluetooth';

  @override
  String get success => 'Con éxito';

  @override
  String get error => 'Error';

  @override
  String get timeProgramAdd => 'Añadir programa de tiempo';

  @override
  String get noConnection => 'Sin conexión';

  @override
  String get timeProgramOnlyActive => 'Programas configurados';

  @override
  String get timeProgramAll => 'Todos los programas';

  @override
  String get active => 'activo';

  @override
  String get inactive => 'Inactivo';

  @override
  String timeProgramSaved(Object number) {
    return 'Programa $number guardado';
  }

  @override
  String get deviceLanguageSaved => 'Idioma del dispositivo guardado';

  @override
  String generalTextTimeShort(Object time) {
    return '$time reloj';
  }

  @override
  String programDeleteHint(Object index) {
    return '¿Debe eliminarse realmente el programa $index? ';
  }

  @override
  String milliseconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Milisegundos',
      one: 'Milisegundo',
    );
    return '$_temp0';
  }

  @override
  String millisecondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count milisegundos',
      one: '$count milisegundo',
    );
    return '$_temp0';
  }

  @override
  String secondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count segundos',
      one: '$count segundo',
    );
    return '$_temp0';
  }

  @override
  String minutesWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count minutos',
      one: '$count minuto',
    );
    return '$_temp0';
  }

  @override
  String hoursWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count horas',
      one: '$count hora',
    );
    return '$_temp0';
  }

  @override
  String get settingsPinFailEmpty => 'El PIN no debe estar vacío';

  @override
  String get detailsConfigurationWifiloginScanNoMatch =>
      'El código escaneado no coincide con el dispositivo';

  @override
  String get wifiAuthorizationPopIsEmpty => 'El PdP no puede estar vacío';

  @override
  String get wifiAuthenticationCredentialsHint =>
      'Como la aplicación no puede acceder a su contraseña Wi-Fi privada, no es posible comprobar si la entrada es correcta. Si no se establece la conexión, comprueba la contraseña e introdúcela de nuevo.';

  @override
  String get generalMatterOpenApplehome => 'Abrir Apple Home';

  @override
  String get timeProgramNoActive => 'Sin programas configurados';

  @override
  String get timeProgramNoEmpty =>
      'No hay ningún programa de tiempo libre disponible.';

  @override
  String get nameOfConfiguration => 'Nombre de la configuración';

  @override
  String get currentDevice => 'Dispositivo actual';

  @override
  String get export => 'Exportar';

  @override
  String get import => 'Importar';

  @override
  String get savedConfigurations => 'Configuraciones guardadas';

  @override
  String get importableServicesLabel =>
      'Se pueden importar los siguientes ajustes:';

  @override
  String get notImportableServicesLabel => 'Ajustes incompatibles';

  @override
  String get deviceCategoryMeterGateway => 'Pasarela de contadores';

  @override
  String get deviceCategory2ChannelTimeSwitch =>
      'Interruptor horario de 2 canales';

  @override
  String get devicategoryOutdoorTimeSwitchBluetooth =>
      'Interruptor horario exterior Bluetooth';

  @override
  String get settingsModbusHeader => 'Modbus';

  @override
  String get settingsModbusDescription =>
      'Ajuste la velocidad en baudios, la paridad y el tiempo de espera para configurar la velocidad de transmisión, la detección de errores y el tiempo de espera.';

  @override
  String get settingsModbusRTU => 'Modbus RTU';

  @override
  String get settingsModbusBaudrate => 'Velocidad de transmisión';

  @override
  String get settingsModbusParity => 'Paridad';

  @override
  String get settingsModbusTimeout => 'Tiempo de espera Modbus';

  @override
  String get locationServiceDisabled => 'Ubicación desactivada';

  @override
  String get locationPermissionDenied =>
      'Por favor, permita la autorización de localización para recuperar su posición actual.';

  @override
  String get locationPermissionDeniedPermanently =>
      'La autorización de localización está denegada permanentemente. Permite la autorización de ubicación en la configuración de tu dispositivo para recuperar tu posición actual.';

  @override
  String get lastSync => 'Última sincronización';

  @override
  String get dhcpActive => 'DHCP activo';

  @override
  String get ipAddress => 'IP';

  @override
  String get subnetMask => 'Máscara de subred';

  @override
  String get standardGateway => 'Puerta de enlace predeterminada';

  @override
  String get dns => 'DNS';

  @override
  String get alternateDNS => 'DNS alternativo';

  @override
  String get errorNoNetworksFound => 'No se ha encontrado ninguna red wifi';

  @override
  String get availableNetworks => 'Redes disponibles';

  @override
  String get enableWifiInterface => 'Habilitar interfaz WiFi';

  @override
  String get enableLANInterface => 'Habilitar interfaz LAN';

  @override
  String get hintDontDisableAllInterfaces =>
      'Asegúrese de que no todas las interfaces están desactivadas. La última interfaz activada tiene prioridad.';

  @override
  String get ssid => 'SSID';

  @override
  String get searchNetworks => 'Buscar redes WiFi';

  @override
  String get errorNoNetworkEnabled =>
      'Al menos un interfaz debe estar activado.';

  @override
  String get errorActiveNetworkInvalid =>
      'No todas las estaciones activas son válidas';

  @override
  String get invalidNetworkConfiguration => 'Configuración de red no válida';

  @override
  String get generalDefault => 'Estandar';

  @override
  String get mqttHeader => 'MQTT';

  @override
  String get mqttConnected => 'Conectado al broker MQTT';

  @override
  String get mqttDisconnected => 'No hay conexión con el broker MQTT';

  @override
  String get mqttBrokerURI => 'Broker URI';

  @override
  String get mqttBrokerURIHint => 'Broker-MQTT URI';

  @override
  String get mqttPort => 'Puerto';

  @override
  String get mqttPortHint => 'Puerto MQTT';

  @override
  String get mqttClientId => 'ID de cliente';

  @override
  String get mqttClientIdHint => 'MQTT Cliente-ID';

  @override
  String get mqttUsername => 'Nombre de usuario';

  @override
  String get mqttUsernameHint => 'Nombre de usuario MQTT';

  @override
  String get mqttPassword => 'Contraseña';

  @override
  String get mqttPasswordHint => 'Contraseña MQTT';

  @override
  String get mqttCertificate => 'Certificado (opcional)';

  @override
  String get mqttCertificateHint => 'Certificado MQTT';

  @override
  String get mqttTopic => 'Tema';

  @override
  String get mqttTopicHint => 'Tema MQTT';

  @override
  String get electricityMeter => 'Contador de corriente';

  @override
  String get electricityMeterCurrent => 'Actual';

  @override
  String get electricityMeterHistory => 'Historia';

  @override
  String get electricityMeterReading => 'Lectura del contador';

  @override
  String get connectivity => 'Conectividad';

  @override
  String electricMeter(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'contadores',
      one: 'contador',
    );
    return '$_temp0 ';
  }

  @override
  String get discoveryZGW16Description => 'Modbus-Energy-Meters-MQTT-Gateway';

  @override
  String get bluetoothConnectionLost => 'Conexión Bluetooth perdida';

  @override
  String get bluetoothConnectionLostDescription =>
      'La conexión Bluetooth con el dispositivo se ha interrumpido. Asegúrese de que el dispositivo está dentro del alcance.';

  @override
  String get openBluetoothSettings => 'Abrir la configuración';

  @override
  String get password => 'Contraseña';

  @override
  String get setInitialPassword => 'Establecer contraseña inicial';

  @override
  String initialPasswordMinimumLength(Object length) {
    return 'La contraseña debe tener al menos $length caracteres';
  }

  @override
  String get repeatPassword => 'Repetir contraseña';

  @override
  String get passwordsDoNotMatch => 'Las contraseñas no coinciden';

  @override
  String get savePassword => 'Guardar contraseña';

  @override
  String get savePasswordHint =>
      'La contraseña se guarda para futuras conexiones en tu dispositivo.';

  @override
  String get retrieveNtpServer => 'Recuperar la hora del servidor NTP';

  @override
  String get retrieveNtpServerFailed =>
      'No se ha podido establecer la conexión con el servidor NTP.';

  @override
  String get retrieveNtpServerSuccess =>
      'La conexión con el servidor NTP se ha realizado correctamente.';

  @override
  String get settingsPasswordNewPasswordDescription =>
      'Introducir nueva contraseña';

  @override
  String get settingsPasswordConfirmationDescription =>
      'Cambio de contraseña correcto';

  @override
  String get dhcpRangeStart => 'Rango de Inicio DHCP';

  @override
  String get dhcpRangeEnd => 'Rango final DHCP';

  @override
  String get forwardOnMQTT => 'Reenviar a MQTT';

  @override
  String get showAll => 'Mostrar todo';

  @override
  String get hide => 'Ocultar';

  @override
  String get changeToAPMode => 'Cambiar a modo AP';

  @override
  String get changeToAPModeDescription =>
      'Está a punto de conectar su dispositivo a una red WiFi, en este caso la conexión con el dispositivo se desconecta y debe volver a conectarse a su dispositivo a través de la red configurada.';

  @override
  String get consumption => 'Consumo';

  @override
  String get currentDay => 'Día actual';

  @override
  String get twoWeeks => '2 semanas';

  @override
  String get oneYear => '1 año';

  @override
  String get threeYears => '3 años';

  @override
  String passwordMinLength(Object length) {
    return 'La contraseña debe tener al menos $length caracteres. ';
  }

  @override
  String get passwordNeedsLetter => 'La contraseña debe contener una letra.';

  @override
  String get passwordNeedsNumber => 'La contraseña debe contener un número.';

  @override
  String get portEmpty => 'El puerto no puede estar vacío';

  @override
  String get portInvalid => 'Puerto no válido';

  @override
  String portOutOfRange(Object rangeEnd, Object rangeStart) {
    return 'El puerto debe estar entre $rangeStart y $rangeEnd. ';
  }

  @override
  String get ipAddressEmpty => 'La dirección IP no puede estar vacía';

  @override
  String get ipAddressInvalid => 'Dirección IP no válida';

  @override
  String get subnetMaskEmpty => 'La máscara de subred no puede estar vacía';

  @override
  String get subnetMaskInvalid => 'Máscara de subred no válida';

  @override
  String get gatewayEmpty => 'La pasarela no puede estar vacía';

  @override
  String get gatewayInvalid => 'Pasarela no válida';

  @override
  String get dnsEmpty => 'DNS no puede estar vacío';

  @override
  String get dnsInvalid => 'DNS no válido';

  @override
  String get uriEmpty => 'El URI no puede estar vacío';

  @override
  String get uriInvalid => 'URI no válido';

  @override
  String get electricityMeterChangedSuccessfully =>
      'Contador de corriente cambiado con éxito';

  @override
  String get networkChangedSuccessfully =>
      'Configuración de red modificada correctamente';

  @override
  String get mqttChangedSuccessfully =>
      'Configuración MQTT modificada correctamente';

  @override
  String get modbusChangedSuccessfully =>
      'Configuración Modbus modificada correctamente';

  @override
  String get loginData => 'Borrar datos de acceso';

  @override
  String get valueConfigured => 'Configurado';

  @override
  String get electricityMeterHistoryNoData => 'No hay datos disponibles';

  @override
  String get locationChangedSuccessfully =>
      'Ubicación modificada correctamente';

  @override
  String get settingsNameFailEmpty => 'El nombre no puede estar vacío';

  @override
  String settingsNameFailLength(Object length) {
    return 'El nombre no debe tener más de $length caracteres.';
  }

  @override
  String get solsticeChangedSuccesfully =>
      'Ajustes de solsticio modificados correctamente';

  @override
  String get relayFunctionChangedSuccesfully =>
      'Relé-Función cambiada con éxito';

  @override
  String get relayFunctionHeader => 'Función relé';

  @override
  String get dimmerValueChangedSuccesfully =>
      'El comportamiento de encendido se ha modificado correctamente';

  @override
  String get dimmerBehaviourChangedSuccesfully =>
      'Se ha modificado correctamente el comportamiento de regulación';

  @override
  String get dimmerBrightnessDescription =>
      'La luminosidad mínima y máxima afecta a todas las luminosidades regulables del regulador.';

  @override
  String get dimmerSettingsChangedSuccesfully =>
      'Configuración básica modificada correctamente';

  @override
  String get liveUpdateEnabled => 'Prueba en directo habilitada';

  @override
  String get liveUpdateDisabled => 'Prueba en vivo desactivada';

  @override
  String get liveUpdateDescription =>
      'Se envía al dispositivo el último valor modificado del deslizador.';

  @override
  String get demoDevices => 'Dispositivos de demostración';

  @override
  String get showDemoDevices => 'Mostrar dispositivos de demostración';

  @override
  String get deviceCategoryTimeSwitch => 'Interruptor horario';

  @override
  String get deviceCategoryMultifunctionalRelay =>
      'Temporizador con multifunción';

  @override
  String get deviceCategoryDimmer => 'Regulador de intensidad';

  @override
  String get deviceCategoryShutter => 'Actuador de persianas y toldos';

  @override
  String get deviceCategoryRelay => 'Relé';

  @override
  String get search => 'Buscar';

  @override
  String get configurationsHeader => 'Configuraciones';

  @override
  String get configurationsDescription => 'Gestione aquí sus configuraciones.';

  @override
  String get configurationsNameFailEmpty =>
      'El nombre de la configuración no puede estar vacío';

  @override
  String get configurationDeleted => 'Configuración eliminada';

  @override
  String codeFound(Object codeType) {
    return '$codeType Código reconocido';
  }

  @override
  String get errorCameraPermission =>
      'Por favor, permita el acceso a la cámara para escanear el código ELTAKO.';

  @override
  String get authorizationSuccessful =>
      'Autorizado con éxito en el dispositivo';

  @override
  String get wifiAuthenticationResetConfirmationDescription =>
      'La autenticación se ha restablecido correctamente.';

  @override
  String get settingsResetConnectionHeader => 'Restablecer conexión';

  @override
  String get settingsResetConnectionDescription =>
      '¿De verdad quieres restablecer la conexión?';

  @override
  String get settingsResetConnectionConfirmationDescription =>
      'La conexión se ha restablecido correctamente.';

  @override
  String get wiredInputChangedSuccesfully =>
      'Se ha modificado correctamente el comportamiento del pulsador';

  @override
  String get runtimeChangedSuccesfully =>
      'Se ha modificado correctamente el comportamiento en tiempo de ejecución';

  @override
  String get expertModeActivated => 'Modo experto activado';

  @override
  String get expertModeDeactivated => 'Modo experto desactivado';

  @override
  String get license => 'Licencias';

  @override
  String get retry => 'Reintentar';

  @override
  String get provisioningConnectingHint =>
      'Se está estableciendo la conexión con el dispositivo. Esto puede tardar hasta 1 minuto.';

  @override
  String get serialnumberEmpty => 'El número de serie no puede estar vacío';

  @override
  String get interfaceStateInactiveDescriptionBLE =>
      'Bluetooth está desactivado, por favor, actívelo para encontrar dispositivos Bluetooth.';

  @override
  String get interfaceStateDeniedDescriptionBLE =>
      'No se concedieron los permisos Bluetooth.';

  @override
  String get interfaceStatePermanentDeniedDescriptionBLE =>
      'No se han concedido los permisos de Bluetooth. Por favor, habilítalos en la configuración de tu dispositivo.';

  @override
  String get requestPermission => 'Solicitar permiso';

  @override
  String get goToSettings => 'Ir a la configuración';

  @override
  String get enableBluetooth => 'Activar bluetooth';

  @override
  String get installed => 'Instalado';

  @override
  String teachInDialogDescription(Object type) {
    return '¿Le gustaría enseñar en su dispositivo a través de $type?';
  }

  @override
  String get useMatter => 'Utilizar Matter';

  @override
  String get relayMode => 'Activar modo relé';

  @override
  String get whatsNew => 'Novedades de esta versión';

  @override
  String get migrationHint =>
      'Es necesaria una migración de tus datos para utilizar las nuevas funciones.';

  @override
  String get migrationHeader => 'Migración';

  @override
  String get migrationProgress => 'Migración en curso...';

  @override
  String get letsGo => '¡Vamos!';

  @override
  String get noDevicesFound =>
      'No se han encontrado dispositivos. Comprueba si tu dispositivo está en modo de emparejamiento.';

  @override
  String get interfaceStateEmpty => 'No se han encontrado dispositivos';

  @override
  String get ssidEmpty => 'SSID no puede estar vacío';

  @override
  String get passwordEmpty => 'La contraseña no puede estar vacía';

  @override
  String get settingsDeleteSettingsHeader => 'Restablecer ajustes';

  @override
  String get settingsDeleteSettingsDescription =>
      '¿De verdad quieres restablecer todos los ajustes?';

  @override
  String get settingsDeleteSettingsConfirmationDescription =>
      'Todos los ajustes se han restablecido correctamente.';

  @override
  String get locationNotFound => 'Ubicación no encontrada';

  @override
  String get timerProgramEmptySaveHint =>
      'El programa de tiempo está vacío y no se puede guardar. ¿ Finalizar la edición ?';

  @override
  String get timerProgramDaysEmptySaveHint =>
      'No hay días seleccionados. ¿Quieres guardar el programa de tiempo de todos modos?';

  @override
  String get timeProgramNoDays =>
      'Un programa sin días activos no puede activarse.';

  @override
  String timeProgramColliding(Object program) {
    return 'El programa de tiempo colisiona con el programa $program.';
  }

  @override
  String timeProgramDuplicated(Object program) {
    return 'El programa de tiempo es un duplicado del programa $program.';
  }

  @override
  String get screenshotZgw16 => 'Casa unifamiliar';

  @override
  String get interfaceStateUnknown => 'No se han encontrado dispositivos';

  @override
  String get settingsPinChange => 'Cambiar PIN';

  @override
  String get timeProgrammOneTime => 'una sola vez';

  @override
  String get timeProgrammRepeating => 'repitiendo';

  @override
  String get generalIgnore => 'Ignore';

  @override
  String get timeProgramChooseDay => 'Elija el día';

  @override
  String get generalToday => 'Hoy';

  @override
  String get generalTomorrow => 'Mañana';

  @override
  String get bluetoothAndPINChangedSuccessfully =>
      'Bluetooth y PIN han sido cambiados con éxito.';

  @override
  String get generalTextDimTime => 'Tiempo de regulación';

  @override
  String get discoverySu62Description =>
      'Interruptor horario Bluetooth de 1 canal';

  @override
  String get bluetoothAlwaysOnTitle => 'ON permanente';

  @override
  String get bluetoothAlwaysOnDescription =>
      'Bluetooth está permanentemente activado.';

  @override
  String get bluetoothAlwaysOnHint =>
      'Nota: ¡Si este ajuste está activado, el dispositivo estará permanentemente visible para todo el mundo a través de Bluetooth! Se recomienda cambiar el PIN predeterminado.';

  @override
  String get bluetoothManualStartupOnTitle => 'Temporal on';

  @override
  String get bluetoothManualStartupOnDescription =>
      'Después de conectar la alimentación, el Bluetooth se activa durante 3 minutos.';

  @override
  String get bluetoothManualStartupOnHint =>
      'Nota: El modo de acoplamiento está activado durante 3 minutos y luego se apaga. Si se desea establecer una nueva conexión, debe pulsarse el pulsador durante aprox. 5 segundos.';

  @override
  String get bluetoothManualStartupOffTitle => 'Manualmente ON';

  @override
  String get bluetoothManualStartupOffDescription =>
      'El Bluetooth se activa manualmente mediante la entrada de pulsador.';

  @override
  String get bluetoothManualStartupOffHint =>
      'Nota: Para activar el Bluetooth, un pulsador conectado en la entrada de pulsadores tiene que estar pulsado durante aprox. 5 segundos.';

  @override
  String get timeProgrammOneTimeRepeatingDescription =>
      'Los programas pueden ejecutarse repetidamente realizando siempre una conmutación en los días y horas configurados, o pueden ejecutarse una sola vez al momento de conmutación configurada.';

  @override
  String versionHeader(Object version) {
    return 'Versión $version';
  }

  @override
  String get releaseNotesHeader => 'Notas de publicación';

  @override
  String get release30Header => 'Ya está aquí la nueva App Eltako Connect.';

  @override
  String get release30FeatureDesignHeader => 'Nuevo diseño';

  @override
  String get release30FeatureDesignDescription =>
      'La App ha sido completamente revisada y tiene un nuevo diseño. Ahora es aún más fácil e intuitiva de usar.';

  @override
  String get release30FeaturePerformanceHeader => 'Rendimiento mejorado';

  @override
  String get release30FeaturePerformanceDescription =>
      'Disfrute de una experiencia más fluida y de tiempos de carga reducidos, para una experiencia de usuario óptima.';

  @override
  String get release30FeatureConfigurationHeader =>
      'Configuraciones entre dispositivos';

  @override
  String get release30FeatureConfigurationDescription =>
      'Guarde las configuraciones de los dispositivos y transfiéralas a otros dispositivos. Aunque no tengan el mismo hardware, puede, por ejemplo, transferir la configuración de su S2U12DBT1+1-UC a un ASSU-BT o viceversa.';

  @override
  String get release31Header =>
      '¡Ya está aquí el nuevo interruptor horario empotrable de 1 canal con Bluetooth!';

  @override
  String get release31Description => '¿Qué puede hacer el SU62PF-BT/UC?';

  @override
  String get release31SU62Name => 'SU62PF-BT/UC';

  @override
  String get release31DeviceNote1 => 'Hasta 60 programas de conmutación';

  @override
  String get release31DeviceNote2 =>
      'El reloj puede conmutar los dispositivos según horas fijas o mediante la función astro basada en la salida y la puesta del sol.';

  @override
  String get release31DeviceNote3 =>
      'Modo aleatorio: Los tiempos de conmutación pueden desplazarse aleatoriamente hasta 15 minutos.';

  @override
  String get release31DeviceNote4 =>
      'Cambio de horario de verano/invierno: El reloj cambia automáticamente al horario de verano o invierno.';

  @override
  String get release31DeviceNote5 =>
      'Tensión universal de alimentación y control de 12-230V UC.';

  @override
  String get release31DeviceNote6 =>
      'Entrada de pulsador para una conmutación manual.';

  @override
  String get release31DeviceNote7 =>
      '1 contacto NA libre de potencial 10 A/250 V AC.';

  @override
  String get release31DeviceNote8 => 'Ejecutar programas horarios una vez.';

  @override
  String get generalNew => 'Novedades';

  @override
  String yearsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count anos atras',
      one: 'Último año',
    );
    return '$_temp0';
  }

  @override
  String monthsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count meses atrás',
      one: 'Último mes',
    );
    return '$_temp0';
  }

  @override
  String weeksAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count semanas atrás',
      one: 'Última semana',
    );
    return '$_temp0';
  }

  @override
  String daysAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count dias atrás',
      one: 'ayer',
    );
    return '$_temp0';
  }

  @override
  String minutesAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count minutos',
      one: 'hace un minuto',
    );
    return '$_temp0';
  }

  @override
  String hoursAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count horas',
      one: 'Hace una hora',
    );
    return '$_temp0';
  }

  @override
  String secondsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count segundos',
      one: 'hace un segundo',
    );
    return '$_temp0';
  }

  @override
  String get justNow => 'Ahora mismo';

  @override
  String get discoveryEsripmDescription => 'Telerruptor-Relé IP Matter';

  @override
  String get generalTextKidsRoom => 'Función de atenuación automática';

  @override
  String generalDescriptionKidsRoom(Object mode) {
    return 'Al encender ($mode), la luz se enciende con el valor de luminosidad más bajo después de aprox. 1 segundo y se va atenuando lentamente mientras se siga pulsando el pulsador, sin modificar el último valor de intensidad memorizado.';
  }

  @override
  String get generalTextSceneButton => 'Pulsador de escenas';

  @override
  String get settingsEnOceanConfigHeader => 'Configuración EnOcean';

  @override
  String get enOceanConfigChangedSuccessfully =>
      'La configuración de EnOcean se ha modificado correctamente';

  @override
  String get activateEnOceanRepeater => 'Activar repetidor EnOcean';

  @override
  String get enOceanRepeaterLevel => 'Nivel del repetidor';

  @override
  String get enOceanRepeaterLevel1 => 'Modo-1';

  @override
  String get enOceanRepeaterLevel2 => 'Modo-2';

  @override
  String get enOceanRepeaterOffDescription =>
      'No se repiten señales inalámbricas de los sensores.';

  @override
  String get enOceanRepeaterLevel1Description =>
      'Sólo las señales inalámbricas de los sensores se reciben, comprueban y reenvían a plena potencia de transmisión. Las señales inalámbricas de otros repetidores se ignoran para reducir la cantidad de datos.';

  @override
  String get enOceanRepeaterLevel2Description =>
      'Además de las señales inalámbricas de los sensores, también se procesan las señales inalámbricas de los repetidores de modo-1. Así, una señal inalámbrica puede recibirse y amplificarse un máximo de dos veces. Los repetidores inalámbricos no necesitan ser programados. Reciben y amplifican las señales inalámbricas de todos los sensores inalámbricos en su alcance de recepción.';

  @override
  String get settingsSensorHeader => 'Sensores';

  @override
  String get sensorChangedSuccessfully => 'Sensores cambiados con éxito';

  @override
  String get wiredButton => 'Pulsador cableado';

  @override
  String get enOceanId => 'EnOcean-ID';

  @override
  String get enOceanAddManually => 'Introducir o escanear la EnOcean-ID';

  @override
  String get enOceanIdInvalid => 'EnOcean-ID no válido';

  @override
  String get enOceanAddAutomatically => 'Enlace con telegrama EnOcean';

  @override
  String get enOceanAddDescription =>
      'El protocolo inalámbrico EnOcean permite enlazar y utilizar pulsadores en tu actuador.\n\nElija entre el enlazamiento automático con un telgramma EnOcean para enlazar pulsadores con solo una pulsación o seleccione la opción manual para escanear o escribir el ID EnOcean de tu pulsador.';

  @override
  String get enOceanTelegram => 'Telegrama';

  @override
  String enOceanCodeScan(Object sensorType) {
    return 'tIntroduzca el EnOcean-ID de su $sensorType o escanee el EnOcean-QR-Code de tu $sensorType, para añadirlo';
  }

  @override
  String get enOceanCode => 'Código QR EnOcean';

  @override
  String enOceanCodeScanDescription(Object sensorType) {
    return 'Busca el código EnOcean en tu $sensorType y escanéalo con tu cámara.';
  }

  @override
  String get enOceanButton => 'Pulsador EnOcean';

  @override
  String get enOceanBackpack => 'Adaptador EnOcean';

  @override
  String get sensorNotAvailable => 'Aún no se ha enlazado ningún sensor';

  @override
  String get sensorAdd => 'Añadir sensores';

  @override
  String get sensorCancel => 'Cancelar el enlazamiento';

  @override
  String get sensorCancelDescription =>
      '¿De verdad quieres cancelar el enlazamiento?';

  @override
  String get getEnOceanBackpack => 'Pida su adaptador EnOcean';

  @override
  String get enOceanBackpackMissing =>
      'Para entrar en el fantástico mundo de la conectividad y la comunicación perfectas, necesita un adaptador EnOcean.\nHaga clic aquí para obtener más información';

  @override
  String sensorEditChangedSuccessfully(Object sensorName) {
    return '$sensorName cambiado con éxito';
  }

  @override
  String sensorConnectedVia(Object deviceName) {
    return 'conectado a través de $deviceName';
  }

  @override
  String get lastSeen => 'Visto por última vez';

  @override
  String get setButtonOrientation => 'Determinar la orientación';

  @override
  String get setButtonType => 'Determinar el tipo del pulsador';

  @override
  String get button1Way => 'Pulsador de 1 canal';

  @override
  String get button2Way => 'Pulsador de 2 canales';

  @override
  String get button4Way => 'Pulsador de 4 canales';

  @override
  String get buttonUnset => 'no ocupado';

  @override
  String get button => 'Pulsador';

  @override
  String get sensor => 'Sensor';

  @override
  String sensorsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count sensores encontrados',
      one: '1 sensor encontrado',
      zero: 'No se encontraron sensores',
    );
    return '$_temp0';
  }

  @override
  String get sensorSearch => 'Búsqueda de sensores';

  @override
  String get searchAgain => 'Buscar de nuevo';

  @override
  String sensorTeachInHeader(Object sensorType) {
    return 'Enlazar en $sensorType';
  }

  @override
  String sensorChooseHeader(Object sensorType) {
    return 'Seleccione $sensorType';
  }

  @override
  String get sensorChooseDescription => 'Elija un pulsador para enlazar en';

  @override
  String get sensorCategoryDescription =>
      'Seleccione la categoría del sensor que desea añadir.';

  @override
  String get sensorName => 'Nombre del pulsador';

  @override
  String get sensorNameFooter => 'Ponga nombre a su pulsador';

  @override
  String sensorAddedSuccessfully(Object sensorName) {
    return '$sensorName se ha añadido correctamente';
  }

  @override
  String sensorDelete(Object sensorType) {
    return 'Borrar $sensorType';
  }

  @override
  String sensorDeleteHint(Object sensorName, Object sensorType) {
    return '¿Realmente desea eliminar el $sensorType? $sensorName?';
  }

  @override
  String sensorDeletedSuccessfully(Object sensorName) {
    return '$sensorName se ha eliminado correctamente';
  }

  @override
  String get buttonTapDescription => 'Pulse el pulsador que desea enlazar.';

  @override
  String get waitingForTelegram => 'El actuador espera el telegrama';

  @override
  String get copied => 'Copiado';

  @override
  String pairingFailed(Object sensorType) {
    return '$sensorType ya enlazado';
  }

  @override
  String get generalDescriptionUniversalbutton =>
      'En caso de pulsador universal, la dirección se invierte soltando brevemente el botón. Los comandos de control cortos encienden o apagan.';

  @override
  String get generalDescriptionDirectionalbutton =>
      'En caso de pulsador direccional es \"encender y atenuar\" en la parte superior y \"apagar y atenuar\" en la parte inferior.';

  @override
  String get matterForwardingDescription =>
      'La pulsación del pulsador se reenvía a Matter.';

  @override
  String get none => 'Ninguno';

  @override
  String get buttonNoneDescription =>
      'El pulsador no tiene ninguna funcionalidad';

  @override
  String get buttonUnsetDescription =>
      'El pulsador no tiene ningún comportamiento establecido';

  @override
  String get sensorButtonTypeChangedSuccessfully =>
      'Tipo de pulsador cambiado correctamente';

  @override
  String forExample(Object example) {
    return 'p.e. $example';
  }

  @override
  String get enOceanQRCodeInvalidDescription =>
      'Sólo posible a partir de la fecha de fabricación 44/20';

  @override
  String get input => 'Entrada';

  @override
  String get buttonSceneValueOverride =>
      'sobrescribir valor del pulsador de escena';

  @override
  String get buttonSceneValueOverrideDescription =>
      'El valor del pulsador de escena se sobrescribirá con el valor de atenuación actual con una pulsación larga del botón';

  @override
  String get buttonSceneDescription =>
      'El pulsador de escena se enciende con un valor de atenuación específico';

  @override
  String get buttonPress => 'Pulsación';

  @override
  String get triggerOn =>
      'con accionamiento prolongado del pulsador universal o del pulsador direccional en el lado de encendido';

  @override
  String get triggerOff =>
      'con doble pulsación en el pulsador universal o en el pulsador direccional del lado de desconexión';

  @override
  String get centralOn => 'Central On';

  @override
  String get centralOff => 'Central Off';

  @override
  String get centralButton => 'pulsador de control central';

  @override
  String get enOceanAdapterNotFound =>
      'No se ha encontrado ningún adaptador EnOcean';

  @override
  String get updateRequired => 'Actualización necesaria';

  @override
  String get updateRequiredDescription =>
      'Tu APP necesita una actualización para ser compatible con este nuevo dispositivo.';

  @override
  String get release32Header =>
      'Ya está disponible la nueva serie 64 con Matter y EnOcean y el nuevo interruptor horario empotrable Bluetooth SU62PF-BT/UC.';

  @override
  String get release32EUD64Header =>
      '¡Ya está aquí el nuevo dimmer empotrable de 1 canal con Matter sobre Wi-Fi y hasta 300W!';

  @override
  String get release32EUD64Note1 =>
      'Configuración de la velocidad de atenuación, velocidad de encendido/apagado, encender con intensidad mínima/atenuación automática y mucho más.';

  @override
  String get release32EUD64Note2 =>
      'La funcionalidad del EUD64NPN-IPM puede ampliarse mediante adaptadores, como el adaptador EnOcean EOA64.';

  @override
  String get release32EUD64Note3 =>
      'En combinación con el adaptador EnOcean EOA64, se pueden enlazarse directamente hasta 30 pulsadores inalámbricos EnOcean al EUD64NPN-IPM y reenviarlos a Matter.';

  @override
  String get release32EUD64Note4 =>
      'Las dos entradas para pulsadores cableadas pueden enlazarse directamente con el EUD64NPN-IPM o reenviarse a Matter.';

  @override
  String get release32ESR64Header =>
      'Ya está aquí el nuevo actuador empotrado de conmutación con 1 canal, libre de potencial, con Matter vía Wi-Fi y hasta 16 A.';

  @override
  String get release32ESR64Note1 =>
      'Configuración de varias funciones, como telerruptor (ES), función de relé (ER), NC (ER-Inverse), y mucho más.';

  @override
  String get release32ESR64Note2 =>
      'La funcionalidad del ESR64PF-IPM puede ampliarse mediante adaptadores, como el adaptador EnOcean EOA64.';

  @override
  String get release32ESR64Note3 =>
      'Se pueden conectar directamente hasta 30 pulsadores inalámbricos EnOcean al ESR64PF-IPM en combinación con el adaptador EnOcean EOA64 y reenviarlos a Matter.';

  @override
  String get release32ESR64Note4 =>
      'Una entrada para pulsadores cableadas pueden enlazarse directamente con el EUD64NPN-IPM o reenviarse a Matter.';

  @override
  String buttonsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count pulsadores encontrados',
      one: '1 pulsador encontrado',
      zero: 'Ningún pulsador encontrado',
    );
    return '$_temp0';
  }

  @override
  String get doubleImpuls => 'con un impulso doble';

  @override
  String get impulseDescription =>
      'Si el canal está encendido, se apaga mediante un impulso.';

  @override
  String get locationServiceEnable => 'Activar ubicación';

  @override
  String get locationServiceDisabledDescription =>
      'La localización está desactivada. La versión de tu sistema operativo necesita la ubicación para poder encontrar dispositivos Bluetooth.';

  @override
  String get locationPermissionDeniedNoPosition =>
      'No se han concedido permisos de localización. La versión de tu sistema operativo requiere permisos de localización para poder encontrar dispositivos Bluetooth. Por favor, permite el permiso de localización en los ajustes de tu dispositivo.';

  @override
  String get interfaceStatePermanentDeniedDescriptionDevicesAround =>
      'No se ha concedido el permiso de dispositivos cercanos. Por favor, habilita el permiso en los ajustes de tu dispositivo.';

  @override
  String get permissionNearbyDevices => 'Dispositivos cercanos';

  @override
  String get release320Header =>
      'Ya está aquí el nuevo y más potente dimmer universal EUD12NPN-BT/600W-230V.';

  @override
  String get release320EUD600Header =>
      '¿Qué puede hacer el nuevo dimmer universal?';

  @override
  String get release320EUD600Note1 =>
      'Regulador de intensidad universal con una potencia de hasta 600 W';

  @override
  String get release320EUD600Note2 =>
      'Ampliable con la extensión de potencia LUD12 hasta 3800 W';

  @override
  String get release320EUD600Note3 =>
      'Accionamiento local con pulsador universal o direccional';

  @override
  String get release320EUD600Note4 => 'Funciones centrales On / Off';

  @override
  String get release320EUD600Note5 =>
      'Entrada de detector de movimiento para mayor comodidad';

  @override
  String get release320EUD600Note6 =>
      'Temporizador integrado con 10 programas de conmutación';

  @override
  String get release320EUD600Note7 => 'Función astronómica';

  @override
  String get release320EUD600Note8 => 'Luminosidad de encendido individual';

  @override
  String get mqttClientCertificate => 'Certificado de cliente';

  @override
  String get mqttClientCertificateHint => 'Certificado de cliente MQTT';

  @override
  String get mqttClientKey => 'Clave de cliente';

  @override
  String get mqttClientKeyHint => 'Clave de cliente MQTT';

  @override
  String get mqttClientPassword => 'Contraseña de cliente';

  @override
  String get mqttClientPasswordHint => 'Contraseña de cliente MQTT';

  @override
  String get mqttEnableHomeAssistantDiscovery =>
      'Activar la detección MQTT de HomeAssistant';

  @override
  String get modbusTcp => 'Modbus TCP';

  @override
  String get enableInterface => 'Habilitar interfaz';

  @override
  String get busAddress => 'Dirección del autobús';

  @override
  String busAddressWithAddress(Object index) {
    return 'Dirección de bus $index';
  }

  @override
  String get deviceType => 'Tipo de dispositivo';

  @override
  String registerTable(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Tabla de registro',
      one: 'Tabla de registro',
    );
    return '$_temp0';
  }

  @override
  String get currentValues => 'Valores actuales';

  @override
  String get requestRTU => 'Solicitar RTU';

  @override
  String get requestPriority => 'Solicitar prioridad';

  @override
  String get mqttForwarding => 'Reenvío a MQTT';

  @override
  String get historicData => 'Datos históricos';

  @override
  String get dataFormat => 'Formato de los datos';

  @override
  String get dataType => 'Tipo de datos';

  @override
  String get description => 'Descripción';

  @override
  String get readWrite => 'Lectura/Escritura';

  @override
  String get unit => 'Unidad';

  @override
  String get registerTableReset => 'Restablecer tabla de registros';

  @override
  String get registerTableResetDescription =>
      '¿Debe restablecerse realmente la tabla de registros?';

  @override
  String get notConfigured => 'No configurado';

  @override
  String get release330ZGW16Header =>
      'Actualización importante para el ZGW16WL-IP';

  @override
  String get release330Header =>
      'El ZGW16WL-IP con hasta 16 contadores de electricidad';

  @override
  String get release330ZGW16Note1 =>
      'Admite hasta 16 contadores de electricidad ELTAKO Modbus';

  @override
  String get release330ZGW16Note2 => 'Compatibilidad con Modbus TCP';

  @override
  String get release330ZGW16Note3 => 'Compatibilidad con MQTT Discovery';

  @override
  String get screenshotButtonLivingRoom => 'Pulsador de salón';

  @override
  String get registerChangedSuccessfully => 'Registro modificado correctamente';

  @override
  String get serverCertificateEmpty =>
      'El certificado del servidor no puede estar vacío';

  @override
  String get registerTemplates => 'Plantillas de registro';

  @override
  String get registerTemplateChangedSuccessfully =>
      'Plantilla de registro modificada correctamente';

  @override
  String get registerTemplateReset => 'Restablecer plantilla de registro';

  @override
  String get registerTemplateResetDescription =>
      '¿Debe restablecerse realmente la plantilla de registro?';

  @override
  String get registerTemplateNotAvailable =>
      'No hay plantillas de registro disponibles';

  @override
  String get rename => 'Cambie el nombre de';

  @override
  String get registerName => 'Nombre de registro';

  @override
  String get registerRenameDescription =>
      'Introduzca un nombre personalizado para el registro';

  @override
  String get restart => 'Reiniciar el dispositivo';

  @override
  String get restartDescription =>
      '¿Realmente quieres reiniciar el dispositivo?';

  @override
  String get restartConfirmationDescription =>
      'El dispositivo se está reiniciando';

  @override
  String get deleteAllElectricityMeters =>
      'Borrar todos los contadores de electricidad';

  @override
  String get deleteAllElectricityMetersDescription =>
      '¿De verdad quiere borrar todos los contadores de electricidad?';

  @override
  String get deleteAllElectricityMetersConfirmationDescription =>
      'Todos los contadores de electricidad se han eliminado correctamente';

  @override
  String get resetAllElectricityMeters =>
      'Restablecer todas las configuraciones de los contadores de electricidad';

  @override
  String get resetAllElectricityMetersDescription =>
      '¿De verdad quieres restablecer todas las configuraciones de los contadores de electricidad?';

  @override
  String get resetAllElectricityMetersConfirmationDescription =>
      'Se han restablecido correctamente todas las configuraciones de los contadores de electricidad';

  @override
  String get deleteElectricityMeterHistories =>
      'Borrar todos los historiales de los contadores de electricidad';

  @override
  String get deleteElectricityMeterHistoriesDescription =>
      '¿De verdad quieres borrar todos los historiales de los contadores de la luz?';

  @override
  String get deleteElectricityMeterHistoriesConfirmationDescription =>
      'Se han borrado correctamente todos los historiales de los contadores de electricidad';

  @override
  String get multipleElectricityMetersSupportMissing =>
      'Su dispositivo sólo admite actualmente un contador de electricidad. Por favor, actualice su firmware.';

  @override
  String get consumptionWithUnit => 'Consumo (kWh)';

  @override
  String get exportWithUnit => 'Entrega (kWh)';

  @override
  String get importWithUnit => 'Consumo (kWh)';

  @override
  String get resourceWarningHeader => 'Limitación de recursos';

  @override
  String mqttAndTcpResourceWarning(Object protocol) {
    return 'Operar MQTT y Modbus TCP al mismo tiempo no es posible debido a los recursos limitados del sistema. Desactive $protocol en primer lugar.';
  }

  @override
  String get mqttEnabled => 'MQTT habilitado';

  @override
  String get redirectMQTT => 'Ir a Configuración MQTT';

  @override
  String get redirectModbus => 'Ir a Configuración Modbus';

  @override
  String get unsupportedSettingDescription =>
      'Con la versión actual del firmware, algunos de los ajustes del dispositivo no son compatibles. Actualiza tu firmware para utilizar las nuevas funciones.';

  @override
  String get updateNow => 'Actualizar ahora';

  @override
  String get zgw241Hint =>
      'Con esta actualización, Modbus TCP está activado por defecto y MQTT está desactivado. Esto se puede cambiar en la configuración. Con soporte para hasta 16 contadores, se han realizado muchas optimizaciones; esto puede provocar cambios en la configuración del dispositivo. Por favor, reinicie el dispositivo después de ajustar la configuración.';

  @override
  String get deviceConfigChangedSuccesfully =>
      'Se ha modificado correctamente el comportamiento en tiempo de ejecución';

  @override
  String get deviceConfiguration => 'Configuración del dispositivo';

  @override
  String get tiltModeToggle => 'Modo de inclinación';

  @override
  String get tiltModeToggleFooter =>
      'Si el dispositivo se configura en Matter, todas las funciones deben reconfigurarse allí.';

  @override
  String get shaderMovementDirection => 'Marcha atrás Arriba/Abajo';

  @override
  String get shaderMovementDirectionDescription =>
      'Invertir la dirección del movimiento arriba/abajo del motor';

  @override
  String get tiltTime => 'Tiempo de ejecución de la inclinación';

  @override
  String changeTiltModeDialogTitle(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Activar',
      'false': 'Desactivar',
      'other': 'Cambiar',
    });
    return '$_temp0 función de inclinación';
  }

  @override
  String changeTiltModeDialogConfirmation(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Activar',
      'false': 'Desactivar',
      'other': 'Cambiar',
    });
    return '$_temp0';
  }

  @override
  String get generalTextSlatSetting => 'Ajuste de las lamas';

  @override
  String get generalTextPosition => 'Posición';

  @override
  String get generalTextSlatPosition => 'Posición de las lamas';

  @override
  String get slatSettingDescription => 'Descripción del ajuste de las lamas';

  @override
  String get scenePositionSliderDescription => 'Altura';

  @override
  String get sceneSlatPositionSliderDescription => 'Inclinación';

  @override
  String get referenceRun => 'Calibración';

  @override
  String get slatAutoSettingHint =>
      'En este modo, la posición de las persianas no importa antes de que las lamas se ajusten a la posición de inclinación deseada.';

  @override
  String get slatReversalSettingHint =>
      'En este modo, las persianas se cerrarán completamente antes de que las lamas se ajusten a la posición de inclinación deseada.';

  @override
  String get release340Header =>
      'Ya está aquí el nuevo actuador de sombreado de materia empotrable ESB64NP-IPM.';

  @override
  String get release340ESB64Header => '¿De qué es capaz el ESB64NP-IPM?';

  @override
  String get release340ESB64Note1 =>
      'Nuestro actuador de sombreado certificado Matter Gateway con función opcional de lamas';

  @override
  String get release340ESB64Note2 =>
      'Dos entradas de botón con cable para conmutación manual y reenvío a Matter';

  @override
  String get release340ESB64Note3 =>
      'Ampliable con adaptador EnOcean (EOA64). Por ejemplo, con el pulsador inalámbrico EnOcean F4T55.';

  @override
  String get release340ESB64Note4 =>
      'Abierto a integraciones gracias a la API REST basada en el estándar OpenAPI';

  @override
  String get activateTiltModeDialogText =>
      'Si se activa la función de inclinación, se perderán todos los ajustes. Está seguro de que desea activar la función de inclinación?';

  @override
  String get deactivateTiltModeDialogText =>
      'Si se desactiva la función de inclinación, se perderán todos los ajustes. Está seguro de que desea desactivar la función de inclinación?';

  @override
  String shareConfiguration(Object name) {
    return 'Share configuration $name';
  }

  @override
  String get configurationSharedSuccessfully =>
      'Configuration shared successfully';

  @override
  String get configurationShareFailed => 'Sharing configuration failed';
}
