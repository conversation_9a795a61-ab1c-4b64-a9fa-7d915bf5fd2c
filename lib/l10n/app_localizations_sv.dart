// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Swedish (`sv`).
class AppLocalizationsSv extends AppLocalizations {
  AppLocalizationsSv([String locale = 'sv']) : super(locale);

  @override
  String get appName => 'ELTAKO Connect';

  @override
  String get discoveryHint => 'Aktivera Bluetooth på enheten för att ansluta';

  @override
  String devicesFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count enheter hittades',
      one: '1 enhet hittades',
      zero: 'inga enheter hittades',
    );
    return '$_temp0';
  }

  @override
  String discoveryDemodeviceName(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Demoenheter',
      one: 'Demoenhet',
    );
    return '$_temp0';
  }

  @override
  String get discoverySu12Description => '2-kanaligt kopplingsur Bluetooth';

  @override
  String get discoveryImprint => 'Impressum';

  @override
  String get discoveryLegalnotice => 'Dataskydd';

  @override
  String get generalSave => 'Spara';

  @override
  String get generalCancel => 'Avbryt';

  @override
  String get detailsHeaderHardwareversion => 'Maskinvaruversion';

  @override
  String get detailsHeaderSoftwareversion => 'Programvaruversion';

  @override
  String get detailsHeaderConnected => 'Ansluten';

  @override
  String get detailsHeaderDisconnected => 'Frånkopplad';

  @override
  String get detailsTimersectionHeader => 'Program';

  @override
  String get detailsTimersectionTimercount => 'av 60 använda program';

  @override
  String get detailsConfigurationsectionHeader => 'Konfiguration';

  @override
  String get detailsConfigurationPin => 'PIN-kod för enheten';

  @override
  String detailsConfigurationChannelsDescription(
    Object channel1,
    Object channel2,
  ) {
    return 'Kanal 1: $channel1 | Kanal 2:  $channel2';
  }

  @override
  String get settingsCentralHeader => 'Centralt På/Av';

  @override
  String get detailsConfigurationCentralDescription =>
      'Gäller endast om kanalen är inställd på AUTO';

  @override
  String get detailsConfigurationDevicedisplaylock => 'Lås display';

  @override
  String get timerOverviewHeader => 'Program';

  @override
  String get timerOverviewTimersectionTimerinactivecount => 'inaktiv';

  @override
  String get timerDetailsListsectionDays1 => 'Måndag';

  @override
  String get timerDetailsListsectionDays2 => 'Tisdag';

  @override
  String get timerDetailsListsectionDays3 => 'Onsdag';

  @override
  String get timerDetailsListsectionDays4 => 'Torsdag';

  @override
  String get timerDetailsListsectionDays5 => 'Fredag';

  @override
  String get timerDetailsListsectionDays6 => 'Lördag';

  @override
  String get timerDetailsListsectionDays7 => 'Söndag';

  @override
  String get timerDetailsHeader => 'Program';

  @override
  String get timerDetailsSunrise => 'Soluppgång';

  @override
  String get generalToggleOff => 'Av';

  @override
  String get generalToggleOn => 'På';

  @override
  String get timerDetailsImpuls => 'Puls';

  @override
  String get generalTextTime => 'Tid';

  @override
  String get generalTextAstro => 'Astro';

  @override
  String get generalTextAuto => 'Auto';

  @override
  String get timerDetailsOffset => 'Tidsförskjutning';

  @override
  String get timerDetailsPlausibility => 'Aktivera rimlighetskontroll';

  @override
  String get timerDetailsPlausibilityDescription =>
      'Om AV är inställd på en tidigare tidpunkt än PÅ ignoreras båda programmen, t.ex. om du slår PÅ vid soluppgången och AV klockan 6:00. Det finns också tillfällen där kontrollen inte önskas, t.ex. slå PÅ vid solnedgång och AV vid 1:00 på natten.';

  @override
  String get generalDone => 'Klar';

  @override
  String get generalDelete => 'Radera';

  @override
  String get timerDetailsImpulsDescription =>
      'Ändra impulstidens konfiguration';

  @override
  String get settingsNameHeader => 'Enhetens namn';

  @override
  String get settingsNameDescription =>
      'Namnet används för identifiering av enheten .';

  @override
  String get settingsFactoryresetHeader => 'Fabriksåterställning';

  @override
  String get settingsFactoryresetDescription =>
      'Vilket innehåll ska återställas?';

  @override
  String get settingsFactoryresetResetbluetooth =>
      'Återställa Bluetooth-inställningar';

  @override
  String get settingsFactoryresetResettime => 'Återställer tidsinställningar';

  @override
  String get settingsFactoryresetResetall => 'Fabriksåterställning';

  @override
  String get settingsDeletetimerHeader => 'Radera program';

  @override
  String get settingsDeletetimerDescription =>
      'Ska verkligen alla program raderas?';

  @override
  String get settingsDeletetimerAllchannels => 'Alla kanaler';

  @override
  String get settingsImpulseHeader => 'Pulstid';

  @override
  String get settingsImpulseDescription =>
      'Pulstiden ställer in längden på pulsen.';

  @override
  String get generalTextRandommode => 'Slumpvalsläge';

  @override
  String get settingsChannelsTimeoffsetHeader =>
      'Förskjutning av solståndstiden';

  @override
  String settingsChannelsTimeoffsetDescription(
    Object summerOffset,
    Object winterOffset,
  ) {
    return 'Sommar: ${summerOffset}min | Vinter: ${winterOffset}min';
  }

  @override
  String get settingsLocationHeader => 'Plats';

  @override
  String get settingsLocationDescription =>
      'Ställ in din plats för att använda astrofunktioner.';

  @override
  String get settingsLanguageHeader => 'Språk för enheten';

  @override
  String get settingsLanguageSetlanguageautomatically =>
      'Ställ in språket automatiskt';

  @override
  String settingsLanguageDescription(Object deviceType) {
    return 'Välj språk för $deviceType';
  }

  @override
  String get settingsLanguageGerman => 'Tyska';

  @override
  String get settingsLanguageFrench => 'Franska';

  @override
  String get settingsLanguageEnglish => 'Engelska';

  @override
  String get settingsLanguageItalian => 'Italienska';

  @override
  String get settingsLanguageSpanish => 'Spanska';

  @override
  String get settingsDatetimeHeader => 'Datum och tid';

  @override
  String get settingsDatetimeSettimeautomatically => 'Tillämpa systemtid';

  @override
  String get settingsDatetimeSettimezoneautomatically =>
      'Ställ in tidszonen automatiskt';

  @override
  String get generalTextTimezone => 'Tidszon';

  @override
  String get settingsDatetime24Hformat => '24-timmarsformat';

  @override
  String get settingsDatetimeSetsummerwintertimeautomatically =>
      'Automatisk sommar-vintertid';

  @override
  String get settingsDatetimeWinter => 'Vinter';

  @override
  String get settingsDatetimeSummer => 'Sommar';

  @override
  String get settingsPasskeyHeader => 'Aktuell PIN-kod för enheten';

  @override
  String get settingsPasskeyDescription => 'Ange PIN-koden för enheten';

  @override
  String get timerDetailsActiveprogram => 'Programmet är aktivt';

  @override
  String get timerDetailsActivedays => 'Aktiva dagar';

  @override
  String get timerDetailsSuccessdialogHeader => 'Framgångsrik';

  @override
  String get timerDetailsSuccessdialogDescription =>
      'Programmet har lagts till';

  @override
  String get settingsRandommodeDescription =>
      'Slumpvalsläget fungerar endast för tidsbaserade program, inte för puls- eller astrobaserade program (soluppgång eller solnedgång). Tiderna förskjuts med upp till 15 minuter.';

  @override
  String get settingsSolsticeHeader => 'Solstånd tidsförskjutning.';

  @override
  String get settingsSolsticeDescription =>
      'Tiden ger en tidsförskjutning av solnedgången. Soluppgången inverteras motsvarande.';

  @override
  String get settingsSolsticeHint =>
      'Exempel:\nPå vintern förskjuts tiden till 30 minuter före solnedgången, och till 30 minuter före soluppgången.';

  @override
  String get generalTextMinutesShort => 'min';

  @override
  String get settingsPinDescription => 'PIN-koden krävs för anslutningen.';

  @override
  String get settingsPinHeader => 'PIN-kod för en ny enhet';

  @override
  String get settingsPinNewpinDescription => 'Ange en ny PIN-kod';

  @override
  String get settingsPinNewpinRepeat => 'Upprepa PIN-koden';

  @override
  String get detailsProductinfo => 'Produktinformation';

  @override
  String get settingsDatetimeSettimeautodescription => 'Välj önskad tid';

  @override
  String minutes(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Minuter',
      one: 'Minut',
    );
    return '$_temp0';
  }

  @override
  String hours(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Timmar',
      one: 'Timme',
    );
    return '$_temp0';
  }

  @override
  String seconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Sekunder',
      one: 'Sekund',
    );
    return '$_temp0';
  }

  @override
  String generalTextChannel(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Kanaler',
      one: 'Kanal',
    );
    return '$_temp0';
  }

  @override
  String generalLabelChannel(Object number) {
    return 'Kanal $number';
  }

  @override
  String get generalTextDate => 'Datum';

  @override
  String get settingsDatetime24HformatDescription => 'Välj önskat format';

  @override
  String get settingsDatetimeSetsummerwintertime => 'Sommar-/Vintertid';

  @override
  String get settingsDatetime24HformatValue24 => '24h';

  @override
  String get settingsDatetime24HformatValue12 => 'AM/PM';

  @override
  String get detailsEdittimer => 'Redigera program';

  @override
  String get settingsPinOldpinRepeat => 'Upprepa den aktuella PIN-koden';

  @override
  String get settingsPinCheckpin => 'PIN-koden kontrolleras';

  @override
  String detailsDevice(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'enheter',
      one: 'enhet',
    );
    return '$_temp0';
  }

  @override
  String get detailsDisconnect => 'Koppla ifrån';

  @override
  String get settingsCentralDescription =>
      'Ingången A1 styr Centralt På/Av.\nCentralt På/Av gäller endast för kanal som är inställd på centralt På/Av.';

  @override
  String get settingsCentralHint =>
      'Exempel:\nKanal 1 = Centralt På/Av\nKanal 2 = Av\nA1 = Centralt På -> Endast C1 växlar till På, C2 förblir Av.';

  @override
  String get settingsCentralToggleheader => 'Central styringång';

  @override
  String get settingsCentralActivechannelsdescription =>
      'Aktuella kanaler med inställningen Centralt På/Av:';

  @override
  String get settingsSolsticeSign => 'Tecken';

  @override
  String get settingsDatetimeTimezoneDescription => 'Centraleuropeisk tid';

  @override
  String get generalButtonContinue => 'Fortsätt';

  @override
  String get settingsPinConfirmationDescription => 'PIN-koden har ändrats';

  @override
  String get settingsPinFailDescription => 'PIN-kodsändringen misslyckades';

  @override
  String get settingsPinFailHeader => 'Misslyckades';

  @override
  String get settingsPinFailShort =>
      'PIN-koden måste vara exakt 6 siffror lång.';

  @override
  String get settingsPinFailWrong => 'PIN-koden är felaktig';

  @override
  String get settingsPinFailMatch => 'PIN-koderna stämmer inte överens';

  @override
  String get discoveryLostconnectionHeader => 'Förbindelsen har brutits';

  @override
  String get discoveryLostconnectionDescription =>
      'Förbindelsen med enheten har brutits.';

  @override
  String get settingsChannelConfigCentralDescription =>
      'Uppför sig som AUTO och styrs även av de trådbundna centralstyringångarna';

  @override
  String get settingsChannelConfigOnDescription =>
      'Kopplar om kanalen till permanent PÅ och ignorerar programmen';

  @override
  String get settingsChannelConfigOffDescription =>
      'Kopplar om kanalen till permanent AV och ignorerar programmen';

  @override
  String get settingsChannelConfigAutoDescription =>
      'Kopplar enligt tids- och astroprogrammen';

  @override
  String get bluetoothPermissionDescription =>
      'Bluetooth krävs för att konfigurera enheterna.';

  @override
  String get timerListitemOn => 'Slå på';

  @override
  String get timerListitemOff => 'Stänga av';

  @override
  String get timerListitemUnknown => 'Okänd';

  @override
  String get timerDetailsAstroHint =>
      'Platsen måste anges i inställningarna för att astroprogrammen ska fungera korrekt.';

  @override
  String get timerDetailsTrigger => 'Utlösare';

  @override
  String get timerDetailsSunset => 'Solnedgång';

  @override
  String get settingsLocationCoordinates => 'Koordinater';

  @override
  String get settingsLocationLatitude => 'Latitud';

  @override
  String get settingsLocationLongitude => 'Longitud';

  @override
  String timerOverviewEmptyday(Object day) {
    return 'Inga program används för närvarande för $day';
  }

  @override
  String get timerOverviewProgramloaded => 'Programmen laddas';

  @override
  String get timerOverviewProgramchanged => 'Programmet har ändrats';

  @override
  String get settingsDatetimeProcessing => 'Datum och tid ändras';

  @override
  String get deviceNameEmpty => 'Inmatningen får inte vara tom.';

  @override
  String deviceNameHint(Object count) {
    return 'Inmatningen får inte innehålla mer än $count tecken.';
  }

  @override
  String get deviceNameChanged => 'Enhetens namn ändras';

  @override
  String get deviceNameChangedSuccessfully => 'Enhetens namn har ändrats.';

  @override
  String get deviceNameChangedFailed => 'Ett fel har inträffat.';

  @override
  String get settingsPinConfirm => 'Bekräfta';

  @override
  String get deviceShowInstructions =>
      '1. Aktivera enhetens Bluetooth med SET\n2. Tryck på knappen högst upp för att starta sökningen.';

  @override
  String get deviceNameNew => 'Ange ett nytt enhetsnamn';

  @override
  String get settingsLanguageRetrieved => 'Språket hämtas';

  @override
  String get detailsProgramsShow => 'Visa program';

  @override
  String get generalTextProcessing => 'Vänta lite';

  @override
  String get generalTextRetrieving => 'hämtas.';

  @override
  String get settingsLocationPermission =>
      'Tillåt ELTAKO Connect att få tillgång till den här enhetens plats';

  @override
  String get timerOverviewChannelloaded => 'Kanalerna laddas';

  @override
  String get generalTextRandommodeChanged => 'Slumpvalsläget har ändrats';

  @override
  String get detailsConfigurationsectionChanged => 'Konfigurationen ändras';

  @override
  String get settingsSettimeFunctions => 'Tidsfunktioner ändras';

  @override
  String get imprintContact => 'Kontakt';

  @override
  String get imprintPhone => 'Telefon';

  @override
  String get imprintMail => 'E-post';

  @override
  String get imprintRegistrycourt => 'Registrerande myndighet';

  @override
  String get imprintRegistrynumber => 'Registreringsnummer';

  @override
  String get imprintCeo => 'Verkställande direktör';

  @override
  String get imprintTaxnumber => 'Organisationsnummer';

  @override
  String get settingsLocationCurrent => 'Nuvarande plats';

  @override
  String get generalTextReset => 'Återställ';

  @override
  String get discoverySearchStart => 'Starta sökning';

  @override
  String get discoverySearchStop => 'Stoppa sökningen';

  @override
  String get settingsImpulsSaved => 'Pulskopplingstiden lagras';

  @override
  String get settingsCentralNochannel =>
      'Det finns inga kanaler med inställningen Centralt På/Av.';

  @override
  String get settingsFactoryresetBluetoothConfirmationDescription =>
      'Bluetooth-anslutningen har återställts.';

  @override
  String get settingsFactoryresetBluetoothFailDescription =>
      'Återställning av Bluetooth-anslutningar misslyckades.';

  @override
  String get imprintPublisher => 'Utgivare';

  @override
  String get discoveryDeviceConnecting => 'Anslutning upprättas';

  @override
  String get discoveryDeviceRestarting => 'Omstart...';

  @override
  String get generalTextConfigurationsaved =>
      'Kanalkonfigurationen har sparats.\n';

  @override
  String get timerOverviewChannelssaved => 'Spara kanaler';

  @override
  String get timerOverviewSaved => 'Timer sparad\n';

  @override
  String get timerSectionList => 'Listvy\n';

  @override
  String get timerSectionDayview => 'Dagvy';

  @override
  String get generalTextChannelInstructions => 'Kanalinställningar';

  @override
  String get generalTextPublisher => 'Utgivare';

  @override
  String get settingsDeletetimerDialog =>
      'Vill du verkligen radera alla program?';

  @override
  String get settingsFactoryresetResetbluetoothDialog =>
      'Vill du verkligen återställa alla Bluetooth-inställningar?';

  @override
  String get settingsCentralTogglecentral => 'Centralt\nPå/Av';

  @override
  String generalTextConfirmation(Object serviceName) {
    return '$serviceName har ändrats.';
  }

  @override
  String generalTextFailed(Object serviceName) {
    return '$serviceName ändringen misslyckades.';
  }

  @override
  String get settingsChannelConfirmationDescription => 'Kanalerna har ändrats.';

  @override
  String get timerDetailsSaveHeader => 'Spara programmet';

  @override
  String get timerDetailsDeleteHeader => 'Radera programmet';

  @override
  String get timerDetailsSaveDescription => 'Programmet har sparats.';

  @override
  String get timerDetailsDeleteDescription => 'Programmet raderades.';

  @override
  String get timerDetailsAlertweekdays =>
      'Programmet kan inte sparas eftersom inga veckodagar har valts.';

  @override
  String get generalTextOk => 'OK';

  @override
  String get settingsDatetimeChangesuccesfull => 'Datum och tid har ändrats.';

  @override
  String get discoveryConnectionFailed => 'Anslutningen misslyckades';

  @override
  String get discoveryDeviceResetrequired =>
      'Ingen anslutning kunde upprättas med enheten. För att lösa problemet raderar du enheten i Bluetooth-inställningarna. Om problemet kvarstår, kontakta vår tekniska support.';

  @override
  String get generalTextSearch => 'Sök enheter';

  @override
  String get generalTextOr => 'eller';

  @override
  String get settingsFactoryresetProgramsConfirmationDescription =>
      'Alla program har raderats.';

  @override
  String get generalTextManualentry => 'Manuell inmatning';

  @override
  String get settingsLocationSaved => 'Plats sparad';

  @override
  String get settingsLocationAutosearch => 'Sök plats automatiskt';

  @override
  String get imprintPhoneNumber => '+49 711 / 9435 0000';

  @override
  String get imprintMailAddress => '<EMAIL>';

  @override
  String get settingsFactoryresetResetallDialog =>
      'Vill du verkligen göra en fabriksåterställning?';

  @override
  String get settingsFactoryresetFactoryConfirmationDescription =>
      'Enheten har blivit fabriksåterställd.';

  @override
  String get settingsFactoryresetFactoryFailDescription =>
      'Återställningen av enheten misslyckades.';

  @override
  String get imprintPhoneNumberIos => '+49711/94350000';

  @override
  String get mfzFunctionA2Title => 'Tillslagsfördröjning 2-stegs (A2)';

  @override
  String get mfzFunctionA2TitleShort => 'Tillslagsfördröjning 2-stegs (A2)';

  @override
  String get mfzFunctionA2Description =>
      'När styrspänningen ansluts startar tidräkningen t1 mellan 0 och 60 sekunder. Efter nedräknigen sluts kontakten 1-2 och tidräkningen t2 mellan 0 och 60 sekunder börjar. I slutet av denna tid sluts kontakten 3-4. Vid avbrott påbörjas tidsförloppet på nytt med t1.';

  @override
  String get mfzFunctionRvTitle => 'Frånslagsfördröjning (RV)';

  @override
  String get mfzFunctionRvTitleShort => 'RV | Frånslagsfördröjning';

  @override
  String get mfzFunctionRvDescription =>
      'När styrspänningen ansluts sluter kontakten 15-18. \nBryts styrspänningen så påbörjas tidräkningen, efter vilken kontakten åter bryter. Kan återställas under tidräkningen.';

  @override
  String get mfzFunctionTiTitle => 'Paus-gångtid gång vid start (TI)';

  @override
  String get mfzFunctionTiTitleShort => 'TI | Paus-gångtid gång vid start';

  @override
  String get mfzFunctionTiDescription =>
      'Cykeln forsätter så länge styrspänningen är på. De båda tiderna kan ställas in separat. När styrspänningen ansluts sluter kontakten 15-18 omedelbart.';

  @override
  String get mfzFunctionAvTitle => 'Tillslagsfördröjning (AV)';

  @override
  String get mfzFunctionAvTitleShort => 'AV | Tillslagsfördröjning';

  @override
  String get mfzFunctionAvDescription =>
      'När styrspänningen ansluts påbörjas tidräkningen, efter nedräkningen sluter kontakten 15-18. Efter avbrott av styrspänningen börjar tidsförloppet på nytt.';

  @override
  String get mfzFunctionAvPlusTitle => 'Tillslagsfördröjning summering (AV+)';

  @override
  String get mfzFunctionAvPlusTitleShort =>
      'AV+ | Tillslagsfördröjning summering';

  @override
  String get mfzFunctionAvPlusDescription =>
      'Fungerar som AV. Men vid brytning av styrspänningen sparas den aktuella tiden.';

  @override
  String get mfzFunctionAwTitle => 'Puls vid frånslag (AW)';

  @override
  String get mfzFunctionAwTitleShort => 'AW | puls vid frånslag';

  @override
  String get mfzFunctionAwDescription =>
      'När styrspänningen bryts växlar kontakten och sluter 15-18, bryter igen när den inställda tiden har gått ut. Om styrspänningen läggs på under pulstiden återgår kontakten omedelbart till viloläget och den återstående tiden nollställs.';

  @override
  String get mfzFunctionIfTitle => 'Pulsfilter (IF)';

  @override
  String get mfzFunctionIfTitleShort => 'IF | Pulsfilter';

  @override
  String get mfzFunctionIfDescription =>
      'När styrspänningen ansluts sluter kontakten 15-18 under den inställda tiden. Pulser under pågående tidräkning ignoreras.';

  @override
  String get mfzFunctionEwTitle => 'Puls vid tillslag (EW)';

  @override
  String get mfzFunctionEwTitleShort => 'EW | Puls vid tillslag';

  @override
  String get mfzFunctionEwDescription =>
      'När styrspänningen ansluts sluter kontakten 15-18 under den inställda tiden. Om styrspänningen försvinner under pulstiden återgår kontakten omedelbart till viloläget och den återstående tiden nollställs.';

  @override
  String get mfzFunctionEawTitle => 'Puls vid till- och frånslag (EAW)';

  @override
  String get mfzFunctionEawTitleShort => 'EAW | Puls vid till- och frånslag';

  @override
  String get mfzFunctionEawDescription =>
      'När styrspänningen ansluts sluter kontakten 15-18 under den inställda tiden (t1). Bryts styrspänningen så sluter den igen inställd tid (t2).';

  @override
  String get mfzFunctionTpTitle => 'Paus-gångtid som börjar med paus (TP)';

  @override
  String get mfzFunctionTpTitleShort => 'TP | Paus-gångtid som börjar med paus';

  @override
  String get mfzFunctionTpDescription =>
      'Funktionsbeskrivningar som TI, men när styrspänningen ansluts förblir kontakten i läge 15-16.';

  @override
  String get mfzFunctionIaTitle => 'Impulsstyrd tillslagsfördröjning (IA)';

  @override
  String get mfzFunctionIaTitleShort => 'IA | Impulsstyrd tillslagsfördröjning';

  @override
  String get mfzFunctionIaDescription =>
      'När styrspänningen ansluts börjar tidräkningen t1, när den har löpt ut sluter kontakten 15-18 och förblir där tiden t2  (t.ex. för automatiska dörröppnare). Om t1 är inställd på den kortaste tiden 0,1 s fungerar IA som en pulsfilter under t2 tiden.';

  @override
  String get mfzFunctionArvTitle => 'Till- och frånslagsfördröjning (ARV)';

  @override
  String get mfzFunctionArvTitleShort => 'ARV | Till- och frånslagsfördröjning';

  @override
  String get mfzFunctionArvDescription =>
      'När styrspänningen ansluts börjar tidräkningen, när den har löpt ut sluter kontakten 15-18. Bryts styrspänningen så påbörjas en ny nedräkning, efter vilken kontakten återgår till viloläget. Om styrspänningen bryts under nedräkning så påbörjas en ny tidräkning.';

  @override
  String get mfzFunctionArvPlusTitle =>
      'Till- och frånslagsfördröjning, summering (ARV+)';

  @override
  String get mfzFunctionArvPlusTitleShort =>
      'ARV+ | Till- och frånslagsfördröjning, summering';

  @override
  String get mfzFunctionArvPlusDescription =>
      'Fungerar som ARV, men om styrspänningen bryts så sparas den redan förflutna tiden.';

  @override
  String get mfzFunctionEsTitle => 'Impulsrelä (ES)';

  @override
  String get mfzFunctionEsTitleShort => 'ES | Impulsrelä';

  @override
  String get mfzFunctionEsDescription =>
      'Vid styrpulser längre än 50 ms växlar kontakten till och från.';

  @override
  String get mfzFunctionEsvTitle =>
      'Impulsrelä med frånslagsfördröjning och frånslagsvarning (ESV)';

  @override
  String get mfzFunctionEsvTitleShort =>
      'ESV | Impulsrelä med frånslagsfördröjning och frånslagsvarning';

  @override
  String get mfzFunctionEsvDescription =>
      'Fungerar som SRV. Dessutom med förvarning för frånslag: Ca 30 sekunder före tidens utgång blinkar belysningen tre gånger med minskande tidsintervall.';

  @override
  String get mfzFunctionErTitle => 'Arbetsströmrelä (ER)';

  @override
  String get mfzFunctionErTitleShort => 'ER | Arbetsströmrelä';

  @override
  String get mfzFunctionErDescription =>
      'Så länge styrspänningen är ansluten växlar reläkontakten från 15-16 till 15-18.';

  @override
  String get mfzFunctionSrvTitle => 'Impulsrelä med frånslagsfördröjning (SRV)';

  @override
  String get mfzFunctionSrvTitleShort =>
      'SRV | Impulsrelä med frånslagsfördröjning';

  @override
  String get mfzFunctionSrvDescription =>
      'Vid styrimpulser längre än 50 ms växlar reläkontakten till och från. När reläkontakten har slutit 15-18 börjar tidräkningen automatiskt. Efter tidräkningen återgår reläet till viloläget.';

  @override
  String get detailsFunctionsHeader => 'Funktioner';

  @override
  String mfzFunctionTimeHeader(Object index) {
    return 'Tid (t$index)';
  }

  @override
  String get mfzFunctionOnDescription => 'permanent På';

  @override
  String get mfzFunctionOffDescription => 'permanent Av';

  @override
  String get mfzFunctionMultiplier => 'Faktor';

  @override
  String get discoveryMfz12Description => 'Multifunktion tidrelä Bluetooth';

  @override
  String get mfzFunctionOnTitle => 'Permanent PÅ';

  @override
  String get mfzFunctionOnTitleShort => 'Permanent PÅ';

  @override
  String get mfzFunctionOffTitle => 'Permanent AV';

  @override
  String get mfzFunctionOffTitleShort => 'Permanent AV';

  @override
  String get mfzMultiplierSecondsFloatingpoint => '0.1 sekunder';

  @override
  String get mfzMultiplierMinutesFloatingpoint => '0.1 minuter';

  @override
  String get mfzMultiplierHoursFloatingpoint => '0.1 timmar';

  @override
  String get mfzOverviewFunctionsloaded => 'Funktioner laddas';

  @override
  String get mfzOverviewSaved => 'Funktion sparad';

  @override
  String get settingsBluetoothHeader => 'Bluetooth';

  @override
  String get bluetoothChangedSuccessfully =>
      'Bluetooth-inställningen har ändrats.';

  @override
  String get settingsBluetoothInformation =>
      'Observera: Om den här inställningen är aktiverad är enheten permanent synlig för alla via Bluetooth! Det rekommenderas att använda PIN-kod vid denna funktion.';

  @override
  String get settingsBluetoothContinuousconnection => 'Permanent synlighet';

  @override
  String settingsBluetoothContinuousconnectionDescription(Object deviceType) {
    return 'Genom aktivering av permanent synlighet förblir Bluetooth aktivt på apparat ($deviceType) och behöver inte aktiveras manuellt innan en anslutning upprättas.';
  }

  @override
  String get settingsBluetoothTimeout => 'Timeout för anslutning';

  @override
  String get settingsBluetoothPinlimit => 'PIN-kodsgräns';

  @override
  String settingsBluetoothTimeoutDescription(Object timeout) {
    return 'Anslutningen bryts efter $timeout minuters inaktivitet.';
  }

  @override
  String settingsBluetoothPinlimitDescription(Object attempts) {
    return 'Av säkerhetsskäl har du högst $attempts försök att ange PIN-koden. Bluetooth avaktiveras sedan och måste återaktiveras manuellt för en ny anslutning.';
  }

  @override
  String get settingsBluetoothPinAttempts => 'försök';

  @override
  String get settingsResetfunctionHeader => 'Återställa funktioner';

  @override
  String get settingsResetfunctionDialog =>
      'Vill du verkligen återställa alla funktioner?';

  @override
  String get settingsFactoryresetFunctionsConfirmationDescription =>
      'Alla funktioner har återställts.';

  @override
  String get mfzFunctionTime => 'Tid (t)';

  @override
  String get discoveryConnectionFailedInfo => 'Ingen Bluetooth-anslutning';

  @override
  String get detailsConfigurationDevicedisplaylockDialogtext =>
      'Omedelbart efter att enhetens display har låsts inaktiveras Bluetooth och måste återaktiveras manuellt för att upprätta en ny anslutning.';

  @override
  String get detailsConfigurationDevicedisplaylockDialogquestion =>
      'Är du säker på att du vill låsa displayen?';

  @override
  String get settingsDemodevices => 'Visa demoenheter';

  @override
  String get generalTextSettings => 'Inställningar';

  @override
  String get discoveryWifi => 'WiFi';

  @override
  String get settingsInformations => 'Information';

  @override
  String get detailsConfigurationDimmingbehavior => 'Funktionsinställningar';

  @override
  String get detailsConfigurationSwitchbehavior => 'Funktionsinställningar';

  @override
  String get detailsConfigurationBrightness => 'Ljusstyrka';

  @override
  String get detailsConfigurationMinimum => 'Min. nivå';

  @override
  String get detailsConfigurationMaximum => 'Max. nivå';

  @override
  String get detailsConfigurationSwitchesGr => 'Manuell funktion (GR)';

  @override
  String get detailsConfigurationSwitchesGs => 'Auto funktion (GS)';

  @override
  String get detailsConfigurationSwitchesCloserer => 'Slutande kontakt (ER)';

  @override
  String get detailsConfigurationSwitchesClosererDescription =>
      'Av -> Håll nedtryckt (På) -> Släpp (Av)';

  @override
  String get detailsConfigurationSwitchesOpenerer =>
      'Brytande kontakt (ER-Inverterad)';

  @override
  String get detailsConfigurationSwitchesOpenererDescription =>
      'På -> Håll nedtryckt (Av) -> Släpp (På)';

  @override
  String get detailsConfigurationSwitchesSwitch => 'Strömbrytare';

  @override
  String get detailsConfigurationSwitchesSwitchDescription =>
      'Med varje tryckning växlar ljuset på och av.';

  @override
  String get detailsConfigurationSwitchesImpulsswitch => 'Impulsbrytare';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription =>
      'En kort tryckning på knappen används för att tända eller släcka lampan.';

  @override
  String get detailsConfigurationSwitchesClosererDescription2 =>
      'Håll ned knappen. När du släpper den stannar motorn.';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription2 =>
      'Tryck kort på knappen för att starta motorn, tryck på den igen för att stoppa den.';

  @override
  String get detailsConfigurationWifiloginScan => 'Skanna QR-kod';

  @override
  String get detailsConfigurationWifiloginScannotvalid =>
      'Den skannade koden är inte giltig';

  @override
  String get detailsConfigurationWifiloginDescription => 'Ange kod';

  @override
  String get detailsConfigurationWifiloginPassword => 'Lösenord';

  @override
  String get discoveryEsbipDescription => 'Aktor för jalusier och markiser IP';

  @override
  String get discoveryEsripDescription => 'Impulsrelä IP';

  @override
  String get discoveryEudipDescription => 'Universaldimmer IP';

  @override
  String get generalTextLoad => 'Laddar';

  @override
  String get wifiBasicautomationsNotFound => 'Ingen automatisering hittades.';

  @override
  String get wifiCodeInvalid => 'Ogiltig kod';

  @override
  String get wifiCodeValid => 'Giltig kod';

  @override
  String get wifiAuthorizationLogin => 'Anslut';

  @override
  String get wifiAuthorizationLoginFailed => 'Inloggning misslyckades';

  @override
  String get wifiAuthorizationSerialnumber => 'Serienummer';

  @override
  String get wifiAuthorizationProductiondate => 'Tillverkningsdatum';

  @override
  String get wifiAuthorizationProofofpossession => 'PoP';

  @override
  String get generalTextWifipassword => 'Lösenord för WiFi';

  @override
  String get generalTextUsername => 'Användarnamn';

  @override
  String get generalTextEnter => 'ELLER ANGE MANUELLT';

  @override
  String get wifiAuthorizationScan => 'Skanna ELTAKO-koden.';

  @override
  String get detailsConfigurationDevicesNofunctionshinttext =>
      'Den här enheten har för närvarande inte stöd för några andra inställningar';

  @override
  String get settingsUsedemodelay => 'Använd demofördröjning';

  @override
  String get settingsImpulsLoad => 'Pulskopplingstiden laddas';

  @override
  String get settingsBluetoothLoad =>
      'Bluetooth-inställningen håller på att laddas.';

  @override
  String get detailsConfigurationsectionLoad => 'Konfigurationer laddas';

  @override
  String get generalTextLogin => 'Logga in\n';

  @override
  String get generalTextAuthentication => 'Autentisera';

  @override
  String get wifiAuthorizationScanDescription =>
      'Titta efter ELTAKO-koden på enheten eller det medföljande infobladet och skanna den i kamerarutan ovan.';

  @override
  String get wifiAuthorizationScanShort => 'Skanna ELTAKO-koden';

  @override
  String get detailsConfigurationEdgemode => 'Dimmkurvor';

  @override
  String get detailsConfigurationEdgemodeLeadingedge => 'Framkantsdimring';

  @override
  String get generalTextNetwork => 'Nätverk';

  @override
  String get wifiAuthenticationSuccessful => 'Autentisering lyckad';

  @override
  String get detailsConfigurationsectionSavechange => 'Konfigurationen ändrad';

  @override
  String get discoveryWifiAdddevice => 'Lägg till WiFi-enhet';

  @override
  String get wifiAuthenticationDelay => 'Detta kan ta upp till 1 minut';

  @override
  String get generalTextRetry => 'Försök igen';

  @override
  String get wifiAuthenticationCredentials =>
      'Vänligen ange inloggningsuppgifterna för ditt WiFi';

  @override
  String get wifiAuthenticationSsid => 'SSID';

  @override
  String get wifiAuthenticationDelaylong =>
      'Det kan ta upp till 1 minut tills enheten är klar\noch visas i appen';

  @override
  String get wifiAuthenticationCredentialsShort => 'Ange WiFi-uppgifter';

  @override
  String get wifiAuthenticationTeachin => 'Lär in enheten i WiFi';

  @override
  String get wifiAuthenticationEstablish => 'Upprätta anslutning till enheten';

  @override
  String wifiAuthenticationEstablishLong(Object ssid) {
    return 'Enheten ansluter till WiFi $ssid';
  }

  @override
  String get wifiAuthenticationFailed =>
      'Anslutningen misslyckades. Bryt strömmen till enheten i några sekunder och anslut den sedan igen.';

  @override
  String get wifiAuthenticationReset => 'Återställ autentisering';

  @override
  String get wifiAuthenticationResetHint => 'All autentiseringsdata raderas';

  @override
  String get wifiAuthenticationInvaliddata => 'Autentiseringsdatan är ogiltig';

  @override
  String get wifiAuthenticationReauthenticate => 'Autentisera igen';

  @override
  String get wifiAddhkdeviceHeader => 'Lägg till en enhet';

  @override
  String get wifiAddhkdeviceDescription =>
      'Anslut din nya ELTAKO-enhet till ditt Wi-Fi via Apple Home-appen.';

  @override
  String get wifiAddhkdeviceStep1 => 'Öppna appen Apple Home.';

  @override
  String get wifiAddhkdeviceStep2 =>
      'Klicka på plusknappen i appens övre högra hörn och välj **Lägg till en enhet**.';

  @override
  String get wifiAddhkdeviceStep3 => 'Följ instruktionerna i appen.';

  @override
  String get wifiAddhkdeviceStep4 =>
      'Nu kan enheten konfigureras i ELTAKO Connect-appen.';

  @override
  String get detailsConfigurationRuntime => 'Gångtid';

  @override
  String get detailsConfigurationRuntimeMode => 'Driftläge';

  @override
  String get generalTextManually => 'Manuell';

  @override
  String get detailsConfigurationRuntimeAutoDescription =>
      'Solskyddsaktorn ställer in gångtiden automatiskt (rekommenderas).\nVid driftsättning kör solskyddet max ut och max in en gång utan stopp, på så sätt ställs gångtiden in automatiskt.';

  @override
  String get detailsConfigurationRuntimeManuallyDescription =>
      'Solskyddsaktorns gångtid ställs in manuellt med hjälp av reglaget nedan. Kontrollera efter inställning att gångtiden stämmer med installationen.';

  @override
  String get detailsConfigurationRuntimeDemoDescription =>
      'LCD displayläget är endast tillgängligt via REST API';

  @override
  String get generalTextDemomodeActive => 'Demoläget aktivt';

  @override
  String get detailsConfigurationRuntimeDuration => 'Tid';

  @override
  String get detailsConfigurationSwitchesGs4 =>
      'Gruppomkopplare med lamellvändfunktion (GS4)';

  @override
  String get detailsConfigurationSwitchesGs4Description =>
      'Gruppomkopplare med lamellvändfunktion för styrning av persienner';

  @override
  String get screenshotSu12 => 'Utebelysning';

  @override
  String get screenshotS2U12 => 'Utebelysning';

  @override
  String get screenshotMfz12 => 'Pump';

  @override
  String get screenshotEsr62 => 'Lampa';

  @override
  String get screenshotEud62 => 'Taklampa';

  @override
  String get screenshotEsb62 => 'Solskydd balkong';

  @override
  String get detailsConfigurationEdgemodeLeadingedgeDescription =>
      'LC1-LC3 är komfortlägen med olika dimmkurvor för dimbara 230 V LED-lampor, som på grund av sin konstruktion inte kan dimras tillräckligt långt med AUTO och därför måste tvingas till framkantsdimring.';

  @override
  String get detailsConfigurationEdgemodeAutoDescription =>
      'AUTO gör det möjligt att dimra alla typer av lampor.';

  @override
  String get detailsConfigurationEdgemodeTrailingedge => 'Bakkant';

  @override
  String get detailsConfigurationEdgemodeTrailingedgeDescription =>
      'LC4-LC6 är komfortlägen med olika dimmkurvor för dimbara 230 V LED-lampor.';

  @override
  String get updateHeader => 'Uppdatering programvara';

  @override
  String get updateTitleStepSearch => 'Söker efter uppdateringar';

  @override
  String get updateTitleStepFound => 'En uppdatering har hittats';

  @override
  String get updateTitleStepDownload => 'Hämta uppdatering';

  @override
  String get updateTitleStepInstall => 'Installation av uppdatering';

  @override
  String get updateTitleStepSuccess => 'Uppdateringen lyckades';

  @override
  String get updateTitleStepUptodate => 'Redan uppdaterad';

  @override
  String get updateTitleStepFailed => 'Uppdateringen misslyckades';

  @override
  String get updateButtonSearch => 'Sök efter uppdatering';

  @override
  String get updateButtonInstall => 'Installera uppdatering';

  @override
  String get updateCurrentversion => 'Aktuell version';

  @override
  String get updateNewversion =>
      'Ny uppdatering av programvaran finns tillgänglig';

  @override
  String get updateHintPower =>
      'Uppdateringen startar först när enhetens utgång inte är aktiv. Enheten får inte kopplas bort från strömförsörjningen och appen måste vara igång under uppdateringen!';

  @override
  String get updateButton => 'Uppdatera';

  @override
  String get updateHintCompatibility =>
      'En uppdatering rekommenderas, annars begränsas vissa funktioner i appen.';

  @override
  String get generalTextDetails => 'Detaljer';

  @override
  String get updateMessageStepMetadata =>
      'Laddar ner information om uppdateringen';

  @override
  String get updateMessageStepPrepare => 'Uppdateringen förbereds';

  @override
  String get updateTitleStepUpdatesuccessful => 'Uppdateringen kontrolleras';

  @override
  String get updateTextStepFailed =>
      'Något gick fel vid uppdateringen, försök igen om några minuter eller vänta tills enheten uppdaterar automatiskt (internetanslutning krävs).';

  @override
  String get configurationsNotavailable =>
      'Det finns inga tillgängliga konfigurationer ännu.';

  @override
  String get configurationsAddHint =>
      'Skapa nya konfigurationer genom att ansluta till en Bluetooth-enhet och spara en konfiguration.';

  @override
  String get configurationsEdit => 'Redigera konfiguration';

  @override
  String get generalTextName => 'Namn';

  @override
  String get configurationsDelete => 'Radera konfiguration';

  @override
  String configurationsDeleteHint(Object configName) {
    return 'Ska konfigurationen: $configName verkligen raderas?';
  }

  @override
  String get configurationsSave => 'Spara konfiguration';

  @override
  String get configurationsSaveHint =>
      'Här kan du spara konfigurationen på din smartphone, eller ladda en sparad konfiguration.';

  @override
  String get configurationsImport => 'Importera konfiguration';

  @override
  String configurationsImportHint(Object configName) {
    return 'Skall konfigurationen $configName verkligen flyttas?';
  }

  @override
  String generalTextConfigurations(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Konfigurationer',
      one: 'Konfiguration',
    );
    return '$_temp0';
  }

  @override
  String get configurationsStepPrepare => 'Konfigurationen förbereds';

  @override
  String get configurationsStepName => 'Ange ett namn för konfigurationen';

  @override
  String get configurationsStepSaving => 'Konfigurationen sparas';

  @override
  String get configurationsStepSavedsuccessfully =>
      'Konfigurationen har sparats';

  @override
  String get configurationsStepSavingfailed =>
      'Det gick inte att spara konfigurationen';

  @override
  String get configurationsStepChoose => 'Välj en konfiguration';

  @override
  String get configurationsStepImporting => 'Konfigurationen importeras';

  @override
  String get configurationsStepImportedsuccessfully =>
      'Konfigurationen har importerats';

  @override
  String get configurationsStepImportingfailed =>
      'Importen av konfigurationen misslyckades';

  @override
  String get discoveryAssuDescription =>
      'Bluetooth kopplingsur astro plug-in utomhusbruk';

  @override
  String get settingsDatetimeDevicetime => 'Aktuell tid';

  @override
  String get settingsDatetimeLoading => 'Tidsinställningarna laddas';

  @override
  String get discoveryEud12Description => 'Dimmer Bluetooth';

  @override
  String get generalTextOffdelay => 'Frånslagsfördröjning';

  @override
  String get generalTextRemainingbrightness => 'Grundljusstyrka';

  @override
  String get generalTextSwitchonvalue => 'Startvärde';

  @override
  String get motionsensorTitleNoremainingbrightness => 'Grundljusfunktion av';

  @override
  String get motionsensorTitleAlwaysremainingbrightness =>
      'Grundljusfunktion på';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogram =>
      'Grundljusfunktion i program och ZEA';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogramandzea =>
      'Grundljus via ZE och ZA';

  @override
  String get motionsensorTitleNoremainingbrightnessauto =>
      'Inget grundljus (halvautomatisk)';

  @override
  String get generalTextMotionsensor => 'Rörelsedetektor';

  @override
  String get generalTextLightclock => 'Ljusväckarklocka';

  @override
  String get generalTextSnoozeclock => 'Snooze-funktion';

  @override
  String generalDescriptionLightclock(Object mode) {
    return 'Vid tillkoppling ($mode) tänds ljuset efter ca 1 sekund med lägsta ljusstyrkan och dimras långsamt upp utan att den senast sparade ljusstyrkan ändras.';
  }

  @override
  String generalDescriptionSnoozeclock(Object mode) {
    return 'Vid frånkoppling ($mode) dimras belysningen ned från det aktuella ljusvärdet till den lägsta ljusstyrkan och släcker sedan. Belysningen kan släckas när som helst under neddimringen med ett kort tryck på knappen. Ett långt tryck under neddimringen dimrar upp och avslutar snooze-funktionen.';
  }

  @override
  String get generalTextImmediately => 'Omedelbart';

  @override
  String get generalTextPercentage => 'Procent';

  @override
  String get generalTextSwitchoffprewarning => 'Frånslagsvarning';

  @override
  String get generalDescriptionSwitchoffprewarning =>
      'Långsam dimring till lägsta ljusstyrka';

  @override
  String get generalDescriptionOffdelay =>
      'Enheten slås på när styrspänningen läggs på. Om styrspänningen bryts påbörjas tidräkningen och därefter stängs enheten av. Apparaten kan kopplas till igen under tidräkningen.';

  @override
  String get generalDescriptionBrightness =>
      'Ljusstyrkan med vilken lampan tänds.';

  @override
  String get generalDescriptionRemainingbrightness =>
      'Dimvärdet i procent som det dimrats till när rörelsedetektorn kopplat från.';

  @override
  String get generalDescriptionRuntime =>
      'Ljuslarmsfunktionens gångtid från lägsta ljusstyrka till högsta ljusstyrka.';

  @override
  String get generalTextUniversalbutton => 'Universaltryckknapp';

  @override
  String get generalTextDirectionalbutton => 'Riktningstryckknapp';

  @override
  String get eud12DescriptionAuto =>
      'Automatisk detektering UT/RT (med riktningstrykknappsdiod RTD)';

  @override
  String get eud12DescriptionRt => 'med riktningstryckknappsdiod RTD';

  @override
  String get generalTextProgram => 'Program';

  @override
  String get eud12MotionsensorOff => 'Med rörelsedetektorn inställd på Off';

  @override
  String get eud12ClockmodeTitleProgramze => 'Program och central på';

  @override
  String get eud12ClockmodeTitleProgramza => 'Program och central av';

  @override
  String get eud12ClockmodeTitleProgrambuttonon => 'Program och UT/RT på';

  @override
  String get eud12ClockmodeTitleProgrambuttonoff => 'Program och UT/RT av';

  @override
  String get eud12TiImpulseTitle => 'Pulstid på (t1)';

  @override
  String get eud12TiImpulseHeader => 'Dimmervärde pulstid på';

  @override
  String get eud12TiImpulseDescription =>
      'Det dimmervärde i procent som lampan dimras till vid pulstiden ON.';

  @override
  String get eud12TiOffTitle => 'Pulstid av (t2)';

  @override
  String get eud12TiOffHeader => 'Dimmervärde pulstid av';

  @override
  String get eud12TiOffDescription =>
      'Det dimmervärde i procent som lampan dimras till vid pulstiden OFF.';

  @override
  String get generalTextButtonpermanentlight => 'Konstantljusfunktion';

  @override
  String get generalDescriptionButtonpermanentlight =>
      'Inställning konstantljus med tryckknapp från 0 till 10 timmar i steg om 0,5 timmar. Aktivering genom att trycka på knappen längre än 1 sekund (blinkar 1 gång), Avaktivering genom knapptryckning längre än 2 sekunder.';

  @override
  String get generalTextNobuttonpermanentlight => 'Ingen TSP';

  @override
  String get generalTextBasicsettings => 'Grundfunktioner';

  @override
  String get generalTextInputswitch => 'Funktion tryckknappsingång (A1)';

  @override
  String get generalTextOperationmode => 'Driftläge';

  @override
  String get generalTextDimvalue => 'Tillkopplinginställningar';

  @override
  String get eud12TitleUsememory => 'Minnesfunktion på';

  @override
  String get eud12DescriptionUsememory =>
      'Minnesfunktionen motsvarar det senast inställda dimmervärdet. Om minnesfunktionen stängs av startar dimmern med maxvärdet.';

  @override
  String get generalTextStartup => 'Ljusstyrka vid tillkoppling';

  @override
  String get generalDescriptionSwitchonvalue =>
      'Det inställbara startvärdet  är det som garanterar att lampan tänds.';

  @override
  String get generalTitleSwitchontime => 'Tillkopplingstid';

  @override
  String get generalDescriptionSwitchontime =>
      'Efter tillkopplingstiden dimrar lampan från startvärdet till minnesvärdet.';

  @override
  String get generalDescriptionStartup =>
      'Vissa LED-lampor kräver ett högre startvärde för att kunna tändas ordentligt. Lampan tänds med tillkopplingsvärdet och dimras sedan till minnesvärdet efter tillkopplingstiden.';

  @override
  String get eud12ClockmodeSubtitleProgramze => 'Kort klick på Central på';

  @override
  String get eud12ClockmodeSubtitleProgramza => 'Kort klick på central av';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonon =>
      'Dubbelklicka på universal-/riktnings-tryckknappen På';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonoff =>
      'Dubbelklicka på universal-/riktnings-tryckknappen Av';

  @override
  String get eud12FunctionStairlighttimeswitchTitleShort =>
      'TLZ | Trappautomat';

  @override
  String get eud12FunctionMinTitleShort => 'MIN';

  @override
  String get eud12FunctionMmxTitleShort => 'MMX';

  @override
  String get eud12FunctionTiDescription =>
      'Timer med justerbar till- och frånslagstid från 0,5 sekunder till 9,9 minuter. Ljusstyrkan kan ställas in från minimal ljusstyrka till maximal ljusstyrka.';

  @override
  String get eud12FunctionAutoDescription =>
      'Universaldimmer med inställning för rörelsedetektor, ljuslarm och snooze-funktion';

  @override
  String get eud12FunctionErDescription =>
      'Arbetsströmrelä, ljusstyrkan kan ställas in från min-ljusstyrka till max-ljusstyrka.';

  @override
  String get eud12FunctionEsvDescription =>
      'Universaldimmer med ställbar frånkopplingstid från 1-120 minuter. Frånkopplingsvarning i slutet genom neddimring valbar och justerbar från 1-3 minuter. Båda centralingångarna är aktiva.';

  @override
  String get eud12FunctionTlzDescription =>
      'Inställning konstantljus med tryckknapp från 0 till 10 timmar i steg om 0,5 timmar. Aktivering genom att trycka på knappen längre än 1 sekund (blinkar 1 gång), Avaktivering genom knapptryckning längre än 2 sekunder. ';

  @override
  String get eud12FunctionMinDescription =>
      'Universal dimmer, tänder med lägsta ljusstyrkan när styrspänning ansluts. Ljuset dimras till maximal ljusstyrka med den inställda dimringstiden 1-120 minuter. När styrspänningen tas bort slocknar ljuset direkt, även under dimringstiden. Båda centralingångarna är aktiva.';

  @override
  String get eud12FunctionMmxDescription =>
      'Universaldimmer, tänder med lägsta  ljusstyrkan när styrspänning ansluts. Med den inställda dimringstiden 1-120 minuter dimras ljuset till maximal ljusstyrka. När styrspänningen tas bort dimrar dimmern ned till den lägsta inställda  ljusstyrkan. Därefter kopplar den från. Båda centralingångarna är aktiva.';

  @override
  String get motionsensorSubtitleNoremainingbrightness =>
      'Med rörelsedetektorn inställd på Off';

  @override
  String get motionsensorSubtitleAlwaysremainingbrightness =>
      'Med rörelsedetektorn inställd på Off';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogram =>
      'Kopplingsprogram aktiverat och avaktiverat med rörelsedetektorn av';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogramandzea =>
      'Centralt/på aktiverar rörelsedetektor, Centralt/av avaktiverar rörelsedetektor samt kopplingsprogram';

  @override
  String get motionsensorSubtitleNoremainingbrightnessauto =>
      'Rörelsedetektorn stänger bara av';

  @override
  String get detailsDimsectionHeader => 'Dimring';

  @override
  String get generalTextFast => 'Snabbt';

  @override
  String get generalTextSlow => 'Långsamt';

  @override
  String get eud12TextDimspeed => 'Dimringshastighet';

  @override
  String get eud12TextSwitchonspeed => 'Tillkopplingshastighet';

  @override
  String get eud12TextSwitchoffspeed => 'Avstängningshastighet';

  @override
  String get eud12DescriptionDimspeed =>
      'Dimringshastigheten är den som dimmern använder, vid dimring från aktuellt värde till målvärdet.';

  @override
  String get eud12DescriptionSwitchonspeed =>
      'Tillkopplingshastighet är den dimmern använder för att kopplas in helt.';

  @override
  String get eud12DescriptionSwitchoffspeed =>
      'Avstängningshastighet är den dimmern använder för att stänga av helt.';

  @override
  String get settingsFactoryresetResetdimHeader =>
      'Återställ dimringsinställningar';

  @override
  String get settingsFactoryresetResetdimDescription =>
      'Skall verkligen alla dimringsinställningar återställas?';

  @override
  String get settingsFactoryresetResetdimConfirmationDescription =>
      'Dimmerinställningarna har återställts';

  @override
  String get eud12TextSwitchonoffspeed => 'Hastighet på/av';

  @override
  String get eud12DescriptionSwitchonoffspeed =>
      'Till-/från-kopplingshastighet är den dimmern använder för att koppla till eller från helt.';

  @override
  String get timerDetailsDimtoval => 'På med dimmervärde i %';

  @override
  String get timerDetailsDimtovalDescription =>
      'Dimmern kopplas alltid in med det fasta dimmervärdet i %.';

  @override
  String timerDetailsDimtovalSubtitle(Object brightness) {
    return 'Slå på med $brightness%';
  }

  @override
  String get timerDetailsDimtomem => 'På med minnesvärde';

  @override
  String get timerDetailsDimtomemSubtitle => 'Tillkoppling med minnesvärde';

  @override
  String get timerDetailsMotionsensorwithremainingbrightness =>
      'Återstående ljusstyrka (rörelsedetektor) På';

  @override
  String get timerDetailsMotionsensornoremainingbrightness =>
      'Återstående ljusstyrka (rörelsedetektor) Av';

  @override
  String get settingsRandommodeHint =>
      'När slumpvalsläge är aktiverat förskjuts alla kopplingstider på denna kanal slumpmässigt med upp till 15 minuter. På- och AV-tiderna förskjuts.';

  @override
  String get runtimeOffsetDescription =>
      'Tilläggsdrifttid efter att körtiden har löpt ut. Detta kan användas för att säkerställa att ändläget nås.';

  @override
  String get loadingTextDimvalue => 'Dimmervärde är laddat';

  @override
  String get discoveryEudipmDescription => 'Universaldimmer IP Matter';

  @override
  String get generalTextOffset => 'Tilläggsdrifttid';

  @override
  String get eud12DimvalueTestText => 'Skicka ljusstyrka';

  @override
  String get eud12DimvalueTestDescription =>
      'Den för tillfället inställda dimhastigheten beaktas under testningen.';

  @override
  String get eud12DimvalueLoadText => 'Ladda ljusstyrka';

  @override
  String get settingsDatetimeNotime =>
      'Datum- och tidsinställningarna måste läsas av via apparatens display.';

  @override
  String get generalMatterText => 'Matter';

  @override
  String get generalMatterMessage =>
      'Lär in din Matter-enhet med hjälp av app t.ex. Google Home, Amazon Alexa eller Samsung SmartThings.';

  @override
  String get generalMatterOpengooglehome => 'Öppna Google Home';

  @override
  String get generalMatterOpenamazonalexa => 'Öppna Amazon Alexa';

  @override
  String get generalMatterOpensmartthings => 'Öppna SmartThings';

  @override
  String generalLabelProgram(Object number) {
    return 'Program $number';
  }

  @override
  String get generalTextDone => 'Klar';

  @override
  String get settingsRandommodeDescriptionShort =>
      'Med slumpmässigt läge aktiverat förskjuts alla program på den här kanalen slumpmässigt med upp till 15 minuter. På-tider kopplar i förväg, av-tider fördröjs.';

  @override
  String get all => 'Alla';

  @override
  String get discoveryBluetooth => 'Bluetooth';

  @override
  String get success => 'Framgång';

  @override
  String get error => 'Fel';

  @override
  String get timeProgramAdd => 'Lägg till tidsprogram';

  @override
  String get noConnection => 'Ingen anslutning';

  @override
  String get timeProgramOnlyActive => 'Konfigurerade program';

  @override
  String get timeProgramAll => 'Alla program';

  @override
  String get active => 'Aktiv';

  @override
  String get inactive => 'Inaktiv';

  @override
  String timeProgramSaved(Object number) {
    return 'Program $number sparat';
  }

  @override
  String get deviceLanguageSaved => 'Enhetens språk sparat';

  @override
  String generalTextTimeShort(Object time) {
    return '$time klocka';
  }

  @override
  String programDeleteHint(Object index) {
    return 'Skall program $index verkligen raderas?';
  }

  @override
  String milliseconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Millisekunder',
      one: 'Millisekund',
    );
    return '$_temp0';
  }

  @override
  String millisecondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Millisekunder',
      one: '$count Millisekund',
    );
    return '$_temp0';
  }

  @override
  String secondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Sekunder',
      one: '$count Sekund',
    );
    return '$_temp0';
  }

  @override
  String minutesWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Minuter',
      one: '$count Minut',
    );
    return '$_temp0';
  }

  @override
  String hoursWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Timmar',
      one: '$count Timme',
    );
    return '$_temp0';
  }

  @override
  String get settingsPinFailEmpty => 'PIN-koden får inte vara tom';

  @override
  String get detailsConfigurationWifiloginScanNoMatch =>
      'Skannad kod stämmer inte överens med enheten';

  @override
  String get wifiAuthorizationPopIsEmpty => 'PoP kan inte vara tom';

  @override
  String get wifiAuthenticationCredentialsHint =>
      'Eftersom appen inte har tillgång till ditt privata Wi-Fi-lösenord går det inte att kontrollera att inmatningen är korrekt. Om ingen anslutning upprättas kontrollerar du lösenordet och anger det igen.';

  @override
  String get generalMatterOpenApplehome => 'Öppna Apple Home';

  @override
  String get timeProgramNoActive => 'Inga konfigurerade program';

  @override
  String get timeProgramNoEmpty => 'Inga fria programplatser tillgängliga';

  @override
  String get nameOfConfiguration => 'Konfigurationsnamn';

  @override
  String get currentDevice => 'Aktuell enhet';

  @override
  String get export => 'Exportera';

  @override
  String get import => 'Importera';

  @override
  String get savedConfigurations => 'Sparade konfigurationer';

  @override
  String get importableServicesLabel =>
      'Följande inställningar kan importeras:';

  @override
  String get notImportableServicesLabel => 'Inkompatibla inställningar';

  @override
  String get deviceCategoryMeterGateway => 'Energimätar Gateway';

  @override
  String get deviceCategory2ChannelTimeSwitch =>
      'Kopplingsur 2-kanal Bluetooth';

  @override
  String get devicategoryOutdoorTimeSwitchBluetooth =>
      'Kopplingsur för utomhusbruk Bluetooth';

  @override
  String get settingsModbusHeader => 'Modbus';

  @override
  String get settingsModbusDescription =>
      'Justera baudhastighet, paritet och timeout för att konfigurera överföringshastighet, feldetektering och väntetid.';

  @override
  String get settingsModbusRTU => 'Modbus RTU';

  @override
  String get settingsModbusBaudrate => 'Baudrate';

  @override
  String get settingsModbusParity => 'Paritet';

  @override
  String get settingsModbusTimeout => 'Modbus tidsgräns';

  @override
  String get locationServiceDisabled => 'Platsen är inaktiverad';

  @override
  String get locationPermissionDenied =>
      'Ge platstjänster tillstånd att använda din aktuella position.';

  @override
  String get locationPermissionDeniedPermanently =>
      'Platstjänster är permanent nekade, tillåt platstjänster i enhetens inställningar att använda din aktuella position.';

  @override
  String get lastSync => 'Senaste synkroniseringen';

  @override
  String get dhcpActive => 'DHCP aktiv';

  @override
  String get ipAddress => 'IP';

  @override
  String get subnetMask => 'Subnätmask';

  @override
  String get standardGateway => 'Standard-gateway';

  @override
  String get dns => 'DNS';

  @override
  String get alternateDNS => 'Alternativ DNS';

  @override
  String get errorNoNetworksFound => 'Inget wifi-nätverk hittades';

  @override
  String get availableNetworks => 'Tillgängliga nätverk';

  @override
  String get enableWifiInterface => 'Aktivera WiFi-gränssnitt';

  @override
  String get enableLANInterface => 'Aktivera LAN-gränssnitt';

  @override
  String get hintDontDisableAllInterfaces =>
      'Se till att inte alla gränssnitt är inaktiverade. Det senast aktiverade gränssnittet har prioritet.';

  @override
  String get ssid => 'SSID';

  @override
  String get searchNetworks => 'Sök WiFi-nätverk';

  @override
  String get errorNoNetworkEnabled => 'Minst en anslutning måste vara aktiv';

  @override
  String get errorActiveNetworkInvalid =>
      'Alla aktiva stationer är inte giltiga';

  @override
  String get invalidNetworkConfiguration => 'Ogiltig nätverkskonfiguration';

  @override
  String get generalDefault => 'Standard';

  @override
  String get mqttHeader => 'MQTT';

  @override
  String get mqttConnected => 'Ansluten till MQTT-broker';

  @override
  String get mqttDisconnected => 'Ingen anslutning till MQTT-brokern';

  @override
  String get mqttBrokerURI => 'URI broker';

  @override
  String get mqttBrokerURIHint => 'URI för MQTT-broker';

  @override
  String get mqttPort => 'Port';

  @override
  String get mqttPortHint => 'MQTT-port';

  @override
  String get mqttClientId => 'Klient-ID';

  @override
  String get mqttClientIdHint => 'MQTT-klient-ID';

  @override
  String get mqttUsername => 'Användarnamn';

  @override
  String get mqttUsernameHint => 'MQTT användarnamn';

  @override
  String get mqttPassword => 'Lösenord';

  @override
  String get mqttPasswordHint => 'MQTT-lösenord';

  @override
  String get mqttCertificate => 'Certifikat';

  @override
  String get mqttCertificateHint => 'MQTT-certifikat';

  @override
  String get mqttTopic => 'Ämne';

  @override
  String get mqttTopicHint => 'MQTT-ämne';

  @override
  String get electricityMeter => 'Energimätare';

  @override
  String get electricityMeterCurrent => 'Nuvarande';

  @override
  String get electricityMeterHistory => 'Historia';

  @override
  String get electricityMeterReading => 'Mätaravläsning';

  @override
  String get connectivity => 'Anslutningsmöjligheter';

  @override
  String electricMeter(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Energimätare',
      one: 'Energimätare',
    );
    return '$_temp0';
  }

  @override
  String get discoveryZGW16Description => 'Modbus-Energimätare-MQTT-Gateway';

  @override
  String get bluetoothConnectionLost => 'Bluetooth-anslutningen förlorad';

  @override
  String get bluetoothConnectionLostDescription =>
      'Bluetooth-anslutningen till enheten har förlorats. Kontrollera anslutningen till enheten.';

  @override
  String get openBluetoothSettings => 'Öppna Bluetooth-inställningar';

  @override
  String get password => 'Lösenord';

  @override
  String get setInitialPassword => 'Ange initialt lösenord';

  @override
  String initialPasswordMinimumLength(Object length) {
    return 'Lösenordet måste innehålla minst $length tecken';
  }

  @override
  String get repeatPassword => 'Upprepa lösenord';

  @override
  String get passwordsDoNotMatch => 'Lösenorden stämmer inte överens';

  @override
  String get savePassword => 'Spara lösenord';

  @override
  String get savePasswordHint =>
      'Lösenordet sparas för framtida anslutningar på din enhet.';

  @override
  String get retrieveNtpServer => 'Hämta tid från NTP-server';

  @override
  String get retrieveNtpServerFailed =>
      'Anslutningen till NTP-servern kunde inte upprättas.';

  @override
  String get retrieveNtpServerSuccess =>
      'Anslutningen till NTP-servern lyckades.';

  @override
  String get settingsPasswordNewPasswordDescription => 'Ange nytt lösenord';

  @override
  String get settingsPasswordConfirmationDescription =>
      'Lösenordsbytet lyckades';

  @override
  String get dhcpRangeStart => 'Start av DHCP-intervall';

  @override
  String get dhcpRangeEnd => 'DHCP-intervallets slut';

  @override
  String get forwardOnMQTT => 'Vidarebefordra till MQTT';

  @override
  String get showAll => 'Visa alla';

  @override
  String get hide => 'Dölj';

  @override
  String get changeToAPMode => 'Ändra till AP-läge';

  @override
  String get changeToAPModeDescription =>
      'Du håller på att ansluta enheten till ett WiFi-nätverk, varvid anslutningen till enheten bryts och du måste återansluta enheten via det konfigurerade nätverket.';

  @override
  String get consumption => 'Förbrukning';

  @override
  String get currentDay => 'Aktuell dag';

  @override
  String get twoWeeks => '2 veckor';

  @override
  String get oneYear => '1 år';

  @override
  String get threeYears => '3 år';

  @override
  String passwordMinLength(Object length) {
    return 'lösenordet måste innehålla minst $length tecken.';
  }

  @override
  String get passwordNeedsLetter =>
      'Lösenordet måste innehålla minst en bokstav.';

  @override
  String get passwordNeedsNumber =>
      'Lösenordet måste innehålla minst en siffra.';

  @override
  String get portEmpty => 'Porten får inte vara tom';

  @override
  String get portInvalid => 'Ogiltig port';

  @override
  String portOutOfRange(Object rangeEnd, Object rangeStart) {
    return 'Port måste vara mellan $rangeStart och $rangeEnd';
  }

  @override
  String get ipAddressEmpty => 'IP-adressen får inte vara tom';

  @override
  String get ipAddressInvalid => 'Ogiltig IP-adress';

  @override
  String get subnetMaskEmpty => 'Subnätmasken får inte vara tom';

  @override
  String get subnetMaskInvalid => 'Ogiltig subnätmask';

  @override
  String get gatewayEmpty => 'Gateway kan inte vara tom';

  @override
  String get gatewayInvalid => 'Ogiltig gateway';

  @override
  String get dnsEmpty => 'DNS kan inte vara tom';

  @override
  String get dnsInvalid => 'Ogiltig DNS';

  @override
  String get uriEmpty => 'URI får inte vara tom';

  @override
  String get uriInvalid => 'Ogiltig URI';

  @override
  String get electricityMeterChangedSuccessfully => 'Elmätaren har ändrats';

  @override
  String get networkChangedSuccessfully =>
      'Nätverkskonfigurationen har ändrats';

  @override
  String get mqttChangedSuccessfully => 'MQTT-konfigurationen har ändrats';

  @override
  String get modbusChangedSuccessfully => 'Modbus-inställningarna har ändrats';

  @override
  String get loginData => 'Radera inloggningsuppgifter';

  @override
  String get valueConfigured => 'Konfigurerad';

  @override
  String get electricityMeterHistoryNoData => 'Inga uppgifter tillgängliga';

  @override
  String get locationChangedSuccessfully => 'Platsen har ändrats';

  @override
  String get settingsNameFailEmpty => 'Namnet får inte vara tomt';

  @override
  String settingsNameFailLength(Object length) {
    return 'Namnet får inte vara längre än $length tecken';
  }

  @override
  String get solsticeChangedSuccesfully =>
      'Solståndsinställningarna har ändrats';

  @override
  String get relayFunctionChangedSuccesfully => 'Reläfunktionen har ändrats';

  @override
  String get relayFunctionHeader => 'Reläfunktion';

  @override
  String get dimmerValueChangedSuccesfully =>
      'Tillkopplingsfunktionen har ändrats';

  @override
  String get dimmerBehaviourChangedSuccesfully =>
      'Dimringsfunktionen har ändrats';

  @override
  String get dimmerBrightnessDescription =>
      'Den lägsta och högsta ljusstyrkan påverkar alla dimmerns inställbara ljusstyrkor.';

  @override
  String get dimmerSettingsChangedSuccesfully =>
      'Grundinställningarna har ändrats';

  @override
  String get liveUpdateEnabled => 'Live-test aktiverad';

  @override
  String get liveUpdateDisabled => 'Live-test inaktiverad';

  @override
  String get liveUpdateDescription =>
      'Det senast ändrade slidervärdet kommer att skickas till enheten';

  @override
  String get demoDevices => 'Demoenheter';

  @override
  String get showDemoDevices => 'Visa demoenheter';

  @override
  String get deviceCategoryTimeSwitch => 'Kopplingsur';

  @override
  String get deviceCategoryMultifunctionalRelay => 'Multifunktionstidrelä';

  @override
  String get deviceCategoryDimmer => 'Dimmer';

  @override
  String get deviceCategoryShutter => 'Markis & jalusiaktor';

  @override
  String get deviceCategoryRelay => 'Relä';

  @override
  String get search => 'Sök';

  @override
  String get configurationsHeader => 'Konfigurationer';

  @override
  String get configurationsDescription =>
      'Här kan du hantera dina sparade konfigurationer.';

  @override
  String get configurationsNameFailEmpty =>
      'Konfigurationsnamnet får inte vara tomt';

  @override
  String get configurationDeleted => 'Konfiguration raderad';

  @override
  String codeFound(Object codeType) {
    return '$codeType kod godkänd';
  }

  @override
  String get errorCameraPermission =>
      'Ge kameran tillåtelse att skanna ELTAKO-koden.';

  @override
  String get authorizationSuccessful => 'Enheten har auktoriserats ';

  @override
  String get wifiAuthenticationResetConfirmationDescription =>
      'Enheten är nu redo för en ny auktorisering.';

  @override
  String get settingsResetConnectionHeader => 'Återställ anslutning';

  @override
  String get settingsResetConnectionDescription =>
      'Vill du verkligen återställa anslutningen?';

  @override
  String get settingsResetConnectionConfirmationDescription =>
      'Uppkopplingen har återställts.';

  @override
  String get wiredInputChangedSuccesfully =>
      'Tryckknappsfunktionen har ändrats.';

  @override
  String get runtimeChangedSuccesfully => 'Gångtiden har ändrats.';

  @override
  String get expertModeActivated => 'Expertläge aktiverat';

  @override
  String get expertModeDeactivated => 'Expertläge avaktiverat';

  @override
  String get license => 'Licens';

  @override
  String get retry => 'Försök igen';

  @override
  String get provisioningConnectingHint =>
      'Anslutningen till enheten håller på att upprättas. Detta kan ta upp till 1 minut.';

  @override
  String get serialnumberEmpty => 'Serienumret får inte vara tomt';

  @override
  String get interfaceStateInactiveDescriptionBLE =>
      'Bluetooth är avaktiverat, aktivera den för att upptäcka Bluetooth-enheter.';

  @override
  String get interfaceStateDeniedDescriptionBLE =>
      'Bluetooth-behörigheter beviljades inte.';

  @override
  String get interfaceStatePermanentDeniedDescriptionBLE =>
      'Bluetooth-behörigheter har inte beviljats. Vänligen aktivera dem i enhetens inställningar.';

  @override
  String get requestPermission => 'Begär behörighet';

  @override
  String get goToSettings => 'Gå till inställningar';

  @override
  String get enableBluetooth => 'Aktivera Bluetooth';

  @override
  String get installed => 'Installerad';

  @override
  String teachInDialogDescription(Object type) {
    return 'Önskar du lära din enhet via $type?';
  }

  @override
  String get useMatter => 'Använda Matter';

  @override
  String get relayMode => 'Aktivera reläfunktion';

  @override
  String get whatsNew => 'Nytt i denna version';

  @override
  String get migrationHint =>
      'En migrering är nödvändig för att kunna använda de nya funktionerna.';

  @override
  String get migrationHeader => 'Migration';

  @override
  String get migrationProgress => 'Migration pågår...';

  @override
  String get letsGo => 'Nu kör vi!';

  @override
  String get noDevicesFound =>
      'Inga enheter hittades. Kontrollera att din enhet är i parningsläge.';

  @override
  String get interfaceStateEmpty => 'Inga enheter hittades';

  @override
  String get ssidEmpty => 'SSID får inte vara tomt';

  @override
  String get passwordEmpty => 'Lösenordet får inte vara tomt';

  @override
  String get settingsDeleteSettingsHeader => 'Återställ inställningar';

  @override
  String get settingsDeleteSettingsDescription =>
      'Vill du verkligen återställa alla inställningar?';

  @override
  String get settingsDeleteSettingsConfirmationDescription =>
      'Alla inställningar har återställts.';

  @override
  String get locationNotFound => 'Platsen hittades inte';

  @override
  String get timerProgramEmptySaveHint =>
      'Tidsprogrammet är tomt och kan inte sparas. Vill du avbryta ändringen?';

  @override
  String get timerProgramDaysEmptySaveHint =>
      'Inga dagar är valda. Vill du spara tidsprogrammet ändå?';

  @override
  String get timeProgramNoDays =>
      'Ett program utan aktiva dagar kan inte aktiveras.';

  @override
  String timeProgramColliding(Object program) {
    return 'Tidsprogrammet kolliderar med program $program';
  }

  @override
  String timeProgramDuplicated(Object program) {
    return 'Tidsprogrammet är ett duplikat av program $program';
  }

  @override
  String get screenshotZgw16 => 'Hus';

  @override
  String get interfaceStateUnknown => 'Inga enheter hittades';

  @override
  String get settingsPinChange => 'Ändra PIN-kod';

  @override
  String get timeProgrammOneTime => 'engångs';

  @override
  String get timeProgrammRepeating => 'upprepning';

  @override
  String get generalIgnore => 'Ignorera';

  @override
  String get timeProgramChooseDay => 'Välj dag';

  @override
  String get generalToday => 'Idag';

  @override
  String get generalTomorrow => 'Imorgon';

  @override
  String get bluetoothAndPINChangedSuccessfully =>
      'Bluetooth och PIN-koden har ändrats.';

  @override
  String get generalTextDimTime => 'Dimringstid';

  @override
  String get discoverySu62Description => '1-kanals kopplingsur Bluetooth';

  @override
  String get bluetoothAlwaysOnTitle => 'Alltid på';

  @override
  String get bluetoothAlwaysOnDescription =>
      'Bluetooth är permanent aktiverat.';

  @override
  String get bluetoothAlwaysOnHint =>
      'Obs: Om den här inställningen är aktiverad är enheten permanent synlig för alla via Bluetooth! Det rekommenderas att ändra standard-PIN-koden.';

  @override
  String get bluetoothManualStartupOnTitle => 'Tillfälligt på';

  @override
  String get bluetoothManualStartupOnDescription =>
      'Efter att strömmen slagits på är Bluetooth aktivt i 3 minuter.';

  @override
  String get bluetoothManualStartupOnHint =>
      'Obs: Kopplingsläget är aktivt i 3 minuter och stängs sedan av. Om en ny anslutning ska upprättas måste knappen hållas intryckt i ca 5 sekunder.';

  @override
  String get bluetoothManualStartupOffTitle => 'Manuellt på';

  @override
  String get bluetoothManualStartupOffDescription =>
      'Bluetooth aktiveras manuellt via knappsatsen och är sedan aktivt i 3 minuter.';

  @override
  String get bluetoothManualStartupOffHint =>
      'Obs: För att aktivera Bluetooth måste knappen på knappsatsen hållas intryckt i ca 5 sekunder.';

  @override
  String get timeProgrammOneTimeRepeatingDescription =>
      'Program kan antingen utföras upprepade gånger genom att alltid utföra en kopplingsoperation på de konfigurerade dagarna och tiderna, eller så kan de endast utföras en gång vid den konfigurerade kopplingstiden.';

  @override
  String versionHeader(Object version) {
    return 'Version $version';
  }

  @override
  String get releaseNotesHeader => 'Releaseinformation';

  @override
  String get release30Header => 'Den nya Eltako Connect-appen är här!';

  @override
  String get release30FeatureDesignHeader => 'Ny design';

  @override
  String get release30FeatureDesignDescription =>
      'Appen har omarbetats helt och fått en ny design. Den är nu enklare och mer intuitiv att använda.';

  @override
  String get release30FeaturePerformanceHeader => 'Förbättrad prestanda';

  @override
  String get release30FeaturePerformanceDescription =>
      'Få en smidigare funktion och kortare laddningstider - för en bättre användarupplevelse.';

  @override
  String get release30FeatureConfigurationHeader =>
      'Konfigurationer mellan olika enheter';

  @override
  String get release30FeatureConfigurationDescription =>
      'Spara konfigurationer och överför dem till andra enheter. Även om det inte är en likadan enhet kan du t.ex. överföra konfigurationen från din S2U12DBT1+1-UC till en ASSU-BT eller vice versa.';

  @override
  String get release31Header =>
      'Den nya infällda 1-kanals tiduret med Bluetooth är här!';

  @override
  String get release31Description => 'Vad kan SU62PF-BT/UC?';

  @override
  String get release31SU62Name => 'SU62PF-BT/UC';

  @override
  String get release31DeviceNote1 => 'Upp till 60 kopplingsprogram.';

  @override
  String get release31DeviceNote2 =>
      'Klockan kan slå på enheter vid fasta kopplingstider eller via astro-funktionen baserat på soluppgång och solnedgång.';

  @override
  String get release31DeviceNote3 =>
      'Slumpvalsläge: kopplingstiderna kan förskjutas med upp till 15 minuter.';

  @override
  String get release31DeviceNote4 =>
      'Automatisk sommartid/vintertid: Klockan växlar automatiskt till sommar- eller vintertid.';

  @override
  String get release31DeviceNote5 =>
      'Universell matnings- och styrspänning 12-230V UC.';

  @override
  String get release31DeviceNote6 => 'Tryckknappsingång för manuell styrning.';

  @override
  String get release31DeviceNote7 => '1 NO-kontakt potentialfri 10 A/250 V AC.';

  @override
  String get release31DeviceNote8 => 'Engångsexekvering av tidsprogram.';

  @override
  String get generalNew => 'Nytt';

  @override
  String yearsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'för $count år sedan',
      one: 'förra året',
    );
    return '$_temp0';
  }

  @override
  String monthsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'för $count månader sedan',
      one: 'Senaste månaden',
    );
    return '$_temp0';
  }

  @override
  String weeksAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'för $count veckor sedan',
      one: 'förra veckan',
    );
    return '$_temp0';
  }

  @override
  String daysAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'för $count dagar sedan',
      one: 'Igår',
    );
    return '$_temp0';
  }

  @override
  String minutesAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'för $count minuter sedan',
      one: 'En minut sedan',
    );
    return '$_temp0';
  }

  @override
  String hoursAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'för $count timmar sedan',
      one: 'En timme sedan',
    );
    return '$_temp0';
  }

  @override
  String secondsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'för $count sekunder sedan',
      one: 'En sekund sedan',
    );
    return '$_temp0';
  }

  @override
  String get justNow => 'Just nu';

  @override
  String get discoveryEsripmDescription => 'Relä IP Matter';

  @override
  String get generalTextKidsRoom => 'Barnkammarfunktion';

  @override
  String generalDescriptionKidsRoom(Object mode) {
    return 'Vid påslagning med en längre knapptryckning ($mode) tänds ljuset med den lägsta ljusstyrkan efter ca 1 sekund och dimras långsamt upp så länge knappen hålls intryckt, utan att den senast sparade ljusstyrkan ändras.';
  }

  @override
  String get generalTextSceneButton => 'Scentryckknapp';

  @override
  String get settingsEnOceanConfigHeader => 'EnOcean-konfiguration';

  @override
  String get enOceanConfigChangedSuccessfully =>
      'EnOcean-konfigurationen har ändrats.';

  @override
  String get activateEnOceanRepeater => 'Aktivera EnOcean Repeater';

  @override
  String get enOceanRepeaterLevel => 'Repeater-nivå';

  @override
  String get enOceanRepeaterLevel1 => '1-nivå';

  @override
  String get enOceanRepeaterLevel2 => '2-nivå';

  @override
  String get enOceanRepeaterOffDescription =>
      'Inga trådlösa signaler från sensorer repeteras.';

  @override
  String get enOceanRepeaterLevel1Description =>
      'Endast de trådlösa signalerna från sensorerna tas emot, kontrolleras och vidarebefordras med full sändningseffekt. Trådlösa signaler från andra repeatrar ignoreras för att minska datamängden.';

  @override
  String get enOceanRepeaterLevel2Description =>
      'Förutom trådlösa signaler från sensorerna repeteras även signalerna från 1-nivå repeaters. En trådlös signal kan därför tas emot och förstärkas maximalt två gånger. Trådlösa repeatrar behöver inte läras in. De tar emot och förstärker trådlösa signaler från alla trådlösa sensorer i sitt mottagningsområde.';

  @override
  String get settingsSensorHeader => 'Sensorer';

  @override
  String get sensorChangedSuccessfully => 'Sensorerna har ändrats';

  @override
  String get wiredButton => 'Trådbunden tryckknapp';

  @override
  String get enOceanId => 'EnOcean-ID';

  @override
  String get enOceanAddManually => 'Ange eller skanna EnOcean-ID';

  @override
  String get enOceanIdInvalid => 'Ogiltigt EnOcean-ID';

  @override
  String get enOceanAddAutomatically => 'Lär in med EnOcean Telegram';

  @override
  String get enOceanAddDescription =>
      'EnOcean protokollet gör det möjligt att lära in och styra tryckknappar i din aktor.\n\nVälj antingen det automatiska inlärningsalternativet med EnOcean telegram, för att koppla in tryckknappar med knapptryckningar, eller välj det manuella alternativet, för att skanna in eller knappa in EnOcean-ID:t.';

  @override
  String get enOceanTelegram => 'Telegram';

  @override
  String enOceanCodeScan(Object sensorType) {
    return 'Ange EnOcean-ID för din $sensorType eller skanna EnOcean-QR-koden för din $sensorType för att lägga till den';
  }

  @override
  String get enOceanCode => 'EnOcean QR-kod';

  @override
  String enOceanCodeScanDescription(Object sensorType) {
    return 'Sök efter EnOcean-koden på din $sensorType och skanna den med din kamera.';
  }

  @override
  String get enOceanButton => 'EnOcean tryckknapp';

  @override
  String get enOceanBackpack => 'EnOcean-adapter';

  @override
  String get sensorNotAvailable => 'Inga sensorer har ännu lärts in';

  @override
  String get sensorAdd => 'Lägg till sensorer';

  @override
  String get sensorCancel => 'Avbryt inlärning';

  @override
  String get sensorCancelDescription =>
      'Vill du verkligen avbryta inlärningen?';

  @override
  String get getEnOceanBackpack => 'Beställ din EnOcean-adapter';

  @override
  String get enOceanBackpackMissing =>
      'För att komma till den fantastiska världen av perfekta anslutningar och kommunikation behöver du en EnOcean-adapter.\nKlicka här för mer information';

  @override
  String sensorEditChangedSuccessfully(Object sensorName) {
    return '$sensorName har ändrats';
  }

  @override
  String sensorConnectedVia(Object deviceName) {
    return 'ansluten via $deviceName';
  }

  @override
  String get lastSeen => 'Senast sedd';

  @override
  String get setButtonOrientation => 'Ställ in orientering';

  @override
  String get setButtonType => 'Ange tryckknappstyp';

  @override
  String get button1Way => '1-vägs tryckknapp';

  @override
  String get button2Way => '2-vägs tryckknapp';

  @override
  String get button4Way => '4-vägs tryckknapp';

  @override
  String get buttonUnset => 'inte använd';

  @override
  String get button => 'Tryckknapp';

  @override
  String get sensor => 'Sensor';

  @override
  String sensorsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count sensorer funna',
      one: '1 sensor funnen',
      zero: 'inga sensorer funna',
    );
    return '$_temp0';
  }

  @override
  String get sensorSearch => 'Sök efter sensorer';

  @override
  String get searchAgain => 'Sök igen';

  @override
  String sensorTeachInHeader(Object sensorType) {
    return 'Lär in $sensorType';
  }

  @override
  String sensorChooseHeader(Object sensorType) {
    return 'Välj $sensorType';
  }

  @override
  String get sensorChooseDescription =>
      'Välj vilken tryckknapp du vill lära in.';

  @override
  String get sensorCategoryDescription =>
      'Välj kategori för den som du vill lära in.';

  @override
  String get sensorName => 'Tryckknappens namn';

  @override
  String get sensorNameFooter => 'Namnge din tryckknapp';

  @override
  String sensorAddedSuccessfully(Object sensorName) {
    return '$sensorName har lärts in';
  }

  @override
  String sensorDelete(Object sensorType) {
    return 'radera $sensorType';
  }

  @override
  String sensorDeleteHint(Object sensorName, Object sensorType) {
    return 'Vill du verkligen radera $sensorType $sensorName?';
  }

  @override
  String sensorDeletedSuccessfully(Object sensorName) {
    return '$sensorName har raderats';
  }

  @override
  String get buttonTapDescription =>
      'Tryck på den tryckknapp som du vill lära in.';

  @override
  String get waitingForTelegram => 'Aktorn väntar på telegram';

  @override
  String get copied => 'Kopierad';

  @override
  String pairingFailed(Object sensorType) {
    return '$sensorType är redan inlärd';
  }

  @override
  String get generalDescriptionUniversalbutton =>
      'Med en universalknapp vänder man riktning genom att kort släppa knappen. Korta tryckningar sätter på eller stänger av.';

  @override
  String get generalDescriptionDirectionalbutton =>
      'Riktningsknappen är \"slå på och dimra upp\" upptill och \"slå av och dimra ner\" nedtill.';

  @override
  String get matterForwardingDescription =>
      'Knapptryckningen leds vidare till Matter';

  @override
  String get none => 'Ingen';

  @override
  String get buttonNoneDescription => 'Knappen har ingen funktion';

  @override
  String get buttonUnsetDescription => 'Knappen har ingen inställd funktion';

  @override
  String get sensorButtonTypeChangedSuccessfully =>
      'Tryckknappsfunktionen har ändrats';

  @override
  String forExample(Object example) {
    return 't.ex. $example';
  }

  @override
  String get enOceanQRCodeInvalidDescription =>
      'Endast möjligt från produktionsdatum 44/20';

  @override
  String get input => 'Ingång';

  @override
  String get buttonSceneValueOverride => 'Åsidosätta scenknappens värde';

  @override
  String get buttonSceneValueOverrideDescription =>
      'Scenknappens värde kommer att skrivas över med det aktuella dimvärdet genom en lång knapptryckning';

  @override
  String get buttonSceneDescription =>
      'Scenknappen tänder med ett specifikt dimvärde';

  @override
  String get buttonPress => 'Knapptryckning';

  @override
  String get triggerOn =>
      'Med en längre knapptryckning  på en universaltryckknapp eller en riktningsknapp på tillsidan';

  @override
  String get triggerOff =>
      'Med en dubbeltryckning på en universaltryckknapp eller en riktningsknapp på frånsidan';

  @override
  String get centralOn => 'Centralt på';

  @override
  String get centralOff => 'Central av';

  @override
  String get centralButton => 'Centraltryckknapp';

  @override
  String get enOceanAdapterNotFound => 'Ingen EnOcean-adapter hittades';

  @override
  String get updateRequired => 'Uppdatering krävs';

  @override
  String get updateRequiredDescription =>
      'Din app behöver en uppdatering för att stödja den nya enheten.';

  @override
  String get release32Header =>
      'Den nya BR64 med Matter och EnOcean samt den nya Bluetooth dosmonterade kopplingsuret SU62PF-BT/UC är nu tillgängliga!';

  @override
  String get release32EUD64Header =>
      'Den nya dosdimmern med Matter via Wi-Fi och upp till 300W är här!';

  @override
  String get release32EUD64Note1 =>
      'Konfiguration av dimringshastighet, på/av-hastighet, barnkammar/insomningsläge och mycket mer.';

  @override
  String get release32EUD64Note2 =>
      'Funktionaliteten hos EUD64NPN-IPM kan utökas med hjälp av adapter, t.ex. EnOcean-adapter EOA64.';

  @override
  String get release32EUD64Note3 =>
      'Upp till 30 trådlösa EnOcean-tryckknappar kan anslutas till EUD64NPN-IPM via EnOcean-adaptern EOA64 och/eller vidarebefordras till Matter.';

  @override
  String get release32EUD64Note4 =>
      'Två trådbundna tryckknappsingångar som kan styra EUD64NPN-IPM direkt eller vidarebefordras till Matter.';

  @override
  String get release32ESR64Header =>
      'Det nya potentialfria, dosmonterade reläpucken med Matter via Wi-Fi och upp till 16A är här!';

  @override
  String get release32ESR64Note1 =>
      'Konfiguration av olika funktioner som t.ex. impulsrelä (ES), arbetsströmrelä (ER), normalt sluten (ER-inverterad) och mycket mer.';

  @override
  String get release32ESR64Note2 =>
      'Funktionaliteten hos ESR64PF-IPM kan utökas med hjälp av adapter, t.ex. EnOcean-adapter EOA64.';

  @override
  String get release32ESR64Note3 =>
      'Upp till 30 trådlösa EnOcean-tryckknappar kan läras in i ESR64PF-IPM i kombination med EnOcean-adaptern EOA64   för direkt styrning och/eller vidarebefordras till Matter.';

  @override
  String get release32ESR64Note4 =>
      'En trådbunden knappingång kan styra ESR64PF-IPM direkt eller vidarebefordras till Matter.';

  @override
  String buttonsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count knappar hittades',
      one: '1 knapp hittades',
      zero: 'inga knappar hittades',
    );
    return '$_temp0';
  }

  @override
  String get doubleImpuls => 'med en dubbel impuls';

  @override
  String get impulseDescription =>
      'När kanalen är tillkopplad, kopplas den från med en impuls.';

  @override
  String get locationServiceEnable => 'Aktivera plats';

  @override
  String get locationServiceDisabledDescription =>
      'Platstjänster är inaktiverat. Din operativsystemversion använder dessa för att kunna hitta Bluetooth-enheter.';

  @override
  String get locationPermissionDeniedNoPosition =>
      'Platstjänster är inte på . Din systemversion kräver platstjänster för att kunna hitta Bluetooth-enheter. Tillåt platstjänster i inställningarna för din enhet.';

  @override
  String get interfaceStatePermanentDeniedDescriptionDevicesAround =>
      'Tillstånd för enheter i närheten beviljades inte. Aktivera behörigheten i inställningarna för din enhet.';

  @override
  String get permissionNearbyDevices => 'Enheter i din närhet';

  @override
  String get release320Header =>
      'Den nya, kraftfulla universaldimmern EUD12NPN-BT/600W-230V är här!';

  @override
  String get release320EUD600Header => 'Vad kan den nya universaldimmern?';

  @override
  String get release320EUD600Note1 =>
      'Universaldimmer med upp till 600 W effekt';

  @override
  String get release320EUD600Note2 =>
      'Utbyggbar med effektutökare LUD12 upp till 3800W';

  @override
  String get release320EUD600Note3 =>
      'Trådbunden styrning med universal- eller riktnings-tryckknapp';

  @override
  String get release320EUD600Note4 => 'Centralt På och Av';

  @override
  String get release320EUD600Note5 =>
      'Ingång för rörelsedetektor för extra bekvämlighet';

  @override
  String get release320EUD600Note6 =>
      'Integrerat kopplingsur med 10 programplatser';

  @override
  String get release320EUD600Note7 => 'Astrofunktion';

  @override
  String get release320EUD600Note8 => 'Individuell ljusstyrka vid tillkoppling';

  @override
  String get mqttClientCertificate => 'Klientcertifikat';

  @override
  String get mqttClientCertificateHint => 'MQTT-klientcertifikat';

  @override
  String get mqttClientKey => 'Klientnyckel';

  @override
  String get mqttClientKeyHint => 'MQTT-klientnyckel';

  @override
  String get mqttClientPassword => 'Klientlösenord';

  @override
  String get mqttClientPasswordHint => 'MQTT-klientlösenord';

  @override
  String get mqttEnableHomeAssistantDiscovery =>
      'Aktivera HomeAssistant MQTT-upptäckt';

  @override
  String get modbusTcp => 'Modbus TCP';

  @override
  String get enableInterface => 'Aktivera port';

  @override
  String get busAddress => 'BUS-adress';

  @override
  String busAddressWithAddress(Object index) {
    return 'BUS-adress $index';
  }

  @override
  String get deviceType => 'Enhetstyp';

  @override
  String registerTable(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Registertabell',
      one: 'Registertabell',
    );
    return '$_temp0';
  }

  @override
  String get currentValues => 'Aktuella värden';

  @override
  String get requestRTU => 'Fråga RTU';

  @override
  String get requestPriority => 'Frågeprioritet';

  @override
  String get mqttForwarding => 'MQTT vidarekoppling';

  @override
  String get historicData => 'Historisk data';

  @override
  String get dataFormat => 'Dataformat';

  @override
  String get dataType => 'Datatyp';

  @override
  String get description => 'Beskrivning';

  @override
  String get readWrite => 'Läsa/skriva';

  @override
  String get unit => 'Enhet';

  @override
  String get registerTableReset => 'Återställ registertabell';

  @override
  String get registerTableResetDescription =>
      'Skall registertabellen verkligen återställas?';

  @override
  String get notConfigured => 'Inte konfigurerad';

  @override
  String get release330ZGW16Header => 'Större uppdatering för ZGW16WL-IP';

  @override
  String get release330Header => 'ZGW16WL-IP med upp till 16 energimätare';

  @override
  String get release330ZGW16Note1 =>
      'Stödjer upp till 16 ELTAKO Modbus-energimätare';

  @override
  String get release330ZGW16Note2 => 'Stöd för Modbus TCP';

  @override
  String get release330ZGW16Note3 => 'Stöd för MQTT Discovery';

  @override
  String get screenshotButtonLivingRoom => 'Tryckknapp för vardagsrummet';

  @override
  String get registerChangedSuccessfully =>
      'Registret har ändrats framgångsrikt';

  @override
  String get serverCertificateEmpty => 'Servercertifikatet får inte vara tomt';

  @override
  String get registerTemplates => 'Registrera mallar';

  @override
  String get registerTemplateChangedSuccessfully =>
      'Registermall framgångsrikt ändrad';

  @override
  String get registerTemplateReset => 'Återställ registermall';

  @override
  String get registerTemplateResetDescription =>
      'Ska registermallen verkligen nollställas?';

  @override
  String get registerTemplateNotAvailable => 'Inga registermallar tillgängliga';

  @override
  String get rename => 'Byt namn';

  @override
  String get registerName => 'Registernamn';

  @override
  String get registerRenameDescription => 'Ange ett eget namn för registret';

  @override
  String get restart => 'Starta om enheten';

  @override
  String get restartDescription => 'Vill du verkligen starta om enheten?';

  @override
  String get restartConfirmationDescription => 'Enheten startas om';

  @override
  String get deleteAllElectricityMeters => 'Radera alla energimätare';

  @override
  String get deleteAllElectricityMetersDescription =>
      'Vill du verkligen radera alla energimätare?';

  @override
  String get deleteAllElectricityMetersConfirmationDescription =>
      'Alla energimätare har raderats';

  @override
  String get resetAllElectricityMeters =>
      'Återställ alla energimätarkonfigurationer';

  @override
  String get resetAllElectricityMetersDescription =>
      'Vill du verkligen återställa alla energimätarkonfigurationer?';

  @override
  String get resetAllElectricityMetersConfirmationDescription =>
      'Alla energimätarkonfigurationer har återställts';

  @override
  String get deleteElectricityMeterHistories =>
      'Raders all energimätarhistorik';

  @override
  String get deleteElectricityMeterHistoriesDescription =>
      'Vill du verkligen radera all energimätarhistorik?';

  @override
  String get deleteElectricityMeterHistoriesConfirmationDescription =>
      'All energimätarhistorik har raderats';

  @override
  String get multipleElectricityMetersSupportMissing =>
      'Din enhet stöder för närvarande endast en energimätare. Vänligen uppdatera din firmware.';

  @override
  String get consumptionWithUnit => 'Förbrukning (kWh)';

  @override
  String get exportWithUnit => 'Leverans (kWh)';

  @override
  String get importWithUnit => 'Produktion (kWh)';

  @override
  String get resourceWarningHeader => 'Begränsade resurser';

  @override
  String mqttAndTcpResourceWarning(Object protocol) {
    return 'Det är inte möjligt att använda MQTT och Modbus TCP samtidigt på grund av begränsade systemresurser. Avaktivera $protocol först.';
  }

  @override
  String get mqttEnabled => 'MQTT aktiverad';

  @override
  String get redirectMQTT => 'Gå till MQTT-inställningar';

  @override
  String get redirectModbus => 'Gå till Modbus-inställningar';

  @override
  String get unsupportedSettingDescription =>
      'Med din nuvarande firmware-version stöds inte vissa av enhetens inställningar. Uppdatera din firmware för att använda de nya funktionerna';

  @override
  String get updateNow => 'Uppdatering nu';

  @override
  String get zgw241Hint =>
      'Med den här uppdateringen är Modbus TCP aktiverat som standard och MQTT är avaktiverat. Detta kan ändras i inställningarna. Med stöd för upp till 16 räknare har många optimeringar gjorts, vilket kan leda till ändringar i enhetens inställningar. Starta om enheten efter att du har justerat inställningarna.';

  @override
  String get deviceConfigChangedSuccesfully => 'Gångtiden har ändrats.';

  @override
  String get deviceConfiguration => 'Konfiguration av enhet';

  @override
  String get tiltModeToggle => 'Tilt-läge';

  @override
  String get tiltModeToggleFooter =>
      'Om enheten installeras i Matter måste alla funktioner konfigureras om där';

  @override
  String get shaderMovementDirection => 'Backa upp/ner';

  @override
  String get shaderMovementDirectionDescription =>
      'Omvänd riktning för upp/ner-rörelse av motorn';

  @override
  String get tiltTime => 'Tilt körtid';

  @override
  String changeTiltModeDialogTitle(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Enable',
      'false': 'Disable',
      'other': 'Change',
    });
    return '$_temp0 tiltfunktion';
  }

  @override
  String changeTiltModeDialogConfirmation(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Enable',
      'false': 'Disable',
      'other': 'Change',
    });
    return '$_temp0';
  }

  @override
  String get generalTextSlatSetting => 'Inställning av lameller';

  @override
  String get generalTextPosition => 'Position';

  @override
  String get generalTextSlatPosition => 'Slat position';

  @override
  String get slatSettingDescription => 'Beskrivning av lamellinställning';

  @override
  String get scenePositionSliderDescription => 'Höjd';

  @override
  String get sceneSlatPositionSliderDescription => 'Lutning';

  @override
  String get referenceRun => 'Kalibreringskörning';

  @override
  String get slatAutoSettingHint =>
      'I det här läget spelar persiennernas position ingen roll innan lamellerna justeras till önskat lutningsläge.';

  @override
  String get slatReversalSettingHint =>
      'I det här läget stängs persiennerna helt innan lamellerna justeras till önskat lutningsläge.';

  @override
  String get release340Header =>
      'Det nya infällda ESB64NP-IPM-ställdonet för solavskärmning är här!';

  @override
  String get release340ESB64Header => 'Vad är ESB64NP-IPM kapabel till?';

  @override
  String get release340ESB64Note1 =>
      'Vårt Matter Gateway-certifierade solskyddsställdon med valfri lamellfunktion';

  @override
  String get release340ESB64Note2 =>
      'Två trådbundna knappingångar för manuell växling och vidarekoppling till Matter';

  @override
  String get release340ESB64Note3 =>
      'Kan byggas ut med EnOcean-adapter (EOA64). T.ex. med EnOcean trådlös tryckknapp F4T55';

  @override
  String get release340ESB64Note4 =>
      'Öppet för integrationer tack vare REST API baserat på OpenAPI-standarden';

  @override
  String get activateTiltModeDialogText =>
      'Om tiltfunktionen är aktiverad kommer alla inställningar att gå förlorade. Är du säker på att du vill aktivera tiltfunktionen?';

  @override
  String get deactivateTiltModeDialogText =>
      'Om tiltfunktionen avaktiveras går alla inställningar förlorade. Är du säker på att du vill avaktivera tiltfunktionen?';

  @override
  String shareConfiguration(Object name) {
    return 'Share configuration $name';
  }

  @override
  String get configurationSharedSuccessfully =>
      'Configuration shared successfully';

  @override
  String get configurationShareFailed => 'Sharing configuration failed';
}
