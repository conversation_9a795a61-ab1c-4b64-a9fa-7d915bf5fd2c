{"appName": "ELTAKO Connect", "discoveryHint": "<PERSON>er Bluetooth op het toestel om te verbinden", "devicesFound": "{count, plural, =0 {No devices found} one {1 device found} other {{count} devices found}}", "@devicesFound": {"description": "Specifies how many device are found in the discovery overview", "type": "text"}, "discoveryDemodeviceName": "{count, plural, one {Demo device} other {Demo devices}}", "discoverySu12Description": "2-kana<PERSON> schakelklok Bluetooth", "discoveryImprint": "Impressum", "discoveryLegalnotice": "Juridische kennisgeving", "generalSave": "<PERSON><PERSON><PERSON>", "generalCancel": "<PERSON><PERSON><PERSON>", "detailsHeaderHardwareversion": "Hardware versie", "detailsHeaderSoftwareversion": "Software versie", "detailsHeaderConnected": "Verbonden", "detailsHeaderDisconnected": "Verbinding verbroken", "detailsTimersectionHeader": "Programma's", "detailsTimersectionTimercount": "van de 60 programma's geb<PERSON><PERSON><PERSON>", "detailsConfigurationsectionHeader": "Configuratie", "detailsConfigurationPin": "PIN-code toestel", "detailsConfigurationChannelsDescription": "Kanaal 1: {channel1} | Kanaal 2:  {channel2}", "settingsCentralHeader": "Centraal aan/uit", "detailsConfigurationCentralDescription": "Geldt alleen als het kanaal op AUTO staat", "detailsConfigurationDevicedisplaylock": "Scherm vergrendelen", "timerOverviewHeader": "Programma's", "timerOverviewTimersectionTimerinactivecount": "inactief", "timerDetailsListsectionDays1": "<PERSON><PERSON><PERSON>", "timerDetailsListsectionDays2": "Dinsdag", "timerDetailsListsectionDays3": "Woensdag", "timerDetailsListsectionDays4": "Donderdag", "timerDetailsListsectionDays5": "Vrijdag", "timerDetailsListsectionDays6": "Zaterdag", "timerDetailsListsectionDays7": "Zondag", "timerDetailsHeader": "Programma", "timerDetailsSunrise": "Zonsopgang", "generalToggleOff": "Uit", "generalToggleOn": "<PERSON><PERSON>", "timerDetailsImpuls": "Impuls", "generalTextTime": "Tijd", "generalTextAstro": "Astro", "generalTextAuto": "Auto", "timerDetailsOffset": "Tijdsverschil", "timerDetailsPlausibility": "<PERSON>er plausibiliteitscon<PERSON>le", "timerDetailsPlausibilityDescription": "Als de uit-tijd ingesteld is op een eerdere tijd dan de aan-tijd, worden beide programma's genegeerd, bv. inschakelen bij zonsopgang en uitschakelen om 6:00 uur 's morgens. Er zijn ook situaties waarin de controle niet gewenst is, bv. inschakelen bij zonsondergang en uitschakelen om 1:00 uur 's morgens.", "generalDone": "<PERSON><PERSON><PERSON>", "generalDelete": "Wissen", "timerDetailsImpulsDescription": "Om te wij<PERSON>en, ga naar de toestelconfiguratie", "settingsNameHeader": "<PERSON><PERSON>", "settingsNameDescription": "Deze naam dient als identificatie van het toestel.", "settingsFactoryresetHeader": "Fabrieksinstellingen", "settingsFactoryresetDescription": "Welke inhoud moet worden gereset?", "settingsFactoryresetResetbluetooth": "Reset Bluetooth-instellingen", "settingsFactoryresetResettime": "Reset instellingen tijd", "settingsFactoryresetResetall": "Terugzetten naar fabrieksinstellingen", "settingsDeletetimerHeader": "Wis programma's", "settingsDeletetimerDescription": "<PERSON>ten echt alle programma's gewist worden?", "settingsDeletetimerAllchannels": "Alle kanalen", "settingsImpulseHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsImpulseDescription": "De impulstijd bepaalt de duur van de impuls.", "generalTextRandommode": "Toeval<PERSON><PERSON>", "settingsChannelsTimeoffsetHeader": "Zonnewende tijdsverschil", "settingsChannelsTimeoffsetDescription": "Zomer: {summerOffset}min | Winter: {winterOffset}min", "settingsLocationHeader": "Locatie", "settingsLocationDescription": "Stel uw locatie in voor de astro functies.", "settingsLanguageHeader": "Taal toestel", "settingsLanguageSetlanguageautomatically": "Stel taal automatisch in", "settingsLanguageDescription": "<PERSON><PERSON> de <PERSON> voor de {deviceType}", "settingsLanguageGerman": "<PERSON><PERSON>", "settingsLanguageFrench": "<PERSON><PERSON>", "settingsLanguageEnglish": "<PERSON><PERSON><PERSON>", "settingsLanguageItalian": "Italiaans", "settingsLanguageSpanish": "Spaans", "settingsDatetimeHeader": "Datum en tijd", "settingsDatetimeSettimeautomatically": "Gebruik systeemtijd", "settingsDatetimeSettimezoneautomatically": "Stel tijdszone automatisch in", "generalTextTimezone": "Tijdszone", "settingsDatetime24Hformat": "24 uur weergave", "settingsDatetimeSetsummerwintertimeautomatically": "Automatisch zomer- wintertijd", "settingsDatetimeWinter": "Winter", "settingsDatetimeSummer": "<PERSON><PERSON>", "settingsPasskeyHeader": "Huidige toestel PIN", "settingsPasskeyDescription": "<PERSON><PERSON><PERSON> de huidige toestel PIN in", "timerDetailsActiveprogram": "Programma <PERSON>ren", "timerDetailsActivedays": "<PERSON><PERSON><PERSON> dagen", "timerDetailsSuccessdialogHeader": "Gelukt", "timerDetailsSuccessdialogDescription": "Programma succesvol toegevoegd", "settingsRandommodeDescription": "De toevalsmodus werkt alleen met programma's die gebaseerd zijn op tijd, niet bij impulsen of astrofuncties (zonsopgang / zonsondergang).", "settingsSolsticeHeader": "Zonnewende tijdsverschil", "settingsSolsticeDescription": "De tijd geeft het tijdsverschi<PERSON> met de zonsondergang aan. De zonsopgang wordt respectievelijk omgekeerd.", "settingsSolsticeHint": "Bijvoorbeeld:\nIn de winter wordt er 30 minuten voor zonsondergang geschakeld, waardoor er tevens 30 minuten na zonsopgang geschakeld wordt.", "generalTextMinutesShort": "min", "settingsPinDescription": "De PIN is vereist voor de verbinding.", "settingsPinHeader": "PIN-code nieuwe toestellen", "settingsPinNewpinDescription": "<PERSON>oer een nieuwe PIN-code in", "settingsPinNewpinRepeat": "<PERSON><PERSON>al de nieuwe PIN-code", "detailsProductinfo": "Productinformatie", "settingsDatetimeSettimeautodescription": "<PERSON>es de gewenste tijd", "minutes": "{count, plural, one {minuut} other {minuten}}", "hours": "{count, plural, one {uur} other {uren}}", "seconds": "{count, plural, one {seconde} other {seconden}}", "generalTextChannel": "{count, plural, one {kanaal} other {kanalen}}", "generalLabelChannel": "<PERSON><PERSON><PERSON> {number}", "generalTextDate": "Datum", "settingsDatetime24HformatDescription": "Kies de gewenste weergave", "settingsDatetimeSetsummerwintertime": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "settingsDatetime24HformatValue24": "24h", "settingsDatetime24HformatValue12": "12 uur", "detailsEdittimer": "Wijzig programma's", "settingsPinOldpinRepeat": "<PERSON><PERSON><PERSON> de huidige PIN", "settingsPinCheckpin": "Controle PIN", "detailsDevice": "{count, plural, one {<PERSON>ce} other {Devices}}", "detailsDisconnect": "Verbreek verbinding", "settingsCentralDescription": "De ingang A1 regelt de centraal aan/uit.\nCentraal aan/uit is alleen van toepassing als het kanaal ingesteld is op centraal aan/uit.", "settingsCentralHint": "Voorbeeld:\nKanaal 1 = centraal aan/uit\nKanaal 2 = uit\nA1 = centraal aan -> enkel kanaal 1 gaat aan, kanaal 2 blijft uit.", "settingsCentralToggleheader": "Centrale input schakelaars", "settingsCentralActivechannelsdescription": "<PERSON><PERSON><PERSON> met de setting centraal aan/uit:", "settingsSolsticeSign": "Symbool", "settingsDatetimeTimezoneDescription": "Centraal-Europese tijd", "generalButtonContinue": "Ga verder", "settingsPinConfirmationDescription": "Wijziging PIN gelukt", "settingsPinFailDescription": "Wijziging PIN mislukt", "settingsPinFailHeader": "Fout", "settingsPinFailShort": "De PIN moet precies 6 digits lang zijn", "settingsPinFailWrong": "De huidige PIN is niet correct", "settingsPinFailMatch": "De PIN-codes komen niet overeen", "discoveryLostconnectionHeader": "Verbinding verbroken", "discoveryLostconnectionDescription": "De verbinding met het toestel is verbroken.", "settingsChannelConfigCentralDescription": "Gedraagt zich als AUTO en reageert bijkomend op de gekableerde centrale ingangen.", "settingsChannelConfigOnDescription": "<PERSON><PERSON><PERSON><PERSON> het kanaal permanent naar AAN en negeert de programma's", "settingsChannelConfigOffDescription": "<PERSON><PERSON><PERSON><PERSON> het kanaal permanent naar UIT en negeert de programma's", "settingsChannelConfigAutoDescription": "<PERSON><PERSON><PERSON><PERSON> in functie van de tijd en astro programma's", "bluetoothPermissionDescription": "Bluetooth is vereist voor de configuratie van <PERSON>.", "timerListitemOn": "<PERSON>et aan", "timerListitemOff": "Zet uit", "timerListitemUnknown": "Onbekend", "timerDetailsAstroHint": "De locatie moet worden ingesteld in de instellingen om de astroprogramma's correct te laten werken.", "timerDetailsTrigger": "<PERSON><PERSON>", "timerDetailsSunset": "Zonsondergang", "settingsLocationCoordinates": "Coördinaten", "settingsLocationLatitude": "Breedtegraad", "settingsLocationLongitude": "Lengtegraad", "timerOverviewEmptyday": "Er worden momenteel geen programma's gebruikt voor {day}", "timerOverviewProgramloaded": "<PERSON><PERSON>'s worden geladen", "timerOverviewProgramchanged": "<PERSON><PERSON> werd gewijzigd.", "settingsDatetimeProcessing": "Datum en tijd zijn gewij<PERSON>d.", "deviceNameEmpty": "Input mag niet leeg zijn.", "deviceNameHint": "De input mag niet meer dan {count} karak<PERSON> bevatten.", "deviceNameChanged": "<PERSON><PERSON> is gewijzigd.", "deviceNameChangedSuccessfully": "<PERSON><PERSON> is succesvol gewijzigd.", "deviceNameChangedFailed": "Er is een fout opgetreden.", "settingsPinConfirm": "Bevestig", "deviceShowInstructions": "1. <PERSON><PERSON> de horloge met SET\n2. Tik op de knop bovenaan om het zoeken te starten.", "deviceNameNew": "Voeg nieuwe naam <PERSON>tel in", "settingsLanguageRetrieved": "De taal is gevonden", "detailsProgramsShow": "Toon programma's", "generalTextProcessing": "<PERSON><PERSON><PERSON> te wachten", "generalTextRetrieving": "zijn <PERSON>.", "settingsLocationPermission": "ELTAKO Connect toegang geven tot de locatie van dit toestel.", "timerOverviewChannelloaded": "<PERSON><PERSON><PERSON> zijn <PERSON>", "generalTextRandommodeChanged": "Toevalsmodus is gewijzigd", "detailsConfigurationsectionChanged": "Configuratie is gewi<PERSON><PERSON>d", "settingsSettimeFunctions": "Tijdfuncties zijn gew<PERSON>j<PERSON>d", "imprintContact": "Contact", "imprintPhone": "Telefoon", "imprintMail": "Mail", "imprintRegistrycourt": "Bevoegde rechtbank", "imprintRegistrynumber": "Registratienummer", "imprintCeo": "Al<PERSON>meen Directeur", "imprintTaxnumber": "BTW nr.", "settingsLocationCurrent": "Huidige locatie", "generalTextReset": "Reset", "discoverySearchStart": "Start zoeken", "discoverySearchStop": "Stop zoeken", "settingsImpulsSaved": "Impulstijd is opgeslagen", "settingsCentralNochannel": "<PERSON>r zijn geen kanalen met de centraal aan/uit functie", "settingsFactoryresetBluetoothConfirmationDescription": "Bluetoothverbinding was succesvol gereset.", "settingsFactoryresetBluetoothFailDescription": "Reset Bluetoothverbindingen mislukt.", "imprintPublisher": "Uitgever", "discoveryDeviceConnecting": "Verbinding is gemaakt", "discoveryDeviceRestarting": "Herstarten", "generalTextConfigurationsaved": "Configuratie kanaal opgeslagen.", "timerOverviewChannelssaved": "Sla de kanalen op", "timerOverviewSaved": "<PERSON>r <PERSON>", "timerSectionList": "Lijstweergave", "timerSectionDayview": "Dagweergave", "generalTextChannelInstructions": "<PERSON>ting<PERSON> kanaal", "generalTextPublisher": "Uitgever", "settingsDeletetimerDialog": "Wenst u werkelijk alle programma's te wissen?", "settingsFactoryresetResetbluetoothDialog": "Wenst u werkelijk alle Bluetooth settings te resetten?", "settingsCentralTogglecentral": "Centraal aan/uit", "generalTextConfirmation": "{serviceName} wij<PERSON>ing succesvol.", "generalTextFailed": "{serviceName} wij<PERSON>ing mislukt.", "settingsChannelConfirmationDescription": "Kanalen werden succesvol veranderd.", "timerDetailsSaveHeader": "Sla programma op", "timerDetailsDeleteHeader": "Wis programma", "timerDetailsSaveDescription": "Opslag programma gelukt.", "timerDetailsDeleteDescription": "Wissen programma gelukt.", "timerDetailsAlertweekdays": "Het programma kan niet opgeslagen worden, omdat er geen weekdagen geselecteerd zijn.", "generalTextOk": "OK", "settingsDatetimeChangesuccesfull": "Datum en tijd zijn succesvol gewijzigd.", "discoveryConnectionFailed": "Verbinding mislukt", "discoveryDeviceResetrequired": "Er kon geen verbinding gemaakt worden met het toestel. Wis het toestel uit uw Bluetooth settings om het probleem op te lossen. Neem contact op met onze technische dienst indien dit niet lukt.", "generalTextSearch": "<PERSON><PERSON>", "generalTextOr": "of", "settingsFactoryresetProgramsConfirmationDescription": "Alle programma's zijn succesvol gewist.", "generalTextManualentry": "Handmatige invoer", "settingsLocationSaved": "Locatie opgeslagen", "settingsLocationAutosearch": "Zoek locatie automatisch", "imprintPhoneNumber": "+49 711 / 9435 0000", "imprintMailAddress": "<EMAIL>", "settingsFactoryresetResetallDialog": "Wilt u het toestel zeker terugzetten naar de fabrieksinstellingen?", "settingsFactoryresetFactoryConfirmationDescription": "<PERSON><PERSON> <PERSON><PERSON> is succesvol teruggezet in de fabrieksinstellingen.", "settingsFactoryresetFactoryFailDescription": "Reset toetsel mislukt.", "imprintPhoneNumberIos": "+49711/94350000", "mfzFunctionA2Title": "2-traps inschakelvertraging (A2)", "mfzFunctionA2TitleShort": "2-traps inschakelvertraging (A2)", "mfzFunctionA2Description": "Bij het aan<PERSON>gen van de stuurspanning begint de ingestelde tijd T1, instelbaar tussen 0 en 60 seconden, te lopen. Na afloop van T1 sluit het contact 1-2 en begint de ingestelde tijd T2, instelbaar tussen 0 en 60 seconden, te lopen. Na afloop van T2 sluit het contact 3-4. Na een onderbreking begint de tijd T1 opnieuw te lopen.", "mfzFunctionRvTitle": "Vertraagd afvallend (RV; uitschakelvertraging)", "mfzFunctionRvTitleShort": "RV | Vertraagd afvallend", "mfzFunctionRvDescription": "Bij het a<PERSON><PERSON><PERSON> van de stuurspanning schakelt het contact naar 15-18. <PERSON><PERSON> de stuurspanning onderbroken wordt, begint de ingestelde tijd te lopen, en bij het verstrijken hiervan schakelt het contact terug in de ruststand. Resetbaar tijdens de ingestelde tijd.", "mfzFunctionTiTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> beginnend met impuls (TI; knipperrelais)", "mfzFunctionTiTitleShort": "TI | tij<PERSON><PERSON><PERSON> beginnend met impuls", "mfzFunctionTiDescription": "Zolang de stuurspanning aanligt, sluit en opent het werkcontact. De omschakeltijd in beide richtingen kan afzonderlijk ingesteld worden. Wanneer de stuurspanning wordt aangelegd, wisselt het werkcontact onmiddellijk naar 15-18.", "mfzFunctionAvTitle": "Vertraagd opkomend (AV; inschakelvertraging)", "mfzFunctionAvTitleShort": "AV | vertraagd opkomend", "mfzFunctionAvDescription": "Bij het aan<PERSON>gen van de stuurspanning begint de ingestelde tijd te lopen en bij het verstrijken hiervan schakelt het contact naar 15-18. Na een onderbreking begint de tijd opnieuw af te lopen.", "mfzFunctionAvPlusTitle": "Vertraagd opkomend met geheugen (AV+; inschakelvertraging)", "mfzFunctionAvPlusTitleShort": "AV+ | vertraagd opkomend met geheugen", "mfzFunctionAvPlusDescription": "Dezelfde functie als AV, maar onthoudt de reeds afgelopen tijd na een onderbreking.", "mfzFunctionAwTitle": "Uitschakelwisrelais (AW)", "mfzFunctionAwTitleShort": "AW | uitschakelwisrelais", "mfzFunctionAwDescription": "Bij het onderbreken van de stuurspanning  schakelt het NO-contact naar 15-18 en keert na het aflopen van de ingestelde tijd terug. Bij het aanleggen van de stuurspanning tijdens het aflopen van de tijd keert het contact onmiddellijk in de ruststand terug en wordt de resterende tijd gewist.", "mfzFunctionIfTitle": "Impulsgever (IF; enkel MFZ12.1)", "mfzFunctionIfTitleShort": "IF | impulsgever", "mfzFunctionIfDescription": "Bij het aanleg<PERSON> van de stuurspanning wisselt het werkcontact voor de ingestelde tijd naar 15-18. Bijkomende aansturingen worden pas na afloop van de ingestelde tijd uitgevoerd.", "mfzFunctionEwTitle": "Inschakelwisrelais (EW)", "mfzFunctionEwTitleShort": "EW | inschakelwisrelais", "mfzFunctionEwDescription": "Bij het aanleggen van de stuurspanning schakelt het NO contact om naar 15-18 en keert na het aflopen van de ingestelde tijd terug. Als de stuurspanning wegvalt tijdens de wistijd, schakelt het NO contact onmiddellijk terug in de ruststand en wordt de resterende tijd gewist.", "mfzFunctionEawTitle": "Inschakel- en uitschakelwissend (EAW; enkel MFZ12.1)", "mfzFunctionEawTitleShort": "EAW | inschakel- en uitschakelwissend", "mfzFunctionEawDescription": "Bij het aanleggen en onderbreken van de stuurspanning wisselt het werkcontact naar 15-18 en keert na afloop van de ingestelde wistijd terug.", "mfzFunctionTpTitle": "Impulsgever beginnend met pauze (TP; <PERSON><PERSON><PERSON><PERSON><PERSON>, enkel MFZ12.1)", "mfzFunctionTpTitleShort": "TP | impulsgever beginnend met pauze", "mfzFunctionTpDescription": "Zelfde functiebeschrijving als voor TI, behalve dat het contact niet wisselt naar 15-18 bij het a<PERSON><PERSON><PERSON> van de stuurspanning, maar open blijft bij 15-16.", "mfzFunctionIaTitle": "Impulsgestuurd vertraagd opkomend en impulsgevend (vb. automatische deuropener) (IA; enkel MFZ12.1)", "mfzFunctionIaTitleShort": "IA | impulsgestuurd vertraagd opkomend", "mfzFunctionIaDescription": "Bij een stuurimpuls vanaf 20ms. begint het tijdsverloop t1 te lopen. Na het verstrijken daarvan wisselt het contact voor het tijdsverloop t2 naar 15-18 (vb. voor automatische deuropener). Indien t1 ingesteld wordt op de kortste tijd 0,1 sec. , dan functioneert IA als impulsgever waarbij t2 afloopt, on<PERSON><PERSON><PERSON><PERSON><PERSON> van de lengte van het stuursignaal (min. 150 ms).", "mfzFunctionArvTitle": "Vertraagd opkomend en vertraagd afvallend (ARV)", "mfzFunctionArvTitleShort": "ARV | vertraagd opkomend en vertraagd afvallend (ARV)", "mfzFunctionArvDescription": "Bij het aan<PERSON>gen van de stuurspanning begint de tijd te lopen. Na verloop daarvan wisselt het contact naar 15-18. Indien daarna de stuurspanning onderbroken wordt, begint een volgend tijdsverloop zodat na afloop daarvan het contact terug<PERSON><PERSON> in de ruststand.\nNa een onderbreking van de inschakelvertraging begint de tijd opnieuw te lopen.", "mfzFunctionArvPlusTitle": "Vertraagd opkomend en vertraagd afvallend met geheugen (ARV+)", "mfzFunctionArvPlusTitleShort": "ARV+ | vertraagd opkomend en vertraagd afvallend met geheugen", "mfzFunctionArvPlusDescription": "Dezelfde functie als ARV, maar onthoudt de reeds verstreken tijd na een onderbreking van de inschakelvertraging.", "mfzFunctionEsTitle": "Teleruptor (ES)", "mfzFunctionEsTitleShort": "ES | teleruptor", "mfzFunctionEsDescription": "<PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON> vanaf 50ms schakelt het werkcontact aan en uit.", "mfzFunctionEsvTitle": "Teleruptor met afvalvertraging en uitschakelverwittiging (ESV)", "mfzFunctionEsvTitleShort": "ESV | teleruptor met afvalvertraging en uitschakelverwittiging", "mfzFunctionEsvDescription": "Dezelfde functie als SRV<PERSON> met uitschakelverwittiging: ca. 30 seconden voor het beëindigen van de ingestelde tijd knippert de verlichting 3 maal in steeds korter wordende intervallen.", "mfzFunctionErTitle": "<PERSON><PERSON><PERSON> (ER)", "mfzFunctionErTitleShort": "ER | relais", "mfzFunctionErDescription": "<PERSON>o lang het stuurcontact gesloten is, schakelt het werkcontact van 15-16 naar 15-18.", "mfzFunctionSrvTitle": "Teleruptor met afvalvertraging", "mfzFunctionSrvTitleShort": "SRV | teleruptor met afvalvertraging", "mfzFunctionSrvDescription": "<PERSON> stu<PERSON><PERSON><PERSON><PERSON> vanaf 50ms schakelt het werkcontact aan en uit. In de contactstand 15-18 schakelt het toestel na afloop van de vertragingstijd automatisch in ruststand 15-16.", "detailsFunctionsHeader": "Functies", "mfzFunctionTimeHeader": "Tijd (t{index})", "mfzFunctionOnDescription": "permanent AAN", "mfzFunctionOffDescription": "permanent UIT", "mfzFunctionMultiplier": "Factor", "discoveryMfz12Description": "Multifunctie ti<PERSON><PERSON><PERSON>s Bluetooth", "mfzFunctionOnTitle": "<PERSON><PERSON> (ON)", "mfzFunctionOnTitleShort": "<PERSON><PERSON> (ON)", "mfzFunctionOffTitle": "Uit (OFF)", "mfzFunctionOffTitleShort": "Uit (OFF)", "mfzMultiplierSecondsFloatingpoint": "0.1 seconden", "mfzMultiplierMinutesFloatingpoint": "0.1 minuten", "mfzMultiplierHoursFloatingpoint": "0.1 uren", "mfzOverviewFunctionsloaded": "Functies zijn geladen", "mfzOverviewSaved": "Functies zijn opgeslagen", "settingsBluetoothHeader": "Bluetooth", "bluetoothChangedSuccessfully": "Bluetooth settings zijn succesvol gewijzigd.", "settingsBluetoothInformation": "Nota : als deze instelling geactiveerd is, is het toestel voor iedereen permanent zichtba<PERSON> via Bluetooth!\nHet is aanbevolen om de PIN-code te wijzigen.", "settingsBluetoothContinuousconnection": "Permanente zichtbaarheid", "settingsBluetoothContinuousconnectionDescription": "Door permanente zichtbaarheid in te schakelen, blijft Bluetooth actief op het toestel ({deviceType}) en moet deze niet handmatig geactiveerd worden voor het maken van verbinding.", "settingsBluetoothTimeout": "Verbindingstijd verlopen", "settingsBluetoothPinlimit": "PIN limiet", "settingsBluetoothTimeoutDescription": "De verbinding is verbroken na {timeout} minuten van inactiviteit.", "settingsBluetoothPinlimitDescription": "Om veiligheidsredenen heeft u een maximum van {attempts} aantal pogingen om de PIN in te voeren. Bluetooth wordt dan gedeactiveerd en moet manueel gereactiveerd worden voor een nieuwe verbinding.", "settingsBluetoothPinAttempts": "pogingen", "settingsResetfunctionHeader": "Reset functies", "settingsResetfunctionDialog": "Wenst u werkelijk alle functies te resetten ?", "settingsFactoryresetFunctionsConfirmationDescription": "Alle functies werden succesvol gereset.", "mfzFunctionTime": "Tijd (t)", "discoveryConnectionFailedInfo": "<PERSON><PERSON> verbinding", "detailsConfigurationDevicedisplaylockDialogtext": "Onmiddellijk na het vergrendelen van de display van het toestel, wordt Bluetooth gedeactiveerd en moet dus manueel gereactiveerd worden om een nieuwe verbinding te maken.", "detailsConfigurationDevicedisplaylockDialogquestion": "Wenst u echt de display van het toestel te vergrendelen ?", "settingsDemodevices": "Toon demo toestellen", "generalTextSettings": "Settings", "discoveryWifi": "WiFi", "settingsInformations": "Informatie", "detailsConfigurationDimmingbehavior": "Dimgedrag", "detailsConfigurationSwitchbehavior": "<PERSON><PERSON><PERSON>gedrag", "detailsConfigurationBrightness": "<PERSON><PERSON><PERSON><PERSON>", "detailsConfigurationMinimum": "Minimale held<PERSON><PERSON><PERSON>", "detailsConfigurationMaximum": "Maximale helderheid", "detailsConfigurationSwitchesGr": "G<PERSON>epenrelais (GR)", "detailsConfigurationSwitchesGs": "<PERSON><PERSON><PERSON><PERSON>chakelaar (GS)", "detailsConfigurationSwitchesCloserer": "Schakelrelais NO (ER)", "detailsConfigurationSwitchesClosererDescription": "Uit -> ing<PERSON><PERSON><PERSON> houden (Aan) -> los<PERSON>n (Uit)", "detailsConfigurationSwitchesOpenerer": "Schakelrelais NC (ER-invers)", "detailsConfigurationSwitchesOpenererDescription": "Aan-> ing<PERSON><PERSON><PERSON> houden (Uit) -> los<PERSON><PERSON> (Aan)", "detailsConfigurationSwitchesSwitch": "<PERSON><PERSON><PERSON><PERSON>", "detailsConfigurationSwitchesSwitchDescription": "Met el<PERSON> schakelaar wordt het licht aan- of uitgeschakeld", "detailsConfigurationSwitchesImpulsswitch": "Impulsschakelaar", "detailsConfigurationSwitchesImpulsswitchDescription": "<PERSON><PERSON><PERSON><PERSON> wordt kort ingedrukt en los<PERSON>aten om het licht aan of uit te doen", "detailsConfigurationSwitchesClosererDescription2": "<PERSON><PERSON> de schakelaar ingedrukt. <PERSON><PERSON><PERSON>, stopt de motor.", "detailsConfigurationSwitchesImpulsswitchDescription2": "Sc<PERSON><PERSON><PERSON> wordt kort ingedrukt om de motor te starten en kort ingedrukt om hem weer te stoppen.", "detailsConfigurationWifiloginScan": "Scan QR-code", "detailsConfigurationWifiloginScannotvalid": "Gescande code is niet geldig", "detailsConfigurationWifiloginDescription": "Voer code in", "detailsConfigurationWifiloginPassword": "Paswoord", "discoveryEsbipDescription": "Rolluik- en jaloeziebediening IP", "discoveryEsripDescription": "Impulsschakelaar relais IP", "discoveryEudipDescription": "Universele dimmerschakelaar IP", "generalTextLoad": "Aan het opladen", "wifiBasicautomationsNotFound": "<PERSON>n automatisering gevonden.", "wifiCodeInvalid": "Code ongeldig", "wifiCodeValid": "Code geldig", "wifiAuthorizationLogin": "Verbind", "wifiAuthorizationLoginFailed": "Log in mislukt", "wifiAuthorizationSerialnumber": "Serienummer", "wifiAuthorizationProductiondate": "Fabricatiedatum", "wifiAuthorizationProofofpossession": "PoP", "generalTextWifipassword": "WiFi paswoord", "generalTextUsername": "Gebruikersnaam", "generalTextEnter": "OF VOER MANUEEL IN", "wifiAuthorizationScan": "Scan de ELTAKO-code.", "detailsConfigurationDevicesNofunctionshinttext": "Dit toestel ondersteunt momenteel geen andere instellingen", "settingsUsedemodelay": "Gebruik demo vertraging", "settingsImpulsLoad": "Imp<PERSON><PERSON> scha<PERSON> is geladen", "settingsBluetoothLoad": "Bluetooth instellingen worden opgeladen.", "detailsConfigurationsectionLoad": "Configuraties zijn <PERSON>", "generalTextLogin": "Log in", "generalTextAuthentication": "Verifiëren", "wifiAuthorizationScanDescription": "Zoek op het WiFi-toestel of op de meegeleverde infokaart  de ELTAKO-code en scan die met uw camera.", "wifiAuthorizationScanShort": "Scan de ELTAKO-code", "detailsConfigurationEdgemode": "Dimcurve", "detailsConfigurationEdgemodeLeadingedge": "Fase-<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextNetwork": "Netwerk", "wifiAuthenticationSuccessful": "Verifiëren succesvol", "detailsConfigurationsectionSavechange": "Configurat<PERSON> g<PERSON>", "discoveryWifiAdddevice": "<PERSON><PERSON><PERSON><PERSON> toestel toe", "wifiAuthenticationDelay": "Dit kan tot 1 minuut duren", "generalTextRetry": "<PERSON><PERSON><PERSON> opnieuw", "wifiAuthenticationCredentials": "<PERSON><PERSON><PERSON> <PERSON><PERSON> van uw Wi<PERSON> in", "wifiAuthenticationSsid": "SSID", "wifiAuthenticationDelaylong": "Het kan tot 1 minuut duren voordat het toestel klaar is en in de app verschijnt", "wifiAuthenticationCredentialsShort": "<PERSON><PERSON><PERSON> W<PERSON><PERSON> g<PERSON> in", "wifiAuthenticationTeachin": "Toestel inleren in de WiFi", "wifiAuthenticationEstablish": "<PERSON><PERSON> verbind<PERSON> met het toestel", "wifiAuthenticationEstablishLong": "<PERSON><PERSON><PERSON> maakt verbinding met WiFi {ssid}", "wifiAuthenticationFailed": "Verbinding mislukt. Onderbreek de stroom naar het toestel gedurende enkele seconden en probeer het opnieuw te verbinden", "wifiAuthenticationReset": "Reset verificatie", "wifiAuthenticationResetHint": "Alle verificatiegegevens werden gewist.", "wifiAuthenticationInvaliddata": "Gegevens verificatie niet geldig", "wifiAuthenticationReauthenticate": "Verifieer opnieuw", "wifiAddhkdeviceHeader": "V<PERSON>g toestel toe", "wifiAddhkdeviceDescription": "Verbind uw nieuw ELTAKO-toestel met uw WiFi via de Apple Home app.", "wifiAddhkdeviceStep1": "1. Open de Apple Home app.", "wifiAddhkdeviceStep2": "2. <PERSON><PERSON> op de plus in de rechter bovenhoek van de app en selecteer **Add Devi<PERSON>**.", "wifiAddhkdeviceStep3": "3. Volg de instructies van de app.", "wifiAddhkdeviceStep4": "4. <PERSON>u kan uw toestel geconfigureerd worden in de ELTAKO-Connect app.", "detailsConfigurationRuntime": "Looptijd", "detailsConfigurationRuntimeMode": "Mode", "generalTextManually": "<PERSON><PERSON><PERSON>", "detailsConfigurationRuntimeAutoDescription": "De zonweringsactor bepaalt zelfstandig de looptijd van de zonweringsmotor bij elke beweging van de onderste naar de bovenste eindstand (aanbevolen).\nNa de eerste inbedrijfstelling of veranderingen, moet een dergelijke beweging zonder onderbreking van beneden naar boven uitgevoerd worden.", "detailsConfigurationRuntimeManuallyDescription": "De looptijd van de zonweringsmotor wordt handmatig ingesteld via onderstaande looptijd.\nControleer of de ingestelde looptijd overeenkomt met de werkelijke looptijd van uw zonweringsmotor. \nNa de eerste inbedrijfstelling of veranderingen, moet een dergelijke beweging zonder onderbreking van beneden naar boven uitgevoerd worden.", "detailsConfigurationRuntimeDemoDescription": "De LCD-display-modus is enkel be<PERSON> via REST API", "generalTextDemomodeActive": "Demo mode actief", "detailsConfigurationRuntimeDuration": "<PERSON><PERSON>", "detailsConfigurationSwitchesGs4": "Groepenschakelaar met tip-omkeerfunctie (GS4)", "detailsConfigurationSwitchesGs4Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>kelaar met tip-omkeerfunctie voor het bedienen van jaloezieën", "screenshotSu12": "Buitenverlichting", "screenshotS2U12": "Buitenverlichting", "screenshotMfz12": "<PERSON><PERSON>", "screenshotEsr62": "<PERSON><PERSON>", "screenshotEud62": "Plafondlamp", "screenshotEsb62": "Zonnewering balkon", "detailsConfigurationEdgemodeLeadingedgeDescription": "LC1-LC3 zijn comfortposities met verschillende dimcurves voor dimbare 230 V LED-lampen, die zich wegens hun constructie niet voldoende laten afdimmen in de positie AUTO (fase-afsnijding) en dus gedimd moeten worden in fase-aansnijding.", "detailsConfigurationEdgemodeAutoDescription": "AUTO laat dimming toe van alle types van lampen.", "detailsConfigurationEdgemodeTrailingedge": "Fase-afsnijding", "detailsConfigurationEdgemodeTrailingedgeDescription": "LC4-LC6 zijn comfortposities met verschillende dimcurves voor dimbare 230V LED-lampen.", "updateHeader": "Firmware update", "updateTitleStepSearch": "Zoeken voor een update", "updateTitleStepFound": "Update gevonden", "updateTitleStepDownload": "Downloaden van de update", "updateTitleStepInstall": "Installeren van de update", "updateTitleStepSuccess": "Update geslaagd", "updateTitleStepUptodate": "Is reeds geüpdated", "updateTitleStepFailed": "Update mislukt", "updateButtonSearch": "<PERSON><PERSON> naar updates", "updateButtonInstall": "Installeer update", "updateCurrentversion": "Huidige versie", "updateNewversion": "Nieuwe firmware update beschikbaar", "updateHintPower": "De update start enkel wanneer de uitgang van het toestel niet actief is. Het toestel mag niet losgekoppeld worden van de voeding en de app mag niet worden afgesloten tijdens de update!", "updateButton": "Update", "updateHintCompatibility": "Een update is aanbe<PERSON>len, anders zullen sommige functies in de app beperkt zijn.", "generalTextDetails": "Details", "updateMessageStepMetadata": "Update informatie opladen", "updateMessageStepPrepare": "Update wordt voorbereid", "updateTitleStepUpdatesuccessful": "Update wordt gecontroleerd", "updateTextStepFailed": "<PERSON><PERSON> genoeg liep er iets fout tijdens de update, probeer het opnieuw binnen een paar minuten of wacht totdat uw toestel automatisch updatet (internetverbinding vereist).", "configurationsNotavailable": "<PERSON>r zijn nog geen configuraties beschikbaar.", "configurationsAddHint": "<PERSON><PERSON> ni<PERSON>we configuraties aan door een verbinding te maken en sla ze op.", "@configurationsAddHint": {"description": "Now available for all devices not only Bluetooth", "type": "text"}, "configurationsEdit": "<PERSON><PERSON><PERSON><PERSON> configuratie", "generalTextName": "<PERSON><PERSON>", "configurationsDelete": "Wis configuratie", "configurationsDeleteHint": "Wenst u werkelijk de configuratie: {configName} te wissen ?", "configurationsSave": "Sla configuratie op", "configurationsSaveHint": "Hier kan u de configuratie van uw huidig toestel opslaan, of een reeds bestaande configuratie opladen.", "configurationsImport": "Importeer configuratie", "configurationsImportHint": "Wenst u werkelijk de configuratie {configName} te transfereren ?", "generalTextConfigurations": "{count, plural, one {Configuration} other {Configurations}}", "configurationsStepPrepare": "Configuratie wordt voorbereid", "configurationsStepName": "Voer een naam in voor de configuratie", "configurationsStepSaving": "Configuratie wordt opgeslagen", "configurationsStepSavedsuccessfully": "Configuratie was succesvol opgeslagen", "configurationsStepSavingfailed": "<PERSON><PERSON><PERSON> configuratie mis<PERSON>t", "configurationsStepChoose": "Kies een configuratie", "configurationsStepImporting": "Configuratie wordt geïmporteerd", "configurationsStepImportedsuccessfully": "Configuratie was succesvol geïmporteerd", "configurationsStepImportingfailed": "Importeren configuratie mislukt", "discoveryAssuDescription": "Buiten stopcontact schakelklok Bluetooth", "settingsDatetimeDevicetime": "Huidige toestel tijd", "settingsDatetimeLoading": "Instellingen tijd zijn <PERSON>n", "discoveryEud12Description": "<PERSON><PERSON>", "generalTextOffdelay": "Afvalvertraging", "generalTextRemainingbrightness": "Resterende helderheid", "generalTextSwitchonvalue": "Inschakelwaarde", "motionsensorTitleNoremainingbrightness": "Geen resterende helderheid", "motionsensorTitleAlwaysremainingbrightness": "Met resterende helderheid", "motionsensorTitleRemainingbrightnesswithprogram": "Restlichtsterkte via schakelprogramma", "motionsensorTitleRemainingbrightnesswithprogramandzea": "Resthelderheid via ZE en ZA", "motionsensorTitleNoremainingbrightnessauto": "<PERSON><PERSON> (halfautomatisch)", "generalTextMotionsensor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextLightclock": "<PERSON>cht<PERSON><PERSON>", "generalTextSnoozeclock": "Sluimerfunctie", "generalDescriptionLightclock": "Bij het inschakelen ({mode}) wordt het licht na ongeveer 1 seconde op de laagste helderheid ingeschakeld en langzaam hooggedimd zonder het laatst opgeslagen helderheidsniveau te wijzigen.", "generalDescriptionSnoozeclock": "Bij het uitschakelen ({mode}) wordt de verlichting vanuit de huidige dimstand naar de minimale helderheid gedimd en uitgeschakeld. De verlichting kan op elk moment tijdens het dimproces worden uitgeschakeld door de knop kort in te drukken. Als u de knop lang indrukt tijdens het dimproces, wordt de verlichting gedimd en wordt de sluimerfunctie beëindigd.", "generalTextImmediately": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextPercentage": "Percentage", "generalTextSwitchoffprewarning": "Waarschuwing vooraf uitschakelen", "generalDescriptionSwitchoffprewarning": "Langzaam dimmen naar <PERSON><PERSON><PERSON>", "generalDescriptionOffdelay": "Het apparaat wordt ingeschakeld wanneer de stuurspanning wordt toegepast. Als de stuurspanning wordt onderbroken, begint het tijdsverloop, waarna het apparaat uitschakelt. Tijdens het tijdsverloop kan het apparaat stroomafwaarts worden ingeschakeld.", "generalDescriptionBrightness": "De <PERSON><PERSON>heid waarbij de lamp wordt ingeschakeld door de dimmer.", "generalDescriptionRemainingbrightness": "De dimwaarde in procenten tot waar de lamp wordt gedimd nadat de bewegingsdetector is uitgeschakeld.", "generalDescriptionRuntime": "<PERSON><PERSON><PERSON><PERSON> van de lichtalarmfunctie van minimale helderheid tot maximale helderheid.", "generalTextUniversalbutton": "<PERSON><PERSON> d<PERSON>", "generalTextDirectionalbutton": "Richtingknop", "eud12DescriptionAuto": "Automatische detectie UT/RT (met richtingssensordiode RTD)", "eud12DescriptionRt": "met richtingsgevoelige diode RTD", "generalTextProgram": "Programma", "eud12MotionsensorOff": "Met bewegingsdetector ingesteld op Uit", "eud12ClockmodeTitleProgramze": "Programma en centrale op", "eud12ClockmodeTitleProgramza": "Programma en Centraal Uit", "eud12ClockmodeTitleProgrambuttonon": "Programma en UT/RT Aan", "eud12ClockmodeTitleProgrambuttonoff": "Programma en UT/RT Uit", "eud12TiImpulseTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (t1)", "eud12TiImpulseHeader": "Dimwaard<PERSON> impulsti<PERSON><PERSON>", "eud12TiImpulseDescription": "De dimwa<PERSON>e in procenten tot waartoe de lamp wordt gedimd bij impulstijd <PERSON>.", "eud12TiOffTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (t2)", "eud12TiOffHeader": "Dimwaarde impulstijd <PERSON>", "eud12TiOffDescription": "De dimwa<PERSON>e in procenten tot waartoe de lamp wordt gedimd bij impulstijd <PERSON>.", "generalTextButtonpermanentlight": "Permanent drukknoplicht", "generalDescriptionButtonpermanentlight": "Inste<PERSON> van het continu licht met <PERSON><PERSON><PERSON><PERSON><PERSON> van 0 tot 10 uur in stappen van 0,5 uur. Activering door de knop langer dan 1 seconde in te drukken (1x flikkeren), deactivering door de knop langer dan 2 seconden in te drukken.", "generalTextNobuttonpermanentlight": "<PERSON><PERSON>", "generalTextBasicsettings": "Basisinstellingen", "generalTextInputswitch": "<PERSON><PERSON> knopingang (A1)", "generalTextOperationmode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextDimvalue": "inschakelgedrag", "eud12TitleUsememory": "Geheugenwaarde gebruiken", "eud12DescriptionUsememory": "De geheugenwaarde komt overeen met de laatst ingestelde dimwaarde. Als de geheugenwaarde is gede<PERSON>erd, wordt het dimmen altijd ingesteld op de inschakelwaarde.", "generalTextStartup": "Helderheid inscha<PERSON>en", "generalDescriptionSwitchonvalue": "De inschakelwaarde is een instelbare helderheidswaarde die een veilige inschakeling garandeert.", "generalTitleSwitchontime": "Inschakeltijd", "generalDescriptionSwitchontime": "Nadat de inschakeltijd is verstreken, wordt de lamp gedimd van de inschakelwaarde naar de geheugenwaarde.", "generalDescriptionStartup": "Sommige LED-lampen hebben een hogere inschakelstroom nodig om betrouwbaar in te schakelen. De lamp wordt bij deze inschakelwaarde ingeschakeld en na de inschakeltijd gedimd naar de geheugenwaarde.", "eud12ClockmodeSubtitleProgramze": "Klik kort op Centraal Aan", "eud12ClockmodeSubtitleProgramza": "Korte klik op centraal uit", "eud12ClockmodeSubtitleProgrambuttonon": "Dubbelklik op universele knop/richtingsknop Aan", "eud12ClockmodeSubtitleProgrambuttonoff": "Dubbelklik op universele knop/richtingsknop Uit", "eud12FunctionStairlighttimeswitchTitleShort": "TLZ | Verlichtingstimer trap", "eud12FunctionMinTitleShort": "MIN", "eud12FunctionMmxTitleShort": "MMX", "eud12FunctionTiDescription": "<PERSON>r met ins<PERSON><PERSON><PERSON> in- en uitschakel<PERSON>jd van 0,5 seconden tot 9,9 minuten. <PERSON> helderheid kan worden ingesteld van minimale helderheid tot maximale helderheid.", "eud12FunctionAutoDescription": "<PERSON><PERSON> met instelling voor bewegingsmelder, lichtalarm en sluimerfunctie.", "eud12FunctionErDescription": "Door relais te schakelen kan de helderheid worden ingesteld van minimale helderheid tot maximale helderheid.", "eud12FunctionEsvDescription": "<PERSON><PERSON> met instelling van een uitschakelvertraging van 1 tot 120 minuten. Uitschakelwaarschuwing op het einde door dimmen instelbaar en instelbaar van 1 tot 3 minuten. Beide centrale ingangen actief.", "eud12FunctionTlzDescription": "Instellen van de verlichtingsduur van de knop van 0 tot 10 uur in stappen van 0,5 uur. Activering door de knop langer dan 1 seconde in te drukken (1x flikkeren), deactivering door de knop langer dan 2 seconden in te drukken.", "eud12FunctionMinDescription": "<PERSON><PERSON>, schakelt naar de ingestelde minimale helderheid wanneer de stuurspanning wordt toegepast. Het licht wordt gedimd tot de maximale helderheid binnen de ingestelde dimtijd van 1 tot 120 minuten. Als de stuurspanning wordt verwijderd, wordt het licht onmiddellijk uitgeschakeld, zelfs tijdens de dimtijd. Beide centrale ingangen actief.", "eud12FunctionMmxDescription": "<PERSON><PERSON>, schakelt naar de ingestelde minimale helderheid wanneer de stuurspanning wordt toegepast. Tijdens de ingestelde dimtijd van 1 tot 120 minuten wordt het licht gedimd tot de maximale helderheid. <PERSON><PERSON> de stuurspanning echter wordt verwijderd, dimt de dimmer tot de ingestelde minimale helderheid. <PERSON><PERSON><PERSON> wordt hij uitgeschakeld. Beide centrale ingangen actief.", "motionsensorSubtitleNoremainingbrightness": "Met bewegingsdetector ingesteld op Uit", "motionsensorSubtitleAlwaysremainingbrightness": "Met bewegingsdetector ingesteld op Uit", "motionsensorSubtitleRemainingbrightnesswithprogram": "Schakelprogramma geactiveerd en gedeactiveerd met BWM-Uit", "motionsensorSubtitleRemainingbrightnesswithprogramandzea": "Centraal AAN activeert de bewegingssensor, Centraal UIT deactiveert de bewegingssensor, maar ook door het schakelen van het programma", "motionsensorSubtitleNoremainingbrightnessauto": "Bewegingsdetector schakelt alleen uit", "detailsDimsectionHeader": "<PERSON><PERSON><PERSON>", "generalTextFast": "Snel", "generalTextSlow": "<PERSON><PERSON><PERSON>", "eud12TextDimspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eud12TextSwitchonspeed": "Inschakelsnelheid", "eud12TextSwitchoffspeed": "Uitschakelsnelheid", "eud12DescriptionDimspeed": "De dimsnelheid is de snelheid waarmee de dimmer dimt van de huidige helderheid naar de gewenste helderheid.", "eud12DescriptionSwitchonspeed": "De inschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig in te schakelen.", "eud12DescriptionSwitchoffspeed": "De uitschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig uit te schakelen.", "settingsFactoryresetResetdimHeader": "Diminstellingen resetten", "settingsFactoryresetResetdimDescription": "Moeten echt alle diminstellingen worden gereset?", "settingsFactoryresetResetdimConfirmationDescription": "De diminstellingen zijn gereset", "eud12TextSwitchonoffspeed": "Aan/uit-schakel<PERSON><PERSON><PERSON>id", "eud12DescriptionSwitchonoffspeed": "De in-/uitschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig in of uit te schakelen.", "timerDetailsDimtoval": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON> in %", "timerDetailsDimtovalDescription": "De dimmer schakelt altijd in met de vaste dimwaarde in %.", "timerDetailsDimtovalSubtitle": "Inschakelen met {brightness}%", "timerDetailsDimtomem": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timerDetailsDimtomemSubtitle": "Inschakelen met geheugenwaarde", "timerDetailsMotionsensorwithremainingbrightness": "Restlichtsterkte (BWM) Aan", "timerDetailsMotionsensornoremainingbrightness": "Restlichtsterkte (BWM) Uit", "settingsRandommodeHint": "<PERSON><PERSON><PERSON> gea<PERSON><PERSON>e toevalsmodus worden alle programma's van dit kanaal will<PERSON> tot 15 minuten verschoven. On-timers worden vooraf verschoven, Off-timers worden vertraagd.", "runtimeOffsetDescription": "Bijkomende nalooptijd, na afloop van de tijd. Die kan gebruikt worden om zeker het eindpunt te bereiken.", "loadingTextDimvalue": "Dimwaarde wordt geladen", "discoveryEudipmDescription": "Universele dimmerschakelaar IP Matter", "generalTextOffset": "<PERSON><PERSON>", "eud12DimvalueTestText": "<PERSON><PERSON><PERSON><PERSON>", "eud12DimvalueTestDescription": "Tijdens het testen wordt rekening gehouden met de huidige ingestelde dimsnelheid.", "eud12DimvalueLoadText": "<PERSON><PERSON><PERSON><PERSON>", "settingsDatetimeNotime": "De datum- en tijdinstellingen moeten worden uitgelezen via het scherm van het apparaat.", "generalMatterText": "Matter", "generalMatterMessage": "<PERSON><PERSON> <PERSON>e <PERSON>-apparaat in met één van de volgende apps.", "generalMatterOpengooglehome": "Open Google Home", "generalMatterOpenamazonalexa": "Open Amazon Alexa", "generalMatterOpensmartthings": "Open SmartThings", "generalLabelProgram": "Programma {number}", "generalTextDone": "<PERSON><PERSON><PERSON>", "settingsRandommodeDescriptionShort": "Bij ingeschakelde toevalsmodus worden alle schakeltijden van het kanaal toevallig verschoven. Bij inschakeltijden tot 15 minuten eerder en uitschakeltijden tot 15 minuten later.", "all": "Alle", "discoveryBluetooth": "Bluetooth", "success": "Gelukt", "error": "Fout", "timeProgramAdd": "Tijdprogramma <PERSON>", "noConnection": "<PERSON><PERSON> verbinding", "timeProgramOnlyActive": "Geconfigureerde programma's", "timeProgramAll": "Alle programma's", "active": "Actief", "inactive": "Non actief", "timeProgramSaved": "Programma {number} opgeslaan", "deviceLanguageSaved": "Taal toestel opgeslaan", "generalTextTimeShort": "{time} uur", "programDeleteHint": "Moet programma {index} echt verwijderd worden?", "milliseconds": "{count, plural, one {milliseconde} other {milliseconde}}", "millisecondsWithValue": "{count, plural, one {{count} milliseconde} other {{count} milliseconden}}", "secondsWithValue": "{count, plural, one {{count} seconde} other {{count} seconde}}", "minutesWithValue": "{count, plural, one {{count} minuut} other {{count} minuten}}", "hoursWithValue": "{count, plural, one {{count} uur} other {{count} uren}}", "settingsPinFailEmpty": "De PIN mag niet leeg zijn.", "detailsConfigurationWifiloginScanNoMatch": "De gescande code past niet bij het toestel.", "wifiAuthorizationPopIsEmpty": "PoP mag niet leeg zijn.", "wifiAuthenticationCredentialsHint": "Omdat de app geen toegang heeft tot uw privaat WLAN-paswoord, kan uw ingave niet op juistheid gecontroleerd worden. Indien er geen verbinding ontstaan is, dient u het paswoord te controleren en opnieuw in te voeren.", "generalMatterOpenApplehome": "Apple Home openen", "timeProgramNoActive": "<PERSON><PERSON> gecon<PERSON>de programma's.", "timeProgramNoEmpty": "<PERSON><PERSON> vrij tijdprogram<PERSON> be<PERSON>.", "nameOfConfiguration": "<PERSON><PERSON> configuratie", "currentDevice": "<PERSON><PERSON><PERSON>", "export": "Exporteren", "import": "Importeren", "savedConfigurations": "Opgeslagen configuratie", "importableServicesLabel": "Volgende instellingen kunnen geïmporteerd worden:", "notImportableServicesLabel": "Ongeldige instellingen", "deviceCategoryMeterGateway": "Teller-gateway", "deviceCategory2ChannelTimeSwitch": "2-kana<PERSON> tijdschakelklok Bluetooth", "devicategoryOutdoorTimeSwitchBluetooth": "Buitenstekker tijdschakelklok Bluetooth", "settingsModbusHeader": "Modbus", "settingsModbusDescription": "<PERSON><PERSON> <PERSON>, prioriteit en timeout aan, om de overdrachtsnelheid, foutherkenning en wachttijd te configureren.", "settingsModbusRTU": "Modbus RTU", "settingsModbusBaudrate": "Baudrate", "settingsModbusParity": "Prioriteit", "settingsModbusTimeout": "Modbus timeout", "locationServiceDisabled": "Standpla<PERSON> is gede<PERSON>erd", "locationPermissionDenied": "<PERSON><PERSON> uw locatie-autorisatie toestemming om uw huidige positie op te halen.", "locationPermissionDeniedPermanently": "De toestemming van uw locatie is continu geblok<PERSON><PERSON>. <PERSON><PERSON><PERSON> de toestem<PERSON> van uw standplaats in uw toestelinstellingen toe te staan om uw actuele positie op te vragen.", "lastSync": "laatst gesynchroniseerd", "dhcpActive": "DHCP actief", "ipAddress": "IP", "subnetMask": "Subnetmasker", "standardGateway": "Standaard gateway", "dns": "DNS", "alternateDNS": "Alternatieve DNS", "errorNoNetworksFound": "Geen WiFi netwerk gevonden", "availableNetworks": "Beschikbare netwerken", "enableWifiInterface": "WiFi interface activeren", "enableLANInterface": "LAN interface activeren", "hintDontDisableAllInterfaces": "Let erop, dat niet alle interfaces gedeactiveerd zijn.\nDe laatst geactiveerde interface heeft prioriteit.", "ssid": "SSID", "searchNetworks": "WiFi netwerken zoeken", "errorNoNetworkEnabled": "Minstens één interface moet geactiveerd zijn.", "errorActiveNetworkInvalid": "Niet alle actieve stations zijn geldig.", "invalidNetworkConfiguration": "Ongeldige netwerkconfiguratie", "generalDefault": "Standaard", "mqttHeader": "MQTT", "mqttConnected": "Verbin<PERSON> met MQTT-broker", "mqttDisconnected": "<PERSON><PERSON> met MQTT-broker.", "mqttBrokerURI": "Broker <PERSON>", "mqttBrokerURIHint": "MQTT-Broker U<PERSON>", "mqttPort": "Poort", "mqttPortHint": "MQTT-poort", "mqttClientId": "Client-ID", "mqttClientIdHint": "MQTT Client-ID", "mqttUsername": "Gebruikersnaam", "mqttUsernameHint": "MQTT-Gebruikersnaam", "mqttPassword": "Paswoord", "mqttPasswordHint": "MQTT-paswoord", "mqttCertificate": "Certificaat (optioneel)", "mqttCertificateHint": "MQTT-certificaat", "mqttTopic": "Topic", "mqttTopicHint": "MQTT-topic", "electricityMeter": "Energieteller", "electricityMeterCurrent": "<PERSON><PERSON><PERSON>", "electricityMeterHistory": "<PERSON>er<PERSON>", "electricityMeterReading": "Tellerstand", "connectivity": "Verbinding", "electricMeter": "{count, plural, one {energiemeter} other {energiemeters}}", "discoveryZGW16Description": "Modbus-energiemeter-MQTT-gateway", "bluetoothConnectionLost": "Bluetooth verbinding verloren.", "bluetoothConnectionLostDescription": "<PERSON> Bluetooth verbinding met het toestel werd verbroken. Het toestel dient binnen het bereik te zijn.", "openBluetoothSettings": "Instellingen openen", "password": "Paswoord", "setInitialPassword": "Initieel paswoord toewijzen", "initialPasswordMinimumLength": "Het paswoord moet minstens {length} karakters lang zijn.", "repeatPassword": "Pas<PERSON><PERSON> herhalen", "passwordsDoNotMatch": "De paswoorden stemmen niet overeen.", "savePassword": "Paswoord opslaan", "savePasswordHint": "Het paswoord wordt voor toekomstige aanmeldingen opgeslaan op uw toestel.", "retrieveNtpServer": "Haal tijd op van de NTP-server", "retrieveNtpServerFailed": "<PERSON> met de NTP-server kon niet gemaakt worden.", "retrieveNtpServerSuccess": "<PERSON> verbinding met de NTP-server was geslaagd.", "settingsPasswordNewPasswordDescription": "Nieuw paswoord ingeven", "settingsPasswordConfirmationDescription": "Wijziging paswoord geslaagd", "dhcpRangeStart": "DHCP startbereik", "dhcpRangeEnd": "DHCP eindbereik", "forwardOnMQTT": "<PERSON><PERSON><PERSON> van MQTT", "showAll": "<PERSON>es a<PERSON>", "hide": "Verbergen", "changeToAPMode": "<PERSON><PERSON><PERSON> in de AP-modus", "changeToAPModeDescription": "U staat op het punt om uw toestel met een WiFi netwerk te verbinden. In dit geval wordt de verbinding naar het toestel gescheiden en dient u zich opnieuw over het geconfigureerde netwerk met uw toestel te verbinden.", "consumption": "Verbruik", "currentDay": "Vandaag", "twoWeeks": "2 weken", "oneYear": "1 jaar", "threeYears": "3 jaar", "passwordMinLength": "Het paswoord moet minstens {length} karakters lang zijn.", "passwordNeedsLetter": "Het paswoord moet minstens één hoofdletter bevatten.", "passwordNeedsNumber": "Het paswoord moet minstens één cijfer bevatten.", "portEmpty": "<PERSON>t mag niet leeg zijn", "portInvalid": "Ongeldige poort", "portOutOfRange": "De poort moet tussen {rangeStart} en {rangeEnd} zijn.", "ipAddressEmpty": "IP-adres mag niet leeg zijn.", "ipAddressInvalid": "Ongeldig IP-adres", "subnetMaskEmpty": "Subnetmasker mag niet leeg zijn.", "subnetMaskInvalid": "Ongeldig subnetmasker", "gatewayEmpty": "Gateway mag niet leeg zijn.", "gatewayInvalid": "Ongeldig gateway", "dnsEmpty": "DNS mag niet leeg zijn.", "dnsInvalid": "Ongeldige DNS", "uriEmpty": "URI mag niet leeg zijn.", "uriInvalid": "Ongeldige URI", "electricityMeterChangedSuccessfully": "Energieteller werd succesvol gewijzigd.", "networkChangedSuccessfully": "Netwerkconfiguratie werd succesvol gewijzigd.", "mqttChangedSuccessfully": "MQTT-configuratie werd succesvol gewijzigd.", "modbusChangedSuccessfully": "Modbus instellingen werden succesvol gewijzigd.", "loginData": "Login data wissen", "valueConfigured": "Geconfigureerd", "electricityMeterHistoryNoData": "Geen data beschik<PERSON>ar", "locationChangedSuccessfully": "Standplaats werd succesvol gewijzigd.", "settingsNameFailEmpty": "<PERSON>am mag niet leeg zijn.", "settingsNameFailLength": "<PERSON><PERSON> moet langer zijn dan {length} karakters.", "solsticeChangedSuccesfully": "Tijdsverschil tijdens de zonnewende werd succesvol gewijzigd.", "relayFunctionChangedSuccesfully": "Relaisfunctie werd succesvol gewijzigd.", "relayFunctionHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dimmerValueChangedSuccesfully": "Inschakelgedrag werd succesvol gewijzigd.", "dimmerBehaviourChangedSuccesfully": "Dimgedrag werd succesvol gewijzigd.", "dimmerBrightnessDescription": "De minimum en maximum helder<PERSON>id heeft betrekking op alle instelbare held<PERSON><PERSON><PERSON> van de dimmer.", "dimmerSettingsChangedSuccesfully": "Basisinstellingen werden succesvol gewijzigd.", "liveUpdateEnabled": "Live test geactiveerd", "liveUpdateDisabled": "Live test gedeactiveerd", "liveUpdateDescription": "De laatst gewijzigde schuifwaarde wordt naar het toestel gestuurd.", "demoDevices": "Demo toestel", "showDemoDevices": "Demo toestel aanduiden", "deviceCategoryTimeSwitch": "Tijdschakelklok", "deviceCategoryMultifunctionalRelay": "Multifunctie-tij<PERSON><PERSON>s", "deviceCategoryDimmer": "<PERSON><PERSON>", "deviceCategoryShutter": "Actor voor rolluiken en zonneweringen", "deviceCategoryRelay": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "configurationsHeader": "Configuraties", "configurationsDescription": "Hier kan u uw opgeslagen configuraties beheren.", "configurationsNameFailEmpty": "<PERSON><PERSON> van de configuratie mag niet leeg zijn.", "configurationDeleted": "Configuratie gewist", "codeFound": "{codeType} code herkend", "errorCameraPermission": "<PERSON><PERSON><PERSON>g tot uw camera te geven om de ELTAKO-code te scannen.", "authorizationSuccessful": "Succesvol toegestaan aan het toestel", "wifiAuthenticationResetConfirmationDescription": "Authentificatie werd succesvol teruggezet.", "settingsResetConnectionHeader": "Verbinding terugzetten", "settingsResetConnectionDescription": "Moet de verbinding werkelijk teruggezet worden?", "settingsResetConnectionConfirmationDescription": "Verbinding werd succesvol teruggezet.", "wiredInputChangedSuccesfully": "Drukknopgedrag werd succesvol gewijzigd.", "runtimeChangedSuccesfully": "Looptijd werd succesvol gewijzigd.", "expertModeActivated": "Experten-modus geactiveerd", "expertModeDeactivated": "Experten-modus gede<PERSON>erd", "license": "Licenties", "retry": "<PERSON><PERSON><PERSON> opnieuw", "provisioningConnectingHint": "Verbinding wordt gemaakt met het toestel. Dit kan tot 1 minuut duren.", "serialnumberEmpty": "Serienummer mag niet leeg zijn.", "interfaceStateInactiveDescriptionBLE": "Bluetooth is gede<PERSON><PERSON>. <PERSON><PERSON><PERSON> het te activeren om het Bluetooth toestel te vinden.", "interfaceStateDeniedDescriptionBLE": "Bluetooth-machtigingen zijn niet verleend", "interfaceStatePermanentDeniedDescriptionBLE": "Bluetooth-machtigingen zijn niet verleend. <PERSON><PERSON><PERSON>-verbindingen toe te staan in de instellingen van uw toestel.", "requestPermission": "<PERSON><PERSON><PERSON> machtiging aan", "goToSettings": "<PERSON><PERSON> de instellingen", "enableBluetooth": "Bluetooth activeren", "installed": "Geïnstalleerd", "teachInDialogDescription": "Wenst u uw toestel via {type} in te leren?", "useMatter": "<PERSON> gebruiken", "relayMode": "Relaismodus activeren", "whatsNew": "Nieuw in deze versie", "migrationHint": "Opdat u uw nieuwe functies zou kunnen aanwenden, dienen wij uw data te migreren.", "migrationHeader": "<PERSON><PERSON><PERSON>", "migrationProgress": "Wij ruimen op...", "letsGo": "We zijn ermee bezig", "noDevicesFound": "<PERSON>n toestellen gevonden. <PERSON><PERSON><PERSON> te controleren of uw toestel in de Pairing-Modus is.", "interfaceStateEmpty": "<PERSON><PERSON> gevonden", "ssidEmpty": "SSID mag niet leeg zijn", "passwordEmpty": "Paswoord mag niet leeg zijn", "settingsDeleteSettingsHeader": "Instellingen terugplaatsen", "settingsDeleteSettingsDescription": "Moeten echt alle instellingen teruggeplaatst worden?", "settingsDeleteSettingsConfirmationDescription": "Alle instellingen werden succesvol teruggeplaatst.", "locationNotFound": "Standplaats niet gevonden.", "timerProgramEmptySaveHint": "Het tijdprogramma is leeg en kan niet opgeslagen worden. Bewerking beëindigen?", "timerProgramDaysEmptySaveHint": "<PERSON>n dagen gekozen. Het tijdprogramma toch opslaan?", "timeProgramNoDays": "Een programma zonder actieve dagen kan niet geactiveerd worden.", "timeProgramColliding": "Tijdprogramma botst met programma {program}.", "timeProgramDuplicated": "Tijdprogramma is een duplicaat van programma {program}.", "screenshotZgw16": "<PERSON><PERSON><PERSON><PERSON><PERSON> huis", "interfaceStateUnknown": "<PERSON><PERSON> gevonden", "settingsPinChange": "Wijzig PIN", "timeProgrammOneTime": "<PERSON><PERSON><PERSON><PERSON>", "timeProgrammRepeating": "Her<PERSON>nd", "generalIgnore": "Negeren", "timeProgramChooseDay": "<PERSON>g kiezen", "generalToday": "Vandaag", "generalTomorrow": "<PERSON><PERSON>", "bluetoothAndPINChangedSuccessfully": "Bluetooth en PIN succesvol gewijzigd", "generalTextDimTime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "discoverySu62Description": "1-kana<PERSON> schakelklok Bluetooth", "bluetoothAlwaysOnTitle": "<PERSON><PERSON>u aan", "bluetoothAlwaysOnDescription": "Bluetooth is continu geactiveerd.", "bluetoothAlwaysOnHint": "Opgelet: indien deze instelling geactiveerd is, is dit toestel via Bluetooth permanent zichtbaar voor iedereen! Het is aanbevolen om de standaard PIN-code te veranderen.", "bluetoothManualStartupOnTitle": "Tijdelijk aan", "bluetoothManualStartupOnDescription": "Na het a<PERSON><PERSON><PERSON> van de spanning wordt Bluetooth voor 3 minuten geactiveerd.", "bluetoothManualStartupOnHint": "Opgelet: Pairing standby wordt gedurende 3 minuten geactiveerd en schakelt dan uit. Als er een nieuwe verbinding tot stand moet gebracht worden, dient men de knop ongeveer 5 seconden ingedrukt te houden.", "bluetoothManualStartupOffTitle": "<PERSON><PERSON><PERSON> aan", "bluetoothManualStartupOffDescription": "Bluetooth wordt manueel geactiveerd via de knopingang en is dan 3 minuten actief.", "bluetoothManualStartupOffHint": "Opgelet: om Bluetooth te activeren, moet de deukknop aan de knopingang voor ca. 5 seconden inged<PERSON>t blijven.", "timeProgrammOneTimeRepeatingDescription": "Programma's kunnen herhaaldelijk worden uitgevoerd door altijd een schakeling uit te voeren op de geconfigureerde dagen en tijden, of ze kunnen slechts eenmaal worden uitgevoerd op de geconfigureerde schakeltijd.", "versionHeader": "V<PERSON><PERSON> {version}", "releaseNotesHeader": "Release opmerkingen", "release30Header": "De nieuwe ELTAKO Connect-app is uit!", "release30FeatureDesignHeader": "Nieuwe design", "release30FeatureDesignDescription": "De app werd compleet herwerkt en schittert in een nieuwe design. De bediening is nu nog gemakkelijker en intuïtiever.", "release30FeaturePerformanceHeader": "Betere prestaties", "release30FeaturePerformanceDescription": "Geniet van een soepelere ervaring en verkorte oplaadtijden - voor een betere gebruiksbeleving.", "release30FeatureConfigurationHeader": "Apparaatoverkoepelende configuraties", "release30FeatureConfigurationDescription": "Laad toestelconfiguraties op en breng ze over op andere toestellen. Zelfs als ze niet dezelfde hardware hebben, kan u vb. de configuratie van uw S2U12DBT1+1-UC  overhevelen op een ASSU-BT en omgekeerd.", "release31Header": "Vanaf heden de nieuwe 1-kana<PERSON> inbouwschakelklok met Bluetooth!", "release31Description": "Wat kan de SU62PF-BT/UC?", "release31SU62Name": "SU62PF-BT/UC", "release31DeviceNote1": "Tot 60 schakelprogramma's.", "release31DeviceNote2": "De klok kan toestellen schakelen volgens vaste tijden of met de astro-functie gebaseerd op de zonsopgang en zonsondergang.", "release31DeviceNote3": "Toevalsmodus: schakel<PERSON><PERSON>den kunnen toevallig tot wel 15 minuten verschoven worden.", "release31DeviceNote4": "Zomer-/wintertijd: de klok schakelt automatisch op zomer- en wintertijd.", "release31DeviceNote5": "Universele voedings- en stuurspanning 12-230V UC.", "release31DeviceNote6": "<PERSON><PERSON><PERSON> met drukk<PERSON>p voor handmatig schakelen.", "release31DeviceNote7": "1 NO potentiaalvrij 10 A/250 V AC.", "release31DeviceNote8": "<PERSON><PERSON><PERSON><PERSON> uitvoeren van tijdprogramma's.", "generalNew": "<PERSON><PERSON><PERSON>", "yearsAgo": "{count, plural, one {vorig jaar} other {voor {count} jaren}}", "monthsAgo": "{count, plural, one {vorige maand} other {voor {count} maanden}}", "weeksAgo": "{count, plural, one {vorige week} other {voor {count} weken}}", "daysAgo": "{count, plural, one {gisteren} other {voor {count} dagen}}", "minutesAgo": "{count, plural, one {voor één minuut} other {voor {count} minuten}}", "hoursAgo": "{count, plural, one {voor één uur} other {voor {count} uren}}", "secondsAgo": "{count, plural, one {voor <PERSON> seconde} other {voor {count} seconden}}", "justNow": "Zojuist", "discoveryEsripmDescription": "Teleruptor-schakelrelais IP Matter", "generalTextKidsRoom": "Kinderkamerschakeling", "generalDescriptionKidsRoom": "Door iets langer op de drukknop te duwen ({mode}), gaat de verlichting pas na ongeveer 1 seconde op zijn minimumwa<PERSON>e oplichten en gaat, zo lang men blij<PERSON> duwen, gel<PERSON><PERSON><PERSON><PERSON> aan feller branden, zonder dat de eerder ingestelde lichtintensiteit veranderd wordt.", "generalTextSceneButton": "Sfeerdrukknop", "settingsEnOceanConfigHeader": "EnOcean configuratie", "enOceanConfigChangedSuccessfully": "EnOcean configuratie werd succesvol gewijzigd.", "activateEnOceanRepeater": "EnOcean repeater activeren", "enOceanRepeaterLevel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enOceanRepeaterLevel1": "1-niveau", "enOceanRepeaterLevel2": "2-nive<PERSON>", "enOceanRepeaterOffDescription": "Er worden geen radiosignalen van sensoren herhaald.", "enOceanRepeaterLevel1Description": "Enkel de radiosignalen van sensoren worden ontvangen, gecontroleerd en met vol vermogen doorgestuurd. Radiosignalen van andere repeaters worden genegeerd, om de hoeveelheid data te beperken.", "enOceanRepeaterLevel2Description": "Niet enkel de radiosignalen van sensoren maar ook de radiosignalen van 1-niveau-repeaters worden verwerkt. Zodoende kan een radiosignaal maximaal 2 maal ontvangen en versterkt worden. Radiorepeaters dienen niet ingeleerd te worden. Ze ontvangen en versterken de radiosignalen van alle radiosensoren binnen hun ontvangstbereik.", "settingsSensorHeader": "Sensoren", "sensorChangedSuccessfully": "Sensoren werden succesvol gewijzigd", "wiredButton": "<PERSON><PERSON> d<PERSON>", "enOceanId": "EnOcean-ID", "enOceanAddManually": "EnOcean-ID ingeven of scannen", "enOceanIdInvalid": "Ongeldig EnOcean-ID", "enOceanAddAutomatically": "Met EnOcean-telegrammen inleren", "enOceanAddDescription": "Met het EnOcean radio-protocol is het mogelijk om drukknoppen in te leren in uw actor en te bedienen.\n<PERSON><PERSON> ofwel het automatisch inleren met EnOcean telegram, om drukknoppen door erop te drukken in te leren of kies de manuele variant, om de EnOcean-ID van uw drukknop in te scannen of in te typen.", "enOceanTelegram": "Telegram", "enOceanCodeScan": "Voer de EnOcean ID van uw {sensorType} in of scan de EnOcean QR-code van uw {sensorType} om deze toe te voegen.", "enOceanCode": "EnOcean QR-code", "enOceanCodeScanDescription": "Zoek naar de EnOcean QR-code op uw {sensorType} en scan die met uw camera.", "enOceanButton": "EnOcean drukknop", "enOceanBackpack": "EnOcean adapter", "sensorNotAvailable": "Er werden nog geen sensoren ingeleerd", "sensorAdd": "<PERSON><PERSON>", "sensorCancel": "Inleren afbreken", "sensorCancelDescription": "Wenst u het inleerproces werkelijk te stoppen?", "getEnOceanBackpack": "Ga uw EnOcean-adapter halen", "enOceanBackpackMissing": "Om toegang te krijgen tot de fantastische wereld van de perfecte connectiviteit en communicatie, heeft u een EnOcean-adapter nodig. <PERSON><PERSON> hier, om meer informatie te bekomen.", "sensorEditChangedSuccessfully": "{sensorName} werd succesvol gewij<PERSON>d", "sensorConnectedVia": "verbonden met {deviceName}", "lastSeen": "Laatst gezien", "setButtonOrientation": "Oriëntatie bevestigen", "setButtonType": "Type drukknop bevestigen", "button1Way": "1-kana<PERSON> drukknop", "button2Way": "2-kana<PERSON> drukknop", "button4Way": "4-kana<PERSON> drukknop", "buttonUnset": "niet geb<PERSON><PERSON>t", "button": "drukknop", "sensor": "sensor", "sensorsFound": "{count, plural, =0 {geen sensoren gevonden} one {1 sensor gevonden} other {{count} sensoren gevonden}}", "sensorSearch": "<PERSON><PERSON> naar <PERSON>en", "searchAgain": "Opnieuw zoeken", "sensorTeachInHeader": "{sensorType} inleren", "sensorChooseHeader": "{sensorType} uitkiezen", "sensorChooseDescription": "Kies een drukknop uit, die u wenst in te leren.", "sensorCategoryDescription": "Kies een categorie uit, die u wenst in te leren.", "sensorName": "Benaming drukknop", "sensorNameFooter": "<PERSON><PERSON> de <PERSON>p een naam", "sensorAddedSuccessfully": "{sensorName} werd succesvol ingeleerd", "sensorDelete": "{sensorType} wissen", "sensorDeleteHint": "<PERSON><PERSON> de {sensorType} {sensorName} werkelijk gewist worden?", "sensorDeletedSuccessfully": "{sensorName} werd succesvol gewist", "buttonTapDescription": "Druk op de drukknop die u wenst in te leren.", "waitingForTelegram": "De actor wacht op het telegram", "copied": "Gecopieerd", "pairingFailed": "{sensorType} reeds ingeleerd", "generalDescriptionUniversalbutton": "Bij de universele drukknop volgt de richtingsomkeer door het kort loslaten van de drukknop. Korte stuurbevelen schakelen aan of uit. (toggelen)", "generalDescriptionDirectionalbutton": "Richtingsdrukknop is bovenaan 'inschakelen en opdimmen' en onderaan 'uitschakelen en afdimmen'.", "matterForwardingDescription": "De richtingsdruk wordt naar Matter doorgestuurd.", "none": "<PERSON><PERSON>", "buttonNoneDescription": "De drukknop heeft geen functie.", "buttonUnsetDescription": "<PERSON><PERSON> de knop is geen functie toe<PERSON>.", "sensorButtonTypeChangedSuccessfully": "Het type drukknop werd succesvol gewijzigd.", "forExample": "vb. {example}}", "enOceanQRCodeInvalidDescription": "Pas vanaf productiedatum 44/20 mogelijk", "input": "<PERSON>gang", "buttonSceneValueOverride": "Waarde sfeerdrukknop overschrijven", "buttonSceneValueOverrideDescription": "De sfeerdrukknopwaarde wordt overschreven met de actuele dimwaarde door de drukknop ingedrukt te houden.", "buttonSceneDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> schakelt in met een vaste dimwaarde", "buttonPress": "Druk op de drukknop", "triggerOn": "door langer op de drukknop te duwen van de universele drukknop of richtingsdrukknop op de inschakelzijde", "triggerOff": "met een dubbele impuls op de universele drukknop of richtingsdrukknop op de uitschakelzijde", "centralOn": "centraal aan", "centralOff": "centraal uit", "centralButton": "centrale drukknop", "enOceanAdapterNotFound": "<PERSON><PERSON>n adapter gevonden", "updateRequired": "Update vereist", "updateRequiredDescription": "Gelieve uw app te updaten, om dit nieuw toestel te ondersteunen.", "release32Header": "De nieuwe reeks 64 met Matter en EnOcean alsook de nieuwe Bluetooth-inbouw-schakelklok SU62PF-BT/UC zijn nu verkrijgbaar!", "release32EUD64Header": "De nieuwe inbouwdimmer met Matter over Wi-Fi en tot 300W is beschik<PERSON><PERSON>!", "release32EUD64Note1": "Configuratie van <PERSON>, aan-/uits<PERSON><PERSON><PERSON><PERSON>he<PERSON>, kinderkamer-/sluimerschakeling en zo veel meer.", "release32EUD64Note2": "De functie-om<PERSON><PERSON> van de EUD64NPN-IPM kan per adapter, vb. de EnOcean-adapter EOA64, uitgebreid worden.", "release32EUD64Note3": "Er kunnen max. 30 EnOcean-radiodrukknoppen direct met de EnOcean-adapter EOA64 gekoppeld worden aan de EUD64NPN-IPM en naar Matter doorgestuurd worden.", "release32EUD64Note4": "Twee bedrade drukknopingangen kunnen rechtstreeks gekoppeld worden aan de EUD64NPN-IPM of naar Matter doorgestuurd worden.", "release32ESR64Header": "De nieuwe potentiaalvrije inbouw-1-kana<PERSON>-schakel<PERSON> met Matter via Wi-Fi en tot 16 A is beschik<PERSON><PERSON>!", "release32ESR64Note1": "Configuratie van verschillende functies zoa<PERSON> te<PERSON><PERSON> (ES), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ER), NO (ER-inversie) en zo veel meer.", "release32ESR64Note2": "Het aantal functies van de ESR64PF-IPM kan met de adapter, vb. de EnOcean-adapter EOA64, uitgebreid worden.", "release32ESR64Note3": "Tot 30 EnOcean-radiodrukknoppen kunnen verbonden met de EnOcean-adapter EOA64 met de ESR64PF-IP direct gekoppeld en naar Matter doorgestuurd worden.", "release32ESR64Note4": "<PERSON><PERSON> bedrade druk<PERSON><PERSON><PERSON> kan met de ESR64PF-IPM direct verbonden of naar Matter doorgestuurd worden.", "buttonsFound": "{count, plural, =0 {geen drukknoppen gevonden} one {1 drukknop gevonden} other {{count} drukknoppen gevonden}}", "doubleImpuls": "met een dubbele impuls", "impulseDescription": "Als het kanaal ingeschakeld is, wordt dit door een impuls uitgeschakeld.", "locationServiceEnable": "<PERSON><PERSON><PERSON>", "locationServiceDisabledDescription": "Locatie is uitgeschakeld. De versie van uw besturingssysteem vereist de locatie om Bluetooth-apparaten te kunnen vinden.", "locationPermissionDeniedNoPosition": "Locatietoestemmingen zijn niet toegekend. De versie van uw besturingssysteem vereist locatieautorisatie om Bluetooth-apparaten te kunnen vinden. Sta de locatieautorisatie toe in de instellingen van uw apparaat.", "interfaceStatePermanentDeniedDescriptionDevicesAround": "De machtiging voor apparaten in de buurt is niet toegekend. Sta de machtiging voor apparaten in de buurt toe in de instellingen van uw apparaat.", "permissionNearbyDevices": "Apparaten in de buurt", "release320Header": "<PERSON>, krachtigere universele dimmer EUD12NPN-BT/600W-230V is er!", "release320EUD600Header": "Wat kan de nieuwe universele dimmer?", "release320EUD600Note1": "<PERSON><PERSON> dimmer met een vermogen tot 600W", "release320EUD600Note2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met een vermogenuitbreiding tot 3800W", "release320EUD600Note3": "Lokale bediening met universele knop/richtingsknop", "release320EUD600Note4": "Centrale sturing aan/uit", "release320EUD600Note5": "Ingang bewe<PERSON><PERSON><PERSON>er voor meer comfort", "release320EUD600Note6": "Geïntegreer<PERSON> schakelklok met 10 schakelprogramma's", "release320EUD600Note7": "Astrofunctie", "release320EUD600Note8": "Individuele inschakelhelderheid", "mqttClientCertificate": "<PERSON><PERSON> certificaat", "mqttClientCertificateHint": "MQTT Klant certificaat", "mqttClientKey": "<PERSON><PERSON> sleutel", "mqttClientKeyHint": "MQTT Klant sleutel", "mqttClientPassword": "<PERSON><PERSON>", "mqttClientPasswordHint": "MQTT Klant paswoord", "mqttEnableHomeAssistantDiscovery": "HomeAssistant MQTT herkenning activeren", "modbusTcp": "Modbus TCP", "enableInterface": "Interface activeren", "busAddress": "Bus-adres", "busAddressWithAddress": "Bus-adressen {index}", "deviceType": "Type toestel", "registerTable": "{count, plural, one {Registertabel} other {Registertabel}}", "currentValues": "<PERSON><PERSON><PERSON> waarde", "requestRTU": "RTU zoeken", "requestPriority": "Zoekprioriteit", "mqttForwarding": "MQTT doorsturen", "historicData": "Historische data", "dataFormat": "Dataformat", "dataType": "Datatype", "description": "Beschrijving", "readWrite": "Lezen/schrijven", "unit": "<PERSON><PERSON><PERSON><PERSON>", "registerTableReset": "Registertabel terugzetten", "registerTableResetDescription": "Moet de registertabel echt teruggezet worden?", "notConfigured": "<PERSON><PERSON>", "release330ZGW16Header": "Omvangrijke update voor de ZGW16WL-IP", "release330Header": "De ZGW16WL-IP met tot 16 energietellers", "release330ZGW16Note1": "Ondersteuning van tot 16 ELTAKO Modbus-energietellers", "release330ZGW16Note2": "Ondersteuning van Modbus TCP", "release330ZGW16Note3": "Ondersteuning van MQTT Discovery", "screenshotButtonLivingRoom": "Drukknoppen woonkamer", "registerChangedSuccessfully": "Register werd succesvol veranderd.", "serverCertificateEmpty": "Server certificaat mag niet leeg zijn.", "registerTemplates": "Register templates", "registerTemplateChangedSuccessfully": "Register template werd succesvol veranderd.", "registerTemplateReset": "Register template terugzetten", "registerTemplateResetDescription": "Moet het register template werkelijk teruggezet worden?", "registerTemplateNotAvailable": "Geen register templates be<PERSON><PERSON><PERSON><PERSON>", "rename": "<PERSON><PERSON><PERSON><PERSON>", "registerName": "Registernaam", "registerRenameDescription": "<PERSON>f het register een door de gebruiker gedefinieerde naam", "restart": "Toestel opnieuw opstarten", "restartDescription": "Wenst u het toestel werkelijk opnieuw op te starten?", "restartConfirmationDescription": "Het toestel wordt nu opnieuw opgestart", "deleteAllElectricityMeters": "Alle energiemeters wissen", "deleteAllElectricityMetersDescription": "Wenst u werkelijk alle energiemeters te wissen?", "deleteAllElectricityMetersConfirmationDescription": "Alle energiemeters werden succesvol gewist", "resetAllElectricityMeters": "Alle energiemeterconfiguraties terugzetten", "resetAllElectricityMetersDescription": "Wenst u werkelijk alle energiemeterconfiguraties terug te zetten?", "resetAllElectricityMetersConfirmationDescription": "Alle energiemeterconfiguraties werden succesvol teruggezet", "deleteElectricityMeterHistories": "Alle energiemeterdata wissen", "deleteElectricityMeterHistoriesDescription": "Wenst u werkelijk alle energiemeterdata te wissen?", "deleteElectricityMeterHistoriesConfirmationDescription": "Alle energiemeterdata werden succesvol gewist", "multipleElectricityMetersSupportMissing": "Uw toestel ondersteunt momenteel slechts één energiemeter. Gelieve uw firmware te actualiseren.", "consumptionWithUnit": "Verbruik (kWh)", "exportWithUnit": "Levering (kWh)", "importWithUnit": "Afname (kWh)", "resourceWarningHeader": "Beperkingen in middelen", "mqttAndTcpResourceWarning": "Het is niet mogelijk om MQTT en Modbus TCP tegelijkertijd te gebruiken vanwege beperkte systeembronnen. Deactiveer {protocol} eerst.", "mqttEnabled": "MQTT ingeschakeld", "redirectMQTT": "Ga naar MQTT-instellingen", "redirectModbus": "Ga naar Modbus-instellingen", "unsupportedSettingDescription": "Met uw huidige firmwareversie worden sommige apparaatinstellingen niet ondersteund. Update uw firmware om de nieuwe functies te gebruiken", "updateNow": "Nu bijwerken", "zgw241Hint": "Met deze update is Modbus TCP standaard ingeschakeld en MQTT uitgeschakeld. Dit kan worden gewijzigd in de instellingen. Met ondersteuning voor maximaal 16 tellers zijn er veel optimalisaties doorgevoerd; dit kan leiden tot wijzigingen in de instellingen van het apparaat. Herstart het apparaat na het aanpassen van de instellingen.", "deviceConfigChangedSuccesfully": "Looptijd werd succesvol gewijzigd.", "deviceConfiguration": "Apparaatconfiguratie", "tiltModeToggle": "<PERSON><PERSON><PERSON>odus", "tiltModeToggleFooter": "Als het apparaat is ingesteld in Matter, moeten alle functies daar opnieuw worden geconfigureerd.", "shaderMovementDirection": "Achteruit O<PERSON>hoog/Omlaag", "shaderMovementDirectionDescription": "<PERSON><PERSON> de richting om voor omhoog/omlaag-beweging<PERSON> van de motor", "tiltTime": "Kantel runtime", "changeTiltModeDialogTitle": "{target, select, true {Enable} false {Disable} other {Change}} kantelfunctie", "changeTiltModeDialogConfirmation": "{target, select, true {Enable} false {Disable} other {Change}}", "generalTextSlatSetting": "Instelling lamellen", "generalTextPosition": "<PERSON><PERSON><PERSON>", "generalTextSlatPosition": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slatSettingDescription": "Beschrijving lamelleninstelling", "scenePositionSliderDescription": "<PERSON><PERSON><PERSON>", "sceneSlatPositionSliderDescription": "Kantelen", "referenceRun": "<PERSON><PERSON><PERSON><PERSON>", "slatAutoSettingHint": "In deze modus is de stand van de zonwering niet van belang voordat de lamellen zich aanpassen aan de gewenste kantelstand.", "slatReversalSettingHint": "In deze modus sluit de zonwering volledig voordat de lamellen zich aanpassen aan de gewenste kantelpositie.", "release340Header": "De nieuwe ESB64NP-IPM-actuator voor inbouwzonwering is er!", "release340ESB64Header": "Wat kan de ESB64NP-IPM?", "release340ESB64Note1": "Onze Matter Gateway-gecertificeerde zonweringactuator met <PERSON><PERSON>", "release340ESB64Note2": "Twee bedrade knopingangen voor handmatig schakelen en doorsturen naar Matter", "release340ESB64Note3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met EnOcean adapter (EOA64). Bijvoorbeeld met EnOcean draad<PERSON>ze drukknop F4T55", "release340ESB64Note4": "Open voor integraties dankzij REST API gebaseerd op OpenAPI-standaard", "activateTiltModeDialogText": "Als de kantelfunctie is ingeschakeld, gaan alle instellingen verloren. Weet je zeker dat je de kantelfunctie wilt inschakelen?", "deactivateTiltModeDialogText": "Als je de kantelfunctie uitschakelt, gaan alle instellingen verloren. Weet je zeker dat je de kantelfunctie wilt uitschakelen?"}