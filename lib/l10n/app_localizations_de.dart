// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appName => 'ELTAKO Connect';

  @override
  String get discoveryHint => 'Bluetooth am Gerät aktivieren, um zu verbinden.';

  @override
  String devicesFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Geräte gefunden',
      one: '1 Gerät gefunden',
      zero: 'Keine Geräte gefunden',
    );
    return '$_temp0';
  }

  @override
  String discoveryDemodeviceName(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Demo Geräte',
      one: 'Demo Gerät',
    );
    return '$_temp0';
  }

  @override
  String get discoverySu12Description => '2-Kanal Schaltuhr Bluetooth';

  @override
  String get discoveryImprint => 'Impressum';

  @override
  String get discoveryLegalnotice => 'Datenschutzerklärung';

  @override
  String get generalSave => 'Speichern';

  @override
  String get generalCancel => 'Abbrechen';

  @override
  String get detailsHeaderHardwareversion => 'Hardware Version';

  @override
  String get detailsHeaderSoftwareversion => 'Software Version';

  @override
  String get detailsHeaderConnected => 'Verbunden';

  @override
  String get detailsHeaderDisconnected => 'Getrennt';

  @override
  String get detailsTimersectionHeader => 'Programme';

  @override
  String get detailsTimersectionTimercount => 'von 60 Programmen verwendet';

  @override
  String get detailsConfigurationsectionHeader => 'Konfiguration';

  @override
  String get detailsConfigurationPin => 'Geräte-PIN';

  @override
  String detailsConfigurationChannelsDescription(
    Object channel1,
    Object channel2,
  ) {
    return 'Kanal 1: $channel1 und Kanal 2: $channel2';
  }

  @override
  String get settingsCentralHeader => 'Zentral Ein/Aus';

  @override
  String get detailsConfigurationCentralDescription =>
      'Greift nur, wenn der Kanal auf Auto steht.';

  @override
  String get detailsConfigurationDevicedisplaylock => 'Gerätedisplay sperren';

  @override
  String get timerOverviewHeader => 'Programme';

  @override
  String get timerOverviewTimersectionTimerinactivecount => 'inaktiv';

  @override
  String get timerDetailsListsectionDays1 => 'Montag';

  @override
  String get timerDetailsListsectionDays2 => 'Dienstag';

  @override
  String get timerDetailsListsectionDays3 => 'Mittwoch';

  @override
  String get timerDetailsListsectionDays4 => 'Donnerstag';

  @override
  String get timerDetailsListsectionDays5 => 'Freitag';

  @override
  String get timerDetailsListsectionDays6 => 'Samstag';

  @override
  String get timerDetailsListsectionDays7 => 'Sonntag';

  @override
  String get timerDetailsHeader => 'Programm';

  @override
  String get timerDetailsSunrise => 'Sonnenaufgang';

  @override
  String get generalToggleOff => 'Aus';

  @override
  String get generalToggleOn => 'Ein';

  @override
  String get timerDetailsImpuls => 'Impuls';

  @override
  String get generalTextTime => 'Uhrzeit';

  @override
  String get generalTextAstro => 'Astro';

  @override
  String get generalTextAuto => 'Auto';

  @override
  String get timerDetailsOffset => 'Zeitversatz';

  @override
  String get timerDetailsPlausibility => 'Plausibilitätsprüfung aktivieren';

  @override
  String get timerDetailsPlausibilityDescription =>
      'Liegt die \'Aus\'-Zeit vor der \'Ein\'-Zeit werden beide Zeiten ignoriert, z.B. Schalte zu Sonnenaufgang ein und um 6:00 morgens aus. Es gibt auch Konstellationen zu denen die Prüfung ungewollt ist, z.B. Schalte zu Sonnenuntergang ein und um 1:00 nachts aus.';

  @override
  String get generalDone => 'Fertig';

  @override
  String get generalDelete => 'Löschen';

  @override
  String get timerDetailsImpulsDescription =>
      'Ändere die globale Impuls Konfiguration';

  @override
  String get settingsNameHeader => 'Gerätename';

  @override
  String get settingsNameDescription =>
      'Dieser Name dient zur Identifikation des Geräts.';

  @override
  String get settingsFactoryresetHeader => 'Werkseinstellungen';

  @override
  String get settingsFactoryresetDescription =>
      'Welche Inhalte sollen zurückgesetzt werden?';

  @override
  String get settingsFactoryresetResetbluetooth =>
      'Bluetooth Einstellungen zurücksetzen';

  @override
  String get settingsFactoryresetResettime =>
      'Uhrzeit Einstellungen zurücksetzen';

  @override
  String get settingsFactoryresetResetall =>
      'Auf Werkseinstellungen zurücksetzen';

  @override
  String get settingsDeletetimerHeader => 'Programme löschen';

  @override
  String get settingsDeletetimerDescription =>
      'Sollen wirklich alle Programme gelöscht werden?';

  @override
  String get settingsDeletetimerAllchannels => 'Alle Kanäle';

  @override
  String get settingsImpulseHeader => 'Impuls-Schaltzeit';

  @override
  String get settingsImpulseDescription =>
      'Die Impuls-Schaltzeit gibt die Dauer des Impulses an.';

  @override
  String get generalTextRandommode => 'Zufallsmodus';

  @override
  String get settingsChannelsTimeoffsetHeader => 'Zeitversatz Sonnenwende';

  @override
  String settingsChannelsTimeoffsetDescription(
    Object summerOffset,
    Object winterOffset,
  ) {
    return 'Sommer: $summerOffset Min | Winter: $winterOffset Min';
  }

  @override
  String get settingsLocationHeader => 'Standort';

  @override
  String get settingsLocationDescription =>
      'Lege deinen Standort fest, um Astro-Funktionen zu nutzen.';

  @override
  String get settingsLanguageHeader => 'Gerätesprache';

  @override
  String get settingsLanguageSetlanguageautomatically =>
      'Sprache automatisch einstellen';

  @override
  String settingsLanguageDescription(Object deviceType) {
    return 'Wähle die Sprache der $deviceType';
  }

  @override
  String get settingsLanguageGerman => 'Deutsch';

  @override
  String get settingsLanguageFrench => 'Französisch';

  @override
  String get settingsLanguageEnglish => 'Englisch';

  @override
  String get settingsLanguageItalian => 'Italienisch';

  @override
  String get settingsLanguageSpanish => 'Spanisch';

  @override
  String get settingsDatetimeHeader => 'Datum und Uhrzeit';

  @override
  String get settingsDatetimeSettimeautomatically => 'Systemzeit übernehmen';

  @override
  String get settingsDatetimeSettimezoneautomatically =>
      'Zeitzone automatisch einstellen';

  @override
  String get generalTextTimezone => 'Zeitzone';

  @override
  String get settingsDatetime24Hformat => '24-Stunden-Format';

  @override
  String get settingsDatetimeSetsummerwintertimeautomatically =>
      'Sommer-/Winterzeit automatisch';

  @override
  String get settingsDatetimeWinter => 'Winter';

  @override
  String get settingsDatetimeSummer => 'Sommer';

  @override
  String get settingsPasskeyHeader => 'Aktuelle Geräte-PIN';

  @override
  String get settingsPasskeyDescription => 'Aktuelle PIN des Geräts eingeben';

  @override
  String get timerDetailsActiveprogram => 'Programm aktiv';

  @override
  String get timerDetailsActivedays => 'Aktive Tage';

  @override
  String get timerDetailsSuccessdialogHeader => 'Erfolgreich';

  @override
  String get timerDetailsSuccessdialogDescription =>
      'Programm erfolgreich hinzugefügt.';

  @override
  String get settingsRandommodeDescription =>
      'Der Zufallsmodus funktioniert nur bei Zeit-Programmen, nicht bei Impuls- oder Astro-Programmen (Sonnenauf- oder untergang).';

  @override
  String get settingsSolsticeHeader => 'Zeitversatz Sonnenwende';

  @override
  String get settingsSolsticeDescription =>
      'Die Zeit gibt den Zeitversatz zum Sonnenuntergang an. Der Sonnenaufgang wird entsprechend invertiert.';

  @override
  String get settingsSolsticeHint =>
      'Beispiel: \nIm Winter wird 30 Minuten vor Sonnenuntergang geschalten, dadurch wird ebenfalls 30 Minuten nach Sonnenaufgang geschalten.';

  @override
  String get generalTextMinutesShort => 'Min';

  @override
  String get settingsPinDescription =>
      'Die PIN wird für die Verbindung benötigt.';

  @override
  String get settingsPinHeader => 'Neue Geräte-PIN';

  @override
  String get settingsPinNewpinDescription => 'Neue PIN eingeben';

  @override
  String get settingsPinNewpinRepeat => 'Neue PIN wiederholen';

  @override
  String get detailsProductinfo => 'Produktinformation';

  @override
  String get settingsDatetimeSettimeautodescription =>
      'Wähle die gewünschte Uhrzeit';

  @override
  String minutes(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Minuten',
      one: 'Minute',
    );
    return '$_temp0';
  }

  @override
  String hours(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Stunden',
      one: 'Stunde',
    );
    return '$_temp0';
  }

  @override
  String seconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Sekunden',
      one: 'Sekunde',
    );
    return '$_temp0';
  }

  @override
  String generalTextChannel(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Kanäle',
      one: 'Kanal',
    );
    return '$_temp0';
  }

  @override
  String generalLabelChannel(Object number) {
    return 'Kanal $number';
  }

  @override
  String get generalTextDate => 'Datum';

  @override
  String get settingsDatetime24HformatDescription =>
      'Wähle das bevorzugte Format';

  @override
  String get settingsDatetimeSetsummerwintertime => 'Sommer-/Winterzeit';

  @override
  String get settingsDatetime24HformatValue24 => '24-Stunden';

  @override
  String get settingsDatetime24HformatValue12 => '12-Stunden';

  @override
  String get detailsEdittimer => 'Programme bearbeiten';

  @override
  String get settingsPinOldpinRepeat => 'Bitte die alte PIN wiederholen';

  @override
  String get settingsPinCheckpin => 'PIN wird überprüft';

  @override
  String detailsDevice(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Geräte',
      one: 'Gerät',
    );
    return '$_temp0';
  }

  @override
  String get detailsDisconnect => 'Verbindung trennen';

  @override
  String get settingsCentralDescription =>
      'Der Eingang A1 steuert den Zentral Ein/Aus.\nZentral Ein/Aus greift nur, wenn der Kanal auf Zentral Ein/Aus steht.';

  @override
  String get settingsCentralHint =>
      'Beispiel:\nKanal 1 = Zentral Ein/Aus\nKanal 2 = Aus\nA1 = Zentral Ein -> Nur K1 schaltet auf Ein, K2 bleibt Aus.';

  @override
  String get settingsCentralToggleheader => 'Zentral Eingang schaltet';

  @override
  String get settingsCentralActivechannelsdescription =>
      'Aktuelle Kanäle mit der Einstellung Zentral Ein/Aus:';

  @override
  String get settingsSolsticeSign => 'Vorzeichen';

  @override
  String get settingsDatetimeTimezoneDescription => 'Mitteleuropäische Zeit';

  @override
  String get generalButtonContinue => 'Weiter';

  @override
  String get settingsPinConfirmationDescription => 'PIN Änderung erfolgreich.';

  @override
  String get settingsPinFailDescription => 'PIN Änderung fehlgeschlagen.';

  @override
  String get settingsPinFailHeader => 'Fehlgeschlagen';

  @override
  String get settingsPinFailShort => 'Die PIN muss exakt 6 Ziffern lang sein';

  @override
  String get settingsPinFailWrong => 'Die aktuelle PIN ist nicht korrekt';

  @override
  String get settingsPinFailMatch => 'Die PINs stimmen nicht überein';

  @override
  String get discoveryLostconnectionHeader => 'Verbindung verloren';

  @override
  String get discoveryLostconnectionDescription =>
      'Die Verbindung zum Gerät wurde getrennt.';

  @override
  String get settingsChannelConfigCentralDescription =>
      'Verhält sich wie AUTO und reagiert zusätzlich auf die drahtgebundenen Zentraleingänge.';

  @override
  String get settingsChannelConfigOnDescription =>
      'Schaltet den Kanal auf Dauer-Ein und ignoriert die Programme.';

  @override
  String get settingsChannelConfigOffDescription =>
      'Schaltet den Kanal auf Dauer-Aus und ignoriert die Programme.';

  @override
  String get settingsChannelConfigAutoDescription =>
      'Schaltet entsprechend der Zeit- und Astro-Programme.';

  @override
  String get bluetoothPermissionDescription =>
      'Bluetooth wird für die Konfiguration der Geräte benötigt.';

  @override
  String get timerListitemOn => 'Einschalten';

  @override
  String get timerListitemOff => 'Ausschalten';

  @override
  String get timerListitemUnknown => 'Unbekannt';

  @override
  String get timerDetailsAstroHint =>
      'Der Standort muss in den Einstellungen gesetzt sein, damit die Astro-Programme korrekt funktionieren.';

  @override
  String get timerDetailsTrigger => 'Auslöser';

  @override
  String get timerDetailsSunset => 'Sonnenuntergang';

  @override
  String get settingsLocationCoordinates => 'Koordinaten';

  @override
  String get settingsLocationLatitude => 'Breitengrad';

  @override
  String get settingsLocationLongitude => 'Längengrad';

  @override
  String timerOverviewEmptyday(Object day) {
    return 'Für $day werden momentan keine Programme verwendet.';
  }

  @override
  String get timerOverviewProgramloaded => 'Programme werden geladen...';

  @override
  String get timerOverviewProgramchanged => 'Programm wurde geändert.';

  @override
  String get settingsDatetimeProcessing => 'Datum und Uhrzeit wird geändert.';

  @override
  String get deviceNameEmpty => 'Die Eingabe darf nicht leer sein.';

  @override
  String deviceNameHint(Object count) {
    return 'Die Eingabe darf nicht mehr als $count Zeichen enthalten.';
  }

  @override
  String get deviceNameChanged => 'Gerätename wird geändert.';

  @override
  String get deviceNameChangedSuccessfully =>
      'Gerätename wurde erfolgreich geändert.';

  @override
  String get deviceNameChangedFailed => 'Ein Fehler ist aufgetreten.';

  @override
  String get settingsPinConfirm => 'Bestätigen';

  @override
  String get deviceShowInstructions =>
      '1. Bluetooth der Uhr mit SET aktivieren\n2. Button oben antippen, um die Suche zu starten';

  @override
  String get deviceNameNew => 'Neuen Gerätename eingeben';

  @override
  String get settingsLanguageRetrieved => 'Sprache wird abgerufen';

  @override
  String get detailsProgramsShow => 'Programme anzeigen';

  @override
  String get generalTextProcessing => 'Bitte warten';

  @override
  String get generalTextRetrieving => 'werden abgerufen.';

  @override
  String get settingsLocationPermission =>
      'Erlaube der ELTAKO Connect den Zugriff auf den Standort dieses Gerätes.';

  @override
  String get timerOverviewChannelloaded => 'Kanäle werden geladen';

  @override
  String get generalTextRandommodeChanged => 'Zufallsmodus wird geändert';

  @override
  String get detailsConfigurationsectionChanged =>
      'Konfiguration wird geändert';

  @override
  String get settingsSettimeFunctions => 'Zeitfunktionen werden geändert';

  @override
  String get imprintContact => 'Kontakt';

  @override
  String get imprintPhone => 'Telefon';

  @override
  String get imprintMail => 'E-Mail';

  @override
  String get imprintRegistrycourt => 'Registergericht';

  @override
  String get imprintRegistrynumber => 'Registernummer';

  @override
  String get imprintCeo => 'Geschäftsführer';

  @override
  String get imprintTaxnumber => 'Umsatzsteuer-Identifikationsnummer';

  @override
  String get settingsLocationCurrent => 'Aktueller Standort';

  @override
  String get generalTextReset => 'Zurücksetzen';

  @override
  String get discoverySearchStart => 'Suche starten';

  @override
  String get discoverySearchStop => 'Suche stoppen';

  @override
  String get settingsImpulsSaved => 'Impuls-Schaltzeit wird gespeichert';

  @override
  String get settingsCentralNochannel =>
      'Es gibt keine Kanäle mit der Einstellung Zentral Ein/Aus.';

  @override
  String get settingsFactoryresetBluetoothConfirmationDescription =>
      'Bluetooth Verbindung wurde erfolgreich zurückgesetzt.';

  @override
  String get settingsFactoryresetBluetoothFailDescription =>
      'Bluetooth Verbindungen zurücksetzen fehlgeschlagen.';

  @override
  String get imprintPublisher => 'Herausgeber';

  @override
  String get discoveryDeviceConnecting => 'Verbindung wird hergestellt';

  @override
  String get discoveryDeviceRestarting => 'Neu starten...';

  @override
  String get generalTextConfigurationsaved => 'Kanalkonfiguration gespeichert.';

  @override
  String get timerOverviewChannelssaved => 'Kanäle speichern';

  @override
  String get timerOverviewSaved => 'Timer gespeichert';

  @override
  String get timerSectionList => 'Listenansicht';

  @override
  String get timerSectionDayview => 'Tagesansicht';

  @override
  String get generalTextChannelInstructions => 'Kanaleinstellungen';

  @override
  String get generalTextPublisher => 'Herausgeber';

  @override
  String get settingsDeletetimerDialog =>
      'Wollen Sie wirklich alle Programme löschen?';

  @override
  String get settingsFactoryresetResetbluetoothDialog =>
      'Sollen wirklich alle Bluetooth Einstellungen zurückgesetzt werden?';

  @override
  String get settingsCentralTogglecentral => 'Zentral\nEin/Aus';

  @override
  String generalTextConfirmation(Object serviceName) {
    return '$serviceName wurde erfolgreich geändert.';
  }

  @override
  String generalTextFailed(Object serviceName) {
    return '$serviceName konnte nicht geändert werden.';
  }

  @override
  String get settingsChannelConfirmationDescription =>
      'Kanäle wurden erfolgreich geändert.';

  @override
  String get timerDetailsSaveHeader => 'Programm speichern';

  @override
  String get timerDetailsDeleteHeader => 'Programm löschen';

  @override
  String get timerDetailsSaveDescription =>
      'Programm wurde erfolgreich gespeichert.';

  @override
  String get timerDetailsDeleteDescription =>
      'Programm wurde erfolgreich gelöscht.';

  @override
  String get timerDetailsAlertweekdays =>
      'Das Programm kann nicht gespeichert werden, da kein Wochentag ausgewählt wurde.';

  @override
  String get generalTextOk => 'OK';

  @override
  String get settingsDatetimeChangesuccesfull =>
      'Datum und Uhrzeit wurden erfolgreich geändert.';

  @override
  String get discoveryConnectionFailed => 'Verbindung fehlgeschlagen';

  @override
  String get discoveryDeviceResetrequired =>
      'Es konnte keine Verbindung mit dem Gerät hergestellt werden, um dieses Problem zu beheben lösche das Gerät aus deinen Bluetooth Einstellungen. Sollte das Problem weiterhin bestehen wende dich bitte an unseren technischen Support.';

  @override
  String get generalTextSearch => 'Geräte suchen';

  @override
  String get generalTextOr => 'oder';

  @override
  String get settingsFactoryresetProgramsConfirmationDescription =>
      'Alle Programme wurden erfolgreich gelöscht.';

  @override
  String get generalTextManualentry => 'Manuelle Eingabe';

  @override
  String get settingsLocationSaved => 'Gespeicherter Standort';

  @override
  String get settingsLocationAutosearch => 'Automatische Standortsuche';

  @override
  String get imprintPhoneNumber => '+49 711 / 9435 0000';

  @override
  String get imprintMailAddress => '<EMAIL>';

  @override
  String get settingsFactoryresetResetallDialog =>
      'Soll das Gerät wirklich auf Werkseinstellungen zurückgesetzt werden?';

  @override
  String get settingsFactoryresetFactoryConfirmationDescription =>
      'Das Gerät wurde erfolgreich auf Werkseinstellungen zurückgesetzt.';

  @override
  String get settingsFactoryresetFactoryFailDescription =>
      'Gerät zurücksetzen fehlgeschlagen.';

  @override
  String get imprintPhoneNumberIos => '+49711/94350000';

  @override
  String get mfzFunctionA2Title => '2-Stufen-Ansprechverzögerung (A2)';

  @override
  String get mfzFunctionA2TitleShort => '2-Stufen-Ansprechverzögerung (A2)';

  @override
  String get mfzFunctionA2Description =>
      'Mit dem Anlegen der Steuerspannung beginnt der Zeitablauf T1 zwischen 0 und 60 Sekunden. An dessen Ende schließt der Kontakt 1-2 und es beginnt der Zeitablauf T2 zwischen 0 und 60 Sekunden. An dessen Ende schließt der Kontakt 3-4. Nach einer Unterbrechung beginnt der Zeitablauf erneut mit T1.';

  @override
  String get mfzFunctionRvTitle =>
      'Rückfallverzögerung (RV; Ausschaltverzögerung)';

  @override
  String get mfzFunctionRvTitleShort => 'RV | Rückfallverzögerung';

  @override
  String get mfzFunctionRvDescription =>
      'Beim Anlegen der Steuerspannung wechselt der Arbeitskontakt nach 15-18. \nMit Unterbrechung der Steuerspannung beginnt der Zeitablauf, an dessen Ende der Arbeitskontakt in die Ruhelage zurückkehrt. Nachschaltbar während des Zeitablaufs.\n';

  @override
  String get mfzFunctionTiTitle =>
      'Taktgeber mit Impuls beginnend (TI; Blinkrelais)';

  @override
  String get mfzFunctionTiTitleShort => 'TI | Taktgeber mit Impuls beginnend';

  @override
  String get mfzFunctionTiDescription =>
      'Solange die Steuerspannung anliegt, schließt und öffnet der Arbeitskontakt. Die Umschaltzeit in beide Richtungen ist getrennt einstellbar. Beim Anlegen der Steuerspannung wechselt der Arbeitskontakt sofort nach 15-18.';

  @override
  String get mfzFunctionAvTitle =>
      'Ansprechverzögerung (AV; Einschaltverzögerung)';

  @override
  String get mfzFunctionAvTitleShort => 'AV | Ansprechverzögerung';

  @override
  String get mfzFunctionAvDescription =>
      'Mit dem Anlegen der Steuerspannung beginnt der Zeitablauf, an dessen Ende der Arbeitskontakt nach 15-18 wechselt. Nach einer Unterbrechung beginnt der Zeitablauf erneut.';

  @override
  String get mfzFunctionAvPlusTitle =>
      'Additive Ansprechverzögerung (AV+; Einschaltverzögerung)';

  @override
  String get mfzFunctionAvPlusTitleShort =>
      'AV+ | Additive Ansprechverzögerung';

  @override
  String get mfzFunctionAvPlusDescription =>
      'Funktion wie AV, nach einer Unterbrechung bleibt jedoch die bereits abgelaufene Zeit gespeichert.';

  @override
  String get mfzFunctionAwTitle => 'Ausschaltwischrelais (AW)';

  @override
  String get mfzFunctionAwTitleShort => 'AW | Ausschaltwischrelais';

  @override
  String get mfzFunctionAwDescription =>
      'Bei Unterbrechung der Steuerspannung wechselt der Arbeitskontakt nach 15-18 und kehrt nach Ablauf der Wischzeit zurück. Beim Anlegen der Steuerspannung während der Wischzeit kehrt der Arbeitskontakt sofort in die Ruhelage zurück, und die Restzeit wird gelöscht.';

  @override
  String get mfzFunctionIfTitle => 'Impulsformer (IF; nur MFZ12.1)';

  @override
  String get mfzFunctionIfTitleShort => 'IF | Impulsformer';

  @override
  String get mfzFunctionIfDescription =>
      'Mit dem Anlegen der Steuerspannung wechselt der Arbeitskontakt für die eingestellte Zeit nach 15-18. Weitere Ansteuerungen werden erst nach dem Ablauf der eingestellten Zeit ausgewertet.';

  @override
  String get mfzFunctionEwTitle => 'Einschaltwischrelais (EW)';

  @override
  String get mfzFunctionEwTitleShort => 'EW | Einschaltwischrelais';

  @override
  String get mfzFunctionEwDescription =>
      'Mit dem Anlegen der Steuerspannung wechselt der Arbeitskontakt nach 15-18 und kehrt nach Ablauf der Wischzeit zurück. Bei Wegnahme der Steuerspannung, während der Wischzeit kehrt der Arbeitskontakt sofort in die Ruhelage zurück und die Restzeit wird gelöscht.\n';

  @override
  String get mfzFunctionEawTitle =>
      'Einschalt- und Ausschaltwischrelais (EAW; nur MFZ12.1)';

  @override
  String get mfzFunctionEawTitleShort =>
      'EAW | Einschalt- und Ausschaltwischrelais';

  @override
  String get mfzFunctionEawDescription =>
      'Mit dem Anlegen und Unterbrechen der Steuerspannung wechselt der Arbeitskontakt nach 15-18 und kehrt nach der eingestellten Wischzeit zurück.';

  @override
  String get mfzFunctionTpTitle =>
      'Taktgeber mit Pause beginnend (TP; Blinkrelais, nur MFZ12.1)';

  @override
  String get mfzFunctionTpTitleShort => 'TP | Taktgeber mit Pause beginnend';

  @override
  String get mfzFunctionTpDescription =>
      'Funktionsbeschreibungen wie TI, beim Anlegen der Steuerspannung wechselt der Kontakt jedoch nicht nach 15-18, sondern bleibt zunächst bei 15-16 bzw. offen.';

  @override
  String get mfzFunctionIaTitle =>
      'Impulsgesteuerte Ansprechverzögerung und Impulsformer (IA; nur MFZ12.1)';

  @override
  String get mfzFunctionIaTitleShort =>
      'IA | Impulsgesteuerte Ansprechverzögerung';

  @override
  String get mfzFunctionIaDescription =>
      'Mit dem Beginn eines Steuerimpulses ab 20 ms beginnt der Zeitablauf T1, an dessen\nEnde der Arbeitskontakt für die Zeit T2 nach 15-18 wechselt (z. B. für automatische Türöffner). Wird T1 auf die kürzeste Zeit 0,1 s gestellt, arbeitet IA als Impulsformer, bei welchem T2 abläuft, unabhängig von der Länge des Steuersignals (mind. 150 ms).';

  @override
  String get mfzFunctionArvTitle => 'Ansprech- und Rückfallverzögerung (ARV)';

  @override
  String get mfzFunctionArvTitleShort =>
      'ARV | Ansprech- und Rückfallverzögerung';

  @override
  String get mfzFunctionArvDescription =>
      'Mit dem Anlegen der Steuerspannung beginnt der Zeitablauf, an dessen Ende der Arbeitskontakt nach 15 -18 wechselt. Wird danach die Steuerspannung unterbrochen, beginnt ein weiterer Zeitablauf, an dessen Ende der Arbeitskontakt in die Ruhelage zurückkehrt.\nNach einer Unterbrechung der Ansprechverzögerung beginnt der Zeitablauf erneut.';

  @override
  String get mfzFunctionArvPlusTitle =>
      'Additive Ansprech- und Rückfallverzögerung (ARV+)';

  @override
  String get mfzFunctionArvPlusTitleShort =>
      'ARV+ | Additive Ansprech- und Rückfallverzögerung';

  @override
  String get mfzFunctionArvPlusDescription =>
      'Funktion wie ARV, nach einer Unterbrechung der Ansprechverzögerung bleibt jedoch die bereits abgelaufene Zeit gespeichert.';

  @override
  String get mfzFunctionEsTitle => 'Stromstoßschalter (ES)';

  @override
  String get mfzFunctionEsTitleShort => 'ES | Stromstoßschalter';

  @override
  String get mfzFunctionEsDescription =>
      'Mit Steuerimpulsen ab 50 ms schaltet der Arbeitskontakt hin und her.';

  @override
  String get mfzFunctionEsvTitle =>
      'Stromstoßschalter mit Rückfallverzögerung und Ausschaltvorwarnung (ESV)';

  @override
  String get mfzFunctionEsvTitleShort =>
      'ESV | Stromstoßschalter mit Rückfallverzögerung';

  @override
  String get mfzFunctionEsvDescription =>
      'Funktion wie SRV. Zusätzlich mit Ausschaltvorwarnung: ca. 30 Sekunden vor Zeitablauf beginnend flackert die Beleuchtung 3-mal in kürzer werdenden Zeitabständen.';

  @override
  String get mfzFunctionErTitle => 'Relais-Funktion (ER)';

  @override
  String get mfzFunctionErTitleShort => 'ER | Relais-Funktion';

  @override
  String get mfzFunctionErDescription =>
      'Solange der Steuerkontakt geschlossen ist, schaltet der Arbeitskontakt von 15-16 auf 15-18.';

  @override
  String get mfzFunctionSrvTitle =>
      'Stromstoßschalter mit Rückfallverzögerung (SRV)';

  @override
  String get mfzFunctionSrvTitleShort =>
      'SRV | Stromstoßschalter mit Rückfallverzögerung';

  @override
  String get mfzFunctionSrvDescription =>
      'Mit Steuerimpulsen ab 50ms schaltet der Arbeitskontakt hin und her. In der Kontaktstellung 15-18 schaltet das Gerät nach Ablauf der Verzögerungszeit selbsttätig in die Ruhestellung 15-16 zurück.';

  @override
  String get detailsFunctionsHeader => 'Funktionen';

  @override
  String mfzFunctionTimeHeader(Object index) {
    return 'Zeit (t$index)';
  }

  @override
  String get mfzFunctionOnDescription => 'Dauer Ein.';

  @override
  String get mfzFunctionOffDescription => 'Dauer Aus.';

  @override
  String get mfzFunctionMultiplier => 'Faktor';

  @override
  String get discoveryMfz12Description => 'Multifunktions-Zeitrelais Bluetooth';

  @override
  String get mfzFunctionOnTitle => 'Ein (ON)';

  @override
  String get mfzFunctionOnTitleShort => 'Ein (ON)';

  @override
  String get mfzFunctionOffTitle => 'Aus (OFF)';

  @override
  String get mfzFunctionOffTitleShort => 'Aus (OFF)';

  @override
  String get mfzMultiplierSecondsFloatingpoint => '0.1 Sekunden';

  @override
  String get mfzMultiplierMinutesFloatingpoint => '0.1 Minuten';

  @override
  String get mfzMultiplierHoursFloatingpoint => '0.1 Stunden';

  @override
  String get mfzOverviewFunctionsloaded => 'Funktionen werden geladen';

  @override
  String get mfzOverviewSaved => 'Funktion gespeichert';

  @override
  String get settingsBluetoothHeader => 'Bluetooth';

  @override
  String get bluetoothChangedSuccessfully =>
      'Bluetooth-Einstellung wurde erfolgreich geändert';

  @override
  String get settingsBluetoothInformation =>
      'Hinweis: Wenn diese Einstellung aktiviert wird, ist das Gerät über Bluetooth dauerhaft für jeden sichtbar!\nEs wird empfohlen den Geräte-PIN zu ändern.';

  @override
  String get settingsBluetoothContinuousconnection => 'Dauerhafte Sichtbarkeit';

  @override
  String settingsBluetoothContinuousconnectionDescription(Object deviceType) {
    return 'Durch das Aktivieren der dauerhaften Sichtbarkeit, bleibt Bluetooth am Gerät ($deviceType) aktiv und muss vor dem Verbindungsaufbau nicht manuell aktiviert werden.';
  }

  @override
  String get settingsBluetoothTimeout => 'Verbindungs-Timeout';

  @override
  String get settingsBluetoothPinlimit => 'PIN-Beschränkung';

  @override
  String settingsBluetoothTimeoutDescription(Object timeout) {
    return 'Die Verbindung wird nach $timeout Minuten Inaktivität getrennt.';
  }

  @override
  String settingsBluetoothPinlimitDescription(Object attempts) {
    return 'Aus Sicherheitsgründen hast du maximal $attempts Versuche für die PIN-Eingabe. Anschließend wird Bluetooth deaktiviert und muss für eine neue Verbindung erst manuell wieder aktiviert werden.';
  }

  @override
  String get settingsBluetoothPinAttempts => 'Versuche';

  @override
  String get settingsResetfunctionHeader => 'Funktionen zurücksetzen';

  @override
  String get settingsResetfunctionDialog =>
      'Sollen wirklich alle Funktionen zurückgesetzt werden?';

  @override
  String get settingsFactoryresetFunctionsConfirmationDescription =>
      'Alle Funktionen wurden erfolgreich zurückgesetzt.';

  @override
  String get mfzFunctionTime => 'Zeit (t)';

  @override
  String get discoveryConnectionFailedInfo => 'Keine Bluetooth-Verbindung.';

  @override
  String get detailsConfigurationDevicedisplaylockDialogtext =>
      'Wenn du das Gerätedisplay sperrst, wird im Anschluss Bluetooth deaktiviert und muss für eine neue Verbindung erst manuell wieder aktiviert werden.';

  @override
  String get detailsConfigurationDevicedisplaylockDialogquestion =>
      'Möchtest du das Gerätedisplay wirklich sperren?';

  @override
  String get settingsDemodevices => 'Demo-Geräte anzeigen';

  @override
  String get generalTextSettings => 'Einstellungen';

  @override
  String get discoveryWifi => 'WiFi';

  @override
  String get settingsInformations => 'Informationen';

  @override
  String get detailsConfigurationDimmingbehavior => 'Dimmverhalten';

  @override
  String get detailsConfigurationSwitchbehavior => 'Tasterverhalten';

  @override
  String get detailsConfigurationBrightness => 'Helligkeit';

  @override
  String get detailsConfigurationMinimum => 'Mindesthelligkeit';

  @override
  String get detailsConfigurationMaximum => 'Maximalhelligkeit';

  @override
  String get detailsConfigurationSwitchesGr => 'Gruppenrelais (GR)';

  @override
  String get detailsConfigurationSwitchesGs => 'Gruppenschalter (GS)';

  @override
  String get detailsConfigurationSwitchesCloserer => 'Schließer (ER)';

  @override
  String get detailsConfigurationSwitchesClosererDescription =>
      'Aus -> Gedrückt halten (An) -> Loslassen (Aus)';

  @override
  String get detailsConfigurationSwitchesOpenerer => 'Öffner (ER-Invers)';

  @override
  String get detailsConfigurationSwitchesOpenererDescription =>
      'An -> Gedrückt halten (Aus) -> Loslassen (An)';

  @override
  String get detailsConfigurationSwitchesSwitch => 'Wechselschalter';

  @override
  String get detailsConfigurationSwitchesSwitchDescription =>
      'Mit jedem Schalten wird das Licht an- und ausgeschalten.';

  @override
  String get detailsConfigurationSwitchesImpulsswitch => 'Stromstoß-Schalter';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription =>
      'Taster wird kurz gedrückt und losgelassen, um Licht an- bzw. auszuschalten.';

  @override
  String get detailsConfigurationSwitchesClosererDescription2 =>
      'Schalter gedrückt halten. Beim loslassen stoppt der Motor.';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription2 =>
      'Taster wird kurz gedrückt, um den Motor zu starten und kurz gedrückt, um ihn wieder zu stoppen.';

  @override
  String get detailsConfigurationWifiloginScan => 'QR-Code scannen';

  @override
  String get detailsConfigurationWifiloginScannotvalid =>
      'Der gescannte Code ist ungültig';

  @override
  String get detailsConfigurationWifiloginDescription => 'Code eingeben';

  @override
  String get detailsConfigurationWifiloginPassword => 'Passwort';

  @override
  String get discoveryEsbipDescription => 'Rolladen- und Beschattungsaktor IP';

  @override
  String get discoveryEsripDescription => 'Stromstoß-Schaltrelais IP';

  @override
  String get discoveryEudipDescription => 'Universal-Dimmschalter IP';

  @override
  String get generalTextLoad => 'Lädt';

  @override
  String get wifiBasicautomationsNotFound => 'Keine Automation gefunden.';

  @override
  String get wifiCodeInvalid => 'Ungültiger Code';

  @override
  String get wifiCodeValid => 'Gültiger Code';

  @override
  String get wifiAuthorizationLogin => 'Verbinden';

  @override
  String get wifiAuthorizationLoginFailed => 'Login fehlgeschlagen';

  @override
  String get wifiAuthorizationSerialnumber => 'Seriennummer';

  @override
  String get wifiAuthorizationProductiondate => 'Produktionsdatum';

  @override
  String get wifiAuthorizationProofofpossession => 'PoP';

  @override
  String get generalTextWifipassword => 'WiFi-Passwort';

  @override
  String get generalTextUsername => 'Benutzername';

  @override
  String get generalTextEnter => 'ODER MANUELL EINGEBEN';

  @override
  String get wifiAuthorizationScan => 'Scanne den ELTAKO-Code.';

  @override
  String get detailsConfigurationDevicesNofunctionshinttext =>
      'Dieses Gerät unterstützt aktuell keine weiteren Einstellungen. ';

  @override
  String get settingsUsedemodelay => 'Demo-Verzögerung verwenden';

  @override
  String get settingsImpulsLoad => 'Impuls-Schaltzeit wird geladen';

  @override
  String get settingsBluetoothLoad => 'Bluetooth-Einstellung wird geladen.';

  @override
  String get detailsConfigurationsectionLoad =>
      'Konfigurationen werden geladen';

  @override
  String get generalTextLogin => 'Anmelden';

  @override
  String get generalTextAuthentication => 'Authentifizieren';

  @override
  String get wifiAuthorizationScanDescription =>
      'Suche auf dem WiFi-Gerät oder auf der beiliegenden Info-Karte nach dem ELTAKO-Code und scanne diesen mit deiner Kamera.';

  @override
  String get wifiAuthorizationScanShort => 'ELTAKO-Code scannen';

  @override
  String get detailsConfigurationEdgemode => 'Dimmkurven';

  @override
  String get detailsConfigurationEdgemodeLeadingedge => 'Phasenanschnitt';

  @override
  String get generalTextNetwork => 'Netzwerk';

  @override
  String get wifiAuthenticationSuccessful => 'Authentifizierung erfolgreich';

  @override
  String get detailsConfigurationsectionSavechange => 'Konfiguration geändert';

  @override
  String get discoveryWifiAdddevice => 'WiFi Gerät hinzufügen';

  @override
  String get wifiAuthenticationDelay => 'Dies kann bis zu 1 Minute dauern.';

  @override
  String get generalTextRetry => 'Erneut versuchen';

  @override
  String get wifiAuthenticationCredentials =>
      'Bitte gib die Daten deines WiFis ein.';

  @override
  String get wifiAuthenticationSsid => 'SSID';

  @override
  String get wifiAuthenticationDelaylong =>
      'Es kann bis zu 1 Minute dauern, bis das Gerät bereit ist und in der App erscheint.';

  @override
  String get wifiAuthenticationCredentialsShort => 'WiFi Daten eingeben';

  @override
  String get wifiAuthenticationTeachin => 'Gerät in WiFi einlernen';

  @override
  String get wifiAuthenticationEstablish => 'Verbindung zum Gerät herstellen';

  @override
  String wifiAuthenticationEstablishLong(Object ssid) {
    return 'Gerät verbindet sich mit WiFi $ssid';
  }

  @override
  String get wifiAuthenticationFailed =>
      'Verbindung fehlgeschlagen. Trenne das Gerät für einige Sekunden vom Strom und verbinde es erneut.';

  @override
  String get wifiAuthenticationReset => 'Authentifizierung zurücksetzen';

  @override
  String get wifiAuthenticationResetHint =>
      'Alle Authentifizierungsdaten werden gelöscht.';

  @override
  String get wifiAuthenticationInvaliddata =>
      'Authentifizierungsdaten ungültig';

  @override
  String get wifiAuthenticationReauthenticate => 'Erneut authentifizieren';

  @override
  String get wifiAddhkdeviceHeader => 'Gerät hinzufügen';

  @override
  String get wifiAddhkdeviceDescription =>
      'Verbinde dein neues ELTAKO-Gerät über die Apple Home App mit deinem WiFi.';

  @override
  String get wifiAddhkdeviceStep1 => '1. Öffne die Apple Home App.';

  @override
  String get wifiAddhkdeviceStep2 =>
      '2. Klicke oben rechts in der App auf das Plus und wähle **Gerät hinzufügen**.';

  @override
  String get wifiAddhkdeviceStep3 => '3. Folge den Anweisungen der App.';

  @override
  String get wifiAddhkdeviceStep4 =>
      '4. Nun kann dein Gerät in der ELTAKO-Connect App konfiguriert werden.';

  @override
  String get detailsConfigurationRuntime => 'Laufzeit';

  @override
  String get detailsConfigurationRuntimeMode => 'Modus';

  @override
  String get generalTextManually => 'Manuell';

  @override
  String get detailsConfigurationRuntimeAutoDescription =>
      'Der Beschattungsaktor bestimmt die Laufzeit des Beschattungsmotors selbständig bei jeder Fahrt von der unteren zur oberen Endlage (empfohlen).\nNach Inbetriebnahme oder Änderungen sollte eine Fahrt von unten nach oben ohne Unterbrechung durchgeführt werden.';

  @override
  String get detailsConfigurationRuntimeManuallyDescription =>
      'Die Laufzeit des Beschattungsmotors wird manuell über die Dauer unten eingestellt.\nBitte achte darauf, dass die eingestellte Laufzeit der tatsächlichen Laufzeit deines Beschattungsmotors entspricht.\nNach Inbetriebnahme oder Änderungen sollte eine Fahrt von unten nach oben ohne Unterbrechung durchgeführt werden.';

  @override
  String get detailsConfigurationRuntimeDemoDescription =>
      'Der LCD-Display-Modus ist nur via REST API verfügbar.';

  @override
  String get generalTextDemomodeActive => 'Demo-Modus aktiv';

  @override
  String get detailsConfigurationRuntimeDuration => 'Dauer';

  @override
  String get detailsConfigurationSwitchesGs4 =>
      'Gruppenschalter mit Tipp-Wendefunktion (GS4)';

  @override
  String get detailsConfigurationSwitchesGs4Description =>
      'Gruppenschalter mit Tipp-Wendefunktion zur Steuerung von Jalousien';

  @override
  String get screenshotSu12 => 'Hoflicht';

  @override
  String get screenshotS2U12 => 'Hoflicht';

  @override
  String get screenshotMfz12 => 'Pumpe';

  @override
  String get screenshotEsr62 => 'Lampe';

  @override
  String get screenshotEud62 => 'Deckenleuchte';

  @override
  String get screenshotEsb62 => 'Rolladen Balkon';

  @override
  String get detailsConfigurationEdgemodeLeadingedgeDescription =>
      'LC1-LC3 sind Comfort-Stellungen mit verschiedenen Dimmkurven für dimmbare 230 V-LED-Lampen, welche sich auf AUTO konstruktionsbedingt nicht weit genug abdimmen lassen und daher auf Phasenanschnitt gezwungen werden müssen.';

  @override
  String get detailsConfigurationEdgemodeAutoDescription =>
      'AUTO lässt das Dimmen aller Lampenarten zu.';

  @override
  String get detailsConfigurationEdgemodeTrailingedge => 'Phasenabschnitt';

  @override
  String get detailsConfigurationEdgemodeTrailingedgeDescription =>
      'LC4-LC6 sind Comfort-Stellungen mit verschiedenen Dimmkurven für dimmbare 230 V-LED-Lampen.';

  @override
  String get updateHeader => 'Firmware Update';

  @override
  String get updateTitleStepSearch => 'Update wird gesucht';

  @override
  String get updateTitleStepFound => 'Ein Update wurde gefunden';

  @override
  String get updateTitleStepDownload => 'Update wird heruntergeladen';

  @override
  String get updateTitleStepInstall => 'Update wird installiert';

  @override
  String get updateTitleStepSuccess => 'Update erfolgreich';

  @override
  String get updateTitleStepUptodate => 'Bereits auf dem neuesten Stand';

  @override
  String get updateTitleStepFailed => 'Update fehlgeschlagen';

  @override
  String get updateButtonSearch => 'Nach Updates suchen';

  @override
  String get updateButtonInstall => 'Update installieren';

  @override
  String get updateCurrentversion => 'Aktuelle Version';

  @override
  String get updateNewversion => 'Neues Firmware Update verfügbar';

  @override
  String get updateHintPower =>
      'Das Update startet nur, wenn der Ausgang des Gerätes nicht aktiv ist. Das Gerät darf während des Updates nicht vom Stromnetz getrennt werden und die App nicht verlassen werden!';

  @override
  String get updateButton => 'Update';

  @override
  String get updateHintCompatibility =>
      'Ein Update wird empfohlen, da sonst einige Funktionen in der App nur eingeschränkt zur Verfügung stehen.';

  @override
  String get generalTextDetails => 'Details';

  @override
  String get updateMessageStepMetadata => 'Lade Update Informationen';

  @override
  String get updateMessageStepPrepare => 'Update wird vorbereitet';

  @override
  String get updateTitleStepUpdatesuccessful => 'Update wird geprüft';

  @override
  String get updateTextStepFailed =>
      'Leider ist beim Update etwas schief gelaufen, probiere es in ein paar Minuten erneut oder warte bis sich dein Gerät automatisch aktualisiert (Internetverbindung erforderlich).';

  @override
  String get configurationsNotavailable =>
      'Es sind noch keine Konfigurationen vorhanden';

  @override
  String get configurationsAddHint =>
      'Lege neue Konfigurationen an, indem du dich mit einem Gerät verbindest und eine Konfiguration speicherst.';

  @override
  String get configurationsEdit => 'Konfiguration bearbeiten';

  @override
  String get generalTextName => 'Name';

  @override
  String get configurationsDelete => 'Konfiguration löschen';

  @override
  String configurationsDeleteHint(Object configName) {
    return 'Soll die Konfiguration: $configName wirklich gelöscht werden?';
  }

  @override
  String get configurationsSave => 'Konfiguration speichern';

  @override
  String get configurationsSaveHint =>
      'Hier kannst du die Konfiguration deines aktuellen Geräts speichern, oder eine bereits gespeicherte Konfiguration laden.';

  @override
  String get configurationsImport => 'Konfiguration importieren';

  @override
  String configurationsImportHint(Object configName) {
    return 'Soll die Konfiguration $configName wirklich übertragen werden?';
  }

  @override
  String generalTextConfigurations(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Konfigurationen',
      one: 'Konfiguration',
    );
    return '$_temp0';
  }

  @override
  String get configurationsStepPrepare => 'Konfiguration wird vorbereitet';

  @override
  String get configurationsStepName =>
      'Gebe einen Namen für die Konfiguration ein.';

  @override
  String get configurationsStepSaving => 'Konfiguration wird gespeichert';

  @override
  String get configurationsStepSavedsuccessfully =>
      'Konfiguration wurde erfolgreich gespeichert';

  @override
  String get configurationsStepSavingfailed =>
      'Konfiguration speichern fehlgeschlagen';

  @override
  String get configurationsStepChoose => 'Wähle eine Konfiguration';

  @override
  String get configurationsStepImporting => 'Konfiguration wird importiert';

  @override
  String get configurationsStepImportedsuccessfully =>
      'Konfiguration wurde erfolgreich importiert';

  @override
  String get configurationsStepImportingfailed =>
      'Konfiguration importieren fehlgeschlagen';

  @override
  String get discoveryAssuDescription =>
      'Außensteckdosen Schaltuhr Bluetooth 230V';

  @override
  String get settingsDatetimeDevicetime => 'Aktuelle Gerätezeit';

  @override
  String get settingsDatetimeLoading => 'Zeiteinstellungen werden geladen';

  @override
  String get discoveryEud12Description => 'Universal-Dimmschalter Bluetooth';

  @override
  String get generalTextOffdelay => 'Rückfallverzögerung';

  @override
  String get generalTextRemainingbrightness => 'Resthelligkeit';

  @override
  String get generalTextSwitchonvalue => 'Einschaltwert';

  @override
  String get motionsensorTitleNoremainingbrightness => 'Keine Resthelligkeit';

  @override
  String get motionsensorTitleAlwaysremainingbrightness => 'Mit Resthelligkeit';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogram =>
      'Resthelligkeit über Schaltprogramm';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogramandzea =>
      'Resthelligkeit über ZE und ZA';

  @override
  String get motionsensorTitleNoremainingbrightnessauto =>
      'Keine Resthelligkeit (Halbautomatik)';

  @override
  String get generalTextMotionsensor => 'Bewegungsmelder';

  @override
  String get generalTextLightclock => 'Lichtwecker';

  @override
  String get generalTextSnoozeclock => 'Schlummerschaltung';

  @override
  String generalDescriptionLightclock(Object mode) {
    return 'Beim Einschalten ($mode) wird nach ca. 1 Sekunde mit kleinster Helligkeit eingeschaltet und langsam hochgedimmt, ohne die zuletzt gespeicherte Helligkeitsstufe zu verändern.';
  }

  @override
  String generalDescriptionSnoozeclock(Object mode) {
    return 'Beim Ausschalten ($mode) wird die Beleuchtung von der aktuellen Dimmstellung bis zur Mindesthelligkeit abgedimmt und ausgeschaltet. Durch kurzes Tasten kann während des Abdimmvorgangs jederzeit ausgeschaltet werden. Ein langes Tasten während des Abdimmvorgangs dimmt hoch und beendet die Schlummerschaltung.';
  }

  @override
  String get generalTextImmediately => 'Sofort';

  @override
  String get generalTextPercentage => 'Prozent';

  @override
  String get generalTextSwitchoffprewarning => 'Ausschaltvorwarnung';

  @override
  String get generalDescriptionSwitchoffprewarning =>
      'Langsames Abdimmen bis zur minimalen Helligkeit.';

  @override
  String get generalDescriptionOffdelay =>
      'Beim Anlegen der Steuerspannung schaltet das Gerät ein. Wenn die Steuerspannung unterbrochen wird, beginnt der Zeitablauf, nach dem das Gerät ausschaltet. Während des Zeitablaufs kann das Gerät nachgeschaltet werden.';

  @override
  String get generalDescriptionBrightness =>
      'Die Helligkeit, mit der die Lampe vom Dimmer eingeschaltet wird.';

  @override
  String get generalDescriptionRemainingbrightness =>
      'Der Dimmwert in Prozent, auf den die Lampe nach Abschaltung des Bewegungsmelders gedimmt wird.';

  @override
  String get generalDescriptionRuntime =>
      'Laufzeit der Lichtweckerfunktion von Minimalhelligkeit bis zur Maximalhelligkeit.';

  @override
  String get generalTextUniversalbutton => 'Universaltaster';

  @override
  String get generalTextDirectionalbutton => 'Richtungstaster';

  @override
  String get eud12DescriptionAuto =>
      'Automatische Erkennung UT/RT (mit Richtungstasterdiode RTD)';

  @override
  String get eud12DescriptionRt => 'mit Richtungstasterdiode RTD';

  @override
  String get generalTextProgram => 'Programm';

  @override
  String get eud12MotionsensorOff => 'Bei Bewegungsmelder auf Aus';

  @override
  String get eud12ClockmodeTitleProgramze => 'Programm und ZE';

  @override
  String get eud12ClockmodeTitleProgramza => 'Programm und ZA';

  @override
  String get eud12ClockmodeTitleProgrambuttonon => 'Programm und UT/RT Ein';

  @override
  String get eud12ClockmodeTitleProgrambuttonoff => 'Programm und UT/RT Aus';

  @override
  String get eud12TiImpulseTitle => 'Impulszeit Ein (t1)';

  @override
  String get eud12TiImpulseHeader => 'Dimmwert Impulszeit Ein';

  @override
  String get eud12TiImpulseDescription =>
      'Der Dimmwert in Prozent, auf den die Lampe bei Impulszeit EIN gedimmt wird.';

  @override
  String get eud12TiOffTitle => 'Impulszeit Aus (t2)';

  @override
  String get eud12TiOffHeader => 'Dimmwert Impulszeit Aus';

  @override
  String get eud12TiOffDescription =>
      'Der Dimmwert in Prozent, auf den die Lampe bei Impulszeit AUS gedimmt wird.';

  @override
  String get generalTextButtonpermanentlight => 'Tasterdauerlicht';

  @override
  String get generalDescriptionButtonpermanentlight =>
      'Einstellung des Tasterdauerlichts von 0 bis 10 Stunden in 0,5-Stunden-Schritten. Aktivierung durch Tastendruck länger als 1 Sekunde (1xFlackern), Deaktivierung durch Tastendruck länger als 2 Sekunden.';

  @override
  String get generalTextNobuttonpermanentlight => 'Kein TDL';

  @override
  String get generalTextBasicsettings => 'Grundeinstellungen';

  @override
  String get generalTextInputswitch => 'Örtlicher Tasteingang (A1)';

  @override
  String get generalTextOperationmode => 'Betriebsart';

  @override
  String get generalTextDimvalue => 'Einschaltverhalten';

  @override
  String get eud12TitleUsememory => 'Memorywert verwenden';

  @override
  String get eud12DescriptionUsememory =>
      'Der Memorywert entspricht dem zuletzt eingestellten Dimmwert. Wird der Memorywert deaktiviert, wird immer auf den Einschaltwert gedimmt.';

  @override
  String get generalTextStartup => 'Einschalthelligkeit';

  @override
  String get generalDescriptionSwitchonvalue =>
      'Der Einschaltwert ist ein einstellbarer Helligkeitswert, der das sichere Einschalten garantiert.';

  @override
  String get generalTitleSwitchontime => 'Einschaltzeit';

  @override
  String get generalDescriptionSwitchontime =>
      'Nach Ablauf der Einschaltzeit wird die Lampe vom Einschaltwert zum Memorywert gedimmt.';

  @override
  String get generalDescriptionStartup =>
      'Einige LED-Leuchtmittel benötigen einen höheren Einschaltstrom, um zuverlässig einzuschalten. Die Lampe wird auf diesem Einschaltwert eingeschaltet und anschließend nach der Einschaltzeit zum Memorywert gedimmt.';

  @override
  String get eud12ClockmodeSubtitleProgramze => 'Kurzklick auf Zentral Ein';

  @override
  String get eud12ClockmodeSubtitleProgramza => 'Kurzklick auf Zentral Aus';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonon =>
      'Doppelklick auf Universaltaster/Richtungstaster Ein';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonoff =>
      'Doppelklick auf Universaltaster/Richtungstaster Aus';

  @override
  String get eud12FunctionStairlighttimeswitchTitleShort =>
      'TLZ | Treppenlichtzeitschalter';

  @override
  String get eud12FunctionMinTitleShort => 'MIN';

  @override
  String get eud12FunctionMmxTitleShort => 'MMX';

  @override
  String get eud12FunctionTiDescription =>
      'Taktgeber mit einstellbarer Einschalt- und Ausschaltzeit von 0,5 Sekunden bis 9,9 Minuten. Die Helligkeit kann von Mindesthelligkeit bis Maximalhelligkeit eingestellt werden.';

  @override
  String get eud12FunctionAutoDescription =>
      'Universal-Dimmschalter mit Einstellung für Bewegungsmelder, Lichtwecker und Schlummerschaltung.';

  @override
  String get eud12FunctionErDescription =>
      'Schaltrelais, die Helligkeit kann von Mindesthelligkeit bis Maximalhelligkeit eingestellt werden.';

  @override
  String get eud12FunctionEsvDescription =>
      'Universal-Dimmschalter mit Einstellung einer Rückfallverzögerung von 1 bis 120 Minuten. Ausschaltvorwarnung am Ende durch Abdimmen wählbar und einstellbar von 1 bis 3 Minuten. Beide Zentraleingänge aktiv.';

  @override
  String get eud12FunctionTlzDescription =>
      'Einstellung des Tasterdauerlichts von 0 bis 10 Stunden in 0,5-Stunden-Schritten. Aktivierung durch Tastendruck länger als 1 Sekunde (1xFlackern), Deaktivierung durch Tastendruck länger als 2 Sekunden.';

  @override
  String get eud12FunctionMinDescription =>
      'Universal-Dimmschalter, schaltet bei dem Anlegen der Steuerspannung auf die eingestellte Mindesthelligkeit. In der eingestellten Dimmzeit von 1 bis 120 Minuten wird zur Maximalhelligkeit gedimmt. Beim Wegnehmen der Steuerspannung wird sofort ausgeschaltet, auch während der Dimmzeit. Beide Zentraleingänge aktiv.';

  @override
  String get eud12FunctionMmxDescription =>
      'Universal-Dimmschalter, schaltet bei dem Anlegen der Steuerspannung auf die eingestellte Mindesthelligkeit. In der eingestellten Dimmzeit von 1 bis 120 Minuten wird zur Maximalhelligkeit gedimmt. Beim Wegnehmen der Steuerspannung wird jedoch bis zur eingestellten Mindesthelligkeit abgedimmt. Danach wird ausgeschaltet. Beide Zentraleingänge aktiv.';

  @override
  String get motionsensorSubtitleNoremainingbrightness =>
      'Bei Bewegungsmelder auf Aus';

  @override
  String get motionsensorSubtitleAlwaysremainingbrightness =>
      'Bei Bewegungsmelder auf Aus';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogram =>
      'Schaltprogramm aktiviert und deaktiviert bei BM-Aus';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogramandzea =>
      'ZE aktiviert BM, ZA deaktiviert BM, sowie per Schaltprogramm';

  @override
  String get motionsensorSubtitleNoremainingbrightnessauto =>
      'Bewegungsmelder schaltet nur Aus';

  @override
  String get detailsDimsectionHeader => 'Dimmen';

  @override
  String get generalTextFast => 'Schnell';

  @override
  String get generalTextSlow => 'Langsam';

  @override
  String get eud12TextDimspeed => 'Dimmgeschwindigkeit';

  @override
  String get eud12TextSwitchonspeed => 'Einschaltgeschwindigkeit';

  @override
  String get eud12TextSwitchoffspeed => 'Ausschaltgeschwindigkeit';

  @override
  String get eud12DescriptionDimspeed =>
      'Die Dimmgeschwindigkeit ist die Geschwindigkeit, mit der der Dimmer von der aktuellen Helligkeit zur Zielhelligkeit dimmt.';

  @override
  String get eud12DescriptionSwitchonspeed =>
      'Die Einschaltgeschwindigkeit ist die Geschwindigkeit, die der Dimmer benötigt, um vollständig einzuschalten.';

  @override
  String get eud12DescriptionSwitchoffspeed =>
      'Die Ausschaltgeschwindigkeit ist die Geschwindigkeit, die der Dimmer benötigt, um vollständig auszuschalten.';

  @override
  String get settingsFactoryresetResetdimHeader =>
      'Dimm Einstellungen zurücksetzen';

  @override
  String get settingsFactoryresetResetdimDescription =>
      'Sollen wirklich alle Dimm Einstellungen zurückgesetzt werden?';

  @override
  String get settingsFactoryresetResetdimConfirmationDescription =>
      'Dimm Einstellungen wurden erfolgreich zurückgesetzt';

  @override
  String get eud12TextSwitchonoffspeed => 'Ein-/Ausschaltgeschwindigkeit';

  @override
  String get eud12DescriptionSwitchonoffspeed =>
      'Die Ein-/Ausschaltgeschwindigkeit ist die Geschwindigkeit, die der Dimmer benötigt, um vollständig ein- bzw. auszuschalten.';

  @override
  String get timerDetailsDimtoval => 'Ein mit Dimmwert in %';

  @override
  String get timerDetailsDimtovalDescription =>
      'Der Dimmer schaltet immer mit dem festen Dimmwert in % Ein.';

  @override
  String timerDetailsDimtovalSubtitle(Object brightness) {
    return 'Einschalten mit $brightness%';
  }

  @override
  String get timerDetailsDimtomem => 'Ein mit Memorywert';

  @override
  String get timerDetailsDimtomemSubtitle => 'Einschalten mit Memorywert';

  @override
  String get timerDetailsMotionsensorwithremainingbrightness =>
      'Resthelligkeit (BM) Ein';

  @override
  String get timerDetailsMotionsensornoremainingbrightness =>
      'Resthelligkeit (BM) Aus';

  @override
  String get settingsRandommodeHint =>
      'Bei eingeschaltetem Zufallsmodus werden alle Schaltzeitpunkte des Kanals zufällig verschoben. Bei Einschaltzeiten bis zu 15 Minuten früher und Ausschaltzeiten bis zu 15 Minuten später.';

  @override
  String get runtimeOffsetDescription =>
      'Zusätzlicher Nachlauf, nach Ablauf der Fahrtdauer. Dieser kann verwendet werden um ein erreichen der Endlage sicherzustellen.';

  @override
  String get loadingTextDimvalue => 'Dimmwert wird geladen';

  @override
  String get discoveryEudipmDescription => 'Universal-Dimmschalter IP Matter';

  @override
  String get generalTextOffset => 'Nachlauf';

  @override
  String get eud12DimvalueTestText => 'Helligkeit senden';

  @override
  String get eud12DimvalueTestDescription =>
      'Beim Testen wird die aktuell eingestellte Dimgeschwindigkeit berücksichtigt.';

  @override
  String get eud12DimvalueLoadText => 'Helligkeit laden';

  @override
  String get settingsDatetimeNotime =>
      'Die Datum und Uhrzeit Einstellungen müssen über das Gerätedisplay ausgelesen werden.';

  @override
  String get generalMatterText => 'Matter';

  @override
  String get generalMatterMessage =>
      'Bitte lerne dein Matter-Gerät mithilfe einer der folgenden Apps ein.';

  @override
  String get generalMatterOpengooglehome => 'Google Home öffnen';

  @override
  String get generalMatterOpenamazonalexa => 'Amazon Alexa öffnen';

  @override
  String get generalMatterOpensmartthings => 'SmartThings öffnen';

  @override
  String generalLabelProgram(Object number) {
    return 'Programm $number';
  }

  @override
  String get generalTextDone => 'Fertig';

  @override
  String get settingsRandommodeDescriptionShort =>
      'Bei eingeschaltetem Zufallsmodus werden alle Schaltzeitpunkte des Kanals zufällig verschoben. Bei Einschaltzeiten bis zu 15 Minuten früher und Ausschaltzeiten bis zu 15 Minuten später.';

  @override
  String get all => 'Alle';

  @override
  String get discoveryBluetooth => 'Bluetooth';

  @override
  String get success => 'Erfolgreich';

  @override
  String get error => 'Fehler';

  @override
  String get timeProgramAdd => 'Zeitprogramm hinzufügen';

  @override
  String get noConnection => 'Keine Verbindung';

  @override
  String get timeProgramOnlyActive => 'Konfigurierte Programme';

  @override
  String get timeProgramAll => 'Alle Programme';

  @override
  String get active => 'Aktiv';

  @override
  String get inactive => 'Inaktiv';

  @override
  String timeProgramSaved(Object number) {
    return 'Programm $number gespeichert';
  }

  @override
  String get deviceLanguageSaved => 'Gerätesprache gespeichert';

  @override
  String generalTextTimeShort(Object time) {
    return '$time Uhr';
  }

  @override
  String programDeleteHint(Object index) {
    return 'Soll Programm $index wirklich gelöscht werden?';
  }

  @override
  String milliseconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Millisekunden',
      one: 'Millisekunde',
    );
    return '$_temp0';
  }

  @override
  String millisecondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Millisekunden',
      one: '$count Millisekunde',
    );
    return '$_temp0';
  }

  @override
  String secondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Sekunden',
      one: '$count Sekunde',
    );
    return '$_temp0';
  }

  @override
  String minutesWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Minuten',
      one: '$count Minute',
    );
    return '$_temp0';
  }

  @override
  String hoursWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Stunden',
      one: '$count Stunde',
    );
    return '$_temp0';
  }

  @override
  String get settingsPinFailEmpty => 'Die PIN darf nicht leer sein.';

  @override
  String get detailsConfigurationWifiloginScanNoMatch =>
      'Der gescannte Code passt nicht zum Gerät.';

  @override
  String get wifiAuthorizationPopIsEmpty => 'PoP darf nicht leer sein.';

  @override
  String get wifiAuthenticationCredentialsHint =>
      'Da die App nicht auf dein privates WLAN-Passwort zugreifen kann, lässt sich die Richtigkeit der Eingabe nicht prüfen. Sollte keine Verbindung zustande kommen, überprüfe das Passwort und gebe es erneut ein.';

  @override
  String get generalMatterOpenApplehome => 'Apple Home öffnen';

  @override
  String get timeProgramNoActive => 'Keine konfigurierten Programme.';

  @override
  String get timeProgramNoEmpty => 'Kein freies Zeitprogramm verfügbar.';

  @override
  String get nameOfConfiguration => 'Name der Konfiguration';

  @override
  String get currentDevice => 'Aktuelles Gerät';

  @override
  String get export => 'Exportieren';

  @override
  String get import => 'Importieren';

  @override
  String get savedConfigurations => 'Gespeicherte Konfigurationen';

  @override
  String get importableServicesLabel =>
      'Folgende Einstellungen können importiert werden:';

  @override
  String get notImportableServicesLabel => 'Inkompatible Einstellungen:';

  @override
  String get deviceCategoryMeterGateway => 'Zähler-Gateway';

  @override
  String get deviceCategory2ChannelTimeSwitch =>
      '2-Kanal-Zeitschaltuhr Bluetooth';

  @override
  String get devicategoryOutdoorTimeSwitchBluetooth =>
      'Außensteckdosen-Zeitschaltuhr Bluetooth';

  @override
  String get settingsModbusHeader => 'Modbus';

  @override
  String get settingsModbusDescription =>
      'Passe die Baudrate, Parität und den Timeout an, um die Übertragungsgeschwindigkeit, Fehlererkennung und Wartezeit zu konfigurieren.';

  @override
  String get settingsModbusRTU => 'Modbus RTU';

  @override
  String get settingsModbusBaudrate => 'Baudrate';

  @override
  String get settingsModbusParity => 'Parität';

  @override
  String get settingsModbusTimeout => 'Modbus Timeout';

  @override
  String get locationServiceDisabled => 'Standort ist deaktiviert';

  @override
  String get locationPermissionDenied =>
      'Bitte erlaube die Standort Berechtigung, um deine aktuelle Position abzurufen.';

  @override
  String get locationPermissionDeniedPermanently =>
      'Die Standortberechtigung ist dauerhaft verweigert. Bitte erlaube die Standortberechtigung in deinen Geräteeinstellungen, um deine aktuelle Position abzurufen.';

  @override
  String get lastSync => 'Zuletzt synchronisiert';

  @override
  String get dhcpActive => 'DHCP aktiv';

  @override
  String get ipAddress => 'IP';

  @override
  String get subnetMask => 'Subnetzmaske';

  @override
  String get standardGateway => 'Standard Gateway';

  @override
  String get dns => 'DNS';

  @override
  String get alternateDNS => 'Alternativen DNS';

  @override
  String get errorNoNetworksFound => 'Keine WiFi Netzwerke gefunden';

  @override
  String get availableNetworks => 'Verfügbare Netzwerke';

  @override
  String get enableWifiInterface => 'WiFi Schnittstelle aktivieren';

  @override
  String get enableLANInterface => 'LAN Schnittstelle aktivieren';

  @override
  String get hintDontDisableAllInterfaces =>
      'Stelle sicher, dass nicht alle Schnittstellen deaktiviert sind. Die zuletzt aktivierte Schnittstelle hat Priorität.';

  @override
  String get ssid => 'SSID';

  @override
  String get searchNetworks => 'WiFi Netzwerke suchen';

  @override
  String get errorNoNetworkEnabled =>
      'Mindestens eine Schnittstelle muss aktiviert sein.';

  @override
  String get errorActiveNetworkInvalid =>
      'Nicht alle aktiven Stationen sind gültig.';

  @override
  String get invalidNetworkConfiguration => 'Ungültige Netzwerkkonfiguration';

  @override
  String get generalDefault => 'Standard';

  @override
  String get mqttHeader => 'MQTT';

  @override
  String get mqttConnected => 'Verbindung zum MQTT-Broker';

  @override
  String get mqttDisconnected => 'Keine Verbindung zum MQTT-Broker.';

  @override
  String get mqttBrokerURI => 'Broker URI';

  @override
  String get mqttBrokerURIHint => 'MQTT-Broker URI';

  @override
  String get mqttPort => 'Port';

  @override
  String get mqttPortHint => 'MQTT-Port';

  @override
  String get mqttClientId => 'Client-ID';

  @override
  String get mqttClientIdHint => 'MQTT Client-ID';

  @override
  String get mqttUsername => 'Benutzername';

  @override
  String get mqttUsernameHint => 'MQTT-Benutzername';

  @override
  String get mqttPassword => 'Password';

  @override
  String get mqttPasswordHint => 'MQTT-Password';

  @override
  String get mqttCertificate => 'Zertifikat (optional)';

  @override
  String get mqttCertificateHint => 'MQTT-Zertifikat';

  @override
  String get mqttTopic => 'Topic';

  @override
  String get mqttTopicHint => 'MQTT-Topic';

  @override
  String get electricityMeter => 'Stromzähler';

  @override
  String get electricityMeterCurrent => 'Aktuell';

  @override
  String get electricityMeterHistory => 'Verlauf';

  @override
  String get electricityMeterReading => 'Zählerstand';

  @override
  String get connectivity => 'Konnektivität';

  @override
  String electricMeter(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Zähler',
      one: 'Zähler',
    );
    return '$_temp0';
  }

  @override
  String get discoveryZGW16Description => 'Modbus-Stromzähler-MQTT-Gateway';

  @override
  String get bluetoothConnectionLost => 'Bluetooth Verbindung verloren.';

  @override
  String get bluetoothConnectionLostDescription =>
      'Die Bluetooth Verbindung zum Gerät wurde unterbrochen. Bitte stelle sicher, dass das Gerät in Reichweite ist.';

  @override
  String get openBluetoothSettings => 'Einstellungen öffnen';

  @override
  String get password => 'Passwort';

  @override
  String get setInitialPassword => 'Initiales Passwort vergeben';

  @override
  String initialPasswordMinimumLength(Object length) {
    return 'Das Passwort muss mindestens $length Zeichen lang sein.';
  }

  @override
  String get repeatPassword => 'Passwort wiederholen';

  @override
  String get passwordsDoNotMatch => 'Die Passwörter stimmen nicht überein.';

  @override
  String get savePassword => 'Passwort speichern';

  @override
  String get savePasswordHint =>
      'Das Passwort wird für zukünftige Anmeldungen auf deinem Gerät gespeichert.';

  @override
  String get retrieveNtpServer => 'Zeit von NTP-Server abrufen';

  @override
  String get retrieveNtpServerFailed =>
      'Die Verbindung zum NTP-Server konnte nicht hergestellt werden.';

  @override
  String get retrieveNtpServerSuccess =>
      'Die Verbindung zum NTP-Server war erfolgreich.';

  @override
  String get settingsPasswordNewPasswordDescription =>
      'Neues Passwort eingeben';

  @override
  String get settingsPasswordConfirmationDescription =>
      'Passwort ändern erfolgreich';

  @override
  String get dhcpRangeStart => 'DHCP Startbereich';

  @override
  String get dhcpRangeEnd => 'DHCP Endbereich';

  @override
  String get forwardOnMQTT => 'Weiterleitung auf MQTT';

  @override
  String get showAll => 'Alle anzeigen';

  @override
  String get hide => 'Ausblenden';

  @override
  String get changeToAPMode => 'Wechseln in den AP-Modus';

  @override
  String get changeToAPModeDescription =>
      'Du bist dabei dein Gerät mit einem WiFi Netzwerk zu verbinden. In diesem Fall wird die Verbindung zum Gerät getrennt und du musst dich erneut über das konfigurierte Netzwerk mit deinem Gerät verbinden.';

  @override
  String get consumption => 'Verbrauch';

  @override
  String get currentDay => 'Aktueller Tag';

  @override
  String get twoWeeks => '2 Wochen';

  @override
  String get oneYear => '1 Jahr';

  @override
  String get threeYears => '3 Jahre';

  @override
  String passwordMinLength(Object length) {
    return 'Das Passwort muss mindestens $length Zeichen lang sein.';
  }

  @override
  String get passwordNeedsLetter =>
      'Das Passwort muss mindestens einen Buchstaben enthalten.';

  @override
  String get passwordNeedsNumber =>
      'Das Passwort muss mindestens eine Zahl enthalten.';

  @override
  String get portEmpty => 'Port darf nicht leer sein';

  @override
  String get portInvalid => 'Ungültiger Port';

  @override
  String portOutOfRange(Object rangeEnd, Object rangeStart) {
    return 'Port muss zwischen $rangeStart und $rangeEnd sein.';
  }

  @override
  String get ipAddressEmpty => 'IP-Adresse darf nicht leer sein.';

  @override
  String get ipAddressInvalid => 'Ungültige IP-Adresse';

  @override
  String get subnetMaskEmpty => 'Subnetzmaske darf nicht leer sein.';

  @override
  String get subnetMaskInvalid => 'Ungültige Subnetzmaske';

  @override
  String get gatewayEmpty => 'Gateway darf nicht leer sein.';

  @override
  String get gatewayInvalid => 'Ungültiges Gateway';

  @override
  String get dnsEmpty => 'DNS darf nicht leer sein.';

  @override
  String get dnsInvalid => 'Ungültiger DNS';

  @override
  String get uriEmpty => 'URI darf nicht leer sein.';

  @override
  String get uriInvalid => 'Ungültige URI';

  @override
  String get electricityMeterChangedSuccessfully =>
      'Stromzähler wurde erfolgreich geändert.';

  @override
  String get networkChangedSuccessfully =>
      'Netzwerk Konfiguration wurde erfolgreich geändert.';

  @override
  String get mqttChangedSuccessfully =>
      'MQTT-Konfiguration wurde erfolgreich geändert.';

  @override
  String get modbusChangedSuccessfully =>
      'Modbus Einstellungen wurden erfolgreich geändert.';

  @override
  String get loginData => 'Login Daten löschen';

  @override
  String get valueConfigured => 'Konfiguriert';

  @override
  String get electricityMeterHistoryNoData => 'Keine Daten verfügbar.';

  @override
  String get locationChangedSuccessfully =>
      'Standort wurde erfolgreich geändert.';

  @override
  String get settingsNameFailEmpty => 'Name darf nicht leer sein.';

  @override
  String settingsNameFailLength(Object length) {
    return 'Der Name darf nicht länger als $length Zeichen sein.';
  }

  @override
  String get solsticeChangedSuccesfully =>
      'Zeitversatz Sonnenwende wurde erfolgreich geändert.';

  @override
  String get relayFunctionChangedSuccesfully =>
      'Relaisfunktion wurde erfolgreich geändert.';

  @override
  String get relayFunctionHeader => 'Relaisfunktion';

  @override
  String get dimmerValueChangedSuccesfully =>
      'Einschaltverhalten wurde erfolgreich geändert.';

  @override
  String get dimmerBehaviourChangedSuccesfully =>
      'Dimmverhalten wurde erfolgreich geändert.';

  @override
  String get dimmerBrightnessDescription =>
      'Die Mindest- und Maximalhelligkeit hat Auswirkung auf alle einstellbaren Helligkeiten des Dimmers.';

  @override
  String get dimmerSettingsChangedSuccesfully =>
      'Grundeinstellungen wurden erfolgreich geändert.';

  @override
  String get liveUpdateEnabled => 'Live Test aktiviert';

  @override
  String get liveUpdateDisabled => 'Live Test deaktiviert';

  @override
  String get liveUpdateDescription =>
      'Der zuletzt geänderte Slider-Wert wird an das Gerät gesendet.';

  @override
  String get demoDevices => 'Demo Geräte';

  @override
  String get showDemoDevices => 'Demo Geräte anzeigen';

  @override
  String get deviceCategoryTimeSwitch => 'Zeitschaltuhr';

  @override
  String get deviceCategoryMultifunctionalRelay => 'Multifunktions-Zeitrelais';

  @override
  String get deviceCategoryDimmer => 'Dimmer';

  @override
  String get deviceCategoryShutter => 'Rolladen- und Beschattungsaktor';

  @override
  String get deviceCategoryRelay => 'Relais';

  @override
  String get search => 'Suchen';

  @override
  String get configurationsHeader => 'Konfigurationen';

  @override
  String get configurationsDescription =>
      'Hier kannst du deine gespeicherten Konfigurationen verwalten.';

  @override
  String get configurationsNameFailEmpty =>
      'Name der Konfiguration darf nicht leer sein.';

  @override
  String get configurationDeleted => 'Konfiguration gelöscht';

  @override
  String codeFound(Object codeType) {
    return '$codeType Code erkannt';
  }

  @override
  String get errorCameraPermission =>
      'Bitte erlaube den Zugriff auf die Kamera, um den ELTAKO-Code zu scannen.';

  @override
  String get authorizationSuccessful => 'Erfolgreich am Gerät autorisiert';

  @override
  String get wifiAuthenticationResetConfirmationDescription =>
      'Authentifizierung wurde erfolgreich zurückgesetzt.';

  @override
  String get settingsResetConnectionHeader => 'Verbindung zurücksetzen';

  @override
  String get settingsResetConnectionDescription =>
      'Soll die Verbindung wirklich zurückgesetzt werden?';

  @override
  String get settingsResetConnectionConfirmationDescription =>
      'Verbindung wurde erfolgreich zurückgesetzt.';

  @override
  String get wiredInputChangedSuccesfully =>
      'Tasterverhalten wurde erfolgreich geändert.';

  @override
  String get runtimeChangedSuccesfully =>
      'Laufzeit wurde erfolgreich geändert.';

  @override
  String get expertModeActivated => 'Experten-Modus aktiviert';

  @override
  String get expertModeDeactivated => 'Experten-Modus deaktiviert';

  @override
  String get license => 'Lizenzen';

  @override
  String get retry => 'Erneut versuchen';

  @override
  String get provisioningConnectingHint =>
      'Verbindung zum Gerät wird hergestellt. Dies kann bis zu 1 Minute dauern.';

  @override
  String get serialnumberEmpty => 'Seriennummer darf nicht leer sein.';

  @override
  String get interfaceStateInactiveDescriptionBLE =>
      'Bluetooth ist deaktiviert. Bitte aktiviere es, um Bluetooth Geräte zu finden.';

  @override
  String get interfaceStateDeniedDescriptionBLE =>
      'Bluetooth Berechtigungen wurden nicht erteilt.';

  @override
  String get interfaceStatePermanentDeniedDescriptionBLE =>
      'Bluetooth Berechtigungen wurden nicht erteilt. Bitte erlaube Bluetooth Verbindungen in deinen Geräte Einstellungen.';

  @override
  String get requestPermission => 'Berechtigung anfordern';

  @override
  String get goToSettings => 'Zu den Einstellungen';

  @override
  String get enableBluetooth => 'Bluetooth aktivieren';

  @override
  String get installed => 'Installiert';

  @override
  String teachInDialogDescription(Object type) {
    return 'Möchtest du dein Gerät via $type einlernen?';
  }

  @override
  String get useMatter => 'Matter verwenden';

  @override
  String get relayMode => 'Relaismodus aktivieren';

  @override
  String get whatsNew => 'Neu in dieser Version';

  @override
  String get migrationHint =>
      'Damit du die neuen Funktionen nutzen kannst, müssen wir deine Daten migrieren.';

  @override
  String get migrationHeader => 'Migration';

  @override
  String get migrationProgress => 'Wir räumen auf...';

  @override
  String get letsGo => 'Los geht\'s';

  @override
  String get noDevicesFound =>
      'Keine Geräte gefunden. Überprüfe ob sich dein Gerät im Pairing-Modus befindet.';

  @override
  String get interfaceStateEmpty => 'Keine Geräte gefunden';

  @override
  String get ssidEmpty => 'SSID darf nicht leer sein';

  @override
  String get passwordEmpty => 'Password darf nicht leer sein';

  @override
  String get settingsDeleteSettingsHeader => 'Einstellungen zurücksetzen';

  @override
  String get settingsDeleteSettingsDescription =>
      'Sollen wirklich alle Einsetllungen zurückgesetzt werden?';

  @override
  String get settingsDeleteSettingsConfirmationDescription =>
      'Alle Einstellungen wurden erfolgreich zurückgesetzt.';

  @override
  String get locationNotFound => 'Standort nicht gefunden';

  @override
  String get timerProgramEmptySaveHint =>
      'Das Zeitprogramm ist leer und kann nicht gespeichert werden. Bearbeitung beenden?';

  @override
  String get timerProgramDaysEmptySaveHint =>
      'Keine Tage ausgewählt. Das Zeitprogramm trotzdem speichern?';

  @override
  String get timeProgramNoDays =>
      'Ein Programm ohne aktive Tage kann nicht aktiviert werden.';

  @override
  String timeProgramColliding(Object program) {
    return 'Zeitprogramm kollidiert mit Programm $program.';
  }

  @override
  String timeProgramDuplicated(Object program) {
    return 'Das Zeitprogramm ist ein Duplikat von Programm $program.';
  }

  @override
  String get screenshotZgw16 => 'Einfamilienhaus';

  @override
  String get interfaceStateUnknown => 'Keine Geräte gefunden';

  @override
  String get settingsPinChange => 'PIN ändern';

  @override
  String get timeProgrammOneTime => 'Einmalig';

  @override
  String get timeProgrammRepeating => 'Wiederholend';

  @override
  String get generalIgnore => 'Ignorieren';

  @override
  String get timeProgramChooseDay => 'Tag wählen';

  @override
  String get generalToday => 'Heute';

  @override
  String get generalTomorrow => 'Morgen';

  @override
  String get bluetoothAndPINChangedSuccessfully =>
      'Bluetooth und PIN wurden erfolgreich geändert.';

  @override
  String get generalTextDimTime => 'Dimmzeit';

  @override
  String get discoverySu62Description => '1-Kanal Schaltuhr Bluetooth';

  @override
  String get bluetoothAlwaysOnTitle => 'Dauer-An';

  @override
  String get bluetoothAlwaysOnDescription =>
      'Bluetooth ist dauerhaft aktiviert.';

  @override
  String get bluetoothAlwaysOnHint =>
      'Hinweis: Wenn diese Einstellung aktiviert wird, ist das Gerät über Bluetooth permanent für jeden sichtbar! Es wird empfohlen den Standart PIN zu ändern.';

  @override
  String get bluetoothManualStartupOnTitle => 'Zeitweilig-An';

  @override
  String get bluetoothManualStartupOnDescription =>
      'Nach anlegen der Spannung wird Bluetooth für 3 Minuten aktiviert.';

  @override
  String get bluetoothManualStartupOnHint =>
      'Hinweis: Die Kopplungsbereitschaft ist für 3 Minuten aktiviert und schaltet sich anschließend aus. Wenn eine neue Verbindung aufgebaut werden soll, muss der Taster für circa 5 Sekunden gedrückt gehalten werden.';

  @override
  String get bluetoothManualStartupOffTitle => 'Manuell-An';

  @override
  String get bluetoothManualStartupOffDescription =>
      'Bluetooth wird manuell über den Tasteingang aktiviert und ist dann für 3 Minuten aktiv.';

  @override
  String get bluetoothManualStartupOffHint =>
      'Hinweis: Um Bluetooth zu aktivieren, muss der Taster am Tasteingang für ca. 5 Sekunden gedrückt gehalten werden.';

  @override
  String get timeProgrammOneTimeRepeatingDescription =>
      'Programme können entweder wiederholt ausgeführt werden, indem sie an den konfigurierten Tagen und Zeiten immer einen Schaltvorgang ausführen, oder sie werden nur einmalig zum konfigurierten Schaltzeitpunkt ausgeführt.';

  @override
  String versionHeader(Object version) {
    return 'Version $version';
  }

  @override
  String get releaseNotesHeader => 'Release Notes';

  @override
  String get release30Header => 'Die neue ELTAKO Connect-App ist da!';

  @override
  String get release30FeatureDesignHeader => 'Neues Design';

  @override
  String get release30FeatureDesignDescription =>
      'Die App wurde komplett überarbeitet und erstrahlt in einem neuen Design. Die Bedienung ist jetzt noch einfacher und intuitiver.';

  @override
  String get release30FeaturePerformanceHeader => 'Verbesserte Performance';

  @override
  String get release30FeaturePerformanceDescription =>
      'Genieße eine flüssigere Erfahrung und reduzierte Ladezeiten – für ein besseres Nutzererlebnis.';

  @override
  String get release30FeatureConfigurationHeader =>
      'Geräteübergreifende Konfigurationen';

  @override
  String get release30FeatureConfigurationDescription =>
      'Speichere Gerätekonfigurationen und übertrage diese auf andere Geräte. Selbst wenn diese nicht die gleiche Hardware haben, kannst du z.B. die Konfiguration deiner S2U12DBT1+1-UC auf eine ASSU-BT übertragen oder andersherum.';

  @override
  String get release31Header =>
      'Die neue Unterputz 1-Kanal Schaltuhr mit Bluetooth ist da!';

  @override
  String get release31Description => 'Was kann die SU62PF-BT/UC?';

  @override
  String get release31SU62Name => 'SU62PF-BT/UC';

  @override
  String get release31DeviceNote1 => 'Bis zu 60 Schaltprogramme.';

  @override
  String get release31DeviceNote2 =>
      'Die Uhr kann Geräte nach festen Zeiten oder über die Astro-Funktion basierend auf Sonnenaufgang und Sonnenuntergang schalten.';

  @override
  String get release31DeviceNote3 =>
      'Zufallsmodus: Schaltzeiten können zufällig um bis zu 15 Minuten verschoben werden.';

  @override
  String get release31DeviceNote4 =>
      'Sommer-/Winterzeitumstellung: Die Uhr stellt automatisch auf Sommer- bzw. Winterzeit um.';

  @override
  String get release31DeviceNote5 =>
      'Universelle Versorgungs- und Steuerspannung 12-230V UC.';

  @override
  String get release31DeviceNote6 => 'Tastereingang für manuelles Schalten.';

  @override
  String get release31DeviceNote7 => '1 Schließer potenzialfrei 10 A/250 V AC.';

  @override
  String get release31DeviceNote8 => 'Einmaliges ausführen von Zeitprogrammen.';

  @override
  String get generalNew => 'Neu';

  @override
  String yearsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Vor $count Jahren',
      one: 'Letztes Jahr',
    );
    return '$_temp0';
  }

  @override
  String monthsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Vor $count Monaten',
      one: 'Letzten Monat',
    );
    return '$_temp0';
  }

  @override
  String weeksAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Vor $count Wochen',
      one: 'Letzte Woche',
    );
    return '$_temp0';
  }

  @override
  String daysAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Vor $count Tagen',
      one: 'Gestern',
    );
    return '$_temp0';
  }

  @override
  String minutesAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Vor $count Minuten',
      one: 'Vor einer Minute',
    );
    return '$_temp0';
  }

  @override
  String hoursAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Vor $count Stunden',
      one: 'Vor einer Stunde',
    );
    return '$_temp0';
  }

  @override
  String secondsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Vor $count Sekunden',
      one: 'Vor einer Sekunde',
    );
    return '$_temp0';
  }

  @override
  String get justNow => 'Gerade eben';

  @override
  String get discoveryEsripmDescription => 'Stromstoß-Schaltrelais IP Matter';

  @override
  String get generalTextKidsRoom => 'Kinderzimmerschaltung';

  @override
  String generalDescriptionKidsRoom(Object mode) {
    return 'Beim Einschalten ($mode) wird nach ca. 1 Sekunde mit kleinster Helligkeit eingeschaltet und, solange weiter getastet wird, langsam hochgedimmt, ohne die zuletzt gespeicherte Helligkeitsstufe zu verändern.';
  }

  @override
  String get generalTextSceneButton => 'Szenentaster';

  @override
  String get settingsEnOceanConfigHeader => 'EnOcean Konfiguration';

  @override
  String get enOceanConfigChangedSuccessfully =>
      'EnOcean Konfiguration wurde erfolgreich geändert.';

  @override
  String get activateEnOceanRepeater => 'EnOcean Repeater aktivieren';

  @override
  String get enOceanRepeaterLevel => 'Repeater Level';

  @override
  String get enOceanRepeaterLevel1 => '1-Level';

  @override
  String get enOceanRepeaterLevel2 => '2-Level';

  @override
  String get enOceanRepeaterOffDescription =>
      'Es werden keine Funksignale von Sensoren repeatet.';

  @override
  String get enOceanRepeaterLevel1Description =>
      'Es werden nur die Funksignale von Sensoren empfangen, geprüft und mit voller Sendeleistung weitergesendet. Funksignale anderer Repeater werden ignoriert, um die Datenmenge zu reduzieren.';

  @override
  String get enOceanRepeaterLevel2Description =>
      'Es werden außer den Funksignalen von Sensoren auch die Funksignale von 1-Level-Repeatern verarbeitet. Ein Funksignal kann damit maximal 2-mal empfangen und verstärkt werden. Funkrepeater müssen nicht eingelernt werden. Sie empfangen und verstärken die Funksignale von allen Funksensoren in ihrem Empfangsbereich.';

  @override
  String get settingsSensorHeader => 'Sensoren';

  @override
  String get sensorChangedSuccessfully =>
      'Sensoren wurden erfolgreich geändert';

  @override
  String get wiredButton => 'Drahtgebundener Taster';

  @override
  String get enOceanId => 'EnOcean-ID';

  @override
  String get enOceanAddManually => 'EnOcean-ID eingeben oder scannen';

  @override
  String get enOceanIdInvalid => 'Ungültige EnOcean-ID';

  @override
  String get enOceanAddAutomatically => 'Mit EnOcean-Telegram einlernen';

  @override
  String get enOceanAddDescription =>
      'Das EnOcean Funk-Protokoll ermöglicht es Taster in deinen Aktor einzulernen und zu bedienen.\n\nWähle entweder das automatische Einlernen mit EnOcean Telegram, um Taster via Tastendruck einzulernen oder wähle die manuelle Variante, um die EnOcean-ID deines Tasters einzuscannen oder einzutippen.';

  @override
  String get enOceanTelegram => 'Telegramm';

  @override
  String enOceanCodeScan(Object sensorType) {
    return 'Gib die EnOcean-ID deines $sensorType ein oder scanne den EnOcean-QR-Code, deines $sensorType, um ihn hinzuzufügen.';
  }

  @override
  String get enOceanCode => 'EnOcean QR-Code';

  @override
  String enOceanCodeScanDescription(Object sensorType) {
    return 'Suche nach dem EnOcean QR-Code auf deinem $sensorType und scanne ihn mit deiner Kamera.';
  }

  @override
  String get enOceanButton => 'EnOcean Taster';

  @override
  String get enOceanBackpack => 'EnOcean-Adapter';

  @override
  String get sensorNotAvailable => 'Es wurden noch keine Sensoren eingelernt';

  @override
  String get sensorAdd => 'Sensor hinzufügen';

  @override
  String get sensorCancel => 'Einlernen abbrechen';

  @override
  String get sensorCancelDescription =>
      'Möchtest du den Einlernvorgang wirklich abbrechen?';

  @override
  String get getEnOceanBackpack => 'Hol dir deinen EnOcean-Adapter';

  @override
  String get enOceanBackpackMissing =>
      'Um in die fantastische Welt der perfekten Konnektivität und Kommunikation einsteigen zu können, benötigst du einen EnOcean-Adapter.\nKlicke hier, um mehr Informationen zu erhalten';

  @override
  String sensorEditChangedSuccessfully(Object sensorName) {
    return '$sensorName wurde erfolgreich geändert';
  }

  @override
  String sensorConnectedVia(Object deviceName) {
    return 'verbunden über $deviceName';
  }

  @override
  String get lastSeen => 'Zuletzt gesehen';

  @override
  String get setButtonOrientation => 'Orientierung festlegen';

  @override
  String get setButtonType => 'Taster-Typ festlegen';

  @override
  String get button1Way => '1-Kanal Taster';

  @override
  String get button2Way => '2-Kanal Taster';

  @override
  String get button4Way => '4-Kanal Taster';

  @override
  String get buttonUnset => 'Nicht belegt';

  @override
  String get button => 'Taster';

  @override
  String get sensor => 'Sensor';

  @override
  String sensorsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Sensoren gefunden',
      one: '1 Sensor gefunden',
      zero: 'Keine Sensoren gefunden',
    );
    return '$_temp0';
  }

  @override
  String get sensorSearch => 'Nach Sensoren suchen';

  @override
  String get searchAgain => 'Erneut suchen';

  @override
  String sensorTeachInHeader(Object sensorType) {
    return '$sensorType einlernen';
  }

  @override
  String sensorChooseHeader(Object sensorType) {
    return '$sensorType auswählen';
  }

  @override
  String get sensorChooseDescription =>
      'Wähle einen Taster aus, den du einlernen möchtest.';

  @override
  String get sensorCategoryDescription =>
      'Wähle eine Kategorie aus, die du einlernen möchten.';

  @override
  String get sensorName => 'Tastername';

  @override
  String get sensorNameFooter => 'Gib dem Taster einen Namen';

  @override
  String sensorAddedSuccessfully(Object sensorName) {
    return '$sensorName wurde erfolgreich eingelernt';
  }

  @override
  String sensorDelete(Object sensorType) {
    return '$sensorType löschen';
  }

  @override
  String sensorDeleteHint(Object sensorName, Object sensorType) {
    return 'Soll der $sensorType $sensorName wirklich gelöscht werden?';
  }

  @override
  String sensorDeletedSuccessfully(Object sensorName) {
    return '$sensorName wurde erfolgreich gelöscht';
  }

  @override
  String get buttonTapDescription =>
      'Tippe auf den Taster, den du einlernen möchtest.';

  @override
  String get waitingForTelegram => 'Der Aktor wartet auf das Telegramm';

  @override
  String get copied => 'Kopiert';

  @override
  String pairingFailed(Object sensorType) {
    return '$sensorType bereits eingelernt';
  }

  @override
  String get generalDescriptionUniversalbutton =>
      'Beim Universaltaster erfolgt die Richtungsumkehr durch kurzes Loslassen des Tasters. Kurze Steuerbefehle schalten ein bzw. aus. (Toggle)';

  @override
  String get generalDescriptionDirectionalbutton =>
      'Richtungstaster ist oben \'einschalten und aufdimmen\' sowie unten \'ausschalten und abdimmen\'.';

  @override
  String get matterForwardingDescription =>
      'Der Tastendruck wird an Matter weitergeleitet.';

  @override
  String get none => 'Keine';

  @override
  String get buttonNoneDescription => 'Der Taster hat keine Funktion.';

  @override
  String get buttonUnsetDescription =>
      'Dem Taster ist kein Verhalten zugewiesen.';

  @override
  String get sensorButtonTypeChangedSuccessfully =>
      'Taster-Typ wurde erfolgreich geändert';

  @override
  String forExample(Object example) {
    return 'z.B. $example';
  }

  @override
  String get enOceanQRCodeInvalidDescription =>
      'Erst ab Produktionsdatum 44/20 möglich';

  @override
  String get input => 'Eingang';

  @override
  String get buttonSceneValueOverride => 'Szenentasterwert überschreiben';

  @override
  String get buttonSceneValueOverrideDescription =>
      'Der Szenentasterwert wird mit dem aktuellen Dimmwert durch gedrückt halten der Taste überschrieben';

  @override
  String get buttonSceneDescription =>
      'Der Szenentaster schaltet mit einem festen Dimmwert ein';

  @override
  String get buttonPress => 'Tastendruck';

  @override
  String get triggerOn =>
      'mit längerer Tasterbetätigung am Universaltaster oder Richtungstaster auf der Einschaltseite';

  @override
  String get triggerOff =>
      'mit einem Doppelimpuls am Universaltaster oder Richtungstaster auf der Ausschaltseite';

  @override
  String get centralOn => 'Zentral Ein';

  @override
  String get centralOff => 'Zentral Aus';

  @override
  String get centralButton => 'Zentraltaster';

  @override
  String get enOceanAdapterNotFound => 'Kein EnOcean Adapter gefunden';

  @override
  String get updateRequired => 'Update erforderlich';

  @override
  String get updateRequiredDescription =>
      'Deine App benötigt ein Update, um dieses neue Gerät zu unterstützen.';

  @override
  String get release32Header =>
      'Die neue BR64 mit Matter und EnOcean sowie die neue Bluetooth-Unterputz-Schaltuhr SU62PF-BT/UC sind jetzt verfügbar!';

  @override
  String get release32EUD64Header =>
      'Der neue Unterputz-Dimmer mit Matter über Wi-Fi und bis zu 300 W ist da!';

  @override
  String get release32EUD64Note1 =>
      'Konfiguration von Dimmgeschwindigkeit, Ein-/Ausschaltgeschwindigkeit, Kinderzimmer-/Schlummerschaltung und vielem mehr.';

  @override
  String get release32EUD64Note2 =>
      'Der Funktionsumfang des EUD64NPN-IPM kann durch Adapter, z. B. den EnOcean-Adapter EOA64, erweitert werden.';

  @override
  String get release32EUD64Note3 =>
      'Bis zu 30 EnOcean-Funktaster können in Verbindung mit dem EnOcean-Adapter EOA64 mit dem EUD64NPN-IPM direkt verknüpft und an Matter weitergeleitet werden.';

  @override
  String get release32EUD64Note4 =>
      'Zwei drahtgebundene Tasteingänge können mit dem EUD64NPN-IPM direkt verknüpft oder an Matter weitergeleitet werden.';

  @override
  String get release32ESR64Header =>
      'Der neue potenzialfreie Unterputz-1-Kanal-Schaltaktor mit Matter über Wi-Fi und bis zu 16 A ist da!';

  @override
  String get release32ESR64Note1 =>
      'Konfiguration von verschiedenen Funktionen wie Stromstoßschalter (ES), Relaisfunktion (ER), Öffner (ER-Invers) und vielem mehr.';

  @override
  String get release32ESR64Note2 =>
      'Der Funktionsumfang des ESR64PF-IPM kann durch Adapter, z. B. den EnOcean-Adapter EOA64, erweitert werden.';

  @override
  String get release32ESR64Note3 =>
      'Bis zu 30 EnOcean-Funktaster können in Verbindung mit dem EnOcean-Adapter EOA64 mit dem ESR64PF-IPM direkt verknüpft und an Matter weitergeleitet werden.';

  @override
  String get release32ESR64Note4 =>
      'Ein drahtgebundener Tasteingang kann mit dem ESR64PF-IPM direkt verknüpft oder an Matter weitergeleitet werden.';

  @override
  String buttonsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Taster gefunden',
      one: '1 Taster gefunden',
      zero: 'Keine Taster gefunden',
    );
    return '$_temp0';
  }

  @override
  String get doubleImpuls => 'mit einem Doppelimpuls';

  @override
  String get impulseDescription =>
      'Wenn der Kanal eingeschaltet ist, wird dieser durch einen Impuls ausgeschaltet.';

  @override
  String get locationServiceEnable => 'Standort aktivieren';

  @override
  String get locationServiceDisabledDescription =>
      'Standort ist deaktiviert. Deine Betriebssystemversion benötigt den Standort, um Bluetooth Geräte finden zu können.';

  @override
  String get locationPermissionDeniedNoPosition =>
      'Standort Berechtigungen wurden nicht erteilt. Deine Betriebssystemversion benötigt die Standortberechtigung, um Bluetooth Geräte finden zu können. Bitte erlaube die Standortberechtigung in deinen Geräteeinstellungen.';

  @override
  String get interfaceStatePermanentDeniedDescriptionDevicesAround =>
      'Die Geräte in der Nähe Berechtigung wurde nicht erteilt. Bitte erlaube die Berechtigung Geräte in der Nähe in deinen Geräteeinstellungen.';

  @override
  String get permissionNearbyDevices => 'Geräte in der Nähe';

  @override
  String get release320Header =>
      'Der neue leistungsstarke Universaldimmer EUD12NPN-BT/600W-230V ist da!';

  @override
  String get release320EUD600Header => 'Was kann der neue Universaldimmer?';

  @override
  String get release320EUD600Note1 =>
      'Universaldimmer mit bis zu 600W Leistung';

  @override
  String get release320EUD600Note2 =>
      'Erweiterbar mit Leistungszusatz LUD12 auf bis zu 3800W';

  @override
  String get release320EUD600Note3 =>
      'Örtliche Bedienung mit Universal- oder Richtungstaster';

  @override
  String get release320EUD600Note4 => 'Zentralfunktionen Ein / Aus';

  @override
  String get release320EUD600Note5 =>
      'Bewegungsmelder Eingang für weiteren Komfort';

  @override
  String get release320EUD600Note6 =>
      'Integrierte Schaltuhr mit 10 Schaltprogramme';

  @override
  String get release320EUD600Note7 => 'Astrofunktion';

  @override
  String get release320EUD600Note8 => 'Individuelle Einschalthelligkeit';

  @override
  String get mqttClientCertificate => 'Client Zertifikat';

  @override
  String get mqttClientCertificateHint => 'MQTT Client Zertifikat';

  @override
  String get mqttClientKey => 'Client Key';

  @override
  String get mqttClientKeyHint => 'MQTT Client Key';

  @override
  String get mqttClientPassword => 'Client Passwort';

  @override
  String get mqttClientPasswordHint => 'MQTT Client Passwort';

  @override
  String get mqttEnableHomeAssistantDiscovery =>
      'HomeAssistant MQTT Discovery aktivieren';

  @override
  String get modbusTcp => 'Modbus TCP';

  @override
  String get enableInterface => 'Schnittstelle aktivieren';

  @override
  String get busAddress => 'Bus-Adresse';

  @override
  String busAddressWithAddress(Object index) {
    return 'Bus-Adresse $index';
  }

  @override
  String get deviceType => 'Gerätetyp';

  @override
  String registerTable(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Registertabellen',
      one: 'Registertabelle',
    );
    return '$_temp0';
  }

  @override
  String get currentValues => 'Aktuelle Werte';

  @override
  String get requestRTU => 'Abfrage RTU';

  @override
  String get requestPriority => 'Abfragepriorität';

  @override
  String get mqttForwarding => 'MQTT Weiterleitung';

  @override
  String get historicData => 'Historische Daten';

  @override
  String get dataFormat => 'Datenformat';

  @override
  String get dataType => 'Datentyp';

  @override
  String get description => 'Beschreibung';

  @override
  String get readWrite => 'Lesen/Schreiben';

  @override
  String get unit => 'Einheit';

  @override
  String get registerTableReset => 'Registertabelle zurücksetzen';

  @override
  String get registerTableResetDescription =>
      'Soll die Registertabelle wirklich zurückgesetzt werden?';

  @override
  String get notConfigured => 'Nicht konfiguriert';

  @override
  String get release330ZGW16Header => 'Umfangreiches Update für das ZGW16WL-IP';

  @override
  String get release330Header => 'Das ZGW16WL-IP mit bis zu 16 Stromzählern';

  @override
  String get release330ZGW16Note1 =>
      'Unterstützung von bis zu 16 ELTAKO Modbus-Stromzählern';

  @override
  String get release330ZGW16Note2 => 'Unterstützung von Modbus TCP';

  @override
  String get release330ZGW16Note3 => 'Unterstützung von MQTT Discovery';

  @override
  String get screenshotButtonLivingRoom => 'Taster Wohnzimmer';

  @override
  String get registerChangedSuccessfully =>
      'Register wurde erfolgreich geändert.';

  @override
  String get serverCertificateEmpty =>
      'Server Zertifikat darf nicht leer sein.';

  @override
  String get registerTemplates => 'Register Templates';

  @override
  String get registerTemplateChangedSuccessfully =>
      'Register Template wurde erfolgreich geändert.';

  @override
  String get registerTemplateReset => 'Register Template zurücksetzen';

  @override
  String get registerTemplateResetDescription =>
      'Soll das Register Template wirklich zurückgesetzt werden?';

  @override
  String get registerTemplateNotAvailable =>
      'Keine Register Templates verfügbar';

  @override
  String get rename => 'Umbenennen';

  @override
  String get registerName => 'Register Name';

  @override
  String get registerRenameDescription =>
      'Gib dem Register einen benutzerdefinierten Namen';

  @override
  String get restart => 'Gerät neu starten';

  @override
  String get restartDescription =>
      'Möchten Sie das Gerät wirklich neu starten?';

  @override
  String get restartConfirmationDescription =>
      'Das Gerät wird nun neu gestartet';

  @override
  String get deleteAllElectricityMeters => 'Alle Stromzähler löschen';

  @override
  String get deleteAllElectricityMetersDescription =>
      'Möchten Sie wirklich alle Stromzähler löschen?';

  @override
  String get deleteAllElectricityMetersConfirmationDescription =>
      'Alle Stromzähler wurden erfolgreich gelöscht';

  @override
  String get resetAllElectricityMeters =>
      'Alle Stromzähler-Konfigurationen zurücksetzen';

  @override
  String get resetAllElectricityMetersDescription =>
      'Möchten Sie wirklich alle Stromzähler-Konfigurationen zurücksetzen?';

  @override
  String get resetAllElectricityMetersConfirmationDescription =>
      'Alle Stromzähler-Konfigurationen wurden erfolgreich zurückgesetzt';

  @override
  String get deleteElectricityMeterHistories =>
      'Alle Stromzähler-Verläufe löschen';

  @override
  String get deleteElectricityMeterHistoriesDescription =>
      'Möchten Sie wirklich alle Stromzähler-Verläufe löschen?';

  @override
  String get deleteElectricityMeterHistoriesConfirmationDescription =>
      'Alle Stromzähler-Verläufe wurden erfolgreich gelöscht';

  @override
  String get multipleElectricityMetersSupportMissing =>
      'Dein Gerät unterstützt derzeit nur einen Stromzähler. Bitte aktualisiere deine Firmware.';

  @override
  String get consumptionWithUnit => 'Verbrauch (kWh)';

  @override
  String get exportWithUnit => 'Lieferung (kWh)';

  @override
  String get importWithUnit => 'Bezug (kWh)';

  @override
  String get resourceWarningHeader => 'Begrenzte Ressourcen';

  @override
  String mqttAndTcpResourceWarning(Object protocol) {
    return 'Ein gleichzeitiger Betrieb von MQTT und Modbus TCP ist wegen begrenzter Systemressourcen nicht möglich. Deaktiviere zuerst $protocol.';
  }

  @override
  String get mqttEnabled => 'MQTT aktiviert';

  @override
  String get redirectMQTT => 'Zu den MQTT Einstellungen';

  @override
  String get redirectModbus => 'Zu den Modbus Einstellungen';

  @override
  String get unsupportedSettingDescription =>
      'Mit deiner aktuellen Firmware-Version werden einige der Geräte-Einstellungen nicht unterstützt. Bitte aktualisiere deine Firmware, um die neuen Funktionen nutzen zu können.';

  @override
  String get updateNow => 'Jetzt aktualisieren';

  @override
  String get zgw241Hint =>
      'Mit diesem Update wird Modbus TCP standardmäßig aktiviert und MQTT deaktiviert. Dies kann in den Einstellungen geändert werden. Mit der Unterstützung von bis zu 16 Zählern wurden viele Optimierungen vorgenommen; dies kann zu Veränderungen in den Geräteeinstellungen führen. Bitte führe nach Anpassungen der Einstellungen einen Neustart des Geräts durch.';

  @override
  String get deviceConfigChangedSuccesfully =>
      'Geräte Konfiguration wurde erfolgreich geändert.';

  @override
  String get deviceConfiguration => 'Geräte Konfiguration';

  @override
  String get tiltModeToggle => 'Lamellenfunktion';

  @override
  String get tiltModeToggleFooter =>
      'Wenn das Gerät in Matter eingelernt wird, müssen dort alle Funktionen neu eingestellt werden';

  @override
  String get shaderMovementDirection => 'Umpolung Auf/Ab';

  @override
  String get shaderMovementDirectionDescription =>
      'Umpolung der Richtung für Auf-/Ab-Bewegung des Motors';

  @override
  String get tiltTime => 'Lamellenzeit';

  @override
  String changeTiltModeDialogTitle(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'aktivieren',
      'false': 'deaktivieren',
      'other': 'ändern',
    });
    return 'Lamellenfunktion $_temp0';
  }

  @override
  String changeTiltModeDialogConfirmation(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Aktivieren',
      'false': 'Deaktivieren',
      'other': 'Ändern',
    });
    return '$_temp0';
  }

  @override
  String get generalTextSlatSetting => 'Einstellung der Lamellen';

  @override
  String get generalTextPosition => 'Position';

  @override
  String get generalTextSlatPosition => 'Position der Lamellen';

  @override
  String get slatSettingDescription => 'Lamelleneinstellung Beschreibung';

  @override
  String get scenePositionSliderDescription => 'Höhe';

  @override
  String get sceneSlatPositionSliderDescription => 'Kippen';

  @override
  String get referenceRun => 'Kalibrierungslauf';

  @override
  String get slatAutoSettingHint =>
      'In diesem Modus spielt die Position der Jalousien keine Rolle, bevor sich die Lamellen auf die gewünschte Neigungsposition einstellen.';

  @override
  String get slatReversalSettingHint =>
      'In diesem Modus werden die Jalousien vollständig geschlossen, bevor sich die Lamellen auf die gewünschte Neigungsposition einstellen.';

  @override
  String get release340Header =>
      'Der neue Unterputz Matter-Beschattungsaktor ESB64NP-IPM ist da!';

  @override
  String get release340ESB64Header => 'Was kann der ESB64NP-IPM?';

  @override
  String get release340ESB64Note1 =>
      'Unser Matter-Gateway zertifizierter Beschattungsaktor mit optionaler Lamellenfunktion';

  @override
  String get release340ESB64Note2 =>
      'Zwei drahtgebundene Tasteingänge für manuelles Schalten und Weiterleitung an Matter';

  @override
  String get release340ESB64Note3 =>
      'Erweiterbar mit EnOcean-Adapter (EOA64). Z.B mit EnOcean-Funktaster F4T55';

  @override
  String get release340ESB64Note4 =>
      'Offen für Integrationen dank REST-API nach OpenAPI Standard';

  @override
  String get activateTiltModeDialogText =>
      'Wenn die Lamellenfunktion aktiviert wird, gehen alle Einstellungen verloren. Bist du sicher, dass du die Lamellenfunktion aktivieren möchtest?';

  @override
  String get deactivateTiltModeDialogText =>
      'Wenn die Lamellenfunktion deaktiviert wird, gehen alle Einstellungen verloren. Bist du sicher, dass du die Lamellenfunktion deaktivieren möchtest?';

  @override
  String shareConfiguration(Object name) {
    return 'Konfiguration $name teilen';
  }

  @override
  String get configurationSharedSuccessfully =>
      'Konfiguration erfolgreich geteilt';

  @override
  String get configurationShareFailed =>
      'Teilen der Konfiguration fehlgeschlagen';
}
