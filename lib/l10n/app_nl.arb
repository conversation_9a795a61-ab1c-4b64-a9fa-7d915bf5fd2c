{"appName": "ELTAKO Connect", "discoveryHint": "Activeer Bluetooth op het apparaat om verbinding te maken", "devicesFound": "{count, plural, =0 {Geen apparaat gevonden} one {1 apparaat gevonden} other {{count} apparaten gevonden}}", "@devicesFound": {"description": "Specifies how many device are found in the discovery overview", "type": "text"}, "discoveryDemodeviceName": "{count, plural, one {Demo apparaat} other {Demo apparaten}}", "discoverySu12Description": "2-<PERSON><PERSON><PERSON> Schakelklok Bluetooth", "discoveryImprint": "<PERSON><PERSON>dr<PERSON>", "discoveryLegalnotice": "Juridische mededeling", "generalSave": "Opsla<PERSON>", "generalCancel": "<PERSON><PERSON><PERSON>", "detailsHeaderHardwareversion": "Hardware versie", "detailsHeaderSoftwareversion": "Software versie", "detailsHeaderConnected": "Verbonden", "detailsHeaderDisconnected": "Verbinding verbroken", "detailsTimersectionHeader": "Programma's", "detailsTimersectionTimercount": "gebruikt door 60 programma's", "detailsConfigurationsectionHeader": "Configuratie", "detailsConfigurationPin": "Apparaat-PIN", "detailsConfigurationChannelsDescription": "Kanaal 1: {channel1} en Kanaal 2: {channel2}", "settingsCentralHeader": "Centraal aan/uit", "detailsConfigurationCentralDescription": "Werkt alleen als het kanaal op Auto staat.", "detailsConfigurationDevicedisplaylock": "<PERSON><PERSON>lay vergrendelen", "timerOverviewHeader": "Programma's", "timerOverviewTimersectionTimerinactivecount": "inactief", "timerDetailsListsectionDays1": "<PERSON><PERSON><PERSON>", "timerDetailsListsectionDays2": "Dinsdag", "timerDetailsListsectionDays3": "Woensdag", "timerDetailsListsectionDays4": "Donderdag", "timerDetailsListsectionDays5": "Vrijdag", "timerDetailsListsectionDays6": "Zaterdag", "timerDetailsListsectionDays7": "Zondag", "timerDetailsHeader": "Programma", "timerDetailsSunrise": "Zonsopkomst", "generalToggleOff": "Uit", "generalToggleOn": "<PERSON><PERSON>", "timerDetailsImpuls": "Impuls", "generalTextTime": "Tijd", "generalTextAstro": "Astro", "generalTextAuto": "Auto", "timerDetailsOffset": "Tijdverschuiving", "timerDetailsPlausibility": "<PERSON>er plausibiliteitscon<PERSON>le", "timerDetailsPlausibilityDescription": "<PERSON><PERSON> de 'Uit'-tijd voor de 'A<PERSON>'-tij<PERSON> ligt, worden beide tijden genegeerd, bijvoorbeeld inschakelen bij zonsopgang en uitschakelen om 6.00 uur in de ochtend. Er zijn ook constellaties waarbij de test ongewenst is, bijvoorbeeld het inschakelen bij zonsondergang en uitschakelen om 01.00 uur.", "generalDone": "<PERSON><PERSON><PERSON>", "generalDelete": "Verwijderen", "timerDetailsImpulsDescription": "Verander de globale impuls configuratie", "settingsNameHeader": "<PERSON><PERSON><PERSON>at naam", "settingsNameDescription": "Deze naam wordt gebruikt om het apparaat te identificeren.", "settingsFactoryresetHeader": "Fabrieksinstellingen", "settingsFactoryresetDescription": "Welke inhoud moet worden gereset?", "settingsFactoryresetResetbluetooth": "Bluetooth-instellingen resetten", "settingsFactoryresetResettime": "Tijdinstellingen resetten", "settingsFactoryresetResetall": "Terugzetten naar fabrieksinstellingen", "settingsDeletetimerHeader": "Programma's verwijderen", "settingsDeletetimerDescription": "Moeten alle programma's echt verwijderd worden?", "settingsDeletetimerAllchannels": "Alle kanalen", "settingsImpulseHeader": "Impuls-<PERSON><PERSON><PERSON><PERSON><PERSON>d", "settingsImpulseDescription": "De impuls-s<PERSON><PERSON><PERSON><PERSON><PERSON> geeft de duur van de puls aan.", "generalTextRandommode": "Willekeurige modus", "settingsChannelsTimeoffsetHeader": "Tijdsverschuiving van de zonnewende", "settingsChannelsTimeoffsetDescription": "Zomer: {summerOffset} Min | Winter: {winterOffset} Min", "settingsLocationHeader": "Plaats", "settingsLocationDescription": "<PERSON>el uw locatie in om astro-functies te gebruiken.", "settingsLanguageHeader": "Apparaattaal", "settingsLanguageSetlanguageautomatically": "Taal automatisch instellen", "settingsLanguageDescription": "<PERSON><PERSON> de <PERSON> voor de {deviceType}", "settingsLanguageGerman": "<PERSON><PERSON>", "settingsLanguageFrench": "<PERSON><PERSON>", "settingsLanguageEnglish": "<PERSON><PERSON><PERSON>", "settingsLanguageItalian": "Italiaans", "settingsLanguageSpanish": "Spaans", "settingsDatetimeHeader": "Datum en tijd", "settingsDatetimeSettimeautomatically": "Systeemtij<PERSON> toe<PERSON>", "settingsDatetimeSettimezoneautomatically": "Tijdzone automatisch instellen", "generalTextTimezone": "Tijdzone", "settingsDatetime24Hformat": "24-u<PERSON>-form<PERSON>t", "settingsDatetimeSetsummerwintertimeautomatically": "Zomer-/wintertijd automatisch", "settingsDatetimeWinter": "Winter", "settingsDatetimeSummer": "<PERSON><PERSON>", "settingsPasskeyHeader": "Huidige apparaat-PIN", "settingsPasskeyDescription": "V<PERSON>r de huidige apparaat-PIN in", "timerDetailsActiveprogram": "Programma actief", "timerDetailsActivedays": "<PERSON><PERSON><PERSON> dagen", "timerDetailsSuccessdialogHeader": "Succesvol", "timerDetailsSuccessdialogDescription": "Programma succesvol toegevoegd", "settingsRandommodeDescription": "De willekeurige modus werkt alleen bij tijd-programma's, niet bij impuls- of astro-programma's (zonsopgang of zonsondergang).", "settingsSolsticeHeader": "<PERSON><PERSON><PERSON><PERSON> van de zonnewendetijd", "settingsSolsticeDescription": "De tijd geeft de tijdsverschuiving vanaf zonsondergang aan. De zonsopgang wordt dienovereenkomstig omgekeerd.", "settingsSolsticeHint": "Voorbeeld:\nIn de winter schakelt hij 30 minuten voor zonsondergang, wat betekent dat hij ook 30 minuten na zonsopgang schakelt.", "generalTextMinutesShort": "Min", "settingsPinDescription": "De PIN is vereist voor de verbinding.", "settingsPinHeader": "Nieuwe apparaat-PIN", "settingsPinNewpinDescription": "<PERSON><PERSON>r een nieuwe PIN in", "settingsPinNewpinRepeat": "<PERSON><PERSON><PERSON> de nieuwe PIN", "detailsProductinfo": "Productinformatie", "settingsDatetimeSettimeautodescription": "<PERSON><PERSON> het gewenste tijdstip", "minutes": "{count, plural, one {minuut} other {minuten}}", "hours": "{count, plural, one {uur} other {uren}}", "seconds": "{count, plural, one {seconde} other {seconden}}", "generalTextChannel": "{count, plural, one {kanaal} other {kanalen}}", "generalLabelChannel": "<PERSON><PERSON><PERSON> {number}", "generalTextDate": "Datum", "settingsDatetime24HformatDescription": "<PERSON><PERSON> het gewenste formaat", "settingsDatetimeSetsummerwintertime": "<PERSON><PERSON>-/wintertijd", "settingsDatetime24HformatValue24": "24h", "settingsDatetime24HformatValue12": "AM/PM", "detailsEdittimer": "<PERSON><PERSON>'s bewerken", "settingsPinOldpinRepeat": "<PERSON><PERSON><PERSON> de huidige PIN", "settingsPinCheckpin": "PIN controleren", "detailsDevice": "{count, plural, one {apparaat} other {apparaten}}", "detailsDisconnect": "Verbinding verbreken", "settingsCentralDescription": "De ingang A1 regelt de centraal aan/uit.\nCentraal aan/uit is alleen van toepassing als het kanaal op centraal aan/uit staat.", "settingsCentralHint": "Voorbeeld:\nKanaal 1 = Centraal aan/uit\nKanaal 2 = Uit\nA1 = Centraal aan -> Alleen K1 schakelt aan, K2 blijft uit", "settingsCentralToggleheader": "Centrale ingang schakelt", "settingsCentralActivechannelsdescription": "<PERSON><PERSON><PERSON> met de instelling centraal aan/uit:", "settingsSolsticeSign": "Teken", "settingsDatetimeTimezoneDescription": "Midden-Europese tijd", "generalButtonContinue": "Doorgaan", "settingsPinConfirmationDescription": "PIN-wijziging geslaagd", "settingsPinFailDescription": "PIN-wi<PERSON><PERSON><PERSON> mislukt", "settingsPinFailHeader": "Mislukt", "settingsPinFailShort": "De PIN moet precies 6 cijfers lang zijn", "settingsPinFailWrong": "De actuele <PERSON> is onjuist", "settingsPinFailMatch": "De PIN-codes komen niet overeen", "discoveryLostconnectionHeader": "Verbinding verbroken", "discoveryLostconnectionDescription": "De verbinding van het apparaat is verbroken.", "settingsChannelConfigCentralDescription": "Gedraagt zich als AUTO en reageert ook op de bedrade centrale ingangen", "settingsChannelConfigOnDescription": "<PERSON><PERSON><PERSON>t het kanaal permanent op AAN en negeert de programma's", "settingsChannelConfigOffDescription": "<PERSON><PERSON><PERSON><PERSON> het kanaal permanent UIT en negeert de programma's", "settingsChannelConfigAutoDescription": "Schakelt volgens de tijd- en astroprogramma's.", "bluetoothPermissionDescription": "Bluetooth is vereist voor de configuratie van de apparaten.", "timerListitemOn": "<PERSON><PERSON><PERSON> a<PERSON>", "timerListitemOff": "<PERSON><PERSON><PERSON>", "timerListitemUnknown": "Onbekend", "timerDetailsAstroHint": "De locatie moet worden ingesteld in de instellingen om de astro-programma's correct te laten werken.", "timerDetailsTrigger": "<PERSON><PERSON>", "timerDetailsSunset": "Zonsondergang", "settingsLocationCoordinates": "Coördinaten", "settingsLocationLatitude": "Breedtegraad", "settingsLocationLongitude": "Lengtegraad", "timerOverviewEmptyday": "Er worden momenteel geen programma's gebruikt voor {day}", "timerOverviewProgramloaded": "<PERSON><PERSON>'s worden geladen", "timerOverviewProgramchanged": "<PERSON><PERSON> is gewijzigd", "settingsDatetimeProcessing": "Datum en tijd zijn gewij<PERSON>d", "deviceNameEmpty": "Invoer mag niet leeg zijn", "deviceNameHint": "De invoer mag niet meer dan {count} tekens bevatten.", "deviceNameChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is gewijzigd.", "deviceNameChangedSuccessfully": "Apparaatnaam is succesvol gewijzigd.", "deviceNameChangedFailed": "Er is een fout opgetreden.", "settingsPinConfirm": "Bevestigen", "deviceShowInstructions": "1. <PERSON><PERSON> de klok met SET\n2. Tik op de knop hierboven om het zoeken te starten", "deviceNameNew": "<PERSON>oer een nieuwe apparaatnaam in", "settingsLanguageRetrieved": "Taal wordt opgehaald", "detailsProgramsShow": "<PERSON><PERSON>'s weergeven", "generalTextProcessing": "Even geduld a.u.b.", "generalTextRetrieving": "worden opge<PERSON>.", "settingsLocationPermission": "Geef ELTAKO Connect toegang tot de locatie van dit apparaat", "timerOverviewChannelloaded": "<PERSON><PERSON>n worden geladen", "generalTextRandommodeChanged": "Willekeurige modus is gewijzigd", "detailsConfigurationsectionChanged": "Configuratie is gewi<PERSON><PERSON>d", "settingsSettimeFunctions": "Tijdfuncties zijn gew<PERSON>j<PERSON>d", "imprintContact": "Contact", "imprintPhone": "Telefoon", "imprintMail": "E-mail", "imprintRegistrycourt": "Registratie rechtbank", "imprintRegistrynumber": "<PERSON><PERSON><PERSON> nummer", "imprintCeo": "Directeur", "imprintTaxnumber": "BTW-identificatienummer", "settingsLocationCurrent": "Huidige locatie", "generalTextReset": "Resetten", "discoverySearchStart": "Start met zoeken", "discoverySearchStop": "Stop met zoeken", "settingsImpulsSaved": "Impuls-schakeltijd is opgeslagen", "settingsCentralNochannel": "<PERSON><PERSON> zijn geen kanalen met de instelling centraal aan/uit", "settingsFactoryresetBluetoothConfirmationDescription": "De Bluetooth-verbinding is succesvol gereset.", "settingsFactoryresetBluetoothFailDescription": "<PERSON><PERSON><PERSON>-verbindingen mislukt.", "imprintPublisher": "Uitgeverij", "discoveryDeviceConnecting": "De verbinding word tot stand gebracht", "discoveryDeviceRestarting": "Opnieuw opstarten...", "generalTextConfigurationsaved": "Kanaalconfiguratie opgeslagen.", "timerOverviewChannelssaved": "<PERSON><PERSON><PERSON>", "timerOverviewSaved": "<PERSON>r <PERSON>", "timerSectionList": "Lijstweergave", "timerSectionDayview": "Dagweergave", "generalTextChannelInstructions": "Ka<PERSON>alinst<PERSON>en", "generalTextPublisher": "Uitgeverij", "settingsDeletetimerDialog": "Wilt u echt alle programma's wissen?", "settingsFactoryresetResetbluetoothDialog": "Wilt u echt alle Bluetooth-instellingen resetten?", "settingsCentralTogglecentral": "Centraal\naan/uit", "generalTextConfirmation": "{serviceName} is succesvol gewij<PERSON>d.", "generalTextFailed": "{serviceName} kan niet worden gewijzigd.", "settingsChannelConfirmationDescription": "<PERSON><PERSON><PERSON> zijn succesvol gewijzigd.", "timerDetailsSaveHeader": "<PERSON><PERSON>", "timerDetailsDeleteHeader": "Programma ver<PERSON>", "timerDetailsSaveDescription": "Het programma is succesvol opgeslagen.", "timerDetailsDeleteDescription": "Programma is succesvol verwijderd.", "timerDetailsAlertweekdays": "Het programma kan niet worden opgeslagen, omdat er geen weekdagen zijn geselecteerd.", "generalTextOk": "OK", "settingsDatetimeChangesuccesfull": "De tijd is succesvol veranderd.", "discoveryConnectionFailed": "Verbinding mislukt", "discoveryDeviceResetrequired": "Er kon geen verbinding met het apparaat tot stand worden gebracht. Om dit probleem op te lossen, verwijdert u het apparaat uit uw Bluetooth-instellingen. Neem contact op met onze technische ondersteuning als het probleem aanhoudt.", "generalTextSearch": "<PERSON><PERSON> apparaten", "generalTextOr": "of", "settingsFactoryresetProgramsConfirmationDescription": "Alle programma's zijn succesvol verwijderd.", "generalTextManualentry": "Handmatige invoer", "settingsLocationSaved": "Locatie opgeslagen", "settingsLocationAutosearch": "Zoek locatie automatisch", "imprintPhoneNumber": "+31 6 504 190 67", "imprintMailAddress": "<EMAIL>", "settingsFactoryresetResetallDialog": "Wilt u het apparaat echt terugzetten naar de fabrieksinstellingen?", "settingsFactoryresetFactoryConfirmationDescription": "Het apparaat is succesvol teruggezet naar de fabrieksinstellingen.", "settingsFactoryresetFactoryFailDescription": "Apparaat resetten is mislukt.", "imprintPhoneNumberIos": "+31 6 504 190 67", "mfzFunctionA2Title": "2-traps responsvertraging (A2)", "mfzFunctionA2TitleShort": "2-traps responsvertraging (A2)", "mfzFunctionA2Description": "Bij het a<PERSON><PERSON><PERSON> van de stuurspanning begint de tijdsverloop T1 tussen 0 en 60 seconden. Aan het einde van deze periode sluit contact 1-2 en begint het tijdsverloop T2 tussen 0 en 60 seconden. Aan het einde van deze tijd sluit contact 3-4. <PERSON> een onderbreking begint het tijdverloop opnieuw met T1.", "mfzFunctionRvTitle": "Uitschakelvertraging (RV)", "mfzFunctionRvTitleShort": "RV | Uitschakelvertraging", "mfzFunctionRvDescription": "Bij het aan<PERSON>gen van de stuurspanning schakelt het relaiscontact naar 15-18. Bij het wegvallen van de stuurspanning wordt de afvalvertragingstijd gestart; bij het beëindigen van de tijd keert het relaiscontact terug naar de normale stand. Resetbaar tijdens de afvalvertragingstijd.", "mfzFunctionTiTitle": "Impulsgever beginnend met puls (TI)", "mfzFunctionTiTitleShort": "TI | impulsgever beginnend met puls", "mfzFunctionTiDescription": "Zolang de stuurspanning aanwezig is, sluit en opent het schakelcontact. De omschakeltijd is in beide richtingen apart instelbaar. Bij het inschakelen van de stuurspanning verandert het schakelcontact direct naar 15-18.", "mfzFunctionAvTitle": "Vertraagd opkomend (AV)", "mfzFunctionAvTitleShort": "AV | vertraagd opkomend", "mfzFunctionAvDescription": "<PERSON><PERSON> de stuurspanning wordt aangelegd, wordt de opkomvertragingstijd gestart; bij het beëindigen van de tijd verandert het schakelcontact naar 15-18. Na een onderbreking wordt de opkomvertragingstijd opnieuw gestart.", "mfzFunctionAvPlusTitle": "Vertraagd opkomend met geheugen (AV+)", "mfzFunctionAvPlusTitleShort": "AV+ | vertraagd opkomend met geheugen", "mfzFunctionAvPlusDescription": "De functie is hetzelfde als AV. Na een onderbreking wordt de verstreken tijd echter opgeslagen.", "mfzFunctionAwTitle": "Uitschakelwissend relais (AW)", "mfzFunctionAwTitleShort": "AW | uitschakelwissend relais", "mfzFunctionAwDescription": "<PERSON><PERSON> de stuurspanning wordt onderbroken, verandert het contact naar 15-18 en keert terug na het beëindigen van de tijd. Als tijdens de wistijd de stuurspanning aanwezig is, springt het contact onmiddellijk terug naar 15-16 en vervalt de resterende tijd.", "mfzFunctionIfTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (IF; alleen MFZ12.1)", "mfzFunctionIfTitleShort": "IF | impulsvormer", "mfzFunctionIfDescription": "Bij het a<PERSON><PERSON><PERSON> van de stuurspanning verandert het schakelcontact gedurende de ingestelde tijd naar 15-18. <PERSON><PERSON> stuurimpulsen worden pas na het verstrijken van de ingestelde tijd geëvalueerd.", "mfzFunctionEwTitle": "Inschakelwissend relais (EW)", "mfzFunctionEwTitleShort": "EW | inschakelwissend relais", "mfzFunctionEwDescription": "<PERSON><PERSON> de stuurspanning wordt aangelegd, verandert het contact naar 15-18 en keert terug na het beëindigen van de tijd. Als de stuurspanning tijdens de wistijd wegvalt, schakelt het contact onmiddellijk terug naar 15-16 en vervalt de resterende tijd.", "mfzFunctionEawTitle": "Inschakel- en uitschakelwissend relais (EAW)", "mfzFunctionEawTitleShort": "EAW | inschakel- en uitschakelwissend relais", "mfzFunctionEawDescription": "Bij het aan<PERSON>gen of onderbreken van de stuurspanning schakelt het schakelcontact naar 15-18 en keert terug na de ingestelde wistijd.", "mfzFunctionTpTitle": "Impulsgever beginnend met pauze (TP)", "mfzFunctionTpTitleShort": "TP | impulsgever beginnend met pauze", "mfzFunctionTpDescription": "Beschrij<PERSON> van de functie hetzelfde als voor TI, behalve dat wanneer de stuurspanning wordt toegepast, het contact aanvankelijk op 15-16 blijft in plaats van te veranderen naar 15-18.", "mfzFunctionIaTitle": "Impulsgestuurd vertraagd opkomend bijv. automatische deuropeners (IA; alleen MFZ12.1))", "mfzFunctionIaTitleShort": "IA | impulsgestuurd vertraagd opkomend", "mfzFunctionIaDescription": "De tijdsduur T1 start met een stuurimpuls vanaf 50ms; bij het beëindigen van de tijd wisselt het schakelcontact gedurende de tijdsduur T2 naar 15-18 gedurende 1 seconde (bijv. voor automatische deuropener). Als T1 is ingesteld op T1 min = 0,1 seconden werkt de IA als impulsvormer wanneer de tijd T2 verstrijkt, on<PERSON><PERSON><PERSON><PERSON><PERSON> van de duur van de stuurimpuls (min. 150 ms).", "mfzFunctionArvTitle": "Vertraagd opkomend en vertraagd\nafvallend (ARV)", "mfzFunctionArvTitleShort": "ARV | vertraagd opkomend en vertraagd\nafvallend", "mfzFunctionArvDescription": "Wanneer de stuurspanning wordt aangelegd, begint de tijd, bij het beëindigen van de tijd wordt het schakelcontact naar de stand 15 -18 gebracht. Als de stuurspanning vervolgens wordt onderbroken, begint een nieuwe tijd, aan het einde van deze tijd schakelt het schakelcontact weer terug naar de ruststand.\nNa een onderbreking van de opkomvertraging begint de tijd opnieuw.", "mfzFunctionArvPlusTitle": "Vertraagd opkomend en vertraagd afvallend met geheugen (ARV+)", "mfzFunctionArvPlusTitleShort": "ARV+ | vertraagd opkomend en vertraagd afvallend met geheugen", "mfzFunctionArvPlusDescription": "Dezelfde functie als ARV, maar na een onderbreking van de opkomvertraging wordt de verstreken tijd opgeslagen.", "mfzFunctionEsTitle": "<PERSON>mp<PERSON>srelais (ES)", "mfzFunctionEsTitleShort": "ES | impulsrelais", "mfzFunctionEsDescription": "<PERSON>ij elke stuuri<PERSON>uls vanaf 50 ms schakelt het schakelcontact om.", "mfzFunctionEsvTitle": "Impulsrel<PERSON> met afvalvertraging en uitschakelvoorwaarschuwing (ESV)", "mfzFunctionEsvTitleShort": "ESV | impulsrelais met afvalvertraging en uitschakelvoorwaarschuwing", "mfzFunctionEsvDescription": "Functie hetzelfde als SRV. Bovendien met uitschakelvoorwaarschuwing: ca. 30 sec. voor het beëindigen van de tijd begint de verlichting 3 keer te knipperen in geleidelijk kortere tijdsintervallen.", "mfzFunctionErTitle": "<PERSON><PERSON><PERSON> (ER)", "mfzFunctionErTitleShort": "ER | relais", "mfzFunctionErDescription": "<PERSON>ij het aan<PERSON><PERSON> van de stuurspanning schakelt het schakelcontact van 15-16 naar 15-18.", "mfzFunctionSrvTitle": "Impulsrelais met af<PERSON><PERSON><PERSON><PERSON> (SRV)", "mfzFunctionSrvTitleShort": "SRV | impulsrel<PERSON> met afvalvertraging", "mfzFunctionSrvDescription": "<PERSON>ij elke stuurimpuls vanaf 50 ms schakelt het schakelcontact heen en weer. In de schakelstand 15-18 schakelt het apparaat na de afvalvertragingstijd automatisch weer terug naar de ruststand 15-16.", "detailsFunctionsHeader": "Functies", "mfzFunctionTimeHeader": "Tijd (t{index})", "mfzFunctionOnDescription": "permanent AAN", "mfzFunctionOffDescription": "permanent UIT", "mfzFunctionMultiplier": "Factor", "discoveryMfz12Description": "Multifunctioneel-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Bluetooth", "mfzFunctionOnTitle": "<PERSON><PERSON> (ON)", "mfzFunctionOnTitleShort": "<PERSON><PERSON> (ON)", "mfzFunctionOffTitle": "Uit (OFF)", "mfzFunctionOffTitleShort": "Uit (OFF)", "mfzMultiplierSecondsFloatingpoint": "0.1 seconden", "mfzMultiplierMinutesFloatingpoint": "0.1 minuten", "mfzMultiplierHoursFloatingpoint": "0.1 uren", "mfzOverviewFunctionsloaded": "Functies worden geladen", "mfzOverviewSaved": "Functie opgeslagen", "settingsBluetoothHeader": "Bluetooth", "bluetoothChangedSuccessfully": "Bluetooth-instelling is succesvol gewijzigd.", "settingsBluetoothInformation": "Opmerking: Al<PERSON> deze instelling is geactiveerd, is het apparaat permanent zichtbaar voor iedereen via Bluetooth! Het wordt aanbevolen om de pincode van het apparaat te wijzigen.", "settingsBluetoothContinuousconnection": "Permanente zichtbaarheid", "settingsBluetoothContinuousconnectionDescription": "Door de zichtbaar<PERSON><PERSON> van het apparaat in te schakelen, blijft Bluetooth actief op de apparaat ({deviceType}) en hoeft deze niet handmatig te worden geactiveerd voordat een verbinding tot stand kan worden gebracht.", "settingsBluetoothTimeout": "Time-out verbinding", "settingsBluetoothPinlimit": "PIN-beperking", "settingsBluetoothTimeoutDescription": "De verbinding wordt verbroken na {timeout} minuten inactiviteit.", "settingsBluetoothPinlimitDescription": "Om veiligheidsredenen heeft u een maximum van {attempts} pogingen om de PIN in te voeren. Bluetooth wordt daarna gede<PERSON>erd en moet handmatig weer opnieuw worden geactiveerd voor een nieuwe verbinding.", "settingsBluetoothPinAttempts": "pogingen", "settingsResetfunctionHeader": "Functies resetten", "settingsResetfunctionDialog": "Wilt u echt alle functies resetten?", "settingsFactoryresetFunctionsConfirmationDescription": "Alle functies zijn succesvol gereset.", "mfzFunctionTime": "Tijd (t)", "discoveryConnectionFailedInfo": "<PERSON><PERSON>-verbinding", "detailsConfigurationDevicedisplaylockDialogtext": "Onmiddellijk na het vergrendelen van het display van het apparaat wordt Bluetooth gedeactiveerd en moet deze handmatig opnieuw worden geactiveerd om een nieuwe verbinding tot stand te brengen.", "detailsConfigurationDevicedisplaylockDialogquestion": "Weet u zeker dat u het display van het apparaat wilt vergrendelen?", "settingsDemodevices": "Demo-apparaten weergeven", "generalTextSettings": "Instellingen", "discoveryWifi": "WiFi", "settingsInformations": "Informatie", "detailsConfigurationDimmingbehavior": "Dimgedrag", "detailsConfigurationSwitchbehavior": "<PERSON><PERSON><PERSON><PERSON> ged<PERSON>", "detailsConfigurationBrightness": "<PERSON><PERSON><PERSON><PERSON>", "detailsConfigurationMinimum": "Minimale held<PERSON><PERSON><PERSON>", "detailsConfigurationMaximum": "Maximale helderheid", "detailsConfigurationSwitchesGr": "<PERSON><PERSON><PERSON><PERSON> (GR)", "detailsConfigurationSwitchesGs": "G<PERSON>ep<PERSON> impulsrel<PERSON> (GS)", "detailsConfigurationSwitchesCloserer": "<PERSON>-relais (ER)", "detailsConfigurationSwitchesClosererDescription": "Uit -> inged<PERSON>t houden (aan) -> los<PERSON>n (uit)", "detailsConfigurationSwitchesOpenerer": "<PERSON>-<PERSON><PERSON>s (ER-Invers)", "detailsConfigurationSwitchesOpenererDescription": "Aan -> inged<PERSON>t houden (uit) -> los<PERSON>n (aan)", "detailsConfigurationSwitchesSwitch": "Wisselschakelaar", "detailsConfigurationSwitchesSwitchDescription": "Met elke schakeling wordt het licht in- en uitgeschakeld", "detailsConfigurationSwitchesImpulsswitch": "<PERSON>mp<PERSON><PERSON>-<PERSON><PERSON>s", "detailsConfigurationSwitchesImpulsswitchDescription": "Pulsdrukker wordt kort ingedrukt en los<PERSON>aten om het licht aan of uit te doen", "detailsConfigurationSwitchesClosererDescription2": "<PERSON><PERSON> de pulsdrukker ingedrukt. Bij het loslaten stopt de motor", "detailsConfigurationSwitchesImpulsswitchDescription2": "Pulsdrukker wordt kort ingedrukt om de motor te starten en kort ingedrukt om hem weer te stoppen", "detailsConfigurationWifiloginScan": "Scan QR-code", "detailsConfigurationWifiloginScannotvalid": "Gescande code is niet geldig", "detailsConfigurationWifiloginDescription": "Code invoeren", "detailsConfigurationWifiloginPassword": "Wachtwoord", "discoveryEsbipDescription": "Rolluik- en zonweringsactor IP", "discoveryEsripDescription": "Impuls-schakelrelais IP", "discoveryEudipDescription": "<PERSON>le dimmer IP", "generalTextLoad": "Laden", "wifiBasicautomationsNotFound": "<PERSON>n automatisering gevonden.", "wifiCodeInvalid": "Ongeldige code", "wifiCodeValid": "Geldige code", "wifiAuthorizationLogin": "Verbinden", "wifiAuthorizationLoginFailed": "Inloggen mislukt", "wifiAuthorizationSerialnumber": "Serienummer", "wifiAuthorizationProductiondate": "Productiedatum", "wifiAuthorizationProofofpossession": "PoP", "generalTextWifipassword": "WiFi-wachtwoord", "generalTextUsername": "Gebruikersnaam", "generalTextEnter": "OF HANDMATIG INVOEREN", "wifiAuthorizationScan": "Scan de ELTAKO-code.", "detailsConfigurationDevicesNofunctionshinttext": "Dit apparaat ondersteunt momenteel geen andere instellingen.␣", "settingsUsedemodelay": "Gebruik demo-vertraging", "settingsImpulsLoad": "Impuls-schakeltijd wordt geladen", "settingsBluetoothLoad": "Bluetooth-instelling wordt geladen.", "detailsConfigurationsectionLoad": "Configuraties worden geladen", "generalTextLogin": "Aanmelden", "generalTextAuthentication": "Authenti<PERSON><PERSON>", "wifiAuthorizationScanDescription": "Zoek de ELTAKO-code op het WiFi-apparaat of op de meegeleverde datasheet en scan deze met je camera.", "wifiAuthorizationScanShort": "ELTAKO-code scannen", "detailsConfigurationEdgemode": "Dimcurve", "detailsConfigurationEdgemodeLeadingedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextNetwork": "Netwerk", "wifiAuthenticationSuccessful": "Authenticatie geslaagd", "detailsConfigurationsectionSavechange": "Configurat<PERSON> g<PERSON>", "discoveryWifiAdddevice": "WiFi-apparaat toevoegen", "wifiAuthenticationDelay": "Dit kan tot 1 minuut duren", "generalTextRetry": "Opnieuw proberen", "wifiAuthenticationCredentials": "<PERSON><PERSON><PERSON> de inloggegevens van uw WiFi-netwerk in", "wifiAuthenticationSsid": "SSID", "wifiAuthenticationDelaylong": "Het kan tot 1 minuut duren voordat het apparaat gereed is\nen in de app wordt weergegeven", "wifiAuthenticationCredentialsShort": "<PERSON><PERSON><PERSON><PERSON> in", "wifiAuthenticationTeachin": "<PERSON><PERSON> het apparaat met <PERSON><PERSON><PERSON><PERSON>", "wifiAuthenticationEstablish": "<PERSON><PERSON> verb<PERSON> met het apparaat", "wifiAuthenticationEstablishLong": "Apparaat maakt verbinding met WiFi {ssid}", "wifiAuthenticationFailed": "Verbinding mislukt. Koppel het apparaat een paar seconden los van de stroom en probeer het opnieuw.", "wifiAuthenticationReset": "Authenticatie resetten", "wifiAuthenticationResetHint": "Alle authenticatiegegevens worden verwijderd.", "wifiAuthenticationInvaliddata": "Authenticatiegegevens ongeldig", "wifiAuthenticationReauthenticate": "Opnieuw authenticeren", "wifiAddhkdeviceHeader": "Apparaat toevoegen", "wifiAddhkdeviceDescription": "Verbind je nieuwe ELTAKO apparaat met je WiFi-netwerk via de Apple Home app.", "wifiAddhkdeviceStep1": "Open de Apple Home app.", "wifiAddhkdeviceStep2": "Klik op het plusje in de rechterbovenhoek van de app en selecteer **Apparaat toevoegen**.", "wifiAddhkdeviceStep3": "Volg de instructies van de app.", "wifiAddhkdeviceStep4": "4. Nu kan uw apparaat worden geconfigureerd in de ELTAKO-connect app.", "detailsConfigurationRuntime": "Looptijd", "detailsConfigurationRuntimeMode": "Modus", "generalTextManually": "<PERSON><PERSON><PERSON>", "detailsConfigurationRuntimeAutoDescription": "De zonweringsactor bepaalt zelfstandig de looptijd bij elke beweging van de onderste naar de bovenste eindstand (aanbevolen).\nNa de inbedrijfstelling moet de zonwering zonder onderbreking bewegen van beneden naar boven.", "detailsConfigurationRuntimeManuallyDescription": "De looptij<PERSON> van de zonweringsmotor wordt handmatig ingesteld met be<PERSON><PERSON> van onderstaande tijdsduur.\nZorg ervoor dat de ingestelde looptijd overeenkomt met de daadwerkelijke looptijd van uw zonweringsmotor.\n<PERSON> inbedrijfstelling of wijzigingen moet de zonwering van onder naar boven lopen zonder onderbreking.", "detailsConfigurationRuntimeDemoDescription": "LCD-display-modus is alleen beschik<PERSON>ar via REST API.", "generalTextDemomodeActive": "Demo-modus actief", "detailsConfigurationRuntimeDuration": "<PERSON><PERSON>", "detailsConfigurationSwitchesGs4": "Groepenschakelaar met tip-omkeerfunctie (GS4)", "detailsConfigurationSwitchesGs4Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>kelaar met tip-omkeerfunctie voor het aansturen van jaloezieën", "screenshotSu12": "Tuinverlichting", "screenshotS2U12": "Tuinverlichting", "screenshotMfz12": "<PERSON><PERSON>", "screenshotEsr62": "<PERSON><PERSON>", "screenshotEud62": "Plafondlamp", "screenshotEsb62": "<PERSON><PERSON><PERSON> balkon", "detailsConfigurationEdgemodeLeadingedgeDescription": "LC1-LC3 zijn comfort-posities met verschillende dimcurves voor dimbare 230 V-LED-lampen, die op grond van hun ontwerp niet ver genoeg kunnen worden gedimd op AUTO en daarom moeten worden gedwongen tot faseaansnijding.", "detailsConfigurationEdgemodeAutoDescription": "Met AUTO kunnen alle soorten lampen worden gedimd.", "detailsConfigurationEdgemodeTrailingedge": "Faseafsnijding", "detailsConfigurationEdgemodeTrailingedgeDescription": "LC4-LC6 zijn Comfort-posities met verschillende dimcurves voor dimbare 230 V-LED-lampen.", "updateHeader": "Firmware update", "updateTitleStepSearch": "Er wordt gezocht naar een update", "updateTitleStepFound": "Er is een update gevonden", "updateTitleStepDownload": "Update wordt gedownload", "updateTitleStepInstall": "Update wordt geïnstalleerd", "updateTitleStepSuccess": "Update succesvol", "updateTitleStepUptodate": "Al bijgewerkt", "updateTitleStepFailed": "Update mislukt", "updateButtonSearch": "Op zoek naar updates", "updateButtonInstall": "Update installeren", "updateCurrentversion": "Huidige versie", "updateNewversion": "Nieuwe firmware update beschikbaar", "updateHintPower": "De update start pas als de uitgang van het apparaat niet actief is. Het apparaat mag tijdens de update niet van de stroomvoorziening worden losgekoppeld en de app mag niet worden afgesloten!", "updateButton": "Update", "updateHintCompatibility": "Een update wordt aanbevolen, anders zijn sommige functies in de app slechts beperkt beschikbaar.", "generalTextDetails": "Details", "updateMessageStepMetadata": "<PERSON><PERSON> van update informatie", "updateMessageStepPrepare": "Update wordt voorbereid", "updateTitleStepUpdatesuccessful": "Update wordt gecontroleerd", "updateTextStepFailed": "<PERSON><PERSON><PERSON> is er iets misgegaan tijdens de update, probeer het over een paar minuten opnieuw of wacht tot uw apparaat automatisch wordt bijgewerkt (internetverbinding vereist).", "configurationsNotavailable": "<PERSON>r zijn nog geen configuraties beschikbaar.", "configurationsAddHint": "<PERSON><PERSON> nieuwe configuraties aan, door een verbinding te maken met het apparaat en een configuratie op te slaan.", "@configurationsAddHint": {"description": "Now available for all devices not only Bluetooth", "type": "text"}, "configurationsEdit": "Configuratie bewerken", "generalTextName": "<PERSON><PERSON>", "configurationsDelete": "Configuratie verwi<PERSON>en", "configurationsDeleteHint": "<PERSON><PERSON> de configuratie: {configName} echt worden verwijderd?", "configurationsSave": "Configurat<PERSON>", "configurationsSaveHint": "Hier kunt u de configuratie van uw huidige apparaat opslaan, of een reeds opgeslagen configuratie laden.", "configurationsImport": "Configuratie <PERSON>eren", "configurationsImportHint": "Moet de configuratie {configName} echt worden overgedragen?", "generalTextConfigurations": "{count, plural, one {configuratie} other {configuraties}}", "configurationsStepPrepare": "Configuratie wordt voorbereid", "configurationsStepName": "Voer een naam in voor de configuratie", "configurationsStepSaving": "Configuratie wordt opgeslagen", "configurationsStepSavedsuccessfully": "Configuratie is succesvol opgeslagen", "configurationsStepSavingfailed": "<PERSON><PERSON><PERSON> de configuratie is mislukt", "configurationsStepChoose": "Selecteer een configuratie", "configurationsStepImporting": "Configuratie wordt geïmporteerd", "configurationsStepImportedsuccessfully": "De configuratie is met succes geïmporteerd", "configurationsStepImportingfailed": "Importeren configuratie mislukt", "discoveryAssuDescription": "Buitentussenstekker tijdschakelklok Bluetooth 230V", "settingsDatetimeDevicetime": "<PERSON><PERSON><PERSON> apparaattijd", "settingsDatetimeLoading": "Tijdinstellingen worden geladen", "discoveryEud12Description": "Universele dimmer Bluetooth", "generalTextOffdelay": "Uitschakelvertraging", "generalTextRemainingbrightness": "resterende lichtsterkte", "generalTextSwitchonvalue": "Inschakelwaarde", "motionsensorTitleNoremainingbrightness": "Geen resterende lichtsterkte", "motionsensorTitleAlwaysremainingbrightness": "Met resterende lichtsterkte", "motionsensorTitleRemainingbrightnesswithprogram": "Resterende lichtsterkte via schakelprogramma", "motionsensorTitleRemainingbrightnesswithprogramandzea": "Resterende lichtsterkte via CA (centraal aan) en CU (centraal uit)", "motionsensorTitleNoremainingbrightnessauto": "Geen resterende lichtsterkte (halfautomatisch)", "generalTextMotionsensor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextLightclock": "<PERSON>cht<PERSON><PERSON>", "generalTextSnoozeclock": "Sluimerfunctie", "generalDescriptionLightclock": "Bij het inschakelen ({mode}) wordt het licht na ongeveer 1 seconde op de laagste dimstand ingeschakeld en langza<PERSON> omhoog gedimd, zonder het laatst opgeslagen lichtniveau te wijzigen.", "generalDescriptionSnoozeclock": "Bij het uitschakelen ({mode}) wordt de verlichting vanuit de huidige dimstand naar de minimale lichtsterkte gedimd en uitgeschakeld. De verlichting kan op elk moment tijdens het dimproces worden uitgeschakeld door de pulsdrukker kort in te drukken. Als je de pulsdrukker lang indrukt tijdens het dimproces, wordt de verlichting omhoog gedimd en wordt de sluimerfunctie beëindigd.", "generalTextImmediately": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextPercentage": "Percentage", "generalTextSwitchoffprewarning": "Uitschakel voorwaarschuwing", "generalDescriptionSwitchoffprewarning": "Langzaam dimmen tot minimale lichtsterkte", "generalDescriptionOffdelay": "Het apparaat schakelt in als de stuurspanning wordt aangelegd. Als de stuurspanning wordt onderbroken, begint de tijdsverloop, daarna schakelt het apparaat weer uit. Gedurende de tijdsverloop kan het apparaat worden ingeschakeld.\n", "generalDescriptionBrightness": "De lichtsterkte waarbij de lamp wordt ingeschakeld door de dimmer.", "generalDescriptionRemainingbrightness": "De dimwaarde in procenten tot waar de lamp naar gedimd wordt nadat de bewegingsmelder is uitgeschakeld.", "generalDescriptionRuntime": "<PERSON><PERSON><PERSON><PERSON> van de lichtwekkerfunctie van minimale lichtsterkte tot maximale lichtsterkte.", "generalTextUniversalbutton": "<PERSON><PERSON> pu<PERSON>", "generalTextDirectionalbutton": "Richtingspulsdrukker", "eud12DescriptionAuto": "Automatische detectie UP/RP (met richtingsdiode RTD)", "eud12DescriptionRt": "met richtingsgevoelige diode RTD", "generalTextProgram": "Programma", "eud12MotionsensorOff": "Met bewegingsmelder ingesteld op uit", "eud12ClockmodeTitleProgramze": "Programma en centraal aan", "eud12ClockmodeTitleProgramza": "Programma en centraal uit", "eud12ClockmodeTitleProgrambuttonon": "Programma en UP/RP aan", "eud12ClockmodeTitleProgrambuttonoff": "Programma en UP/RP uit", "eud12TiImpulseTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (t1)", "eud12TiImpulseHeader": "<PERSON>m<PERSON><PERSON><PERSON> impulstijd aan", "eud12TiImpulseDescription": "De dimwaarde in percentage, waarnaar de lamp gedimd wordt als de impulstijd AAN is.", "eud12TiOffTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uit (t2)", "eud12TiOffHeader": "Dimwaarde Impulstijd uit", "eud12TiOffDescription": "<PERSON> dimwa<PERSON>e in percentage waarnaar de lamp wordt gedimd als de impulstijd UIT is.", "generalTextButtonpermanentlight": "Permanent lichtpulsdrukker", "generalDescriptionButtonpermanentlight": "Instelling van het continu lichtpulsdrukker van 0 tot 10 uur in stappen van 0,5 uur. Activering door de pulksdrukker langer dan 1 seconde in te drukken (1x knipperen), deactivering door de pulsdrukker langer dan 2 seconden in te drukken.", "generalTextNobuttonpermanentlight": "<PERSON><PERSON>", "generalTextBasicsettings": "Basisinstellingen", "generalTextInputswitch": "Ingang lokale pulsdrukker (A1)", "generalTextOperationmode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generalTextDimvalue": "Inschakel gedrag", "eud12TitleUsememory": "Geheugenwaarde gebruiken", "eud12DescriptionUsememory": "De geheugenwaarde komt overeen met de laatst ingestelde dimwaarde. Als de geheugenfunctie is gede<PERSON><PERSON>, wordt de dimmer altijd ingeschakeld op de inschakelwaarde.", "generalTextStartup": "Lichtsterkte inschakelen", "generalDescriptionSwitchonvalue": "De inschakelwaarde is een instelbare lichtsterkte waarde die een veilige inschakeling garandeert.", "generalTitleSwitchontime": "Inschakeltijd", "generalDescriptionSwitchontime": "Nadat de inschakeltijd is verstrek<PERSON>, wordt de lamp van de inschakelwaarde gedimd naar de geheugenwaarde.", "generalDescriptionStartup": "Sommige LED lampen hebben een hogere inschakelstroom nodig om betrouwbaar in te schakelen. De lamp wordt bij deze inschakelwaarde ingeschakeld en na de inschakeltijd gedimd naar de geheugenwaarde.", "eud12ClockmodeSubtitleProgramze": "Korte klik op centraal aan", "eud12ClockmodeSubtitleProgramza": "Korte klik op centraal uit", "eud12ClockmodeSubtitleProgrambuttonon": "Dubbelklik op universele pulsdrukker/richtingspulsdrukker aan", "eud12ClockmodeSubtitleProgrambuttonoff": "Dubbelklik op universele pulsdrukker/richtingspulsdrukker uit", "eud12FunctionStairlighttimeswitchTitleShort": "TLZ | Trappenhuisverlichting timer", "eud12FunctionMinTitleShort": "MIN", "eud12FunctionMmxTitleShort": "MMX", "eud12FunctionTiDescription": "Timer met ins<PERSON><PERSON><PERSON> in- en uitschakeltijd van 0,5 seconden tot 9,9 minuten. De lichtsterkte kan worden ingesteld van minimale licht- tot maximale lichtsterkte.", "eud12FunctionAutoDescription": "<PERSON><PERSON> <PERSON>mer met instelling voor bewegingsm<PERSON>er, lichtwekker en sluimerfunctie", "eud12FunctionErDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, de lichtsterkte kan worden ingesteld van minimale licht- tot maximale lichtsterkte.", "eud12FunctionEsvDescription": "<PERSON><PERSON> dimmer met instelling van een uitschakelvertraging van 1 tot 120 minuten. Uitschakelvoorwaarschuwing op het einde van omlaag dimmen instelbaar van 1 tot 3 minuten. Beide centrale ingangen actief.", "eud12FunctionTlzDescription": "Instellen van de verlichtingsduur van 0 tot 10 uur in stappen van 0,5 uur. Activering door de pulsdrukker  langer dan 1 seconde in te drukken (1x knipperen), deactivering door de knop langer dan 2 seconden in te drukken.", "eud12FunctionMinDescription": "<PERSON><PERSON> dimmer, dimt naar de ingestelde minimale lichtsterkte wanneer de stuurspanning wordt aangesloten. Het licht wordt omhoog gedimd tot de maximale lichtsterkte binnen de ingestelde dimtijd van 1 tot 120 minuten. Als de stuurspanning wordt verwijderd, wordt het licht onmiddellijk uitgeschakeld, zelfs tijdens de dimtijd. Beide centrale ingangen actief.", "eud12FunctionMmxDescription": "<PERSON><PERSON> dimmer, dimt naar de ingestelde minimale lichtsterkte wanneer de stuurspanning wordt aangesloten. Tijdens de ingestelde dimtijd van 1 tot 120 minuten wordt het licht gedimd tot de maximale lichtsterkte. Wan<PERSON> de stuurspanning echter wordt verwijderd, dimt de dimmer tot de ingestelde minimale lichtsterkte. Daarna wordt uitgeschakeld. Beide centrale ingangen actief.", "motionsensorSubtitleNoremainingbrightness": "Met bewegingsmelder ingesteld op uit", "motionsensorSubtitleAlwaysremainingbrightness": "Met bewegingsmelder ingesteld op uit", "motionsensorSubtitleRemainingbrightnesswithprogram": "Schakelprogramma geactiveerd en gedeactiveerd met BWM-uit", "motionsensorSubtitleRemainingbrightnesswithprogramandzea": "Centraal aan <PERSON>ert de bewegingsmelder, centraal uit deactiveert de bewegingsmelder, zoa<PERSON> ook met het scha<PERSON>programma", "motionsensorSubtitleNoremainingbrightnessauto": "Bewegingsmelder schakelt alleen uit", "detailsDimsectionHeader": "<PERSON><PERSON><PERSON>", "generalTextFast": "Snel", "generalTextSlow": "<PERSON><PERSON><PERSON>", "eud12TextDimspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eud12TextSwitchonspeed": "Inschakelsnelheid", "eud12TextSwitchoffspeed": "Uitschakelsnelheid", "eud12DescriptionDimspeed": "De dimsnelheid is de snelheid waarmee de dimmer dimt van de huidige licht- naar de gewenste lichtsterkte.", "eud12DescriptionSwitchonspeed": "De inschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig in te schakelen.", "eud12DescriptionSwitchoffspeed": "De uitschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig uit te schakelen.", "settingsFactoryresetResetdimHeader": "Diminstellingen resetten", "settingsFactoryresetResetdimDescription": "Moeten alle diminstellingen echt worden gereset?", "settingsFactoryresetResetdimConfirmationDescription": "De diminstellingen zijn succesvol gereset", "eud12TextSwitchonoffspeed": "Aan/uit-snelheid", "eud12DescriptionSwitchonoffspeed": "De in-/uitschakelsnelheid is de snelheid die de dimmer nodig heeft om volledig in of uit te schakelen.", "timerDetailsDimtoval": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON> in %", "timerDetailsDimtovalDescription": "De dimmer schakelt altijd in met de vaste dimwaarde in %.", "timerDetailsDimtovalSubtitle": "Inschakelen met {brightness}%", "timerDetailsDimtomem": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timerDetailsDimtomemSubtitle": "Inschakelen met geheugenwaarde", "timerDetailsMotionsensorwithremainingbrightness": "Resterende lichtsterkte (BWM) aan", "timerDetailsMotionsensornoremainingbrightness": "Resterende lichtsterkte (BWM) uit", "settingsRandommodeHint": "Als de willekeurige modus is ingeschakeld, worden alle schakeltijden van het kanaal willekeurig verschoven. Met inschakeltijden tot 15 minuten eerder en uitschakeltijden tot 15 minuten later.", "runtimeOffsetDescription": "Extra naloop na het verstrijken van de looptijd. <PERSON><PERSON><PERSON> kan ervoor worden gezorgd dat de eindpositie wordt bereikt.", "loadingTextDimvalue": "Dimwaarde is geladen", "discoveryEudipmDescription": "Universele dimmer IP Matter", "generalTextOffset": "<PERSON><PERSON>", "eud12DimvalueTestText": "Lichtsterkte verzenden", "eud12DimvalueTestDescription": "Tijdens het testen wordt er rekening gehouden met de huidige ingestelde dimsnelheid.", "eud12DimvalueLoadText": "Lichtsterkte laden", "settingsDatetimeNotime": "De datum- en tijdinstellingen moeten worden uitgelezen via het display van het apparaat.", "generalMatterText": "Matter", "generalMatterMessage": "<PERSON><PERSON> uw Matter-apparaat in met een van de volgende apps.", "generalMatterOpengooglehome": "Google Home openen", "generalMatterOpenamazonalexa": "Open Amazon Alexa", "generalMatterOpensmartthings": "Open SmartThings", "generalLabelProgram": "Programma {number}", "generalTextDone": "<PERSON><PERSON><PERSON>", "settingsRandommodeDescriptionShort": "Als de willekeurige modus is ingeschakeld, worden alle schakeltijden van het kanaal willekeurig verschoven. Met inschakeltijden tot 15 minuten eerder en uitschakeltijden tot 15 minuten later.", "all": "Alle", "discoveryBluetooth": "Bluetooth", "success": "Succesvol", "error": "Fout", "timeProgramAdd": "Tijdprogramma <PERSON>", "noConnection": "<PERSON><PERSON> verbinding", "timeProgramOnlyActive": "Geconfigureerde programma's", "timeProgramAll": "Alle programma's", "active": "Actief", "inactive": "Inactief", "timeProgramSaved": "Programma {number} opgeslagen", "deviceLanguageSaved": "Apparaattaal opgeslagen", "generalTextTimeShort": "{time} klok", "programDeleteHint": "Moet programma {index} echt worden verwijderd?", "milliseconds": "{count, plural, one {milliseconde} other {milliseconden}}", "millisecondsWithValue": "{count, plural, one {{count} milliseconde} other {{count} milliseconden}}", "secondsWithValue": "{count, plural, one {{count} seconde} other {{count} seconden}}", "minutesWithValue": "{count, plural, one {{count} minuut} other {{count} minuten}}", "hoursWithValue": "{count, plural, one {{count} uur} other {{count} uren}}", "settingsPinFailEmpty": "De pincode mag niet leeg zijn.", "detailsConfigurationWifiloginScanNoMatch": "De gescande code komt niet overeen met het apparaat", "wifiAuthorizationPopIsEmpty": "PoP mag niet leeg zijn", "wifiAuthenticationCredentialsHint": "Aangezien de app geen toegang heeft tot je privé Wi-Fi-wachtwoord, is het niet mogelijk om de juistheid van de invoer te controleren. Als er geen verbinding tot stand komt, controleer dan het wachtwoord en voer het opnieuw in.", "generalMatterOpenApplehome": "Open Apple Home", "timeProgramNoActive": "<PERSON><PERSON> gecon<PERSON>de programma's", "timeProgramNoEmpty": "Geen lege tijdsprogramma be<PERSON>ar", "nameOfConfiguration": "<PERSON><PERSON> configuratie", "currentDevice": "<PERSON><PERSON><PERSON> apparaat", "export": "Exporteren", "import": "Importeren", "savedConfigurations": "Opgeslagen configuraties", "importableServicesLabel": "De volgende instellingen kunnen worden geïmporteerd:", "notImportableServicesLabel": "Incompatibele instellingen:", "deviceCategoryMeterGateway": "Meter-Gateway", "deviceCategory2ChannelTimeSwitch": "2-kana<PERSON> schakelklok Bluetooth", "devicategoryOutdoorTimeSwitchBluetooth": "<PERSON><PERSON><PERSON> tuss<PERSON>kker-tijdschakelaar Bluetooth", "settingsModbusHeader": "Modbus", "settingsModbusDescription": "<PERSON><PERSON> <PERSON> b<PERSON>, pariteit en time-out aan om de transmissiesnelheid, foutdetectie en wachttijd te configureren.", "settingsModbusRTU": "Modbus RTU", "settingsModbusBaudrate": "Baudrate", "settingsModbusParity": "Pariteit", "settingsModbusTimeout": "Modbus time-out", "locationServiceDisabled": "Locatie is uitgeschakeld", "locationPermissionDenied": "<PERSON><PERSON> locatietoestemming om uw huidige positie op te halen.", "locationPermissionDeniedPermanently": "Locatietoestemmingen zijn permanent geweigerd, sta de locatietoestemming toe in de instellingen van je apparaat om je huidige positie op te vragen.", "lastSync": "Laatste synchronisatie", "dhcpActive": "DHCP actief", "ipAddress": "IP", "subnetMask": "Subnetmasker", "standardGateway": "Standaard gateway", "dns": "DNS", "alternateDNS": "Alternatief DNS", "errorNoNetworksFound": "Geen W<PERSON>i-netwerk gevonden", "availableNetworks": "Beschikbare netwerken", "enableWifiInterface": "WiFi-interface inschakelen", "enableLANInterface": "LAN-interface inschakelen", "hintDontDisableAllInterfaces": "Zorg ervoor dat niet alle interfaces uitgeschakeld zijn. De laatst geactiveerde interface heeft prioriteit.", "ssid": "SSID", "searchNetworks": "WiFi-Netwerken zoeken", "errorNoNetworkEnabled": "Er moet minstens één interface actief zijn", "errorActiveNetworkInvalid": "<PERSON>et alle actieve interfaces zijn geldig", "invalidNetworkConfiguration": "Ongeldige netwerkconfiguratie", "generalDefault": "Standaard", "mqttHeader": "MQTT", "mqttConnected": "<PERSON><PERSON><PERSON><PERSON> met MQTT-broker", "mqttDisconnected": "<PERSON><PERSON> met MQTT-broker", "mqttBrokerURI": "Broker <PERSON>", "mqttBrokerURIHint": "MQTT-broker URI", "mqttPort": "Poort", "mqttPortHint": "MQTT-poort", "mqttClientId": "Client-ID", "mqttClientIdHint": "MQTT client-ID", "mqttUsername": "Gebruikersnaam", "mqttUsernameHint": "MQTT-gebruikersnaam", "mqttPassword": "Wachtwoord", "mqttPasswordHint": "MQTT-wachtwoord", "mqttCertificate": "Certificaat (optioneel)", "mqttCertificateHint": "MQTT-certificaat", "mqttTopic": "Topic", "mqttTopicHint": "MQTT-topic", "electricityMeter": "Elektriciteitsmeter", "electricityMeterCurrent": "<PERSON><PERSON><PERSON>", "electricityMeterHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "electricityMeterReading": "Meterstand", "connectivity": "Connectiviteit", "electricMeter": "{count, plural, one {Elektriciteitsmeter} other {Elektriciteitsmeters}}", "discoveryZGW16Description": "Modbus-elektriciteitsmeter-MQTT-gateway", "bluetoothConnectionLost": "Bluetooth verbinding verloren", "bluetoothConnectionLostDescription": "De Bluetooth verbinding met het apparaat is onderbroken. <PERSON>org ervoor dat het apparaat binnen bereik is.", "openBluetoothSettings": "Open instellingen", "password": "Wachtwoord", "setInitialPassword": "Initieel wachtwoord gegeven", "initialPasswordMinimumLength": "Het wachtwoord moet minimaal {length} tekens lang zijn.", "repeatPassword": "<PERSON><PERSON><PERSON> wa<PERSON>", "passwordsDoNotMatch": "Wachtwoorden komen niet overeen", "savePassword": "Wachtwoord opslaan", "savePasswordHint": "Het wachtwoord wordt opgeslagen voor toekomstige verbindingen op uw apparaat.", "retrieveNtpServer": "<PERSON>i<PERSON><PERSON> van een NTP-server", "retrieveNtpServerFailed": "<PERSON> met de NTP-server kon niet tot stand worden gebracht.", "retrieveNtpServerSuccess": "<PERSON> verbinding met de NTP-server was succesvol.", "settingsPasswordNewPasswordDescription": "<PERSON><PERSON><PERSON> wachtwoord invoeren", "settingsPasswordConfirmationDescription": "Wachtwoord succesvol gewijzigd", "dhcpRangeStart": "Start DHCP-bereik", "dhcpRangeEnd": "Einde DHCP-bereik", "forwardOnMQTT": "Doorsturen naar MQTT", "showAll": "<PERSON>es weergeven", "hide": "Ver<PERSON>", "changeToAPMode": "Overschakelen naar AP-modus", "changeToAPModeDescription": "U staat op het punt uw apparaat te verbinden met een WiFi-netwerk. In dit geval wordt de verbinding met het apparaat verbroken en moet u opnieuw verbinding maken met uw apparaat via het geconfigureerde netwerk.", "consumption": "Verbruik", "currentDay": "<PERSON>dige dag", "twoWeeks": "2 weken", "oneYear": "1 jaar", "threeYears": "3 jaren", "passwordMinLength": "Het wachtwoord moet minimaal {length} tekens lang zijn.", "passwordNeedsLetter": "Het wachtwoord moet minimaal één letter bevatten.", "passwordNeedsNumber": "Het wachtwoord moet minimaal één cijfer bevatten.", "portEmpty": "<PERSON>t mag niet leeg zijn", "portInvalid": "Ongeldige poort", "portOutOfRange": "De poort moet tussen {rangeStart} en {rangeEnd} liggen.", "ipAddressEmpty": "IP-adres mag niet leeg zijn", "ipAddressInvalid": "Ongeldig IP-adres", "subnetMaskEmpty": "Subnetmasker mag niet leeg zijn", "subnetMaskInvalid": "Ongeldig subnetmasker", "gatewayEmpty": "Gateway mag niet leeg zijn", "gatewayInvalid": "Ongeldige gateway", "dnsEmpty": "DNS mag niet leeg zijn", "dnsInvalid": "Ongeldig DNS", "uriEmpty": "URI mag niet leeg zijn", "uriInvalid": "Ongeldige URI", "electricityMeterChangedSuccessfully": "De elektriciteitsmeter is met succes gewijzigd.", "networkChangedSuccessfully": "De netwerkconfiguratie is succesvol gewijzigd.", "mqttChangedSuccessfully": "De MQTT-configuratie is succesvol gewij<PERSON>d.", "modbusChangedSuccessfully": "Modbus-instellingen zijn succesvol gewijzigd.", "loginData": "Inloggegevens verwijderen", "valueConfigured": "Geconfigureerd", "electricityMeterHistoryNoData": "<PERSON><PERSON> g<PERSON><PERSON>", "locationChangedSuccessfully": "Lo<PERSON>ie is succesvol gewijzigd.", "settingsNameFailEmpty": "<PERSON>am mag niet leeg zijn.", "settingsNameFailLength": "De naam mag niet langer zijn dan {length} tekens.", "solsticeChangedSuccesfully": "De tijdsverschuiving voor de zonnewende is met succes gewijzigd.", "relayFunctionChangedSuccesfully": "De relaisfunctie is succesvol gewijzigd.", "relayFunctionHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dimmerValueChangedSuccesfully": "Het inschakelgedrag is succesvol gewijzigd.", "dimmerBehaviourChangedSuccesfully": "Het dim<PERSON> is succesvol gewijzigd.", "dimmerBrightnessDescription": "De minimale en maximale lichtsterkte zijn van invloed op alle instelbare helderheidsniveaus van de dimmer.", "dimmerSettingsChangedSuccesfully": "De basisinstellingen zijn succesvol gewijzigd.", "liveUpdateEnabled": "Live test geactiveerd", "liveUpdateDisabled": "Live test gedeactiveerd", "liveUpdateDescription": "De laatst gewijzigde slider-waarde wordt naar het apparaat verzonden.", "demoDevices": "Demo apparaten", "showDemoDevices": "Demo apparaten weergeven", "deviceCategoryTimeSwitch": "Tijdschakelklok", "deviceCategoryMultifunctionalRelay": "Multifunctioneel-t<PERSON><PERSON><PERSON><PERSON><PERSON>", "deviceCategoryDimmer": "<PERSON><PERSON>", "deviceCategoryShutter": "Rolluik- en zonweringactor", "deviceCategoryRelay": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "configurationsHeader": "Configuraties", "configurationsDescription": "Hier kunt u uw opgeslagen configuraties beheren.", "configurationsNameFailEmpty": "Configuratienaam mag niet leeg zijn.", "configurationDeleted": "Configuratie ver<PERSON>", "codeFound": "{codeType} Code herkent", "errorCameraPermission": "<PERSON><PERSON> toegang tot de camera om de ELTAKO-code te scannen.", "authorizationSuccessful": "Succesvol geautoriseerd op het apparaat", "wifiAuthenticationResetConfirmationDescription": "Authenticatie is succesvol gereset.", "settingsResetConnectionHeader": "Verbinding resetten", "settingsResetConnectionDescription": "<PERSON>t de verbinding echt worden gereset?", "settingsResetConnectionConfirmationDescription": "De verbinding is succesvol gereset.", "wiredInputChangedSuccesfully": "Het puls<PERSON><PERSON><PERSON> gedrag is succesvol gewijzigd.", "runtimeChangedSuccesfully": "Looptijd is succesvol gewijzigd.", "expertModeActivated": "Expert-modus geactiveerd", "expertModeDeactivated": "Expert-modus gedeactiveerd", "license": "Licentie", "retry": "Opnieuw proberen", "provisioningConnectingHint": "Apparaatverbinding wordt tot stand gebracht. Dit kan tot 1 minuut duren.", "serialnumberEmpty": "Serienummer mag niet leeg zijn", "interfaceStateInactiveDescriptionBLE": "Bluetooth is uitgeschakeld, schakel dit in om Bluetooth-apparaten te vinden.", "interfaceStateDeniedDescriptionBLE": "Bluetooth-machtigingen zijn niet verleend.", "interfaceStatePermanentDeniedDescriptionBLE": "Bluetooth-machtigingen zijn niet verleend. Sta Bluetooth-verbindingen toe in uw apparaatinstellingen.", "requestPermission": "Toestemming vragen", "goToSettings": "Ga naar instellingen", "enableBluetooth": "Bluetooth activeren", "installed": "Geïnstalleerd", "teachInDialogDescription": "Wil je een apparaat via {type} inleren?", "useMatter": "<PERSON> gebruiken", "relayMode": "Relaismodus activeren", "whatsNew": "Nieuw in deze versie", "migrationHint": "Om ervoor te zorgen dat u de nieuwe functies kunt gebruiken, moeten we uw gegevens migreren.", "migrationHeader": "<PERSON><PERSON><PERSON>", "migrationProgress": "Wij ruimen op...", "letsGo": "Laten we beginnen", "noDevicesFound": "<PERSON><PERSON> apparaten gevonden. Controleer of uw apparaat in de verbindingsmodus staat.", "interfaceStateEmpty": "<PERSON><PERSON> apparaten gevonden", "ssidEmpty": "SSID mag niet leeg zijn", "passwordEmpty": "Wachtwoord mag niet leeg zijn", "settingsDeleteSettingsHeader": "Instellingen resetten", "settingsDeleteSettingsDescription": "Wil je echt alle instellingen resetten?", "settingsDeleteSettingsConfirmationDescription": "Alle instellingen zijn succesvol gereset.", "locationNotFound": "<PERSON><PERSON>ie niet gevonden", "timerProgramEmptySaveHint": "Het tijdprogramma is leeg en kan niet worden opgeslagen. Bewerken beeindigen?", "timerProgramDaysEmptySaveHint": "<PERSON><PERSON> dagen geselecteerd. Het tijdprogramma toch opslaan?", "timeProgramNoDays": "Een programma zonder actieve dagen kan niet worden geactiveerd.", "timeProgramColliding": "Tijdprogramma botst met programma {program}.", "timeProgramDuplicated": "Het tijdprogramma is een duplicaat van programma {program}.", "screenshotZgw16": "<PERSON><PERSON><PERSON><PERSON><PERSON> huis", "interfaceStateUnknown": "<PERSON><PERSON> apparaten gevonden", "settingsPinChange": "PIN veranderen", "timeProgrammOneTime": "<PERSON><PERSON><PERSON><PERSON>", "timeProgrammRepeating": "Her<PERSON>nd", "generalIgnore": "Negeren", "timeProgramChooseDay": "<PERSON>g kiezen", "generalToday": "Vandaag", "generalTomorrow": "<PERSON><PERSON>", "bluetoothAndPINChangedSuccessfully": "Bluetooth en pincode zijn succesvol gewijzigd.", "generalTextDimTime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "discoverySu62Description": "1-kana<PERSON> tijdschakelklok Bluetooth", "bluetoothAlwaysOnTitle": "Permanent aan", "bluetoothAlwaysOnDescription": "Bluetooth is permanent ingeschakeld.", "bluetoothAlwaysOnHint": "Opmerking: Al<PERSON> deze instelling geactiveerd is, is het apparaat permanent zichtba<PERSON> voor iedereen via Bluetooth! Het wordt aanbevolen om de standaardpincode te wijzigen.", "bluetoothManualStartupOnTitle": "Tijdelijk-aan", "bluetoothManualStartupOnDescription": "Nadat de voeding is ingeschakeld, wordt Bluetooth gedurende 3 minuten geactiveerd.", "bluetoothManualStartupOnHint": "Opmerking: Pairing-modus wordt gedurende 3 minuten geactiveerd en schakelt daarna uit. Als u een nieuwe verbinding tot stand wilt brengen, moet u de knop ongeveer 5 seconden ingedrukt houden.", "bluetoothManualStartupOffTitle": "<PERSON><PERSON><PERSON> aan", "bluetoothManualStartupOffDescription": "Bluetooth wordt handmatig geactiveerd via de pulsdrukkeringang en is dan voor 3 minuten actief.", "bluetoothManualStartupOffHint": "Opmerking: <PERSON><PERSON> te activeren, moet de pulsdrukker op de pulsdrukkeringang ongeveer 5 seconden bediend worden.", "timeProgrammOneTimeRepeatingDescription": "Programma's kunnen herhaaldelijk worden uitgevoerd door altijd op de geconfigureerde dagen en tijden een schakelproces uit te voeren, of ze kunnen slechts één keer op het geconfigureerde schakeltijdstip worden uitgevoerd.", "versionHeader": "V<PERSON><PERSON> {version}", "releaseNotesHeader": "Uitgave notities", "release30Header": "De nieuwe Eltako Connect app is er!", "release30FeatureDesignHeader": "Nieuw ontwerp", "release30FeatureDesignDescription": "De app is volledig herzien en heeft een nieuw ontwerp. Hij is nu nog eenvoudiger en intuïtiever te gebruiken.", "release30FeaturePerformanceHeader": "Verbeterde prestaties", "release30FeaturePerformanceDescription": "Geniet van een soepelere ervaring en kortere laadtijden - voor een soepele gebruikerservaring.", "release30FeatureConfigurationHeader": "Configuraties voor meerdere apparaten", "release30FeatureConfigurationDescription": "Apparaatconfiguraties opslaan en overdragen naar andere apparaten. Zelfs als ze niet dezelfde hardware hebben, kun je bijvoorbeeld de configuratie van een S2U12DBT1+1-UC overbrengen naar een ASSU-BT of omgekeerd.", "release31Header": "De nieuwe 1-kana<PERSON> inbouwklok met Bluetooth is er!", "release31Description": "Wat kan de SU62PF-BT/UC?", "release31SU62Name": "SU62PF-BT/UC", "release31DeviceNote1": "Tot 60 schakelprogramma's.", "release31DeviceNote2": "De klok schakelt op vaste tijden of via de astrofunctie apparaten op basis van zonsopgang en zonsondergang.", "release31DeviceNote3": "Willekeurige modus: scha<PERSON><PERSON><PERSON><PERSON> kunnen will<PERSON><PERSON>ig tot 15 minuten worden verschoven.", "release31DeviceNote4": "Omschakeling zomer-/wintertijd: De klok schakelt automatisch naar zomer- of wintertijd.", "release31DeviceNote5": "Universele voedings- en stuurspanning 12-230V UC.", "release31DeviceNote6": "Pulsdrukkeringang voor handmatig schakelen.", "release31DeviceNote7": "1 NO-contact potentiaalvrij 10 A/250 V AC.", "release31DeviceNote8": "Eenmalige uitvoering van tijdprogramma's.", "generalNew": "<PERSON><PERSON><PERSON>", "yearsAgo": "{count, plural, one {<PERSON><PERSON><PERSON> jaar} other {{count} jaren geleden}}", "monthsAgo": "{count, plural, one {laatste maand} other {{count} maanden geleden}}", "weeksAgo": "{count, plural, one {Laatste week} other {{count} weken geleden}}", "daysAgo": "{count, plural, one {gisteren} other {{count} dagen geleden}}", "minutesAgo": "{count, plural, one {een minuut geleden} other {{count} minuten geleden}}", "hoursAgo": "{count, plural, one {een uur geleden} other {{count} uur geleden}}", "secondsAgo": "{count, plural, one {een seconde geleden} other {{count} seconden geleden}}", "justNow": "Zojuist", "discoveryEsripmDescription": "Impuls-schakelrelais IP Matter", "generalTextKidsRoom": "Kinderkamerfunctie", "generalDescriptionKidsRoom": "Bij het inschakelen ({mode}) wordt de verlichting na ongeveer 1 seconde op het laagste helderheidsniveau ingeschakeld en langzaam hoog gedimd zolang de pulsdrukker ingedrukt wordt gehouden, zonder het laatst opgeslagen helderheidsniveau te wijzigen.", "generalTextSceneButton": "Scénepulsdrukker", "settingsEnOceanConfigHeader": "EnOcean configuratie", "enOceanConfigChangedSuccessfully": "EnOcean configuratie met succ<PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON>", "activateEnOceanRepeater": "EnOcean repeater activeren", "enOceanRepeaterLevel": "Repeater level", "enOceanRepeaterLevel1": "Level-1", "enOceanRepeaterLevel2": "Level-2", "enOceanRepeaterOffDescription": "Er worden geen draadloze signalen ontvangen van sensoren.", "enOceanRepeaterLevel1Description": "Alleen de d<PERSON>adloze signalen van sensoren worden ontvangen, gecontroleerd en doorgestuurd op vol zendvermogen. Draadloze signalen van andere repeaters worden genegeerd om de hoeveelheid gegevens te beperken.", "enOceanRepeaterLevel2Description": "Naast de draadloze signalen van sensoren worden ook de draadloze signalen van level-1 repeaters verwerkt. Een draadloos signaal kan dus maximaal twee keer ontvangen en versterkt worden. Draadloze repeaters hoeven niet ingeleerd te worden. Ze ontvangen en versterken de draadloze signalen van alle draadloze sensoren in hun ontvangstgebied.", "settingsSensorHeader": "Sensoren", "sensorChangedSuccessfully": "<PERSON><PERSON><PERSON> werden met succes veranderd.", "wiredButton": "<PERSON><PERSON> pu<PERSON>", "enOceanId": "EnOcean-ID", "enOceanAddManually": "EnOcean-ID invoeren of scannen", "enOceanIdInvalid": "Ongeldige EnOcean-ID", "enOceanAddAutomatically": "Met EnOcean-telegram inleren", "enOceanAddDescription": "<PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON> EnOcean-protocol maakt het mogelijk om pulsdrukkers in uw actor in te leren en te bedienen.\n\nKies voor automatisch inleren met EnOcean-telegram om pulsdrukkers in te leren door op een pulsdrukker te drukken of kies voor de handmatige variant om de EnOcean-<PERSON> van je pulsdrukker in te scannen of in te voeren.", "enOceanTelegram": "Telegram", "enOceanCodeScan": "<PERSON><PERSON><PERSON> de EnOcean-ID van je {sensorType} in of scan de EnOcean-QR-code van je {sensorType} om deze toe te voegen.", "enOceanCode": "EnOcean QR-code", "enOceanCodeScanDescription": "<PERSON>k naar de EnOcean QR-code op je {sensorType} en scan deze met je camera.", "enOceanButton": "EnOcean pulsdrukker", "enOceanBackpack": "EnOcean-adapter", "sensorNotAvailable": "<PERSON>r zijn nog geen sensoren ingele<PERSON>.", "sensorAdd": "Sensoren toevoegen", "sensorCancel": "Inleren afbreken", "sensorCancelDescription": "Wil je echt het inleerproces afbreken?", "getEnOceanBackpack": "<PERSON><PERSON> een EnOcean-adapter", "enOceanBackpackMissing": "Om de fantastische wereld van perfecte connectiviteit en communicatie te betreden, heb je een EnOcean-adapter nodig.\n<PERSON><PERSON> hier voor meer informatie", "sensorEditChangedSuccessfully": "{sensorName} werd met succes g<PERSON><PERSON><PERSON><PERSON>d", "sensorConnectedVia": "verbonden via {deviceName}", "lastSeen": "Laatst gezien", "setButtonOrientation": "Oriëntatie vaststellen", "setButtonType": "Type pulsdrukker instellen", "button1Way": "1-ka<PERSON><PERSON> pu<PERSON><PERSON><PERSON><PERSON>", "button2Way": "2-<PERSON><PERSON><PERSON><PERSON> puls<PERSON><PERSON><PERSON>", "button4Way": "4-<PERSON><PERSON><PERSON><PERSON> pu<PERSON><PERSON><PERSON><PERSON>", "buttonUnset": "niet ingesteld", "button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor": "Sensor", "sensorsFound": "{count, plural, =0 {geen sensoren gevonden} one {1 sensor gevonden} other {{count} sensoren gevonden}}", "sensorSearch": "<PERSON>ar <PERSON>en zoeken", "searchAgain": "Opnieuw zoeken", "sensorTeachInHeader": "{sensorType} inleren", "sensorChooseHeader": "{sensorType} kiezen", "sensorChooseDescription": "Kies een pulsdrukker die je wilt inleren", "sensorCategoryDescription": "Selecteer de categorie van de sensor die je wilt inleren.", "sensorName": "P<PERSON>sdruk<PERSON><PERSON><PERSON>", "sensorNameFooter": "<PERSON><PERSON> je pulsdrukker een naam", "sensorAddedSuccessfully": "{sensorName} is met succes ingeleerd", "sensorDelete": "{sensorType} verwijderen", "sensorDeleteHint": "Wil je echt de {sensorType} {sensorName} verwijderen?", "sensorDeletedSuccessfully": "{sensorName} is met succes verwijderd", "buttonTapDescription": "<PERSON><PERSON> de pulsdrukker die je wilt toevoegen.", "waitingForTelegram": "De actor wacht op het telegram", "copied": "Gekopieerd", "pairingFailed": "{sensorType} al ingeleerd", "generalDescriptionUniversalbutton": "Met de universele pulsdrukker wordt de richting omgekeerd door de pulsdrukker kort los te laten. Korte pulsen schakelen aan of uit.", "generalDescriptionDirectionalbutton": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is boven 'inschakelen en omhoog dimmen' en onderaan 'uitzetten en omlaag dimmen'.", "matterForwardingDescription": "De toetsaanslag wordt aan Matter doorgestuurd.", "none": "<PERSON><PERSON>", "buttonNoneDescription": "Deze pulsdrukker heeft geen functionaliteit", "buttonUnsetDescription": "Er is geen functie aan de pulsdrukker toegewezen.", "sensorButtonTypeChangedSuccessfully": "Pulsdrukker-type is met succes gewijzigd", "forExample": "bijv. {example}}", "enOceanQRCodeInvalidDescription": "Alleen mogelijk vanaf productiedatum 44/20", "input": "<PERSON>gang", "buttonSceneValueOverride": "Opgeslagen scènepuldrukker overschrijven", "buttonSceneValueOverrideDescription": "De scènepulsdrukker wordt overschreven met de huidige dimwaarde als de pulsdrukker lang wordt ingedrukt.", "buttonSceneDescription": "<PERSON> scènek<PERSON><PERSON> schakelt aan met een vaste dimwa<PERSON>e", "buttonPress": "Pulsdrukker ingedrukt", "triggerOn": "met lang bedienen bij een universele pulsdrukker of richtingspulsdrukker op de inschakelzijde", "triggerOff": "met een dubbele puls op de universele pulsdrukker of richtingspulsdrukker op de uitschakelzijde", "centralOn": "<PERSON><PERSON><PERSON><PERSON> aan", "centralOff": "Centraal uit", "centralButton": "Centrale pulsdrukker", "enOceanAdapterNotFound": "Geen EnOcean plug-in adapter gevonden", "updateRequired": "Bijwerken vereist", "updateRequiredDescription": "Je app heeft een update nodig om dit nieuwe apparaat te ondersteunen.", "release32Header": "De nieuwe 64-serie met Matter en EnOcean geïntegreerd en de nieuwe Bluetooth-inbouwklok SU62PF-BT/UC zijn nu verkrijgbaar!", "release32EUD64Header": "De nieuwe 1-kana<PERSON> inbouwdimmer met Matter via Wi-Fi en tot 300W is er!", "release32EUD64Note1": "Configuratie van <PERSON>, aan/uit-snelheid, kinder<PERSON><PERSON>/sluimerfunctie en nog veel meer.", "release32EUD64Note2": "De functionaliteit van de EUD64NPN-IPM kan worden uitgebreid met adapters, zoals bijv. de EnOcean plug-in adapter EOA64.", "release32EUD64Note3": "Tot 30 draadloze EnOcean pulsdrukkers kunnen direct worden gekoppeld aan de EUD64NPN-IPM in combinatie met de EnOcean plug-in adapter EOA64 en worden doorgestuurd naar Matter.", "release32EUD64Note4": "Twee bedrade pulsdrukkeringangen kunnen rechtstreeks worden gekoppeld aan de EUD64NPN-IPM of rechtstreeks worden doorgestuurd naar Matter.", "release32ESR64Header": "De nieuwe potentiaalvrije inbouw 1-kana<PERSON> schakelactor met Matter via Wi-Fi en tot 16A is er!", "release32ESR64Note1": "Configuratie van verschillende functies zoals imp<PERSON> (ES), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ER), <PERSON><PERSON> g<PERSON> (ER-Inverse) en nog veel meer.", "release32ESR64Note2": "De functionaliteit van de ESR64PF-IPM kan worden uitgebreid met adapters, zoals bijv. de EnOcean-adapter EOA64.", "release32ESR64Note3": "Tot 30 draadloze EnOcean-pulsdrukkers kunnen rechtstreeks worden gekoppeld aan de ESR64PF-IPM in combinatie met de EnOcean-adapter EOA64 en worden vervolgens doorgestuurd naar de Matter.", "release32ESR64Note4": "<PERSON><PERSON> bedrade pulsdrukker-ingang kan rechtstreeks worden gekoppeld aan de ESR64PF-IPM of rechtstreeks worden doorgestuurd naar Matter.", "buttonsFound": "{count, plural, =0 {geen pulsdrukkers gevonden} one {1 pulsdrukker gevonden} other {{count} pulsdrukkers gevonden}}", "doubleImpuls": "met een dubbele impuls", "impulseDescription": "Als het kanaal is ingeschakeld, wordt deze door een impuls uitgeschakeld.", "locationServiceEnable": "<PERSON><PERSON><PERSON>", "locationServiceDisabledDescription": "Locatie is uitgeschakeld. De versie van uw besturingssysteem vereist de locatie om Bluetooth-apparaten te vinden.", "locationPermissionDeniedNoPosition": "Er zijn geen locatierechten verleend. De versie van uw besturingssysteem vereist locatierechten om Bluetooth-apparaten te vinden. <PERSON><PERSON> toestemming voor locatiebepaling in de instellingen van uw apparaat.", "interfaceStatePermanentDeniedDescriptionDevicesAround": "<PERSON><PERSON> is geen toestemming verleend voor apparaten in de buurt. <PERSON><PERSON> toestemming voor apparaten in de buurt in de instellingen van uw apparaat.", "permissionNearbyDevices": "Apparaten in de buurt", "release320Header": "<PERSON>, krachtigere universele dimmer EUD12NPN-BT/600W-230V is er!", "release320EUD600Header": "Wat kan de nieuwe universele dimmer?", "release320EUD600Note1": "Universele dimmer tot 600W vermogen", "release320EUD600Note2": "Uitbreidbaar met vermogensuitbreiding LUD12 tot 3800W", "release320EUD600Note3": "Lokale bediening met universele of richtingspulsdrukker", "release320EUD600Note4": "Centrale functies aan / uit", "release320EUD600Note5": "Bewegingsdetector ingang voor extra comfort", "release320EUD600Note6": "Geïntegreerde astroklok met 10 schakelprogramma's", "release320EUD600Note7": "Astro-functie", "release320EUD600Note8": "Individuele inschakelhelderheid", "mqttClientCertificate": "Clientcertificaat", "mqttClientCertificateHint": "MQTT-clientcertificaat", "mqttClientKey": "Clientsleutel", "mqttClientKeyHint": "MQTT-clientsleutel", "mqttClientPassword": "Clientwachtwoord", "mqttClientPasswordHint": "MQTT-clientwachtwoord", "mqttEnableHomeAssistantDiscovery": "HomeAssistant MQTT-detectie inschakelen", "modbusTcp": "Modbus-TCP", "enableInterface": "Interface activeren", "busAddress": "Busadres", "busAddressWithAddress": "Busadres {index}", "deviceType": "Apparaattype", "registerTable": "{count, plural, one {<PERSON><PERSON><PERSON> tafel} other {<PERSON>tratie tafel}}", "currentValues": "<PERSON><PERSON><PERSON> waarden", "requestRTU": "Query-RTU", "requestPriority": "Queryprioriteit", "mqttForwarding": "MQTT-doorsturen", "historicData": "Historische gegevens", "dataFormat": "Gegevensformaat", "dataType": "Gegevenstype", "description": "Beschrijving", "readWrite": "Lezen/Schrijven", "unit": "<PERSON><PERSON><PERSON><PERSON>", "registerTableReset": "Registertabel resetten", "registerTableResetDescription": "Wilt u de registertabel echt resetten?", "notConfigured": "<PERSON><PERSON>", "release330ZGW16Header": "Uitgebreide update voor de ZGW16WL-IP", "release330Header": "De ZGW16WL-IP met maximaal 16 kWh-meters", "release330ZGW16Note1": "Ondersteuning tot maximaal 16 ELTAKO Modbus kWh-meters", "release330ZGW16Note2": "Ondersteuning voor Modbus TCP", "release330ZGW16Note3": "Ondersteuning voor MQTT-detectie", "screenshotButtonLivingRoom": "Pulsdrukker woonkamer", "registerChangedSuccessfully": "Het register is succesvol gewijzigd.", "serverCertificateEmpty": "Het servercertificaat mag niet leeg zijn.", "registerTemplates": "<PERSON> sjablonen", "registerTemplateChangedSuccessfully": "<PERSON><PERSON><PERSON> is succesvol gewijzigd.", "registerTemplateReset": "Register sjabloon resetten", "registerTemplateResetDescription": "Wilt u het <PERSON><PERSON><PERSON><PERSON> echt resetten?", "registerTemplateNotAvailable": "<PERSON><PERSON>", "rename": "<PERSON><PERSON><PERSON><PERSON>", "registerName": "Register naam", "registerRenameDescription": "<PERSON>f het register een aangepaste naam", "restart": "Apparaat opnieuw opstarten", "restartDescription": "Wilt u het apparaat echt opnieuw opstarten?", "restartConfirmationDescription": "Het apparaat zal nu opnieuw opstarten", "deleteAllElectricityMeters": "Verwijder alle kWh-meters", "deleteAllElectricityMetersDescription": "Wilt u echt alle kWh-meters verwijderen?", "deleteAllElectricityMetersConfirmationDescription": "Alle kWh-meters zijn succesvol verwijderd", "resetAllElectricityMeters": "Reset alle kWh-meterconfiguraties", "resetAllElectricityMetersDescription": "Wilt u echt alle kWh-meterconfiguraties resetten?", "resetAllElectricityMetersConfirmationDescription": "Alle kWh-meterconfiguraties zijn succesvol gereset", "deleteElectricityMeterHistories": "Alle kWh-metergeschiedenissen verwijderen", "deleteElectricityMeterHistoriesDescription": "Wilt u echt alle kWh-metergeschiedenissen verwijderen?", "deleteElectricityMeterHistoriesConfirmationDescription": "Alle kWh-metergeschiedenissen zijn succesvol verwijderd", "multipleElectricityMetersSupportMissing": "Uw apparaat ondersteunt momenteel slechts één kWh-meter. Update uw firmware.", "consumptionWithUnit": "Verbruik (kWh)", "exportWithUnit": "Levering (kWh)", "importWithUnit": "Verbruik (kWh)", "resourceWarningHeader": "Beperkingen in middelen", "mqttAndTcpResourceWarning": "Het is niet mogelijk om MQTT en Modbus TCP tegelijkertijd te gebruiken vanwege beperkte systeembronnen. Deactiveer {protocol} eerst.", "mqttEnabled": "MQTT ingeschakeld", "redirectMQTT": "Ga naar MQTT-instellingen", "redirectModbus": "Ga naar Modbus-instellingen", "unsupportedSettingDescription": "Met uw huidige firmwareversie worden sommige apparaatinstellingen niet ondersteund. Update uw firmware om de nieuwe functies te gebruiken", "updateNow": "Nu bijwerken", "zgw241Hint": "Met deze update is Modbus TCP standaard ingeschakeld en MQTT uitgeschakeld. Dit kan worden gewijzigd in de instellingen. Met ondersteuning voor maximaal 16 tellers zijn er veel optimalisaties doorgevoerd; dit kan leiden tot wijzigingen in de instellingen van het apparaat. Herstart het apparaat na het aanpassen van de instellingen.", "deviceConfigChangedSuccesfully": "Looptijd is succesvol gewijzigd.", "deviceConfiguration": "Apparaatconfiguratie", "tiltModeToggle": "<PERSON><PERSON><PERSON>odus", "tiltModeToggleFooter": "Als het apparaat is ingesteld in Matter, moeten alle functies daar opnieuw worden geconfigureerd.", "shaderMovementDirection": "Achteruit O<PERSON>hoog/Omlaag", "shaderMovementDirectionDescription": "<PERSON><PERSON> de richting om voor omhoog/omlaag-beweging<PERSON> van de motor", "tiltTime": "Kantel runtime", "changeTiltModeDialogTitle": "{target, select, true {Enable} false {Disable} other {Change}} kantelfunctie", "changeTiltModeDialogConfirmation": "{target, select, true {Enable} false {Disable} other {Change}}", "generalTextSlatSetting": "Instelling lamellen", "generalTextPosition": "<PERSON><PERSON><PERSON>", "generalTextSlatPosition": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slatSettingDescription": "Beschrijving lamelleninstelling", "scenePositionSliderDescription": "<PERSON><PERSON><PERSON>", "sceneSlatPositionSliderDescription": "Kantelen", "referenceRun": "<PERSON><PERSON><PERSON><PERSON>", "slatAutoSettingHint": "In deze modus is de stand van de zonwering niet van belang voordat de lamellen zich aanpassen aan de gewenste kantelstand.", "slatReversalSettingHint": "In deze modus sluit de zonwering volledig voordat de lamellen zich aanpassen aan de gewenste kantelpositie.", "release340Header": "De nieuwe ESB64NP-IPM-actuator voor inbouwzonwering is er!", "release340ESB64Header": "Wat kan de ESB64NP-IPM?", "release340ESB64Note1": "Onze Matter Gateway-gecertificeerde zonweringactuator met <PERSON><PERSON>", "release340ESB64Note2": "Twee bedrade knopingangen voor handmatig schakelen en doorsturen naar Matter", "release340ESB64Note3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met EnOcean adapter (EOA64). Bijvoorbeeld met EnOcean draad<PERSON>ze drukknop F4T55", "release340ESB64Note4": "Open voor integraties dankzij REST API gebaseerd op OpenAPI-standaard", "activateTiltModeDialogText": "Als de kantelfunctie is ingeschakeld, gaan alle instellingen verloren. Weet je zeker dat je de kantelfunctie wilt inschakelen?", "deactivateTiltModeDialogText": "Als je de kantelfunctie uitschakelt, gaan alle instellingen verloren. Weet je zeker dat je de kantelfunctie wilt uitschakelen?"}