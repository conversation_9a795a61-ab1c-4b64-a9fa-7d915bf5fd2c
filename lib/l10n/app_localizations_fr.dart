// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appName => 'ELTAKO Connect';

  @override
  String get discoveryHint =>
      'Activez le Bluetooth de l\'appareil pour vous connecter';

  @override
  String devicesFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count appareils trouvés',
      one: '1 appareil trouvé',
      zero: 'Aucun appareil trouvé',
    );
    return '$_temp0';
  }

  @override
  String discoveryDemodeviceName(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Demo devices',
      one: 'Demo device',
    );
    return '$_temp0';
  }

  @override
  String get discoverySu12Description => 'Horloge 2 canaux Bluetooth';

  @override
  String get discoveryImprint => 'Impression';

  @override
  String get discoveryLegalnotice => 'Mention légale';

  @override
  String get generalSave => 'Sauvegarder';

  @override
  String get generalCancel => 'Annuler';

  @override
  String get detailsHeaderHardwareversion => 'Version du matériel';

  @override
  String get detailsHeaderSoftwareversion => 'Version du logiciel';

  @override
  String get detailsHeaderConnected => 'Connecté';

  @override
  String get detailsHeaderDisconnected => 'Déconnecté';

  @override
  String get detailsTimersectionHeader => 'Programmes';

  @override
  String get detailsTimersectionTimercount => 'de 60 programmes utilisés';

  @override
  String get detailsConfigurationsectionHeader => 'Configuration';

  @override
  String get detailsConfigurationPin => 'Code PIN de l´appareil';

  @override
  String detailsConfigurationChannelsDescription(
    Object channel1,
    Object channel2,
  ) {
    return 'Canal 1 :$channel1 | Canal 2 :  $channel2';
  }

  @override
  String get settingsCentralHeader => 'On/Off centralisé';

  @override
  String get detailsConfigurationCentralDescription =>
      'S\'applique uniquement si le canal est réglé sur AUTO';

  @override
  String get detailsConfigurationDevicedisplaylock =>
      'Verrouiller l\'affichage de l´appareil';

  @override
  String get timerOverviewHeader => 'Programmes';

  @override
  String get timerOverviewTimersectionTimerinactivecount => 'inactif';

  @override
  String get timerDetailsListsectionDays1 => 'Lundi';

  @override
  String get timerDetailsListsectionDays2 => 'Mardi';

  @override
  String get timerDetailsListsectionDays3 => 'Mercredi';

  @override
  String get timerDetailsListsectionDays4 => 'Jeudi';

  @override
  String get timerDetailsListsectionDays5 => 'Vendredi';

  @override
  String get timerDetailsListsectionDays6 => 'Samedi';

  @override
  String get timerDetailsListsectionDays7 => 'Dimanche';

  @override
  String get timerDetailsHeader => 'Programme %@';

  @override
  String get timerDetailsSunrise => 'Lever du soleil';

  @override
  String get generalToggleOff => 'Éteint';

  @override
  String get generalToggleOn => 'Allumé';

  @override
  String get timerDetailsImpuls => 'Impulsion';

  @override
  String get generalTextTime => 'Horaire';

  @override
  String get generalTextAstro => 'Astro';

  @override
  String get generalTextAuto => 'Auto';

  @override
  String get timerDetailsOffset => 'Décalage horaire';

  @override
  String get timerDetailsPlausibility => 'Activer le contrôle de plausibilité';

  @override
  String get timerDetailsPlausibilityDescription =>
      'Si l\'heure d\'arrêt est réglée sur une heure antérieure à l\'heure de mise en marche, les deux programmes sont ignorés, par exemple, mise en marche au lever du soleil et arrêt à 6h00 du matin. Il existe également des cas où la vérification n\'est pas souhaitée, par exemple allumer au coucher du soleil et éteindre à 1h00 du matin.';

  @override
  String get generalDone => 'Prêt';

  @override
  String get generalDelete => 'Supprimer';

  @override
  String get timerDetailsImpulsDescription =>
      'Modifier la configuration globale des impulsions';

  @override
  String get settingsNameHeader => 'Nom de l´appareil';

  @override
  String get settingsNameDescription =>
      'Ce nom est utilisé pour identifier l\'appareil.';

  @override
  String get settingsFactoryresetHeader => 'Paramètres d\'usine';

  @override
  String get settingsFactoryresetDescription =>
      'Quel contenu doit être remis à zéro ?';

  @override
  String get settingsFactoryresetResetbluetooth =>
      'Réinitialiser les paramètres Bluetooth';

  @override
  String get settingsFactoryresetResettime =>
      'Réinitialiser les paramètres de l\'heure';

  @override
  String get settingsFactoryresetResetall => 'Revenir aux paramètres d\'usine';

  @override
  String get settingsDeletetimerHeader => 'Supprimer des programmes';

  @override
  String get settingsDeletetimerDescription =>
      'Faut-il vraiment supprimer tous les programmes ?';

  @override
  String get settingsDeletetimerAllchannels => 'Tous les canaux';

  @override
  String get settingsImpulseHeader => 'Temps de commutation des impulsions';

  @override
  String get settingsImpulseDescription =>
      'Le temps de commutation de l\'impulsion définit la durée de l\'impulsion.';

  @override
  String get generalTextRandommode => 'Mode aléatoire';

  @override
  String get settingsChannelsTimeoffsetHeader =>
      'Décalage de l\'heure du solstice';

  @override
  String settingsChannelsTimeoffsetDescription(
    Object summerOffset,
    Object winterOffset,
  ) {
    return 'Été : $summerOffset min | Hiver : $winterOffset min';
  }

  @override
  String get settingsLocationHeader => 'Localisation';

  @override
  String get settingsLocationDescription =>
      'Définir l´emplacement pour utiliser les fonctions astro.';

  @override
  String get settingsLanguageHeader => 'Langue de l´appareil';

  @override
  String get settingsLanguageSetlanguageautomatically =>
      'Définir la langue automatiquement';

  @override
  String settingsLanguageDescription(Object deviceType) {
    return 'Choisissez la langue pour le $deviceType';
  }

  @override
  String get settingsLanguageGerman => 'Allemand';

  @override
  String get settingsLanguageFrench => 'Français';

  @override
  String get settingsLanguageEnglish => 'Anglais';

  @override
  String get settingsLanguageItalian => 'Italien';

  @override
  String get settingsLanguageSpanish => 'Espagnol';

  @override
  String get settingsDatetimeHeader => 'Date et heure';

  @override
  String get settingsDatetimeSettimeautomatically =>
      'Appliquer l\'heure du système';

  @override
  String get settingsDatetimeSettimezoneautomatically =>
      'Définir automatiquement le fuseau horaire';

  @override
  String get generalTextTimezone => 'Fuseau horaire';

  @override
  String get settingsDatetime24Hformat => 'Format 24 heures';

  @override
  String get settingsDatetimeSetsummerwintertimeautomatically =>
      'Été/hiver automatique';

  @override
  String get settingsDatetimeWinter => 'Hiver';

  @override
  String get settingsDatetimeSummer => 'Été';

  @override
  String get settingsPasskeyHeader => 'Code PIN actuel de l´appareil';

  @override
  String get settingsPasskeyDescription =>
      'Saisir le code PIN actuel de l\'appareil';

  @override
  String get timerDetailsActiveprogram => 'Programme actif';

  @override
  String get timerDetailsActivedays => 'Jours actifs';

  @override
  String get timerDetailsSuccessdialogHeader => 'Avec succès';

  @override
  String get timerDetailsSuccessdialogDescription =>
      'Programme ajouté avec succès';

  @override
  String get settingsRandommodeDescription =>
      'Le mode aléatoire ne fonctionne qu\'avec les programmes horaires, pas avec les programmes d´impulsions ou astro (lever ou coucher du soleil).';

  @override
  String get settingsSolsticeHeader => 'Décalage de l\'heure du solstice';

  @override
  String get settingsSolsticeDescription =>
      'L\'heure indique le décalage horaire par rapport au coucher du soleil. Le lever du soleil est inversé en conséquence.';

  @override
  String get settingsSolsticeHint =>
      'Exemple:\nEn hiver, il s\'allume 30 minutes avant le coucher du soleil, ce qui signifie qu\'il s\'allume également 30 minutes après le lever du soleil.';

  @override
  String get generalTextMinutesShort => 'min';

  @override
  String get settingsPinDescription =>
      'Le code PIN est nécessaire pour la connexion.';

  @override
  String get settingsPinHeader => 'Nouveau code PIN de l\'appareil';

  @override
  String get settingsPinNewpinDescription => 'Saisir un nouveau code PIN';

  @override
  String get settingsPinNewpinRepeat => 'Répéter le nouveau code PIN';

  @override
  String get detailsProductinfo => 'Informations sur le produit';

  @override
  String get settingsDatetimeSettimeautodescription =>
      'Choisir l\'heure souhaitée';

  @override
  String minutes(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Minutes',
      one: 'Minute',
    );
    return '$_temp0';
  }

  @override
  String hours(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Hours',
      one: 'Hour',
    );
    return '$_temp0';
  }

  @override
  String seconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Seconds',
      one: 'Second',
    );
    return '$_temp0';
  }

  @override
  String generalTextChannel(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Canaux',
      one: 'Canal',
    );
    return '$_temp0';
  }

  @override
  String generalLabelChannel(Object number) {
    return 'Canal $number';
  }

  @override
  String get generalTextDate => 'Date';

  @override
  String get settingsDatetime24HformatDescription =>
      'Choisir le format préféré';

  @override
  String get settingsDatetimeSetsummerwintertime => 'Été/hiver';

  @override
  String get settingsDatetime24HformatValue24 => '24h';

  @override
  String get settingsDatetime24HformatValue12 => 'AM/PM';

  @override
  String get detailsEdittimer => 'Modifier les programmes';

  @override
  String get settingsPinOldpinRepeat => 'Répéter le code PIN actuel';

  @override
  String get settingsPinCheckpin => 'Vérification du code PIN';

  @override
  String detailsDevice(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Devices',
      one: 'Device',
    );
    return '$_temp0';
  }

  @override
  String get detailsDisconnect => 'Déconnexion';

  @override
  String get settingsCentralDescription =>
      'L\'entrée A1 contrôle la marche/l´arrêt central.\nCentral On/Off ne prend effet que si le canal est réglé sur Central On/Off.';

  @override
  String get settingsCentralHint =>
      'Exemple:\nCanal 1 = Marche/Arrêt central\nCanal 2 = Désactivé\nA1 = Central On -> Seul K1 passe à On, K2 reste éteint.';

  @override
  String get settingsCentralToggleheader => 'Mode de la commande centralisée';

  @override
  String get settingsCentralActivechannelsdescription =>
      'Canaux actuels avec le réglage Central On/Off :';

  @override
  String get settingsSolsticeSign => 'Signe';

  @override
  String get settingsDatetimeTimezoneDescription => 'Heure d\'Europe centrale';

  @override
  String get generalButtonContinue => 'Continuer';

  @override
  String get settingsPinConfirmationDescription =>
      'Changement de code PIN réussi';

  @override
  String get settingsPinFailDescription => 'Échec du changement de code PIN';

  @override
  String get settingsPinFailHeader => 'Échec';

  @override
  String get settingsPinFailShort =>
      'Le code PIN doit comporter exactement 6 chiffres.';

  @override
  String get settingsPinFailWrong => 'Le code PIN actuel est incorrect';

  @override
  String get settingsPinFailMatch => 'Les codes PIN ne correspondent pas';

  @override
  String get discoveryLostconnectionHeader => 'Connexion perdue';

  @override
  String get discoveryLostconnectionDescription =>
      'L\'appareil a été déconnecté.';

  @override
  String get settingsChannelConfigCentralDescription =>
      'Se comporte comme AUTO et réagit également aux entrées centrales filaires.';

  @override
  String get settingsChannelConfigOnDescription =>
      'Commute le canal en permanence sur ON et ignore les programmes.';

  @override
  String get settingsChannelConfigOffDescription =>
      'Commute le canal en permanence sur OFF et ignore les programmes';

  @override
  String get settingsChannelConfigAutoDescription =>
      'Commutation en fonction de l\'heure et des programmes astro';

  @override
  String get bluetoothPermissionDescription =>
      'Le Bluetooth est nécessaire pour la configuration des appareils.';

  @override
  String get timerListitemOn => 'Allumer';

  @override
  String get timerListitemOff => 'Éteindre';

  @override
  String get timerListitemUnknown => 'Inconnu';

  @override
  String get timerDetailsAstroHint =>
      'L\'emplacement doit être défini dans les paramètres pour que les programmes astro fonctionnent correctement.';

  @override
  String get timerDetailsTrigger => 'Déclencheur';

  @override
  String get timerDetailsSunset => 'Coucher de soleil';

  @override
  String get settingsLocationCoordinates => 'Coordonnées';

  @override
  String get settingsLocationLatitude => 'Latitude';

  @override
  String get settingsLocationLongitude => 'Longitude';

  @override
  String timerOverviewEmptyday(Object day) {
    return 'Aucun programme n\'est actuellement utilisé pour $day';
  }

  @override
  String get timerOverviewProgramloaded => 'Les programmes sont chargés';

  @override
  String get timerOverviewProgramchanged => 'Le programme a été modifié';

  @override
  String get settingsDatetimeProcessing => 'La date et l\'heure sont modifiées';

  @override
  String get deviceNameEmpty => 'La saisie ne doit pas être vide';

  @override
  String deviceNameHint(Object count) {
    return 'La saisie ne doit pas contenir plus de $count caractères.';
  }

  @override
  String get deviceNameChanged => 'Le nom de l´appareil est modifié';

  @override
  String get deviceNameChangedSuccessfully =>
      'Le nom de l´appareil a été modifié avec succès.';

  @override
  String get deviceNameChangedFailed => 'Une erreur s\'est produite.';

  @override
  String get settingsPinConfirm => 'Confirmer';

  @override
  String get deviceShowInstructions =>
      '1. Activer le Bluetooth de l´horloge avec SET\n2. Appuyer sur le bouton en haut pour lancer la recherche.';

  @override
  String get deviceNameNew => 'Entrer le nouveau nom de l´appareil';

  @override
  String get settingsLanguageRetrieved => 'La langue est récupérée';

  @override
  String get detailsProgramsShow => 'Montrer les programmes';

  @override
  String get generalTextProcessing => 'Veuillez patienter';

  @override
  String get generalTextRetrieving => 'sont récupérés';

  @override
  String get settingsLocationPermission =>
      'Autoriser ELTAKO Connect à accéder à la localisation de cet appareil.';

  @override
  String get timerOverviewChannelloaded => 'Les canaux sont chargés';

  @override
  String get generalTextRandommodeChanged => 'Le mode aléatoire est modifié';

  @override
  String get detailsConfigurationsectionChanged =>
      'La configuration est modifiée';

  @override
  String get settingsSettimeFunctions =>
      'Les fonctions horaires sont modifiées';

  @override
  String get imprintContact => 'Contact';

  @override
  String get imprintPhone => 'Téléphone';

  @override
  String get imprintMail => 'Courrier';

  @override
  String get imprintRegistrycourt => 'Tribunal d\'enregistrement';

  @override
  String get imprintRegistrynumber => 'Numéro d\'enregistrement';

  @override
  String get imprintCeo => 'Directeur général';

  @override
  String get imprintTaxnumber => 'Numéro d\'identification TVA';

  @override
  String get settingsLocationCurrent => 'Localisation actuelle';

  @override
  String get generalTextReset => 'Réinitialiser';

  @override
  String get discoverySearchStart => 'Démarrer la recherche';

  @override
  String get discoverySearchStop => 'Arrêt de la recherche';

  @override
  String get settingsImpulsSaved =>
      'Le temps de commutation des impulsions est mémorisé';

  @override
  String get settingsCentralNochannel =>
      'Il n\'y a pas de canaux avec le réglage central On/Off.';

  @override
  String get settingsFactoryresetBluetoothConfirmationDescription =>
      'La connexion Bluetooth a été réinitialisée avec succès.';

  @override
  String get settingsFactoryresetBluetoothFailDescription =>
      'La réinitialisation des connexions Bluetooth a échoué.';

  @override
  String get imprintPublisher => 'Éditeur';

  @override
  String get discoveryDeviceConnecting => 'Connexion...';

  @override
  String get discoveryDeviceRestarting => 'Redémarrage...';

  @override
  String get generalTextConfigurationsaved =>
      'La configuration du canal est sauvegardée.\n';

  @override
  String get timerOverviewChannelssaved => 'Sauvegarder les canaux';

  @override
  String get timerOverviewSaved => 'Horloge enregistrée\n';

  @override
  String get timerSectionList => 'Affichage liste';

  @override
  String get timerSectionDayview => 'Affichage jour';

  @override
  String get generalTextChannelInstructions => 'Réglages des canaux';

  @override
  String get generalTextPublisher => 'Éditeur';

  @override
  String get settingsDeletetimerDialog =>
      'Voulez-vous vraiment supprimer tous les programmes ?';

  @override
  String get settingsFactoryresetResetbluetoothDialog =>
      'Voulez-vous vraiment réinitialiser tous les paramètres Bluetooth ?';

  @override
  String get settingsCentralTogglecentral => 'On/Off\nCentralisé';

  @override
  String generalTextConfirmation(Object serviceName) {
    return '$serviceName changement réussi.';
  }

  @override
  String generalTextFailed(Object serviceName) {
    return '$serviceName changement échoué.';
  }

  @override
  String get settingsChannelConfirmationDescription =>
      'Les canaux ont été changés avec succès.';

  @override
  String get timerDetailsSaveHeader => 'Sauvegarder le programme.';

  @override
  String get timerDetailsDeleteHeader => 'Supprimer le programme';

  @override
  String get timerDetailsSaveDescription => 'Sauvegarde du programme réussie.';

  @override
  String get timerDetailsDeleteDescription =>
      'Suppression du programme réussie.';

  @override
  String get timerDetailsAlertweekdays =>
      'Le programme ne peut pas être sauvegardé, car aucun jour de la semaine n\'est sélectionné.';

  @override
  String get generalTextOk => 'OK';

  @override
  String get settingsDatetimeChangesuccesfull =>
      'La date et l\'heure ont été modifiées avec succès.';

  @override
  String get discoveryConnectionFailed => 'La connexion a échoué';

  @override
  String get discoveryDeviceResetrequired =>
      'Aucune connexion n\'a pu être établie avec l\'appareil. Pour résoudre ce problème, supprimer l\'appareil des paramètres Bluetooth. Si le problème persiste, contacter notre support technique.';

  @override
  String get generalTextSearch => 'Rechercher des appareils';

  @override
  String get generalTextOr => 'ou';

  @override
  String get settingsFactoryresetProgramsConfirmationDescription =>
      'Tous les programmes ont été supprimés avec succès.';

  @override
  String get generalTextManualentry => 'Saisie manuelle';

  @override
  String get settingsLocationSaved => 'Emplacement sauvegardé';

  @override
  String get settingsLocationAutosearch =>
      'Recherche automatique de l\'emplacement';

  @override
  String get imprintPhoneNumber => '+49 711 / 9435 0000';

  @override
  String get imprintMailAddress => '<EMAIL>';

  @override
  String get settingsFactoryresetResetallDialog =>
      'Voulez-vous vraiment réinitialiser l\'appareil aux paramètres d\'usine ?';

  @override
  String get settingsFactoryresetFactoryConfirmationDescription =>
      'L\'appareil a été réinitialisé avec succès aux paramètres d\'usine.';

  @override
  String get settingsFactoryresetFactoryFailDescription =>
      'Échec de la réinitialisation de l\'appareil.';

  @override
  String get imprintPhoneNumberIos => '+49711/94350000';

  @override
  String get mfzFunctionA2Title => 'Délai de réponse en 2 étapes (A2)';

  @override
  String get mfzFunctionA2TitleShort => 'Délai de réponse en 2 étapes (A2)';

  @override
  String get mfzFunctionA2Description =>
      'Lorsque la tension de commande est appliquée, le laps de temps T1 compris entre 0 et 60 secondes commence. A la fin de cette période, le contact 1-2 se ferme et le laps de temps t2 entre 0 et 60 secondes commence. A la fin de ce temps, le contact 3-4 se ferme. Après une interruption, la séquence temporelle recommence avec t1.';

  @override
  String get mfzFunctionRvTitle => 'Délai d\'arrêt (RV)';

  @override
  String get mfzFunctionRvTitleShort => 'RV | Délai d\'arrêt';

  @override
  String get mfzFunctionRvDescription =>
      'Lorsque la tension de commande est appliquée, le contact à fermeture change après 15-18. \nLorsque la tension de commande est interrompue, le laps de temps commence, à la fin duquel le contact à fermeture revient en position de repos. Peut être mis en marche pendant le laps de temps.';

  @override
  String get mfzFunctionTiTitle =>
      'Générateur d\'impulsion démarrant avec une impulsion (TI; relais de clignotant)';

  @override
  String get mfzFunctionTiTitleShort =>
      'TI | Générateur d\'impulsion démarrant avec sune impulsion';

  @override
  String get mfzFunctionTiDescription =>
      'Tant que la tension de commande est appliquée, le contact de commande se ferme et s\'ouvre. Le temps de commutation dans les deux sens peut être réglé séparément. Lorsque la tension de commande est appliquée, le contact de commande passe immédiatement à 15-18.';

  @override
  String get mfzFunctionAvTitle => 'Délai de réponse (AV; délai d\'allumage)';

  @override
  String get mfzFunctionAvTitleShort => 'AV | Délai de réponse';

  @override
  String get mfzFunctionAvDescription =>
      'Lorsque la tension de commande est appliquée, le laps de temps commence, à la fin duquel le contact à fermeture passe à 15-18. Après une interruption, le laps de temps recommence.';

  @override
  String get mfzFunctionAvPlusTitle =>
      'Délai de réponse additif (AV+; délai de mise en marche)';

  @override
  String get mfzFunctionAvPlusTitleShort => 'AV+ | Délai de réponse additif';

  @override
  String get mfzFunctionAvPlusDescription =>
      'Fonctionne comme AV, mais après une interruption, le temps déjà écoulé reste mémorisé.';

  @override
  String get mfzFunctionAwTitle => 'Relais temporisé (AW)';

  @override
  String get mfzFunctionAwTitleShort => 'AW | Relais temporisé';

  @override
  String get mfzFunctionAwDescription =>
      'Lorsque la tension de commande est interrompue, le contact change vers 15-18 et revient après que la temporisation se soit écoulée. Lorsque la tension de commande est appliquée pendant la temporisation, le contact revient immédiatement en position de repos et le temps restant est effacé.';

  @override
  String get mfzFunctionIfTitle =>
      'Mise en forme des impulsions (IF; MFZ12.1 uniquement)';

  @override
  String get mfzFunctionIfTitleShort => 'IF | Mise en forme des impulsions';

  @override
  String get mfzFunctionIfDescription =>
      'Lorsque la tension de commande est appliquée, le contact de fermeture change pour le temps réglé après 15-18. Les autres actionnements ne sont évalués qu\'après l\'écoulement du temps réglé.';

  @override
  String get mfzFunctionEwTitle => 'Relais temporisé (EW)';

  @override
  String get mfzFunctionEwTitleShort => 'EW | Relais temporisé';

  @override
  String get mfzFunctionEwDescription =>
      'Lorsque la tension de commande est appliquée, le contact passe à 15-18 et revient après que la temporisation se soit écoulée. Lorsque la tension de commande est supprimée pendant la temporisation, le contact revient immédiatement en position de repos et le temps restant est effacé.';

  @override
  String get mfzFunctionEawTitle =>
      'Relais temporisé  à l\'allumage et à l\'extinction (EAW; MFZ12.1 seulement)';

  @override
  String get mfzFunctionEawTitleShort =>
      'EAW | Relais temporisé de mise en marche et d\'arrêt';

  @override
  String get mfzFunctionEawDescription =>
      'Lorsque la tension de commande est appliquée et interrompue, le contact de fermeture passe à 15-18 et revient après le temps réglé.';

  @override
  String get mfzFunctionTpTitle =>
      'Générateur d\'impulsions démarrant avec une pause (TP; relais de clignotant, MFZ12.1 seulement)';

  @override
  String get mfzFunctionTpTitleShort =>
      'TP | Générateur d\'impulsions démarrant par une pause';

  @override
  String get mfzFunctionTpDescription =>
      'Descriptions fonctionnelles comme TI, mais lorsque la tension de commande est appliquée, le contact ne passe pas à 15-18, mais reste initialement à 15-16 ou ouvert.';

  @override
  String get mfzFunctionIaTitle =>
      'Délai de réponse et mise en forme des impulsions commandés par impulsion (IA; MFZ12.1 uniquement)';

  @override
  String get mfzFunctionIaTitleShort =>
      'IA |  Délai de réponse commandé par impulsion';

  @override
  String get mfzFunctionIaDescription =>
      'Avec le début d\'une impulsion de commande à partir de 20 ms commence le laps de temps t1, à la fin duquel le\nà la fin duquel le contact de fermeture change pour le temps t2 après 15-18 (par exemple pour les ouvreurs de portes automatiques). Si t1 est réglé sur le temps le plus court 0,1 s, IA fonctionne comme un façonneur d\'impulsions à la fin duquel t2 expire, quelle que soit la longueur du signal de commande (min. 150 ms).';

  @override
  String get mfzFunctionArvTitle =>
      'Temporisation de réponse et délai d´ouverture (ARV)';

  @override
  String get mfzFunctionArvTitleShort =>
      'ARV | Temporisation de réponse et délai d´ouverture';

  @override
  String get mfzFunctionArvDescription =>
      'Lorsque la tension de commande est appliquée, la temporisation commence, à la fin de laquelle le contact de commande passe à 15 -18. Si la tension de commande est ensuite interrompue, une nouvelle temporisation commence, à la fin de laquelle le contact de commande revient en position de repos.\nAprès une interruption de la temporisation de réponse, la temporisation recommence.';

  @override
  String get mfzFunctionArvPlusTitle =>
      'Réponse additive et délai d´ouverture (ARV+)';

  @override
  String get mfzFunctionArvPlusTitleShort =>
      'ARV+ | Réponse additive et délai d´ouverture';

  @override
  String get mfzFunctionArvPlusDescription =>
      'Fonctionne comme ARV, mais après une interruption du délai de réponse, le temps déjà écoulé reste mémorisé.';

  @override
  String get mfzFunctionEsTitle => 'Télérupteur (ES)';

  @override
  String get mfzFunctionEsTitleShort => 'ES | Télérupteur';

  @override
  String get mfzFunctionEsDescription =>
      'Le contact à fermeture commute d\'avant en arrière avec des impulsions de commande de 50 ms.';

  @override
  String get mfzFunctionEsvTitle =>
      'Télérupteur avec temporisation et alerte précoce d\'arrêt (ESV)';

  @override
  String get mfzFunctionEsvTitleShort => 'ESV | Télérupteur avec temporisation';

  @override
  String get mfzFunctionEsvDescription =>
      'Fonctionne comme SRV. En outre, avec un avertissement préalable à l\'extinction : à partir d\'environ 30 secondes avant l\'expiration du temps, l\'éclairage clignote 3 fois à intervalles décroissants.';

  @override
  String get mfzFunctionErTitle => 'Relais (ER)';

  @override
  String get mfzFunctionErTitleShort => 'ER | Relais';

  @override
  String get mfzFunctionErDescription =>
      'Tant que le contact de commande est fermé, le contact de travail passe de 15-16 à 15-18.';

  @override
  String get mfzFunctionSrvTitle => 'Télérupteur avec temporisation (SRV)';

  @override
  String get mfzFunctionSrvTitleShort => 'SRV | Télérupteur avec temporisation';

  @override
  String get mfzFunctionSrvDescription =>
      'Le contact à fermeture commute en alternance avec des impulsions de commande à partir de 50ms. En position de contact 15-18, le dispositif revient automatiquement en position de repos 15-16 après l\'écoulement de la temporisation.';

  @override
  String get detailsFunctionsHeader => 'Fonctions';

  @override
  String mfzFunctionTimeHeader(Object index) {
    return 'Temps (t$index)';
  }

  @override
  String get mfzFunctionOnDescription => 'activé en permanence';

  @override
  String get mfzFunctionOffDescription => 'arrêt permanent';

  @override
  String get mfzFunctionMultiplier => 'Facteur';

  @override
  String get discoveryMfz12Description =>
      'Relais horaire multifonctionnel Bluetooth';

  @override
  String get mfzFunctionOnTitle => 'activé en permanence';

  @override
  String get mfzFunctionOnTitleShort => 'activé en permanence';

  @override
  String get mfzFunctionOffTitle => 'arrêt permanent';

  @override
  String get mfzFunctionOffTitleShort => 'arrêt permanent';

  @override
  String get mfzMultiplierSecondsFloatingpoint => '0.1 secondes';

  @override
  String get mfzMultiplierMinutesFloatingpoint => '0.1 minutes';

  @override
  String get mfzMultiplierHoursFloatingpoint => '0.1 heures';

  @override
  String get mfzOverviewFunctionsloaded => 'Les fonctions sont chargées';

  @override
  String get mfzOverviewSaved => 'Fonction sauvegardée';

  @override
  String get settingsBluetoothHeader => 'Bluetooth';

  @override
  String get bluetoothChangedSuccessfully =>
      'Le paramètre Bluetooth a été modifié avec succès.';

  @override
  String get settingsBluetoothInformation =>
      'Remarque : Si ce paramètre est activé, l\'appareil sera visible en permanence par tout le monde via Bluetooth !\nIl est recommandé de modifier le code PIN de l\'appareil.';

  @override
  String get settingsBluetoothContinuousconnection => 'Visibilité durable';

  @override
  String settingsBluetoothContinuousconnectionDescription(Object deviceType) {
    return 'En activant la visibilité permanente, Bluetooth reste actif sur l´appareil ($deviceType) et ne doit pas être activé manuellement avant l\'établissement de la connexion.';
  }

  @override
  String get settingsBluetoothTimeout => 'Délais de connexion écoulé';

  @override
  String get settingsBluetoothPinlimit => 'limite du PIN';

  @override
  String settingsBluetoothTimeoutDescription(Object timeout) {
    return 'La connexion est interrompue après $timeout minutes d\'inactivité.';
  }

  @override
  String settingsBluetoothPinlimitDescription(Object attempts) {
    return 'Pour des raisons de sécurité, vous disposez au maximum de $attempts tentatives pour la saisie du code PIN. Ensuite, Bluetooth est désactivé \net doit être réactivé manuellement pour une nouvelle connexion. \nêtre activée à nouveau.';
  }

  @override
  String get settingsBluetoothPinAttempts => 'tentatives';

  @override
  String get settingsResetfunctionHeader => 'Réinitialiser les fonctions';

  @override
  String get settingsResetfunctionDialog =>
      'Voulez-vous vraiment réinitialiser toutes les fonctions?';

  @override
  String get settingsFactoryresetFunctionsConfirmationDescription =>
      'Toutes les fonctions ont été réinitialisées avec succès.';

  @override
  String get mfzFunctionTime => 'Temps (t)';

  @override
  String get discoveryConnectionFailedInfo => 'Pas de connexion Bluetooth';

  @override
  String get detailsConfigurationDevicedisplaylockDialogtext =>
      'Immédiatement après le verrouillage de l’écran de l’appareil, Bluetooth est désactivé et doit être réactivé manuellement afin d’établir une nouvelle connexion.';

  @override
  String get detailsConfigurationDevicedisplaylockDialogquestion =>
      'Êtes-vous sûr de verrouiller l’affichage de l’appareil?';

  @override
  String get settingsDemodevices => 'Afficher les appareils de démonstration';

  @override
  String get generalTextSettings => 'Paramètres';

  @override
  String get discoveryWifi => 'WiFi';

  @override
  String get settingsInformations => 'Informations';

  @override
  String get detailsConfigurationDimmingbehavior => 'Comportement de variation';

  @override
  String get detailsConfigurationSwitchbehavior =>
      'Comportement du commutateur';

  @override
  String get detailsConfigurationBrightness => 'Luminosité';

  @override
  String get detailsConfigurationMinimum => 'Minimum';

  @override
  String get detailsConfigurationMaximum => 'Maximum';

  @override
  String get detailsConfigurationSwitchesGr => 'Groupe relais (GR)';

  @override
  String get detailsConfigurationSwitchesGs => 'Commutateur de groupe (GS)';

  @override
  String get detailsConfigurationSwitchesCloserer => 'Contact NO (ER)';

  @override
  String get detailsConfigurationSwitchesClosererDescription =>
      'Arrêt -> Maintien enfoncé (On) -> Relâchement (Off)';

  @override
  String get detailsConfigurationSwitchesOpenerer => 'Contact NF (ER-Invers)';

  @override
  String get detailsConfigurationSwitchesOpenererDescription =>
      'On -> Maintien enfoncé (Off) -> Relâchement (On)';

  @override
  String get detailsConfigurationSwitchesSwitch => 'Interrupteur';

  @override
  String get detailsConfigurationSwitchesSwitchDescription =>
      'Chaque pression sur le bouton-poussoir permet d\'allumer et d\'éteindre la lumière.';

  @override
  String get detailsConfigurationSwitchesImpulsswitch => 'Télérupteur';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription =>
      'Le bouton-poussoir est brièvement pressé et relâché pour allumer ou éteindre la lumière.';

  @override
  String get detailsConfigurationSwitchesClosererDescription2 =>
      'Maintenez le bouton-poussoir enfoncé. Lorsqu\'il est relâché, le moteur s\'arrête';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription2 =>
      'Le bouton-poussoir est pressé brièvement pour démarrer le moteur et pressé brièvement pour l\'arrêter à nouveau.';

  @override
  String get detailsConfigurationWifiloginScan => 'Scanner le code QR';

  @override
  String get detailsConfigurationWifiloginScannotvalid =>
      'Le code scanné n\'est pas valide';

  @override
  String get detailsConfigurationWifiloginDescription => 'Saisir le code';

  @override
  String get detailsConfigurationWifiloginPassword => 'Mot de passe';

  @override
  String get discoveryEsbipDescription =>
      'Actionneur de volets roulants et d\'ombrage IP';

  @override
  String get discoveryEsripDescription => 'Télérupteurs IP';

  @override
  String get discoveryEudipDescription => 'Variateur de lumière universel IP';

  @override
  String get generalTextLoad => 'Chargement';

  @override
  String get wifiBasicautomationsNotFound => 'Aucune automatisation trouvée.';

  @override
  String get wifiCodeInvalid => 'Code non valide';

  @override
  String get wifiCodeValid => 'Code valide';

  @override
  String get wifiAuthorizationLogin => 'Connecter';

  @override
  String get wifiAuthorizationLoginFailed => 'La connexion a échoué';

  @override
  String get wifiAuthorizationSerialnumber => 'Numéro de série';

  @override
  String get wifiAuthorizationProductiondate => 'Date de production';

  @override
  String get wifiAuthorizationProofofpossession => 'PoP';

  @override
  String get generalTextWifipassword => 'Mot de passe WiFi';

  @override
  String get generalTextUsername => 'Nom d\'utilisateur';

  @override
  String get generalTextEnter => 'OU ENTRER MANUELLEMENT';

  @override
  String get wifiAuthorizationScan => 'Scanner le code ELTAKO.';

  @override
  String get detailsConfigurationDevicesNofunctionshinttext =>
      'Cet appareil ne prend actuellement en charge aucun autre paramètre';

  @override
  String get settingsUsedemodelay => 'Utiliser le délai de démonstration';

  @override
  String get settingsImpulsLoad =>
      'Temps de commutation des impulsions en cours de chargement';

  @override
  String get settingsBluetoothLoad =>
      'Paramètre Bluetooth en cours de chargement.';

  @override
  String get detailsConfigurationsectionLoad =>
      'Les configurations sont chargées';

  @override
  String get generalTextLogin => 'Connexion';

  @override
  String get generalTextAuthentication => 'S\'authentifier';

  @override
  String get wifiAuthorizationScanDescription =>
      'Recherchez le code ELTAKO sur l\'appareil WiFi ou sur la carte d\'information ci-jointe et scannez-le avec votre appareil photo.';

  @override
  String get wifiAuthorizationScanShort => 'Scanner le code ELTAKO';

  @override
  String get detailsConfigurationEdgemode => 'Courve de variation';

  @override
  String get detailsConfigurationEdgemodeLeadingedge =>
      'Coupure de début de phase';

  @override
  String get generalTextNetwork => 'Réseau';

  @override
  String get wifiAuthenticationSuccessful => 'Authentification réussie';

  @override
  String get detailsConfigurationsectionSavechange => 'Configuration modifiée';

  @override
  String get discoveryWifiAdddevice => 'Ajouter un appareil Wi-Fi';

  @override
  String get wifiAuthenticationDelay => 'Cela peut durer jusqu\'à 1 minute';

  @override
  String get generalTextRetry => 'Retenter';

  @override
  String get wifiAuthenticationCredentials =>
      'Veuillez entrer les informations d\'identification de votre WiFi';

  @override
  String get wifiAuthenticationSsid => 'SSID';

  @override
  String get wifiAuthenticationDelaylong =>
      'Cela peut durer jusqu\'à 1 minute jusqu\'à ce que l\'appareil soit prêt et s\'affiche dans l\'application';

  @override
  String get wifiAuthenticationCredentialsShort =>
      'Entrez les informations d\'identification Wi-Fi';

  @override
  String get wifiAuthenticationTeachin =>
      'Appairage de l\'appareil dans le WiFi';

  @override
  String get wifiAuthenticationEstablish =>
      'Établir la connexion avec l\'appareil';

  @override
  String wifiAuthenticationEstablishLong(Object ssid) {
    return 'L\'appareil se connecte au Wi-Fi $ssid';
  }

  @override
  String get wifiAuthenticationFailed =>
      'La connexion a échoué. Débranchez l\'appareil de l\'alimentation pendant quelques secondes et réessayez de le connecter';

  @override
  String get wifiAuthenticationReset => 'Réinitialiser l\'authentification';

  @override
  String get wifiAuthenticationResetHint =>
      'Toutes les données d\'authentification seront supprimées.';

  @override
  String get wifiAuthenticationInvaliddata =>
      'Données d\'authentification non valides';

  @override
  String get wifiAuthenticationReauthenticate => 'S\'authentifier à nouveau';

  @override
  String get wifiAddhkdeviceHeader => 'Ajouter un appareil';

  @override
  String get wifiAddhkdeviceDescription =>
      'Connectez votre nouvel appareil ELTAKO à votre WIFI via l\'application Apple Home.';

  @override
  String get wifiAddhkdeviceStep1 => 'Ouvrez l\'application Apple Home.';

  @override
  String get wifiAddhkdeviceStep2 =>
      'Cliquez sur le plus dans le coin supérieur droit de l\'application et sélectionnez **Add Device**.';

  @override
  String get wifiAddhkdeviceStep3 =>
      'Suivez les instructions de l\'application.';

  @override
  String get wifiAddhkdeviceStep4 =>
      '4. Votre appareil peut maintenant être configuré dans l\'application ELTAKO Connect.';

  @override
  String get detailsConfigurationRuntime => 'Temps d\'exécution';

  @override
  String get detailsConfigurationRuntimeMode => 'Mode';

  @override
  String get generalTextManually => 'Manuellement';

  @override
  String get detailsConfigurationRuntimeAutoDescription =>
      'L\'actionneur d\'ombrage détermine indépendamment le temps de fonctionnement pendant chaque déplacement de la position finale inférieure à la position finale supérieure (recommandé).\nAprès la mise en service, une telle\ndoit être effectuée de bas en haut sans interruption.\nsans interruption.';

  @override
  String get detailsConfigurationRuntimeManuallyDescription =>
      'La durée de fonctionnement du moteur d\'ombrage est réglée manuellement via la durée ci-dessous.';

  @override
  String get detailsConfigurationRuntimeDemoDescription =>
      'Le mode démo est uniquement disponible via l\'API REST.';

  @override
  String get generalTextDemomodeActive => 'Mode démo actif';

  @override
  String get detailsConfigurationRuntimeDuration => 'Durée';

  @override
  String get detailsConfigurationSwitchesGs4 => 'Commutateur de groupe (GS4)';

  @override
  String get detailsConfigurationSwitchesGs4Description =>
      'Interrupteur de groupe avec fonction d\'inversion de marche pour la commande de stores';

  @override
  String get screenshotSu12 => 'Lampe jardin';

  @override
  String get screenshotS2U12 => 'Lampe jardin';

  @override
  String get screenshotMfz12 => 'Pompe';

  @override
  String get screenshotEsr62 => 'Lampe';

  @override
  String get screenshotEud62 => 'Lampe plafond';

  @override
  String get screenshotEsb62 => 'Ombrage balcon';

  @override
  String get detailsConfigurationEdgemodeLeadingedgeDescription =>
      'LC1-LC3 sont des positions de confort avec différentes courbes de variation pour les lampes LED 230 V dimmables, qui ne peuvent pas être variées suffisamment en AUTO en raison de leur conception et doivent donc être forcées en coupure de début de phase.';

  @override
  String get detailsConfigurationEdgemodeAutoDescription =>
      'AUTO permet la variation de tous les types de lampes.';

  @override
  String get detailsConfigurationEdgemodeTrailingedge =>
      'Coupure de fin de phase';

  @override
  String get detailsConfigurationEdgemodeTrailingedgeDescription =>
      'LC4-LC6 sont des positions de confort avec différentes courbes de variation pour les lampes LED 230 V dimmables.';

  @override
  String get updateHeader => 'Mise à jour du logiciel';

  @override
  String get updateTitleStepSearch => 'Recherche d\'une mise à jour';

  @override
  String get updateTitleStepFound => 'Une mise à jour a été trouvée';

  @override
  String get updateTitleStepDownload => 'Téléchargement de la mise à jour';

  @override
  String get updateTitleStepInstall => 'Installation de la mise à jour';

  @override
  String get updateTitleStepSuccess => 'Mise à jour réussie';

  @override
  String get updateTitleStepUptodate => 'Déjà à jour';

  @override
  String get updateTitleStepFailed => 'Échec de la mise à jour';

  @override
  String get updateButtonSearch => 'Recherche de mises à jour';

  @override
  String get updateButtonInstall => 'Installer la mise à jour';

  @override
  String get updateCurrentversion => 'Version actuelle';

  @override
  String get updateNewversion => 'Nouvelle mise à jour du firmware disponible';

  @override
  String get updateHintPower =>
      'La mise à jour ne démarre que si la sortie de l\'appareil n\'est pas active. L\'appareil ne doit pas être débranché de l\'alimentation électrique et l\'application ne doit pas être abandonnée pendant la mise à jour !';

  @override
  String get updateButton => 'Mise à jour';

  @override
  String get updateHintCompatibility =>
      'Une mise à jour est recommandée, sinon certaines fonctions de l\'application seront limitées.';

  @override
  String get generalTextDetails => 'Détails';

  @override
  String get updateMessageStepMetadata =>
      'Télécharger les informations de mise à jour';

  @override
  String get updateMessageStepPrepare => 'La mise à jour est obligatoire';

  @override
  String get updateTitleStepUpdatesuccessful => 'La mise à jour est terminée';

  @override
  String get updateTextStepFailed =>
      'La mise à jour est un peu difficile à réaliser, il faudra attendre quelques minutes avant de pouvoir procéder à une mise à jour automatique de l\'appareil (connexion Internet obligatoire).';

  @override
  String get configurationsNotavailable =>
      'Aucune configuration n\'est encore disponible';

  @override
  String get configurationsAddHint =>
      'Créez de nouvelles configurations en vous connectant à un appareil et en enregistrant une configuration.';

  @override
  String get configurationsEdit => 'Modifier la configuration';

  @override
  String get generalTextName => 'Nom';

  @override
  String get configurationsDelete => 'Supprimer la configuration';

  @override
  String configurationsDeleteHint(Object configName) {
    return 'La configuration: $configName doit-elle vraiment être supprimée ?';
  }

  @override
  String get configurationsSave => 'Sauvegarder la configuration';

  @override
  String get configurationsSaveHint =>
      'Ici, vous pouvez enregistrer la configuration de votre appareil actuel ou charger une configuration déjà enregistrée.';

  @override
  String get configurationsImport => 'Importer la configuration';

  @override
  String configurationsImportHint(Object configName) {
    return 'La configuration $configName doit-elle vraiment être transférée ?';
  }

  @override
  String generalTextConfigurations(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Configurations',
      one: 'Configuration',
    );
    return '$_temp0';
  }

  @override
  String get configurationsStepPrepare =>
      'La configuration est en cours de préparation';

  @override
  String get configurationsStepName => 'Saisir un nom pour la configuration';

  @override
  String get configurationsStepSaving => 'La configuration est sauvegardée';

  @override
  String get configurationsStepSavedsuccessfully =>
      'La configuration a été sauvegardée avec succès';

  @override
  String get configurationsStepSavingfailed =>
      'L\'enregistrement de la configuration a échoué';

  @override
  String get configurationsStepChoose => 'Sélectionner une configuration';

  @override
  String get configurationsStepImporting => 'La configuration est importée';

  @override
  String get configurationsStepImportedsuccessfully =>
      'La configuration a été importée avec succès';

  @override
  String get configurationsStepImportingfailed =>
      'L\'importation de la configuration a échoué';

  @override
  String get discoveryAssuDescription =>
      'Interrupteur horaire pour prise extérieure Bluetooth';

  @override
  String get settingsDatetimeDevicetime => 'Temps réel de l\'appareil';

  @override
  String get settingsDatetimeLoading => 'Les réglages de l\'heure sont chargés';

  @override
  String get discoveryEud12Description => 'Variateur universel Bluetooth';

  @override
  String get generalTextOffdelay => 'Délai d\'arrêt';

  @override
  String get generalTextRemainingbrightness => 'Luminosité restante';

  @override
  String get generalTextSwitchonvalue => 'Valeur d\'enclenchement';

  @override
  String get motionsensorTitleNoremainingbrightness =>
      'Pas de luminosité résiduelle';

  @override
  String get motionsensorTitleAlwaysremainingbrightness =>
      'Avec une luminosité résiduelle';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogram =>
      'Luminosité résiduelle par programme de commutation';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogramandzea =>
      'Luminosité résiduelle via ZE et ZA';

  @override
  String get motionsensorTitleNoremainingbrightnessauto =>
      'Pas de luminosité résiduelle (semi-automatique)';

  @override
  String get generalTextMotionsensor => 'Détecteur de mouvement';

  @override
  String get generalTextLightclock => 'Réveil lumineux';

  @override
  String get generalTextSnoozeclock => 'Fonction Snooze';

  @override
  String generalDescriptionLightclock(Object mode) {
    return 'Lors de l\'allumage ($mode), la lumière s\'allume après environ 1 seconde à la luminosité la plus faible et augmente lentement sans modifier le dernier niveau de luminosité enregistré.';
  }

  @override
  String generalDescriptionSnoozeclock(Object mode) {
    return 'Lors de l\'extinction ($mode), l\'éclairage est réduit de la position de variation actuelle jusqu\'à la luminosité minimale et s\'éteint. L\'éclairage peut être éteint à tout moment pendant le processus de variation en appuyant brièvement sur la touche. Une pression prolongée sur la touche pendant le processus de variation permet d\'augmenter la luminosité et de mettre fin à la fonction \"snooze\".';
  }

  @override
  String get generalTextImmediately => 'Immédiatement';

  @override
  String get generalTextPercentage => 'Pourcentage';

  @override
  String get generalTextSwitchoffprewarning => 'Préavis d\'extinction';

  @override
  String get generalDescriptionSwitchoffprewarning =>
      'Diminution lente jusqu\'à la luminosité minimale';

  @override
  String get generalDescriptionOffdelay =>
      'L\'appareil s\'allume lorsque la tension de commande est appliquée. Si la tension de commande est interrompue, le délai commence à s\'écouler, après quoi l\'appareil s\'éteint. L\'appareil peut être mis en marche en aval pendant le délai.';

  @override
  String get generalDescriptionBrightness =>
      'La luminosité à laquelle la lampe est allumée par le variateur.';

  @override
  String get generalDescriptionRemainingbrightness =>
      'La valeur de variation en pourcentage à laquelle la lampe est variée après la désactivation du détecteur de mouvement.';

  @override
  String get generalDescriptionRuntime =>
      'Durée de fonctionnement de la fonction d\'alarme lumineuse, de la luminosité minimale à la luminosité maximale.';

  @override
  String get generalTextUniversalbutton => 'Bouton-poussoir universel';

  @override
  String get generalTextDirectionalbutton => 'Bouton directionnel';

  @override
  String get eud12DescriptionAuto =>
      'Détection automatique UT/RT (avec diode RTD)';

  @override
  String get eud12DescriptionRt => 'avec diode de détection directionnelle RTD';

  @override
  String get generalTextProgram => 'Programme';

  @override
  String get eud12MotionsensorOff =>
      'Lorsque le détecteur de mouvement est réglé sur Off';

  @override
  String get eud12ClockmodeTitleProgramze => 'Programme et central On';

  @override
  String get eud12ClockmodeTitleProgramza => 'Programme et arrêt central';

  @override
  String get eud12ClockmodeTitleProgrambuttonon => 'Programme et UT/RT On';

  @override
  String get eud12ClockmodeTitleProgrambuttonoff => 'Programme et UT/RT Off';

  @override
  String get eud12TiImpulseTitle => 'Temps d\'impulsion On (t1)';

  @override
  String get eud12TiImpulseHeader =>
      'Valeur de variation Temps d\'impulsion On';

  @override
  String get eud12TiImpulseDescription =>
      'Valeur de variation en pourcentage à laquelle la lampe est variée lors de l´impulsion ON.';

  @override
  String get eud12TiOffTitle => 'Temps d\'impulsion Off (t2)';

  @override
  String get eud12TiOffHeader => 'Valeur de variation Temps d\'impulsion Arrêt';

  @override
  String get eud12TiOffDescription =>
      'La valeur de variation en pourcentage à laquelle la lampe est variée lors de l\'impulsion OFF.';

  @override
  String get generalTextButtonpermanentlight =>
      'Bouton-poussoir pour lumière permanente';

  @override
  String get generalDescriptionButtonpermanentlight =>
      'Réglage de l\'éclairage continu par bouton-poussoir de 0 à 10 heures par incréments de 0,5 heure. Activation en appuyant sur le bouton pendant plus d\'une seconde (1x clignotement), désactivation en appuyant sur le bouton pendant plus de 2 secondes.';

  @override
  String get generalTextNobuttonpermanentlight => 'Pas de TSP';

  @override
  String get generalTextBasicsettings => 'Paramètres de base';

  @override
  String get generalTextInputswitch => 'Entrée bouton local (A1)';

  @override
  String get generalTextOperationmode => 'Mode de fonctionnement';

  @override
  String get generalTextDimvalue => 'Comportement d´allumage';

  @override
  String get eud12TitleUsememory => 'Utiliser la valeur de mémoire';

  @override
  String get eud12DescriptionUsememory =>
      'La valeur de mémoire correspond à la dernière valeur de variation réglée. Si la valeur mémoire est désactivée, la variation est toujours réglée sur la valeur d\'enclenchement.';

  @override
  String get generalTextStartup => 'Luminosité à l\'allumage';

  @override
  String get generalDescriptionSwitchonvalue =>
      'La valeur d\'enclenchement est une valeur de luminosité réglable qui garantit un allumage sûr.';

  @override
  String get generalTitleSwitchontime => 'Durée de mise en service';

  @override
  String get generalDescriptionSwitchontime =>
      'Une fois le temps d\'allumage écoulé, la lampe passe de la valeur d\'allumage à la valeur de mémorisation.';

  @override
  String get generalDescriptionStartup =>
      'Certaines lampes LED nécessitent un courant d\'appel plus élevé pour s\'allumer de manière fiable. La lampe est allumée à cette valeur d\'allumage, puis réduite à la valeur de mémoire après le temps d\'allumage.';

  @override
  String get eud12ClockmodeSubtitleProgramze => 'Court clic sur Central On';

  @override
  String get eud12ClockmodeSubtitleProgramza =>
      'Clic court sur l\'arrêt central';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonon =>
      'Double-clic sur le bouton universel/le bouton de direction Activé';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonoff =>
      'Double-clic sur le bouton universel/le bouton de direction Désactivé';

  @override
  String get eud12FunctionStairlighttimeswitchTitleShort =>
      'TLZ | Minuterie d\'éclairage d\'escalier';

  @override
  String get eud12FunctionMinTitleShort => 'MIN';

  @override
  String get eud12FunctionMmxTitleShort => 'MMX';

  @override
  String get eud12FunctionTiDescription =>
      'Minuterie avec temps d\'allumage et d\'extinction réglable de 0,5 seconde à 9,9 minutes. La luminosité peut être réglée de la luminosité minimale à la luminosité maximale.';

  @override
  String get eud12FunctionAutoDescription =>
      'Variateur universel avec réglage du détecteur de mouvement, de l\'alarme lumineuse et de la fonction \"snooze\".';

  @override
  String get eud12FunctionErDescription =>
      'Relais de commutation, la luminosité peut être réglée de la luminosité minimale à la luminosité maximale.';

  @override
  String get eud12FunctionEsvDescription =>
      'Variateur universel avec réglage d\'un délai d\'extinction de 1 à 120 minutes. Préavis d\'extinction à la fin par diminution de l\'intensité lumineuse sélectionnable et réglable de 1 à 3 minutes. Les deux entrées centrales sont actives.';

  @override
  String get eud12FunctionTlzDescription =>
      'Réglage de la durée d\'éclairage du bouton de 0 à 10 heures par incréments de 0,5 heure. Activation en appuyant sur le bouton pendant plus d\'une seconde (1x clignotement), désactivation en appuyant sur le bouton pendant plus de 2 secondes.';

  @override
  String get eud12FunctionMinDescription =>
      'Variateur de lumière universel, qui se met à la luminosité minimale réglée lorsque la tension de commande est appliquée. La lumière est atténuée jusqu\'à la luminosité maximale pendant la durée de variation réglée de 1 à 120 minutes. Lorsque la tension de commande est supprimée, la lumière s\'éteint immédiatement, même pendant le temps de variation. Les deux entrées centrales sont actives.';

  @override
  String get eud12FunctionMmxDescription =>
      'Le variateur universel permet de régler la luminosité minimale lorsque la tension de commande est appliquée. Pendant la durée de variation programmée de 1 à 120 minutes, la lumière est atténuée jusqu\'à la luminosité maximale. Toutefois, lorsque la tension de commande est supprimée, le variateur descend à la luminosité minimale réglée. Il s\'éteint ensuite. Les deux entrées centrales sont actives.';

  @override
  String get motionsensorSubtitleNoremainingbrightness =>
      'Lorsque le détecteur de mouvement est réglé sur Off';

  @override
  String get motionsensorSubtitleAlwaysremainingbrightness =>
      'Lorsque le détecteur de mouvement est réglé sur Off';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogram =>
      'Programme de commutation activé et désactivé avec BWM désactivé';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogramandzea =>
      'Central On active le détecteur de mouvement, Central Off désactive le détecteur de mouvement, ainsi que par le programme de commutation.';

  @override
  String get motionsensorSubtitleNoremainingbrightnessauto =>
      'Le détecteur de mouvement s\'éteint uniquement';

  @override
  String get detailsDimsectionHeader => 'Variation';

  @override
  String get generalTextFast => 'Rapide';

  @override
  String get generalTextSlow => 'Lentement';

  @override
  String get eud12TextDimspeed => 'Vitesse de variation';

  @override
  String get eud12TextSwitchonspeed => 'Vitesse d\'allumage';

  @override
  String get eud12TextSwitchoffspeed => 'Vitesse d\'extinction';

  @override
  String get eud12DescriptionDimspeed =>
      'La vitesse de variation est la vitesse à laquelle le variateur passe de la luminosité actuelle à la luminosité cible.';

  @override
  String get eud12DescriptionSwitchonspeed =>
      'La vitesse d\'enclenchement est la vitesse dont le variateur a besoin pour s\'allumer complètement.';

  @override
  String get eud12DescriptionSwitchoffspeed =>
      'La vitesse d\'extinction est la vitesse dont le variateur a besoin pour s\'éteindre complètement.';

  @override
  String get settingsFactoryresetResetdimHeader =>
      'Réinitialisation des paramètres de variation';

  @override
  String get settingsFactoryresetResetdimDescription =>
      'Faut-il vraiment réinitialiser tous les paramètres de variation ?';

  @override
  String get settingsFactoryresetResetdimConfirmationDescription =>
      'Les paramètres de variation ont été réinitialisés avec succès';

  @override
  String get eud12TextSwitchonoffspeed => 'Vitesse de marche/arrêt';

  @override
  String get eud12DescriptionSwitchonoffspeed =>
      'La vitesse d\'allumage et d\'extinction est la vitesse dont le variateur a besoin pour s\'allumer ou s\'éteindre complètement.';

  @override
  String get timerDetailsDimtoval => 'Allumé avec valeur de variation en %';

  @override
  String get timerDetailsDimtovalDescription =>
      'Le variateur s\'allume toujours avec la valeur de variation fixe en %.';

  @override
  String timerDetailsDimtovalSubtitle(Object brightness) {
    return 'Allumer avec $brightness%';
  }

  @override
  String get timerDetailsDimtomem => 'Activé avec la valeur de la mémoire';

  @override
  String get timerDetailsDimtomemSubtitle =>
      'Mise en marche avec valeur de mémoire';

  @override
  String get timerDetailsMotionsensorwithremainingbrightness =>
      'Luminosité résiduelle (BWM) Activé';

  @override
  String get timerDetailsMotionsensornoremainingbrightness =>
      'Luminosité résiduelle (BWM) Désactivé';

  @override
  String get settingsRandommodeHint =>
      'Lorsque le mode aléatoire est activé, tous les temps de commutation du canal sont décalés de manière aléatoire. Avec des heures d\'allumage jusqu\'à 15 minutes plus tôt et des heures d\'arrêt jusqu\'à 15 minutes plus tard.';

  @override
  String get runtimeOffsetDescription =>
      'Dépassement supplémentaire, après la fin du temps de trajet';

  @override
  String get loadingTextDimvalue => 'La valeur de variation est chargée';

  @override
  String get discoveryEudipmDescription => 'Télévariateur universel IP Matter';

  @override
  String get generalTextOffset => 'Dépassement';

  @override
  String get eud12DimvalueTestText => 'Envoyer la luminosité';

  @override
  String get eud12DimvalueTestDescription =>
      'La vitesse de variation actuellement réglée est prise en compte lors des tests.';

  @override
  String get eud12DimvalueLoadText => 'Charger la luminosité';

  @override
  String get settingsDatetimeNotime =>
      'Les réglages de la date et de l\'heure doivent être lus sur l\'écran de l\'appareil.';

  @override
  String get generalMatterText => 'Matter';

  @override
  String get generalMatterMessage =>
      'Veuillez appairer votre appareil Matter à l\'aide de l\'une des applications suivantes.';

  @override
  String get generalMatterOpengooglehome => 'Ouvrir Google Home';

  @override
  String get generalMatterOpenamazonalexa => 'Ouvrir Amazon Alexa';

  @override
  String get generalMatterOpensmartthings => 'Ouvrir SmartThings';

  @override
  String generalLabelProgram(Object number) {
    return 'Programme $number';
  }

  @override
  String get generalTextDone => 'Terminé';

  @override
  String get settingsRandommodeDescriptionShort =>
      'Lorsque le mode aléatoire est activé, tous les programmes de ce canal sont décalés de manière aléatoire jusqu\'à 15 minutes. Les programmes d´allumage sont décalés à l\'avance, les programmes d´extinction sont retardés.';

  @override
  String get all => 'Tous';

  @override
  String get discoveryBluetooth => 'Bluetooth';

  @override
  String get success => 'Succès';

  @override
  String get error => 'Erreur';

  @override
  String get timeProgramAdd => 'Ajouter un programme horaire';

  @override
  String get noConnection => 'Pas de connexion';

  @override
  String get timeProgramOnlyActive => 'Programmes configurés';

  @override
  String get timeProgramAll => 'Tous les programmes';

  @override
  String get active => 'Actif';

  @override
  String get inactive => 'Inactif';

  @override
  String timeProgramSaved(Object number) {
    return 'Programme $number enregistré';
  }

  @override
  String get deviceLanguageSaved => 'Langue de l\'appareil sauvegardée';

  @override
  String generalTextTimeShort(Object time) {
    return '$time';
  }

  @override
  String programDeleteHint(Object index) {
    return 'Le programme $index doit-il vraiment être supprimé ?';
  }

  @override
  String milliseconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Millisecondes',
      one: 'Milliseconde',
    );
    return '$_temp0';
  }

  @override
  String millisecondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Millisecondes',
      one: '$count Milliseconde',
    );
    return '$_temp0';
  }

  @override
  String secondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Secondes',
      one: '$count Seconde',
    );
    return '$_temp0';
  }

  @override
  String minutesWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Minutes',
      one: '$count Minute',
    );
    return '$_temp0';
  }

  @override
  String hoursWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Heures',
      one: '$count Heure',
    );
    return '$_temp0';
  }

  @override
  String get settingsPinFailEmpty => 'Le code PIN ne doit pas être vide';

  @override
  String get detailsConfigurationWifiloginScanNoMatch =>
      'Le code scanné ne correspond pas à l\'appareil';

  @override
  String get wifiAuthorizationPopIsEmpty => 'Le PoP ne peut pas être vide';

  @override
  String get wifiAuthenticationCredentialsHint =>
      'Comme l\'application ne peut pas accéder à votre mot de passe Wi-Fi privé, il n\'est pas possible de vérifier l\'exactitude de la saisie. Si aucune connexion n\'est établie, vérifiez le mot de passe et saisissez-le à nouveau.';

  @override
  String get generalMatterOpenApplehome => 'Ouvrir Apple Home';

  @override
  String get timeProgramNoActive => 'Pas de programmes configurés';

  @override
  String get timeProgramNoEmpty => 'Pas de programme temporel libre disponible';

  @override
  String get nameOfConfiguration => 'Nom de la configuration';

  @override
  String get currentDevice => 'Appareil actuel';

  @override
  String get export => 'Exporter';

  @override
  String get import => 'Importer';

  @override
  String get savedConfigurations => 'Configurations enregistrées';

  @override
  String get importableServicesLabel =>
      'Les paramètres suivants peuvent être importés :';

  @override
  String get notImportableServicesLabel => 'Paramètres incompatibles';

  @override
  String get deviceCategoryMeterGateway => 'Passerelle pour compteurs';

  @override
  String get deviceCategory2ChannelTimeSwitch => 'Horloge à 2 canaux';

  @override
  String get devicategoryOutdoorTimeSwitchBluetooth =>
      'Horloge extérieure Bluetooth';

  @override
  String get settingsModbusHeader => 'Modbus';

  @override
  String get settingsModbusDescription =>
      'Réglez le débit en bauds, la parité et le délai d\'attente pour configurer la vitesse de transmission, la détection des erreurs et le temps d\'attente.';

  @override
  String get settingsModbusRTU => 'Modbus RTU';

  @override
  String get settingsModbusBaudrate => 'Débit en bauds';

  @override
  String get settingsModbusParity => 'Parité';

  @override
  String get settingsModbusTimeout => 'Délai d\'attente Modbus';

  @override
  String get locationServiceDisabled => 'La localisation est désactivée';

  @override
  String get locationPermissionDenied =>
      'Veuillez autoriser la localisation à demander votre position actuelle.';

  @override
  String get locationPermissionDeniedPermanently =>
      'Les autorisations de localisation sont refusées en permanence. Veuillez autoriser l\'autorisation de localisation dans les paramètres de votre appareil pour demander votre position actuelle.';

  @override
  String get lastSync => 'Dernière synchronisation';

  @override
  String get dhcpActive => 'DHCP actif';

  @override
  String get ipAddress => 'IP';

  @override
  String get subnetMask => 'Masque de sous-réseau';

  @override
  String get standardGateway => 'Passerelle par défaut';

  @override
  String get dns => 'DNS';

  @override
  String get alternateDNS => 'DNS alternatif';

  @override
  String get errorNoNetworksFound => 'Pas de réseau wifi trouvé';

  @override
  String get availableNetworks => 'Réseaux disponibles';

  @override
  String get enableWifiInterface => 'Activer l\'interface WiFi';

  @override
  String get enableLANInterface => 'Activer l\'interface LAN';

  @override
  String get hintDontDisableAllInterfaces =>
      'Assurez-vous que toutes les interfaces ne sont pas désactivées. La dernière interface activée est prioritaire.';

  @override
  String get ssid => 'SSID';

  @override
  String get searchNetworks => 'Recherche de réseau wifi';

  @override
  String get errorNoNetworkEnabled => 'Au moins une interface doit être active';

  @override
  String get errorActiveNetworkInvalid =>
      'Toutes les stations actives ne sont pas valides';

  @override
  String get invalidNetworkConfiguration => 'Configuration réseau invalide';

  @override
  String get generalDefault => 'Défaut';

  @override
  String get mqttHeader => 'MQTT';

  @override
  String get mqttConnected => 'Connecté au courtier MQTT';

  @override
  String get mqttDisconnected => 'Pas de connexion au courtier MQTT';

  @override
  String get mqttBrokerURI => 'URI du courtier';

  @override
  String get mqttBrokerURIHint => 'URI du courtier MQTT';

  @override
  String get mqttPort => 'Port';

  @override
  String get mqttPortHint => 'Port MQTT';

  @override
  String get mqttClientId => 'Client-ID';

  @override
  String get mqttClientIdHint => 'MQTT Client-ID';

  @override
  String get mqttUsername => 'Nom d\'utilisateur';

  @override
  String get mqttUsernameHint => 'Nom d\'utilisateur MQTT';

  @override
  String get mqttPassword => 'Mot de passe';

  @override
  String get mqttPasswordHint => 'Mot de passe MQTT';

  @override
  String get mqttCertificate => 'Certificat';

  @override
  String get mqttCertificateHint => 'Certificat MQTT';

  @override
  String get mqttTopic => 'Sujet';

  @override
  String get mqttTopicHint => 'Sujet MQTT';

  @override
  String get electricityMeter => 'Compteur d\'électricité';

  @override
  String get electricityMeterCurrent => 'Actuel';

  @override
  String get electricityMeterHistory => 'Historique';

  @override
  String get electricityMeterReading => 'Relevé de compteur';

  @override
  String get connectivity => 'Connectivité';

  @override
  String electricMeter(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Compteurs',
      one: 'Compteurs',
    );
    return '$_temp0';
  }

  @override
  String get discoveryZGW16Description => 'Passerelle Modbus-compteurs-MQTT';

  @override
  String get bluetoothConnectionLost => 'Perte de la connexion Bluetooth';

  @override
  String get bluetoothConnectionLostDescription =>
      'La connexion Bluetooth avec l\'appareil a été perdue. Veuillez vérifier la connexion à l\'appareil.';

  @override
  String get openBluetoothSettings => 'Ouvrir les paramètres Bluetooth';

  @override
  String get password => 'Mot de passe';

  @override
  String get setInitialPassword => 'Définir le mot de passe initial';

  @override
  String initialPasswordMinimumLength(Object length) {
    return 'Le mot de passe doit comporter au moins $length caractères.';
  }

  @override
  String get repeatPassword => 'Répéter le mot de passe';

  @override
  String get passwordsDoNotMatch => 'Les mots de passe ne correspondent pas';

  @override
  String get savePassword => 'Sauvegarder le mot de passe';

  @override
  String get savePasswordHint =>
      'Le mot de passe est enregistré pour les connexions futures sur votre appareil.';

  @override
  String get retrieveNtpServer => 'Récupérer l\'heure du serveur NTP';

  @override
  String get retrieveNtpServerFailed =>
      'La connexion au serveur NTP n\'a pas pu être établie.';

  @override
  String get retrieveNtpServerSuccess =>
      'La connexion au serveur NTP a été établie avec succès.';

  @override
  String get settingsPasswordNewPasswordDescription =>
      'Saisir le nouveau mot de passe';

  @override
  String get settingsPasswordConfirmationDescription =>
      'Changement de mot de passe réussi';

  @override
  String get dhcpRangeStart => 'Début de la plage DHCP';

  @override
  String get dhcpRangeEnd => 'Fin de la plage DHCP';

  @override
  String get forwardOnMQTT => 'Transférer vers MQTT';

  @override
  String get showAll => 'Afficher tout';

  @override
  String get hide => 'Cacher';

  @override
  String get changeToAPMode => 'Passer en mode AP';

  @override
  String get changeToAPModeDescription =>
      'Vous êtes sur le point de connecter votre appareil à un réseau WiFi, auquel cas la connexion à l\'appareil est déconnectée et vous devez vous reconnecter à votre appareil via le réseau configuré.';

  @override
  String get consumption => 'Consommation';

  @override
  String get currentDay => 'Journée en cours';

  @override
  String get twoWeeks => '2 semaines';

  @override
  String get oneYear => '1 an';

  @override
  String get threeYears => '3 ans';

  @override
  String passwordMinLength(Object length) {
    return 'Le mot de passe doit comporter au moins $length caractères.';
  }

  @override
  String get passwordNeedsLetter => 'Le mot de passe doit contenir une lettre.';

  @override
  String get passwordNeedsNumber => 'Le mot de passe doit contenir un chiffre.';

  @override
  String get portEmpty => 'Le port ne peut pas être vide';

  @override
  String get portInvalid => 'Port non valide';

  @override
  String portOutOfRange(Object rangeEnd, Object rangeStart) {
    return 'Le port doit être compris entre $rangeStart et $rangeEnd.';
  }

  @override
  String get ipAddressEmpty => 'L\'adresse IP ne peut pas être vide';

  @override
  String get ipAddressInvalid => 'Adresse IP invalide';

  @override
  String get subnetMaskEmpty =>
      'Le masque de sous-réseau ne peut pas être vide';

  @override
  String get subnetMaskInvalid => 'Masque de sous-réseau non valide';

  @override
  String get gatewayEmpty => 'La passerelle ne peut pas être vide';

  @override
  String get gatewayInvalid => 'Passerelle invalide';

  @override
  String get dnsEmpty => 'Le DNS ne peut pas être vide';

  @override
  String get dnsInvalid => 'DNS invalide';

  @override
  String get uriEmpty => 'L\'URI ne peut pas être vide';

  @override
  String get uriInvalid => 'URI non valide';

  @override
  String get electricityMeterChangedSuccessfully =>
      'Changement de compteur d\'électricité réussi';

  @override
  String get networkChangedSuccessfully =>
      'La configuration du réseau a été modifiée avec succès';

  @override
  String get mqttChangedSuccessfully =>
      'La configuration MQTT a été modifiée avec succès';

  @override
  String get modbusChangedSuccessfully =>
      'Les paramètres Modbus ont été modifiés avec succès';

  @override
  String get loginData => 'Supprimer les données de connexion';

  @override
  String get valueConfigured => 'Configuré';

  @override
  String get electricityMeterHistoryNoData => 'Pas de données disponibles';

  @override
  String get locationChangedSuccessfully => 'Changement de lieu réussi';

  @override
  String get settingsNameFailEmpty => 'Le nom ne peut être vide';

  @override
  String settingsNameFailLength(Object length) {
    return 'Le nom ne peut pas comporter plus de $length caractères.';
  }

  @override
  String get solsticeChangedSuccesfully =>
      'Les paramètres du solstice ont été modifiés avec succès';

  @override
  String get relayFunctionChangedSuccesfully =>
      'Fonction relais modifiée avec succès';

  @override
  String get relayFunctionHeader => 'Fonction relais';

  @override
  String get dimmerValueChangedSuccesfully =>
      'Le comportement d´allumage a été modifié avec succès';

  @override
  String get dimmerBehaviourChangedSuccesfully =>
      'Modification réussie du comportement de variation';

  @override
  String get dimmerBrightnessDescription =>
      'La luminosité minimale et maximale affecte toutes les luminosités réglables du variateur.';

  @override
  String get dimmerSettingsChangedSuccesfully =>
      'Modification réussie des paramètres de base';

  @override
  String get liveUpdateEnabled => 'Test en direct activé';

  @override
  String get liveUpdateDisabled => 'Test en direct désactivé';

  @override
  String get liveUpdateDescription =>
      'La dernière valeur modifiée du curseur sera envoyée à l\'appareil.';

  @override
  String get demoDevices => 'Dispositifs de démonstration';

  @override
  String get showDemoDevices => 'Montrer des appareils de démonstration';

  @override
  String get deviceCategoryTimeSwitch => 'Horloge programmable';

  @override
  String get deviceCategoryMultifunctionalRelay => 'Relais multifonction';

  @override
  String get deviceCategoryDimmer => 'Variateur';

  @override
  String get deviceCategoryShutter => 'Actionneur pour ombrage';

  @override
  String get deviceCategoryRelay => 'Relais';

  @override
  String get search => 'Recherche';

  @override
  String get configurationsHeader => 'Configurations';

  @override
  String get configurationsDescription => 'Gérez vos configurations ici.';

  @override
  String get configurationsNameFailEmpty =>
      'Le nom de la configuration ne peut pas être vide';

  @override
  String get configurationDeleted => 'Configuration supprimée';

  @override
  String codeFound(Object codeType) {
    return '$codeType code détecté';
  }

  @override
  String get errorCameraPermission =>
      'Veuillez autoriser la caméra à scanner le code ELTAKO.';

  @override
  String get authorizationSuccessful => 'Autorisé avec succès sur l\'appareil';

  @override
  String get wifiAuthenticationResetConfirmationDescription =>
      'L\'appareil est maintenant prêt à recevoir une nouvelle autorisation.';

  @override
  String get settingsResetConnectionHeader => 'Réinitialiser la connexion';

  @override
  String get settingsResetConnectionDescription =>
      'Voulez-vous vraiment réinitialiser la connexion ?';

  @override
  String get settingsResetConnectionConfirmationDescription =>
      'La connexion a été réinitialisée avec succès.';

  @override
  String get wiredInputChangedSuccesfully =>
      'Modification réussie du comportement du bouton-poussoir';

  @override
  String get runtimeChangedSuccesfully =>
      'Le temps d\'exécution a été modifié avec succès';

  @override
  String get expertModeActivated => 'Mode expert activé';

  @override
  String get expertModeDeactivated => 'Mode expert désactivé';

  @override
  String get license => 'Licence';

  @override
  String get retry => 'Réessayer';

  @override
  String get provisioningConnectingHint =>
      'La connexion de l\'appareil est en cours d\'établissement. Cela peut prendre jusqu\'à 1 minute.';

  @override
  String get serialnumberEmpty => 'Le numéro de série ne peut pas être vide';

  @override
  String get interfaceStateInactiveDescriptionBLE =>
      'Bluetooth est désactivé, veuillez l\'activer pour découvrir les appareils Bluetooth.';

  @override
  String get interfaceStateDeniedDescriptionBLE =>
      'Les autorisations Bluetooth n\'ont pas été accordées.';

  @override
  String get interfaceStatePermanentDeniedDescriptionBLE =>
      'Les autorisations Bluetooth n\'ont pas été accordées. Veuillez les activer dans les paramètres de votre appareil.';

  @override
  String get requestPermission => 'Demande d\'autorisation';

  @override
  String get goToSettings => 'Aller dans les paramètres';

  @override
  String get enableBluetooth => 'Activer le bluetooth';

  @override
  String get installed => 'Installé';

  @override
  String teachInDialogDescription(Object type) {
    return 'Souhaitez-vous appairer votre appareil via $type ?';
  }

  @override
  String get useMatter => 'Utiliser Matter';

  @override
  String get relayMode => 'Activer le mode relais';

  @override
  String get whatsNew => 'Nouveau dans cette version';

  @override
  String get migrationHint =>
      'Une migration est nécessaire pour utiliser les nouvelles fonctionnalités.';

  @override
  String get migrationHeader => 'Migration';

  @override
  String get migrationProgress => 'Migration en cours...';

  @override
  String get letsGo => 'Allons-y !';

  @override
  String get noDevicesFound =>
      'Aucun appareil trouvé. Vérifiez si votre appareil est en mode d´appairage.';

  @override
  String get interfaceStateEmpty => 'Aucun dispositif n\'a été trouvé';

  @override
  String get ssidEmpty => 'Le SSID ne peut pas être vide';

  @override
  String get passwordEmpty => 'Le mot de passe ne peut pas être vide';

  @override
  String get settingsDeleteSettingsHeader => 'Réinitialiser les paramètres';

  @override
  String get settingsDeleteSettingsDescription =>
      'Voulez-vous vraiment réinitialiser tous les paramètres ?';

  @override
  String get settingsDeleteSettingsConfirmationDescription =>
      'Tous les paramètres ont été réinitialisés avec succès.';

  @override
  String get locationNotFound => 'Emplacement non trouvé';

  @override
  String get timerProgramEmptySaveHint =>
      'Le programme horaire est vide. Voulez-vous annuler l\'édition ?';

  @override
  String get timerProgramDaysEmptySaveHint =>
      'Aucun jour n\'est sélectionné. Voulez-vous quand même enregistrer le programme horaire ?';

  @override
  String get timeProgramNoDays => 'Au moins un jour doit être activé';

  @override
  String timeProgramColliding(Object program) {
    return 'Le programme horaire entre en collision avec le programme $program.';
  }

  @override
  String timeProgramDuplicated(Object program) {
    return 'Le programme horaire est une copie du programme $program.';
  }

  @override
  String get screenshotZgw16 => 'Maison individuelle';

  @override
  String get interfaceStateUnknown => 'Aucun appareil n\'a été trouvé';

  @override
  String get settingsPinChange => 'Modifier le code PIN';

  @override
  String get timeProgrammOneTime => 'unique';

  @override
  String get timeProgrammRepeating => 'répétitif';

  @override
  String get generalIgnore => 'Ignorer';

  @override
  String get timeProgramChooseDay => 'Choisir le jour';

  @override
  String get generalToday => 'Aujourd\'hui';

  @override
  String get generalTomorrow => 'Demain';

  @override
  String get bluetoothAndPINChangedSuccessfully =>
      'Bluetooth et code PIN modifiés avec succès';

  @override
  String get generalTextDimTime => 'Durée de la variation';

  @override
  String get discoverySu62Description =>
      'Horloge programmable à 1 canal Bluetooth';

  @override
  String get bluetoothAlwaysOnTitle => 'Marche continue';

  @override
  String get bluetoothAlwaysOnDescription =>
      'Bluetooth est activé en permanence.';

  @override
  String get bluetoothAlwaysOnHint =>
      'Remarque : si ce paramètre est activé, l\'appareil est visible en permanence pour tout le monde via Bluetooth ! Il est recommandé de modifier le code PIN par défaut.';

  @override
  String get bluetoothManualStartupOnTitle => 'Allumage temporaire';

  @override
  String get bluetoothManualStartupOnDescription =>
      'Après la mise sous tension, le Bluetooth est activé pendant 3 minutes.';

  @override
  String get bluetoothManualStartupOnHint =>
      'Remarque : la fonction « prêt à appairer » est activée pendant 3 minutes et s\'éteint ensuite. Si une nouvelle connexion doit être établie, le bouton doit être maintenu enfoncé pendant environ 5 secondes.';

  @override
  String get bluetoothManualStartupOffTitle => 'Allumage manuel';

  @override
  String get bluetoothManualStartupOffDescription =>
      'Le Bluetooth est activé manuellement à l\'aide du bouton et reste actif pendant 3 minutes.';

  @override
  String get bluetoothManualStartupOffHint =>
      'Remarque : pour activer le Bluetooth, il faut maintenir le bouton de l\'entrée de bouton-poussoir enfoncé pendant environ 5 secondes.';

  @override
  String get timeProgrammOneTimeRepeatingDescription =>
      'Les programmes peuvent soit être exécutés de manière répétée, en exécutant toujours une commutation aux jours et heures configurés, soit être exécutés une seule fois à l\'heure de commutation configurée.';

  @override
  String versionHeader(Object version) {
    return 'Version $version';
  }

  @override
  String get releaseNotesHeader => 'Notes de mise à jour';

  @override
  String get release30Header =>
      'La nouvelle application Eltako Connect est arrivée !';

  @override
  String get release30FeatureDesignHeader => 'Nouvelle conception';

  @override
  String get release30FeatureDesignDescription =>
      'L\'application a été entièrement revue et bénéficie d\'un nouveau design. Elle est désormais encore plus facile et plus intuitive à utiliser.';

  @override
  String get release30FeaturePerformanceHeader =>
      'Amélioration des performances';

  @override
  String get release30FeaturePerformanceDescription =>
      'Profitez d\'une expérience plus fluide et de temps de chargement réduits - pour une expérience utilisateur sans heurts.';

  @override
  String get release30FeatureConfigurationHeader =>
      'Configurations inter-appareils';

  @override
  String get release30FeatureConfigurationDescription =>
      'Sauvegarder les configurations des appareils et les transférer à d\'autres appareils. Même avec des produits différents, vous pouvez, par exemple, transférer la configuration de votre S2U12DBT1+1-UC vers un ASSU-BT ou vice versa.';

  @override
  String get release31Header =>
      'La nouvelle horloge programmable encastrée à 1 canal avec Bluetooth est arrivée !';

  @override
  String get release31Description => 'Que peut faire le SU62PF-BT/UC ?';

  @override
  String get release31SU62Name => 'SU62PF-BT/UC';

  @override
  String get release31DeviceNote1 => 'Jusqu\'à 60 programmes horaires.';

  @override
  String get release31DeviceNote2 =>
      'Fonction astro : L\'horloge change d\'appareil en fonction du lever et du coucher du soleil.';

  @override
  String get release31DeviceNote3 =>
      'Mode aléatoire : les heures de commutation peuvent être décalées de manière aléatoire jusqu\'à 15 minutes.';

  @override
  String get release31DeviceNote4 =>
      'Passage à l\'heure d\'été ou d\'hiver : L\'horloge passe automatiquement à l\'heure d\'été ou à l\'heure d\'hiver.';

  @override
  String get release31DeviceNote5 =>
      'Tension universelle d\'alimentation et de contrôle 12-230V UC.';

  @override
  String get release31DeviceNote6 =>
      'Entrée de bouton-poussoir pour la commutation manuelle.';

  @override
  String get release31DeviceNote7 =>
      '1 contact NO sans potentiel 10 A/250 V AC.';

  @override
  String get release31DeviceNote8 => 'Exécution unique de programmes horaires.';

  @override
  String get generalNew => 'Nouveau';

  @override
  String yearsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Depuis $count années',
      one: 'Année dernière',
    );
    return '$_temp0';
  }

  @override
  String monthsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Depuis $count Mois',
      one: 'Mois dernier',
    );
    return '$_temp0';
  }

  @override
  String weeksAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Depuis $count semaines',
      one: 'Semaine dernière',
    );
    return '$_temp0';
  }

  @override
  String daysAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Depuis $count jours',
      one: 'Hier',
    );
    return '$_temp0';
  }

  @override
  String minutesAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Depuis $count minutes',
      one: 'depuis une minute',
    );
    return '$_temp0';
  }

  @override
  String hoursAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Depuis $count heures',
      one: 'Depuis une heure',
    );
    return '$_temp0';
  }

  @override
  String secondsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Depuis $count secondes',
      one: 'Depuis une seconde',
    );
    return '$_temp0';
  }

  @override
  String get justNow => 'À l´instant';

  @override
  String get discoveryEsripmDescription => 'Télérupteur IP Matter';

  @override
  String get generalTextKidsRoom => 'Fonction chambre d\'enfant';

  @override
  String generalDescriptionKidsRoom(Object mode) {
    return 'Lors de l\'allumage en appuyant longuement sur le bouton ($mode), l\'appareil s\'allume après environ 1 seconde avec la luminosité la plus faible et, tant que vous continuez à appuyer sur le bouton, s\'assombrit lentement sans modifier la luminosité enregistré.';
  }

  @override
  String get generalTextSceneButton => 'Bouton de scénario';

  @override
  String get settingsEnOceanConfigHeader => 'Configuration EnOcean';

  @override
  String get enOceanConfigChangedSuccessfully =>
      'La configuration EnOcean a été modifiée avec succès.';

  @override
  String get activateEnOceanRepeater => 'Activer le répétiteur EnOcean';

  @override
  String get enOceanRepeaterLevel => 'Niveau du répétiteur';

  @override
  String get enOceanRepeaterLevel1 => 'Niveau 1';

  @override
  String get enOceanRepeaterLevel2 => 'Niveau 2';

  @override
  String get enOceanRepeaterOffDescription =>
      'Aucun signal radio n\'est reçu des émetteurs.';

  @override
  String get enOceanRepeaterLevel1Description =>
      'Seuls les signaux radio des émetteurs sont reçus, vérifiés et retransmis à pleine puissance d\'émission. Les signaux radio des autres répétiteurs sont ignorés afin de réduire la quantité de données.';

  @override
  String get enOceanRepeaterLevel2Description =>
      'Outre les signaux radio des émetteurs, les signaux radio des répétiteurs de niveau 1 sont également traités. Un signal radio peut donc être reçu et amplifié au maximum deux fois. Les répétiteurs radio n\'ont pas besoin d\'être appairés. Ils reçoivent et amplifient les signaux radio de tous les émetteurs radio présents dans leur zone de réception.';

  @override
  String get settingsSensorHeader => 'Emetteurs';

  @override
  String get sensorChangedSuccessfully =>
      'les émetteurs ont été modifiés avec succès';

  @override
  String get wiredButton => 'Bouton-poussoir filaire';

  @override
  String get enOceanId => 'ID EnOcean';

  @override
  String get enOceanAddManually => 'Saisir ou scanner l\'ID EnOcean';

  @override
  String get enOceanIdInvalid => 'ID EnOcean invalide';

  @override
  String get enOceanAddAutomatically => 'Appairer avec un télégramme EnOcean';

  @override
  String get enOceanAddDescription =>
      'Le protocole radio EnOcean permet d\'appairer des boutons-poussoir dans votre actionneur et de l\'actionner.\n\nChoisissez soit l\'appairage automatique avec télégramme EnOcean pour appairer les boutons en appuyant sur un bouton, soit choisissez la variante manuelle pour scanner ou saisir l\'ID EnOcean de votre bouton.';

  @override
  String get enOceanTelegram => 'Télégramme';

  @override
  String enOceanCodeScan(Object sensorType) {
    return 'Saisissez l\'identifiant EnOcean de votre $sensorType ou scannez le code QR EnOcean de votre $sensorType pour l\'ajouter.';
  }

  @override
  String get enOceanCode => 'Code QR EnOcean';

  @override
  String enOceanCodeScanDescription(Object sensorType) {
    return 'Recherchez le code QR EnOcean sur votre $sensorType et scannez-le avec votre appareil photo.';
  }

  @override
  String get enOceanButton => 'Bouton EnOcean';

  @override
  String get enOceanBackpack => 'Adaptateur EnOcean';

  @override
  String get sensorNotAvailable => 'Aucun bouton n\'a encore été appairé';

  @override
  String get sensorAdd => 'Ajouter un émetteur';

  @override
  String get sensorCancel => 'Annuler l\'appairage';

  @override
  String get sensorCancelDescription =>
      'Voulez-vous vraiment annuler le processus d’appairage ?';

  @override
  String get getEnOceanBackpack => 'Procurez-vous votre adaptateur EnOcean';

  @override
  String get enOceanBackpackMissing =>
      'Pour entrer dans le monde fantastique de la connectivité et de la communication parfaites, il vous faut adaptateur EnOcean !\nCliquez ici pour plus d\'informations';

  @override
  String sensorEditChangedSuccessfully(Object sensorName) {
    return '$sensorName a été modifié avec succès';
  }

  @override
  String sensorConnectedVia(Object deviceName) {
    return 'connecté via $deviceName';
  }

  @override
  String get lastSeen => 'Vu pour la dernière fois';

  @override
  String get setButtonOrientation => 'Établir l\'orientation';

  @override
  String get setButtonType => 'Définir le type de bouton';

  @override
  String get button1Way => 'Bouton 1 canal';

  @override
  String get button2Way => 'Bouton 2 canaux';

  @override
  String get button4Way => 'Bouton 4 canaux';

  @override
  String get buttonUnset => 'Non occupé';

  @override
  String get button => 'Bouton';

  @override
  String get sensor => 'Sonde';

  @override
  String sensorsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count émetteurs trouvés',
      one: '1 émetteur trouvé',
      zero: 'Aucun émetteur trouvé',
    );
    return '$_temp0';
  }

  @override
  String get sensorSearch => 'Rechercher des émetteurs';

  @override
  String get searchAgain => 'Rechercher à nouveau';

  @override
  String sensorTeachInHeader(Object sensorType) {
    return 'Appairer $sensorType';
  }

  @override
  String sensorChooseHeader(Object sensorType) {
    return 'Choisir $sensorType';
  }

  @override
  String get sensorChooseDescription =>
      'Sélectionnez un bouton que vous souhaitez utiliser pour l\'appairage.';

  @override
  String get sensorCategoryDescription =>
      'Sélectionnez une catégorie que vous souhaitez appairer.';

  @override
  String get sensorName => 'Nom du bouton';

  @override
  String get sensorNameFooter => 'Donnez un nom au bouton';

  @override
  String sensorAddedSuccessfully(Object sensorName) {
    return '$sensorName a été appairé avec succès';
  }

  @override
  String sensorDelete(Object sensorType) {
    return 'Supprimer le $sensorType';
  }

  @override
  String sensorDeleteHint(Object sensorName, Object sensorType) {
    return 'Le $sensorType $sensorName doit-il vraiment être supprimé ?';
  }

  @override
  String sensorDeletedSuccessfully(Object sensorName) {
    return '$sensorName a été supprimé avec succès';
  }

  @override
  String get buttonTapDescription =>
      'Appuyez sur le bouton que vous souhaitez appairer.';

  @override
  String get waitingForTelegram => 'L\'actionneur attend le télégramme';

  @override
  String get copied => 'Copié';

  @override
  String pairingFailed(Object sensorType) {
    return '$sensorType déjà appairé';
  }

  @override
  String get generalDescriptionUniversalbutton =>
      'Avec le bouton universel, le sens est inversé en relâchant brièvement le bouton. Des commandes de contrôle courtes s\'activent ou s\'éteignent.';

  @override
  String get generalDescriptionDirectionalbutton =>
      'Le bouton directionnel est « allumer et augmenter l\'intensité » en haut et « éteindre et diminuer l\'intensité » en bas.';

  @override
  String get matterForwardingDescription =>
      'La pression sur le bouton est transmise à Matter.';

  @override
  String get none => 'Aucun';

  @override
  String get buttonNoneDescription => 'Le bouton n\'a aucune fonction.';

  @override
  String get buttonUnsetDescription =>
      'Le bouton n’a aucun comportement défini .';

  @override
  String get sensorButtonTypeChangedSuccessfully =>
      'Le type de bouton a été modifié avec succès';

  @override
  String forExample(Object example) {
    return 'par ex. $example';
  }

  @override
  String get enOceanQRCodeInvalidDescription =>
      'Uniquement possible à partir de la date de production 44/20';

  @override
  String get input => 'Entrée';

  @override
  String get buttonSceneValueOverride =>
      'Remplacer la valeur du bouton de scenario';

  @override
  String get buttonSceneValueOverrideDescription =>
      'La valeur du bouton de scenario est écrasée par la valeur de variation actuelle en maintenant le bouton enfoncé.';

  @override
  String get buttonSceneDescription =>
      'Le bouton de scenario s\'allume avec une valeur de variation fixe';

  @override
  String get buttonPress => 'Pression du bouton';

  @override
  String get triggerOn =>
      'Bouton universel ou bouton directionnel côté allumage';

  @override
  String get triggerOff =>
      'Bouton universel ou bouton de direction côté extinction';

  @override
  String get centralOn => 'Allumage centralisé';

  @override
  String get centralOff => 'Extinction centralisée';

  @override
  String get centralButton => 'Bouton de commande centralisée';

  @override
  String get enOceanAdapterNotFound =>
      'Aucun adaptateur EnOcean n\'a été trouvé';

  @override
  String get updateRequired => 'Mise à jour nécessaire';

  @override
  String get updateRequiredDescription =>
      'Votre application nécessite une mise à jour pour prendre en charge ce nouvel appareil.';

  @override
  String get release32Header =>
      'La nouvelle série 64 avec Matter et EnOcean ainsi que la nouvelle minuterie encastrée Bluetooth SU62PF-BT/UC sont désormais disponibles !';

  @override
  String get release32EUD64Header =>
      'Le nouveau variateur encastré à 1 canal avec Matter over Wi-Fi et jusqu\'à 300W est arrivé !';

  @override
  String get release32EUD64Note1 =>
      'Configuration de la vitesse de variation, de la vitesse d\'allumage et d\'extinction, du mode chambre d\'enfant/sommeil, et bien plus encore.';

  @override
  String get release32EUD64Note2 =>
      'Les fonctionnalités de l\'EUD64NPN-IPM peuvent être étendues grâce à des adaptateurs, tels que l\'adaptateur EnOcean EOA64.';

  @override
  String get release32EUD64Note3 =>
      'Jusqu\'à 30 boutons-poussoir sans fil EnOcean peuvent être directement reliés à l\'EUD64NPN-IPM en combinaison avec l\'adaptateur EnOcean EOA64 et transmis à Matter.';

  @override
  String get release32EUD64Note4 =>
      'Deux entrées de boutons câblés peuvent être directement reliées à l\'EUD64NPN-IPM ou transmises à Matter.';

  @override
  String get release32ESR64Header =>
      'Le nouvel actionneur de commutation à 1 canal, encastré et libre de potentiel, avec communication Wi-Fi et jusqu\'à 16 A, est arrivé !';

  @override
  String get release32ESR64Note1 =>
      'Configuration de diverses fonctions telles que télérupteur (ES), relais (ER), relais normalement fermé (ER-Inverse), et bien d\'autres encore.';

  @override
  String get release32ESR64Note2 =>
      'Les fonctionnalités de l\'ESR64PF-IPM peuvent être étendues grâce à des adaptateurs, tels que l\'adaptateur EnOcean EOA64.';

  @override
  String get release32ESR64Note3 =>
      'Jusqu\'à 30 boutons-poussoir sans fil EnOcean peuvent être directement reliés à l\'ESR64PF-IPM en combinaison avec l\'adaptateur EnOcean EOA64 et transmis à Matter.';

  @override
  String get release32ESR64Note4 =>
      'Un bouton d\'entrée câblé peut être directement relié à l\'ESR64PF-IPM ou transmis à Matter.';

  @override
  String buttonsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count boutons trouvés',
      one: '1 bouton trouvé',
      zero: 'Aucun bouton trouvé',
    );
    return '$_temp0';
  }

  @override
  String get doubleImpuls => 'avec une double impulsion';

  @override
  String get impulseDescription =>
      'Si le canal est activé, une impulsion l\'éteindra.';

  @override
  String get locationServiceEnable => 'Activer le lieu';

  @override
  String get locationServiceDisabledDescription =>
      'L\'emplacement est désactivé. La version de votre système d\'exploitation a besoin de la localisation pour pouvoir trouver les appareils Bluetooth.';

  @override
  String get locationPermissionDeniedNoPosition =>
      'Les autorisations de localisation n\'ont pas été accordées. La version de votre système d\'exploitation nécessite des autorisations de localisation pour pouvoir trouver des appareils Bluetooth. Veuillez autoriser l\'autorisation de localisation dans les paramètres de votre appareil.';

  @override
  String get interfaceStatePermanentDeniedDescriptionDevicesAround =>
      'L\'autorisation pour les appareils à proximité n\'a pas été accordée. Veuillez activer cette autorisation dans les paramètres de votre appareil.';

  @override
  String get permissionNearbyDevices => 'Appareils à proximité';

  @override
  String get release320Header =>
      'Le nouveau télévariateur universel EUD12NPN-BT/600W-230V, plus puissant, est arrivé !';

  @override
  String get release320EUD600Header =>
      'Que peut faire le nouveau télévariateur universel ?';

  @override
  String get release320EUD600Note1 =>
      'Télévariateur universel d\'une puissance maximale de 600W';

  @override
  String get release320EUD600Note2 =>
      'Extensible avec l\'extension de puissance LUD12 jusqu\'à 3800W';

  @override
  String get release320EUD600Note3 =>
      'Commande locale avec boutons-poussoirs universels ou directionnels';

  @override
  String get release320EUD600Note4 =>
      'Fonctions centrales allumage / extinction';

  @override
  String get release320EUD600Note5 =>
      'Borne pour détecteur de mouvement pour plus de commodité';

  @override
  String get release320EUD600Note6 =>
      'Horloge intégrée avec 10 programmes de commutation';

  @override
  String get release320EUD600Note7 => 'Fonction astro';

  @override
  String get release320EUD600Note8 => 'Luminosité individuelle à l\'allumage';

  @override
  String get mqttClientCertificate => 'Certificat du client';

  @override
  String get mqttClientCertificateHint => 'Certificat du client MQTT';

  @override
  String get mqttClientKey => 'Clé du client';

  @override
  String get mqttClientKeyHint => 'Clé du client MQTT';

  @override
  String get mqttClientPassword => 'Mot de passe du client';

  @override
  String get mqttClientPasswordHint => 'Mot de passe du client MQTT';

  @override
  String get mqttEnableHomeAssistantDiscovery =>
      'Activer la découverte MQTT de HomeAssistant';

  @override
  String get modbusTcp => 'Modbus TCP';

  @override
  String get enableInterface => 'Activer l\'interface';

  @override
  String get busAddress => 'Adresse du bus';

  @override
  String busAddressWithAddress(Object index) {
    return 'Adresse du bus $index';
  }

  @override
  String get deviceType => 'Type d\'appareil';

  @override
  String registerTable(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Tableaux des registres',
      one: 'Tableau des registres',
    );
    return '$_temp0';
  }

  @override
  String get currentValues => 'Valeurs actuelles';

  @override
  String get requestRTU => 'Demande RTU';

  @override
  String get requestPriority => 'Priorité de la demande';

  @override
  String get mqttForwarding => 'Transfert vers MQTT';

  @override
  String get historicData => 'Données historiques';

  @override
  String get dataFormat => 'Format des données';

  @override
  String get dataType => 'Type de données';

  @override
  String get description => 'Description';

  @override
  String get readWrite => 'Lecture/écriture';

  @override
  String get unit => 'Unité';

  @override
  String get registerTableReset => 'Réinitialisation du tableau des registres';

  @override
  String get registerTableResetDescription =>
      'La table de registre doit-elle vraiment être réinitialisée ?';

  @override
  String get notConfigured => 'Non configuré';

  @override
  String get release330ZGW16Header => 'Mise à jour majeure pour le ZGW16WL-IP';

  @override
  String get release330Header =>
      'Le ZGW16WL-IP avec jusqu\'à 16 compteurs d\'électricité';

  @override
  String get release330ZGW16Note1 =>
      'Prend en charge jusqu\'à 16 compteurs d\'électricité Modbus ELTAKO';

  @override
  String get release330ZGW16Note2 => 'Support Modbus TCP';

  @override
  String get release330ZGW16Note3 => 'Prise en charge de la découverte MQTT';

  @override
  String get screenshotButtonLivingRoom => 'Bouton-poussoir du salon';

  @override
  String get registerChangedSuccessfully => 'Changement de registre réussi';

  @override
  String get serverCertificateEmpty =>
      'Le certificat du serveur ne peut pas être vide';

  @override
  String get registerTemplates => 'Enregistrer les modèles';

  @override
  String get registerTemplateChangedSuccessfully =>
      'Le modèle de registre a été modifié avec succès';

  @override
  String get registerTemplateReset => 'Réinitialisation du modèle de registre';

  @override
  String get registerTemplateResetDescription =>
      'Le modèle de registre doit-il vraiment être réinitialisé ?';

  @override
  String get registerTemplateNotAvailable =>
      'Pas de modèle de registre disponible';

  @override
  String get rename => 'Renommer';

  @override
  String get registerName => 'Nom du registre';

  @override
  String get registerRenameDescription =>
      'Saisir un nom personnalisé pour le registre';

  @override
  String get restart => 'Redémarrer l\'appareil';

  @override
  String get restartDescription =>
      'Voulez-vous vraiment redémarrer l\'appareil ?';

  @override
  String get restartConfirmationDescription =>
      'L\'appareil est en train de redémarrer';

  @override
  String get deleteAllElectricityMeters =>
      'Effacer tous les compteurs d\'électricité';

  @override
  String get deleteAllElectricityMetersDescription =>
      'Voulez-vous vraiment supprimer tous les compteurs d\'électricité ?';

  @override
  String get deleteAllElectricityMetersConfirmationDescription =>
      'Tous les compteurs d\'électricité ont été supprimés avec succès';

  @override
  String get resetAllElectricityMeters =>
      'Réinitialiser toutes les configurations des compteurs d\'électricité';

  @override
  String get resetAllElectricityMetersDescription =>
      'Voulez-vous vraiment réinitialiser toutes les configurations des compteurs d\'électricité ?';

  @override
  String get resetAllElectricityMetersConfirmationDescription =>
      'Toutes les configurations des compteurs d\'électricité ont été réinitialisées avec succès';

  @override
  String get deleteElectricityMeterHistories =>
      'Supprimer tous les historiques des compteurs d\'électricité';

  @override
  String get deleteElectricityMeterHistoriesDescription =>
      'Voulez-vous vraiment effacer tous les historiques des compteurs d\'électricité ?';

  @override
  String get deleteElectricityMeterHistoriesConfirmationDescription =>
      'Tous les historiques des compteurs d\'électricité ont été supprimés avec succès';

  @override
  String get multipleElectricityMetersSupportMissing =>
      'Votre appareil ne prend actuellement en charge qu\'un seul compteur d\'électricité. Veuillez mettre à jour votre firmware.';

  @override
  String get consumptionWithUnit => 'Consommation (kWh)';

  @override
  String get exportWithUnit => 'Livraison (kWh)';

  @override
  String get importWithUnit => 'Consommation (kWh)';

  @override
  String get resourceWarningHeader => 'Limites des ressources';

  @override
  String mqttAndTcpResourceWarning(Object protocol) {
    return 'L\'exploitation simultanée de MQTT et de Modbus TCP n\'est pas possible en raison des ressources limitées du système. Désactivez d\'abord $protocol.';
  }

  @override
  String get mqttEnabled => 'MQTT activé';

  @override
  String get redirectMQTT => 'Aller dans les paramètres MQTT';

  @override
  String get redirectModbus => 'Aller à Paramètres Modbus';

  @override
  String get unsupportedSettingDescription =>
      'Avec la version actuelle de votre micrologiciel, certains paramètres de l\'appareil ne sont pas pris en charge. Veuillez mettre à jour votre micrologiciel pour utiliser les nouvelles fonctionnalités.';

  @override
  String get updateNow => 'Mise à jour';

  @override
  String get zgw241Hint =>
      'Avec cette mise à jour, Modbus TCP est activé par défaut et MQTT est désactivé. Ceci peut être modifié dans les paramètres. Avec la prise en charge d\'un maximum de 16 compteurs, de nombreuses optimisations ont été réalisées, ce qui peut entraîner des modifications dans les paramètres de l\'appareil. Veuillez redémarrer l\'appareil après avoir ajusté les paramètres.';

  @override
  String get deviceConfigChangedSuccesfully =>
      'Le temps d\'exécution a été modifié avec succès';

  @override
  String get deviceConfiguration => 'Configuration de l\'appareil';

  @override
  String get tiltModeToggle => 'Mode d\'inclinaison';

  @override
  String get tiltModeToggleFooter =>
      'Si l\'appareil est installé dans Matter, toutes les fonctions doivent y être reconfigurées';

  @override
  String get shaderMovementDirection => 'Marche arrière haut/bas';

  @override
  String get shaderMovementDirectionDescription =>
      'Inverser le sens du mouvement du moteur vers le haut ou vers le bas';

  @override
  String get tiltTime => 'Durée d\'exécution de l\'inclinaison';

  @override
  String changeTiltModeDialogTitle(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Enable',
      'false': 'Disable',
      'other': 'Change',
    });
    return '$_temp0 tilt function';
  }

  @override
  String changeTiltModeDialogConfirmation(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Activer',
      'false': 'Désactiver',
      'other': 'Change',
    });
    return '$_temp0';
  }

  @override
  String get generalTextSlatSetting => 'Réglage des lamelles';

  @override
  String get generalTextPosition => 'Position';

  @override
  String get generalTextSlatPosition => 'Position des lamelles';

  @override
  String get slatSettingDescription => 'Description du réglage des lamelles';

  @override
  String get scenePositionSliderDescription => 'Hauteur';

  @override
  String get sceneSlatPositionSliderDescription => 'Inclinaison';

  @override
  String get referenceRun => 'Cycle d\'étalonnage';

  @override
  String get slatAutoSettingHint =>
      'Dans ce mode, la position des stores n\'a pas d\'importance avant que les lamelles ne s\'ajustent à la position d\'inclinaison souhaitée.';

  @override
  String get slatReversalSettingHint =>
      'Dans ce mode, les volets se ferment complètement avant que les lames ne s\'ajustent à la position d\'inclinaison souhaitée.';

  @override
  String get release340Header =>
      'Le nouvel actionneur d\'ombrage encastré ESB64NP-IPM est arrivé !';

  @override
  String get release340ESB64Header =>
      'Quelles sont les capacités de l\'ESB64NP-IPM ?';

  @override
  String get release340ESB64Note1 =>
      'Notre actionneur d\'ombrage certifié Matter Gateway avec fonction de lamelles en option';

  @override
  String get release340ESB64Note2 =>
      'Deux entrées de boutons câblés pour la commutation manuelle et la transmission à Matter';

  @override
  String get release340ESB64Note3 =>
      'Extensible avec l\'adaptateur EnOcean (EOA64). Par exemple avec le bouton poussoir sans fil EnOcean F4T55';

  @override
  String get release340ESB64Note4 =>
      'Ouvert aux intégrations grâce à l\'API REST basée sur la norme OpenAPI';

  @override
  String get activateTiltModeDialogText =>
      'Si la fonction d\'inclinaison est activée, tous les réglages seront perdus. Êtes-vous sûr de vouloir activer la fonction d\'inclinaison ?';

  @override
  String get deactivateTiltModeDialogText =>
      'Si la fonction d\'inclinaison est désactivée, tous les réglages seront perdus. Êtes-vous sûr de vouloir désactiver la fonction d\'inclinaison ?';

  @override
  String shareConfiguration(Object name) {
    return 'Share configuration $name';
  }

  @override
  String get configurationSharedSuccessfully =>
      'Configuration shared successfully';

  @override
  String get configurationShareFailed => 'Sharing configuration failed';
}
