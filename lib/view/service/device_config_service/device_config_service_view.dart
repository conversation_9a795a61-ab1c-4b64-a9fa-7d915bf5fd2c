//
//  device_config_service_view.dart
//  EltakoConnect
//
//  Created by <PERSON> on 31.03.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/enumeration/eltako_slider_unit.dart';
import 'package:eltako_connect/enumeration/movement_direction.dart';
import 'package:eltako_connect/enumeration/runtime_mode.dart';
import 'package:eltako_connect/enumeration/tilt_mode.dart';
import 'package:eltako_connect/extension/string_extension.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/model/sub_device/sub_device/eltako_shader.dart';
import 'package:eltako_connect/theme/eltako_padding.dart';
import 'package:eltako_connect/theme/styles/eltako_style.dart';
import 'package:eltako_connect/view/service/device_config_service/i_device_config_service_view_model.dart';
import 'package:eltako_connect/widget/service_loading/service_loader.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Device Configuration Service View
class DeviceConfigServiceView extends StatelessWidget {
  /// Default constructor
  const DeviceConfigServiceView({required IDeviceConfigServiceViewModel viewModel, super.key}) : _viewModel = viewModel;

  final IDeviceConfigServiceViewModel _viewModel;

  Future<void> _save(BuildContext context) async {
    final result = await _viewModel.save();
    if (!context.mounted) return;
    EltakoSnackBar.result(
      result,
      message: result.success ? AppLocalizations.of(context).deviceConfigChangedSuccesfully : result.error.message,
    ).show(context);

    if (!result.success) return;
    context.pop();
  }

  @override
  Widget build(BuildContext context) => NavigationView(
    header: NavigationHeader(
      title: AppLocalizations.of(context).deviceConfiguration,
      trailing: NavigationSaveItem(onTap: () async => _save(context)),
    ),
    child: ServiceLoader(
      loadServiceData: _viewModel.loadService,
      child: SliverToBoxAdapter(
        child: ListenableBuilder(
          listenable: _viewModel,
          builder:
              (context, snapshot) => ListenableBuilder(
                listenable: _viewModel.shader,
                builder:
                    (context, snapshot) => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (_viewModel.supportsTiltFeature)
                          Column(
                            children: [
                              EltakoToggle(
                                onChanged: (value) {
                                  _viewModel.shader.movementDirection?.value =
                                      value ? MovementDirection.inverted : MovementDirection.normal;
                                },
                                value: _viewModel.shader.movementDirection?.value == MovementDirection.inverted,
                                label: AppLocalizations.of(context).shaderMovementDirection,
                                description: AppLocalizations.of(context).shaderMovementDirectionDescription,
                              ),
                              const SizedBox(height: EltakoPadding.l),
                              const Divider(),
                              const SizedBox(height: EltakoPadding.l),
                            ],
                          ),
                        Text(
                          AppLocalizations.of(context).detailsConfigurationRuntimeMode,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: EltakoPadding.xs),
                        EltakoPicker(
                          tabs: _viewModel.supportedRuntimeModes,
                          initialSelection: _viewModel.shader.runtimeMode?.value,
                          onTap: (value) {
                            final newRuntimeMode = _viewModel.shader.runtimeMode;
                            newRuntimeMode?.value = value;
                            _viewModel.shader.runtimeMode = newRuntimeMode;
                          },
                          labelBuilder: (value) => value.localizedName,
                        ),
                        const SizedBox(height: EltakoPadding.xs),
                        Text(_viewModel.shader.runtimeMode?.value?.localizedDescription ?? StringExtension.empty),
                        const SizedBox(height: EltakoPadding.xl),
                        AnimatedCrossFade(
                          firstChild: _ManualMode(_viewModel.shader, _viewModel.supportsTiltFeature),
                          secondChild: _viewModel.supportsTiltFeature ? _AutoMode(_viewModel.shader) : const SizedBox(),
                          crossFadeState:
                              _viewModel.shader.runtimeMode?.value == RuntimeMode.auto
                                  ? CrossFadeState.showSecond
                                  : CrossFadeState.showFirst,
                          duration: const Duration(milliseconds: 200),
                        ),
                        const SizedBox(height: EltakoPadding.xl),
                      ],
                    ),
              ),
        ),
      ),
    ),
  );
}

class _AutoMode extends StatelessWidget {
  const _AutoMode(this.shader);

  final EltakoShader shader;

  @override
  Widget build(BuildContext context) {
    if (shader.tiltMode == null) return const SizedBox();
    return ListenableBuilder(
      listenable: shader,
      builder:
          (context, child) => Column(
            children: [
              EltakoToggle(
                beforeChange:
                    (value) async => await showEltakoDialog(
                      context: context,
                      title: AppLocalizations.of(context).changeTiltModeDialogTitle(value.toString()),
                      content: Text(
                        value
                            ? AppLocalizations.of(context).activateTiltModeDialogText
                            : AppLocalizations.of(context).deactivateTiltModeDialogText,
                      ),
                      actions: [
                        EltakoButton(
                          label: Text(AppLocalizations.of(context).generalCancel),
                          color: EltakoStyle.of(context).error,
                          onPressed: () => context.pop(false),
                        ),
                        EltakoButton(
                          eltakoButtonStyle: EltakoButtonStyle.filled,
                          color: EltakoStyle.of(context).error,
                          label: Text(AppLocalizations.of(context).changeTiltModeDialogConfirmation(value.toString())),
                          onPressed: () => context.pop(true),
                        ),
                      ],
                    ),
                onChanged:
                    (value) => shader.tiltMode = shader.tiltMode?..value = value ? TiltMode.enabled : TiltMode.disabled,
                value: shader.isTiltEnabled,
                label: AppLocalizations.of(context).tiltModeToggle,
                description: AppLocalizations.of(context).tiltModeToggleFooter,
              ),
              const SizedBox(height: EltakoPadding.xs),
              AnimatedCrossFade(
                firstChild: EltakoSlider.time(
                  headerText: AppLocalizations.of(context).tiltTime,
                  value: (shader.tiltRuntime?.value ?? 0) / 1000,
                  onChangeEnd: (value) => shader.tiltRuntime?.value = (value * 1000).toInt(),
                  unit: EltakoSliderUnit.seconds,
                  min: 0.1,
                  max: 5,
                  stepSize: 0.1,
                ),
                secondChild: const SizedBox(),
                crossFadeState: shader.isTiltEnabled ? CrossFadeState.showFirst : CrossFadeState.showSecond,
                duration: const Duration(milliseconds: 300),
              ),
            ],
          ),
    );
  }
}

class _ManualMode extends StatelessWidget {
  const _ManualMode(this.shader, this.supportsTiltFeature);

  final EltakoShader shader;
  final bool? supportsTiltFeature;

  @override
  Widget build(BuildContext context) => Column(
    children: [
      EltakoSlider.time(
        headerText: AppLocalizations.of(context).detailsConfigurationRuntimeDuration,
        value: (shader.runtime?.value ?? 0) / 1000,
        onChangeEnd: (value) => shader.runtime?.value = (value * 1000).toInt(),
        unit: EltakoSliderUnit.seconds,
        min: 10,
        max: 200,
      ),
      const SizedBox(height: EltakoPadding.xl),
      EltakoSlider.time(
        headerText: AppLocalizations.of(context).generalTextOffset,
        footerText: AppLocalizations.of(context).runtimeOffsetDescription,
        value: (shader.runtimeOffset?.value ?? 0) / 1000,
        onChangeEnd: (value) => shader.runtimeOffset?.value = (value * 1000).toInt(),
        unit: EltakoSliderUnit.seconds,
        max: 15,
      ),
      const SizedBox(height: EltakoPadding.l),
      if (supportsTiltFeature ?? false) _AutoMode(shader),
    ],
  );
}
