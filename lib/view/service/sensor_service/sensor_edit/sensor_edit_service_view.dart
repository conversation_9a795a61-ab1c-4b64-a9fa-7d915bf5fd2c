//
//  sensor_edit_service_view.dart
//  EltakoConnect
//
//  Created by <PERSON> on 15.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:collection/collection.dart';
import 'package:eltako_connect/enumeration/sensor_channel_position.dart';
import 'package:eltako_connect/enumeration/sensor_type.dart';
import 'package:eltako_connect/enumeration/sensor_type_category.dart';
import 'package:eltako_connect/env/environment.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/model/sensor/sensor/enocean_button.dart';
import 'package:eltako_connect/model/sensor/sensor/wired_button.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/global/button_unset_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/i_sensor_configuration.dart';
import 'package:eltako_connect/theme/helper/eltako_icon.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/view/service/sensor_service/sensor_edit/i_sensor_edit_service_view_model.dart';
import 'package:eltako_connect/view/service/sensor_service/widgets/button_behavior.dart';
import 'package:eltako_connect/view/service/sensor_service/widgets/switch_behavior.dart';
import 'package:eltako_connect/widget/device/device_information_section.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_popup_menu.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_sheet.dart';
import 'package:eltako_connect/widget/navigation/sensor_navigation_header.dart';
import 'package:eltako_connect/widget/service_loading/service_loader.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Sensor_edit Service View
class SensorEditServiceView extends StatelessWidget {
  /// Default constructor
  const SensorEditServiceView({required ISensorEditServiceViewModel viewModel, super.key}) : _viewModel = viewModel;

  final ISensorEditServiceViewModel _viewModel;

  @override
  Widget build(BuildContext context) => ListenableBuilder(
    listenable: _viewModel.sensor,
    builder:
        (context, snapshot) => NavigationView(
          header: SensorNavigationHeader(
            sensor: _viewModel.sensor,
            leading:
                Environment.current.type == EnvironmentType.screenshots
                    ? NavigationItem(icon: Assets.icons.arrowLeft.outline.light, onTap: () => {})
                    : null,
            device: _viewModel.device,
            trailing:
                _viewModel.sensor.sensorType.category != SensorTypeCategory.wiredButton
                    ? EltakoPopupMenu.subDevice(
                      deviceName: _viewModel.sensor.name,
                      deviceType: _viewModel.sensor.category.localizedCategory,
                      onDelete: _viewModel.delete,
                    )
                    : null,
          ),
          child: ServiceLoader(
            loadServiceData: _viewModel.loadService,
            child: SliverToBoxAdapter(
              child: ListenableBuilder(
                listenable: _viewModel,
                builder:
                    (context, snapshot) => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context).detailsConfigurationSwitchbehavior.toUpperCase(),
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: EltakoPadding.xs),

                        /// Switch Behavior Wired and EnOcean Button
                        _buildSwitchBehavior(context),
                        const SizedBox(height: EltakoPadding.xl),
                        Text(
                          AppLocalizations.of(context).generalTextSettings.toUpperCase(),
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: EltakoPadding.xs),

                        /// Sensor Services
                        ..._viewModel.sensor.sensorServices.mapIndexed(
                          (index, subService) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: EltakoPadding.xxxxs),
                            child: EltakoListTile(
                              position: RowPositionHelper.fromIndex(index, _viewModel.sensor.sensorServices.length),
                              leading: EltakoIcon.getIcon(subService.icon, context).image(height: 32, width: 32),
                              title: subService.localizedName,
                              onTap: () async {
                                await subService
                                    .onTap(context: context, sensor: _viewModel.sensor, onSave: _viewModel.save)
                                    .call();
                                await _viewModel.loadService();
                              },
                            ),
                          ),
                        ),
                        const SizedBox(height: EltakoPadding.xl),
                        DeviceInformationSection(information: _viewModel.sensor.information),
                        const SizedBox(height: EltakoPadding.xl),
                      ],
                    ),
              ),
            ),
          ),
        ),
  );

  /// Switch Behavior Wired and EnOcean Button
  Widget _buildSwitchBehavior(BuildContext context) {
    switch (_viewModel.sensorType) {
      case SensorType.wiredButton1Way:
      case SensorType.wiredButton2Way:
        final sensor = _viewModel.sensor as WiredButton;
        final buttonConfigs = sensor.channels.whereNot((c) => c.position == SensorChannelPosition.bridged);
        return Column(
          children: [
            ...buttonConfigs.mapIndexed(
              (index, buttonConfig) => Padding(
                padding: const EdgeInsets.symmetric(vertical: EltakoPadding.xxxxs),
                child: EltakoListTile(
                  position: RowPositionHelper.fromIndex(index, buttonConfigs.length),
                  leading: EltakoIcon.getIcon(Assets.icons.lightSwitch.circle, context).image(height: 32, width: 32),
                  title: buttonConfig.configurationLogic.localizedName,
                  // ignore: avoid_string_literals_inside_widget
                  subtitle: '${AppLocalizations.of(context).input} ${index + 1}',
                  onTap:
                      () async => showEltakoSheet(
                        context: context,
                        builder:
                            (context, _) => SwitchBehavior(
                              isWired: true,
                              device: _viewModel.device,
                              configuration: buttonConfig.configuration,
                              onSave: (configuration) async {
                                sensor.setConfiguration(configuration, position: buttonConfig.position);
                                final result = await _viewModel.save();
                                if (!context.mounted) return;
                                EltakoSnackBar.result(
                                  result,
                                  message:
                                      result.success
                                          ? AppLocalizations.of(context).wiredInputChangedSuccesfully
                                          : result.message,
                                ).show(context);
                                if (!result.success) return;
                                context.pop();
                              },
                            ),
                      ),
                ),
              ),
            ),
          ],
        );
      case SensorType.enOceanButton1Way:
      case SensorType.enOceanButton2Way:
      case SensorType.enOceanButton4Way:
        final sensor = _viewModel.sensor as EnOceanButton;
        return ButtonBehavior(
          key: ValueKey(sensor),
          device: _viewModel.device,
          buttonConfigs: sensor.channels,
          onButtonChange: (position, category, configuration) async {
            final oldConfig = sensor.channels.firstWhere((c) => c.position == position);
            sensor.setConfiguration(configuration, position: position);
            final result = await _viewModel.save();
            if (!result.success) {
              sensor.setConfiguration(
                (oldConfig.configuration ?? ButtonUnsetConfiguration()) as ISensorConfiguration,
                position: position,
              );
            }
            return result;
          },
          sensorType: SensorType.fromId(sensor.productId),
        );
      default:
        error(UnimplementedError('Sensor type ${_viewModel.sensorType} is not implemented'));
        // ignore: avoid_string_literals_inside_widget
        return Text('Sensor type ${_viewModel.sensorType} is not implemented');
    }
  }
}
