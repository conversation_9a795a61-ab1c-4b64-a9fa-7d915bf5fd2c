//
//  i_configurations_view_model.dart
//  EltakoConnect
//
//  Created by <PERSON> on 26.03.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:io';

import 'package:eltako_connect/model/config_info.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/share_result.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

/// Configurations View Model Interface
abstract class IConfigurationsViewModel with ChangeNotifier {
  // region [PUBLIC PROPERTIES]

  /// Configs
  List<ConfigInfo> configs = [];

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Load all configs for the Device
  Future<VoidResult> loadConfigs();

  /// Get config file
  Future<DataResult<File>> getConfigFile(String fileId);

  /// Get config file content as yaml
  Future<DataResult<String>> getConfigAsYaml(String fileId);

  /// Share config file
  Future<EltakoShareResult<ShareResult, Object>> shareConfig({required ConfigInfo config});

  /// Delete the [ConfigInfo]
  Future<VoidResult> delete(ConfigInfo config);

  /// Delete all configs
  void deleteAll();

  // endregion
}
