//
//  configurations_view.dart
//  EltakoConnect
//
//  Created by <PERSON> on 26.03.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:collection/collection.dart';
import 'package:eltako_connect/enumeration/device_type.dart';
import 'package:eltako_connect/env/environment.dart';
import 'package:eltako_connect/extension/config_info_list_extension.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/model/config_info.dart';
import 'package:eltako_connect/model/share_result.dart';
import 'package:eltako_connect/routing/eltako_router.dart';
import 'package:eltako_connect/theme/helper/eltako_icon.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/view/configurations_view/i_configurations_view_model.dart';
import 'package:eltako_connect/view/configurations_view/widgets/no_configurations_available.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Config view
class ConfigurationsView extends StatelessWidget {
  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  const ConfigurationsView({required this.viewModel, super.key});

  // endregion

  // region [PRIVATE VARIABLES]

  /// View model
  final IConfigurationsViewModel viewModel;

  // endregion

  // TODO(Kotti): This is not typesafe. Although specifying the <bool> generics i can still return a non bool type in Navigator.of(context).pop(xyz);
  Future<bool?> _showDeleteConfigDialog(BuildContext context, ConfigInfo config) async => showEltakoDialog<bool>(
    context: context,
    title: AppLocalizations.of(context).configurationsDelete,
    content: EltakoStyledText(
      text: AppLocalizations.of(context).configurationsDeleteHint(config.name),
      textStyle: Theme.of(context).textTheme.bodyMedium,
      styledText: config.name,
      styledTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
    ),
    actions: [
      EltakoButton(
        label: Text(AppLocalizations.of(context).generalCancel),
        onPressed: () => Navigator.of(context).pop(false),
      ),
      EltakoButton(
        label: Text(AppLocalizations.of(context).generalDelete, style: const TextStyle(color: Colors.white)),
        eltakoButtonStyle: EltakoButtonStyle.filled,
        color: EltakoStyle.of(context).error,
        onPressed: () {
          Navigator.of(context).pop(true);
          EltakoSnackBar(
            title: AppLocalizations.of(context).configurationDeleted,
            state: EltakoSnackBarState.success,
          ).show(context);
        },
      ),
    ],
  );

  // region [OVERRIDES]

  @override
  Widget build(BuildContext context) => NavigationView(
    header: NavigationHeader(
      title: AppLocalizations.of(context).configurationsHeader,
      leading:
          Environment.current.type == EnvironmentType.screenshots
              ? NavigationItem(icon: Assets.icons.arrowLeft.outline.light, onTap: () => {})
              : null,
    ),
    toolbar: Text(AppLocalizations.of(context).configurationsDescription, style: Theme.of(context).textTheme.bodySmall),
    child: ListenableBuilder(
      listenable: viewModel,
      builder:
          (context, child) =>
              viewModel.configs.isEmpty
                  ? const SliverToBoxAdapter(child: NoConfigurationsAvailable())
                  : SliverList.builder(
                    itemBuilder:
                        (context, index) =>
                            _configSection(viewModel.configs.groupByDevice.entries.elementAt(index), context),
                    itemCount: viewModel.configs.groupByDevice.length,
                  ),
    ),
  );

  /// Config section
  Widget _configSection(MapEntry<DeviceType, List<ConfigInfo>> entry, BuildContext context) => ExpansionTile(
    shape: const Border(),
    tilePadding: EdgeInsets.zero,
    title: Text(entry.key.name, style: Theme.of(context).textTheme.titleMedium),
    initiallyExpanded: true,
    children: [
      ...entry.value.mapIndexed(
        (index, config) => Dismissible(
          key: ValueKey(config),
          confirmDismiss: (direction) async {
            switch (direction) {
              case DismissDirection.endToStart: // Delete config
                return _showDeleteConfigDialog(context, config);
              case DismissDirection.startToEnd: // Share config
                final shareResult = await viewModel.shareConfig(config: config);
                switch (shareResult) {
                  case EltakoShareSuccess():
                    if (context.mounted) {
                      EltakoSnackBar(
                        title: AppLocalizations.of(context).configurationSharedSuccessfully,
                        state: EltakoSnackBarState.success,
                      ).show(context);
                    }
                  case EltakoShareFailure():
                    if (context.mounted) {
                      EltakoSnackBar(
                        title: AppLocalizations.of(context).configurationShareFailed,
                        state: EltakoSnackBarState.error,
                      ).show(context);
                    }
                  case EltakoShareDismissed():
                    break;
                }
                return false;
              default:
                warning('Unsupported dismiss direction: $direction');
                return false;
            }
          },
          background: DecoratedBox(
            decoration: BoxDecoration(
              color: EltakoStyle.of(context).success,
              borderRadius: BorderRadius.circular(EltakoRadius.l),
            ),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.only(left: EltakoPadding.xl),
                child: EltakoIcon.getIcon(
                  Assets.icons.share.outline,
                  context,
                ).image(height: 24, color: EltakoStyle.of(context).white),
              ),
            ),
          ),
          secondaryBackground: DecoratedBox(
            decoration: BoxDecoration(
              color: EltakoStyle.of(context).error,
              borderRadius: BorderRadius.circular(EltakoRadius.l),
            ),
            child: Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: const EdgeInsets.only(right: EltakoPadding.xl),
                child: EltakoIcon.getIcon(
                  Assets.icons.bin.outline,
                  context,
                ).image(height: 24, color: EltakoStyle.of(context).white),
              ),
            ),
          ),
          onDismissed: (direction) async {
            switch (direction) {
              case DismissDirection.endToStart:
                {
                  await viewModel.delete(config);
                  await viewModel.loadConfigs();
                }
              default:
                warning('Unsupported dismiss direction: $direction');
            }
          },
          child: Padding(
            padding: const EdgeInsets.only(bottom: EltakoPadding.xxxs),
            child: ConfigRow(
              onTap: () async {
                final result = await context.pushNamed(EltakoRouter.configurationEditRouteName, extra: config);
                if (result != null) await viewModel.loadConfigs();
              },
              config: config,
              position: RowPositionHelper.fromIndex(index, entry.value.length),
            ),
          ),
        ),
      ),
    ],
  );
  // endregion
}
