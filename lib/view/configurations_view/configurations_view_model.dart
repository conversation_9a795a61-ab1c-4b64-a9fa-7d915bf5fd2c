//
//  configurations_view_model.dart
//  EltakoConnect
//
//  Created by <PERSON> on 26.03.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:async';
import 'dart:io';

import 'package:eltako_connect/enumeration/localized_error.dart';
import 'package:eltako_connect/manager/config_manager.dart';
import 'package:eltako_connect/manager/file_sharing_manager.dart';
import 'package:eltako_connect/model/config_info.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/share_result.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/view/configurations_view/i_configurations_view_model.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:get_it/get_it.dart';
import 'package:share_plus/share_plus.dart';

/// Config Service View Model
class ConfigurationsViewModel extends IConfigurationsViewModel {
  final ConfigManager _configManager = GetIt.I.get<ConfigManager>();

  /// Default constructor
  ConfigurationsViewModel() {
    unawaited(loadConfigs());
  }

  @override
  Future<VoidResult> loadConfigs() async {
    final result = await _configManager.getInfoList();
    if (result.success) {
      configs = result.data ?? [];
    }
    debug(configs);
    notifyListeners();
    return result;
  }

  @override
  Future<void> deleteAll() async {
    await GetIt.I.get<ConfigManager>().reset();
    await loadConfigs();
    notifyListeners();
  }

  @override
  Future<DataResult<File>> getConfigFile(String fileId) => _configManager.getConfigFile(fileId);

  @override
  Future<DataResult<String>> getConfigAsYaml(String fileId) => _configManager.getAsYaml(fileId);

  @override
  Future<EltakoShareResult<ShareResult, Object>> shareConfig({required ConfigInfo config}) async {
    final getConfigFileResult = await getConfigFile(config.fileId);

    if (!getConfigFileResult.success) {
      error('Error getting config file: ${getConfigFileResult.message}');
      return EltakoShareFailure(getConfigFileResult.error);
    }

    final file = getConfigFileResult.data;

    if (file == null) {
      error('Error getting config file: File is null');
      return const EltakoShareFailure(LocalizedError.notFound);
    }

    final shareResult = await GetIt.I.get<FileSharingManager>().shareConfigFile(file: file, fileName: config.name);

    if (!shareResult.success) {
      error('Error sharing config file: ${shareResult.message}');
      return EltakoShareFailure(shareResult.error);
    }

    final shareResultData = shareResult.data;

    if (shareResultData == null) {
      // this should not happen as we checked for success before
      error('Error sharing config file: shareResultData is null');
      return const EltakoShareFailure(LocalizedError.unexpectedNullValue, message: 'shareResultData is null');
    }

    switch (shareResultData.status) {
      case ShareResultStatus.unavailable:
        return const EltakoShareFailure(LocalizedError.deviceError, message: 'Sharing not available');
      case ShareResultStatus.success:
        return EltakoShareSuccess(shareResultData);
      case ShareResultStatus.dismissed:
        return const EltakoShareDismissed();
    }
  }

  @override
  Future<VoidResult> delete(ConfigInfo config) => _configManager.delete(config.fileId);
}
