//
//  configuration_detail.dart
//  EltakoConnect
//
//  Created by <PERSON> on 26.03.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/extension/string_extension.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/manager/config_manager.dart';
import 'package:eltako_connect/model/config_info.dart';
import 'package:eltako_connect/model/share_result.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/view/configurations_view/configurations_view_model.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_popup_menu.dart';
import 'package:eltako_connect/widget/service_loading/service_loader.dart';
import 'package:eltako_connect/widget/service_loading/service_loading_error.dart';
import 'package:eltako_connect/widget/service_loading/service_loading_result.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

/// Configuration Detail
class ConfigurationDetail extends StatefulWidget {
  /// Configuration
  final ConfigInfo config;

  /// View model
  final ConfigurationsViewModel viewModel;

  /// Default constructor
  const ConfigurationDetail({required this.config, required this.viewModel, super.key});

  @override
  State<ConfigurationDetail> createState() => _ConfigurationDetailState();
}

class _ConfigurationDetailState extends State<ConfigurationDetail> {
  /// Title notifier
  final ValueNotifier<String> _titleNotifier = ValueNotifier(StringExtension.empty);
  bool showBadge = false;
  String? _configContent;

  @override
  void initState() {
    super.initState();
    _titleNotifier.value = widget.config.name;
  }

  Future<ServiceLoadingResult> _loadConfigContent() async {
    final result = await widget.viewModel.getConfigAsYaml(widget.config.fileId);

    if (result.success) {
      setState(() => _configContent = result.data);
      return ServiceLoadingResult.success();
    }

    return ServiceLoadingResult.error(ServiceLoadingError.unknown, result.message);
  }

  Future<void> _showRenameDialog(BuildContext context) async {
    final controller = TextEditingController(text: _titleNotifier.value);
    final bool? shouldSave = await showEltakoDialog<bool>(
      context: context,
      title: AppLocalizations.of(context).rename,
      content: EltakoTextField(
        controller: controller,
        hintText: AppLocalizations.of(context).nameOfConfiguration,
        showClearButton: true,
      ),
      actions: [
        EltakoButton(
          onPressed: () => Navigator.of(context).pop(false),
          label: Text(AppLocalizations.of(context).generalCancel),
        ),
        EltakoButton(
          eltakoButtonStyle: EltakoButtonStyle.filled,
          onPressed: () {
            if (controller.text.trim().isEmpty) {
              EltakoSnackBar(
                title: AppLocalizations.of(context).configurationsNameFailEmpty,
                state: EltakoSnackBarState.error,
              ).show(context);
              return;
            }
            Navigator.of(context).pop(true);
          },
          label: Text(AppLocalizations.of(context).generalSave),
        ),
      ],
    );

    if (shouldSave != true || !context.mounted) return;

    final newName = controller.text.trim();
    final configManager = GetIt.I.get<ConfigManager>();
    final result = await configManager.rename(widget.config.fileId, name: newName);
    if (!result.success && context.mounted) {
      EltakoSnackBar.result(result).show(context);
      return;
    }
    widget.config.name = newName;
    _titleNotifier.value = newName;
  }

  @override
  Widget build(BuildContext context) {
    final configContent = _configContent;

    return NavigationView(
      header: NavigationHeader(
        titleNotifier: _titleNotifier,
        title: widget.config.name,
        trailing: EltakoPopupMenu(
          items: [
            PopupMenuItem(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [Text(AppLocalizations.of(context).rename, style: Theme.of(context).textTheme.bodyMedium)],
              ),
              onTap: () async => _showRenameDialog(context),
            ),
            PopupMenuItem(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [Text(AppLocalizations.of(context).export, style: Theme.of(context).textTheme.bodyMedium)],
              ),
              onTap: () async {
                final shareResult = await widget.viewModel.shareConfig(config: widget.config);
                if (!context.mounted) return;
                switch (shareResult) {
                  case EltakoShareSuccess():
                    EltakoSnackBar(
                      title: AppLocalizations.of(context).configurationSharedSuccessfully,
                      state: EltakoSnackBarState.success,
                    ).show(context);
                  case EltakoShareFailure():
                    EltakoSnackBar(
                      title: AppLocalizations.of(context).configurationShareFailed,
                      state: EltakoSnackBarState.error,
                    ).show(context);
                  case EltakoShareDismissed():
                    break;
                }
              },
            ),
            PopupMenuItem(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    AppLocalizations.of(context).configurationsDelete,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: EltakoStyle.of(context).error),
                  ),
                ],
              ),
              onTap: () async {
                final shouldDelete = await showEltakoDialog(
                  context: context,
                  title: AppLocalizations.of(context).configurationsDelete,
                  content: EltakoStyledText(
                    text: AppLocalizations.of(context).configurationsDeleteHint(widget.config.name),
                    textStyle: Theme.of(context).textTheme.bodyMedium,
                    styledText: widget.config.name,
                    styledTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  actions: [
                    EltakoButton(
                      label: Text(AppLocalizations.of(context).generalCancel),
                      onPressed: () => Navigator.of(context).pop(false),
                    ),
                    EltakoButton(
                      label: Text(
                        AppLocalizations.of(context).generalDelete,
                        style: const TextStyle(color: Colors.white),
                      ),
                      eltakoButtonStyle: EltakoButtonStyle.filled,
                      color: EltakoStyle.of(context).error,
                      onPressed: () {
                        Navigator.of(context).pop(true);
                        EltakoSnackBar(
                          title: AppLocalizations.of(context).configurationDeleted,
                          state: EltakoSnackBarState.success,
                        ).show(context);
                      },
                    ),
                  ],
                );
                if (shouldDelete) {
                  final configManager = GetIt.I.get<ConfigManager>();
                  await configManager.delete(widget.config.fileId);
                  if (!context.mounted) return;
                  context.pop(-1);
                }
              },
            ),
          ],
        ),
      ),
      child: ServiceLoader(
        loadServiceData: _loadConfigContent,
        child: SliverToBoxAdapter(
          child:
              configContent != null
                  ? Text(configContent, style: const TextStyle(fontFamily: 'monospace', fontSize: 12))
                  : const SizedBox.shrink(),
        ),
      ),
    );
  }
}
