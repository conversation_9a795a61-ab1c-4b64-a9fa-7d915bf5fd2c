//
//  configurations_view_model_demo.dart
//  EltakoConnect
//
//  Created by <PERSON> on 26.03.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:io';

import 'package:eltako_connect/enumeration/device_type.dart';
import 'package:eltako_connect/l10n/app_localizations_extension.dart';
import 'package:eltako_connect/model/config_info.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/share_result.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/view/configurations_view/i_configurations_view_model.dart';
import 'package:share_plus_platform_interface/platform_interface/share_plus_platform.dart';

/// Config Service View Model
class ConfigurationsViewModelDemo extends IConfigurationsViewModel {
  @override
  List<ConfigInfo> get configs => [
    ConfigInfo.demoConfig(l10n.screenshotSu12, deviceType: DeviceType.assubt),
    ConfigInfo.demoConfig(l10n.screenshotS2U12, deviceType: DeviceType.s2u12dbt),
    ConfigInfo.demoConfig(l10n.screenshotMfz12, deviceType: DeviceType.mfz12dbt),
    ConfigInfo.demoConfig(l10n.screenshotZgw16, deviceType: DeviceType.zgw16ip),
    ConfigInfo.demoConfig(l10n.screenshotEud62, deviceType: DeviceType.eud12bt300w),
    ConfigInfo.demoConfig(l10n.screenshotEsr62, deviceType: DeviceType.esr62pfip),
  ];

  @override
  Future<DataResult<File>> getConfigFile(String fileId) {
    throw UnimplementedError();
  }

  @override
  Future<VoidResult> delete(ConfigInfo config) {
    throw UnimplementedError();
  }

  @override
  void deleteAll() {}

  @override
  Future<VoidResult> loadConfigs() {
    notifyListeners();
    return Future.value(const VoidResult.success());
  }

  @override
  Future<EltakoShareResult<ShareResult, Object>> shareConfig({required ConfigInfo config}) {
    throw UnimplementedError();
  }
}
