import 'dart:async';

import 'package:eltako_connect/device/i_device.dart';
import 'package:eltako_connect/enumeration/service_position.dart';
import 'package:eltako_connect/extension/i_connection_extension.dart';
import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/routing/eltako_router.dart';
import 'package:eltako_connect/service/firmware_update_service/br62ip_firmware_update_service.dart';
import 'package:eltako_connect/service/firmware_update_service/i_firmware_update_service.dart';
import 'package:eltako_connect/service/i_service.dart';
import 'package:eltako_connect/service/i_service_data.dart';
import 'package:eltako_connect/service/reset_service/i_reset_service.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_popup_menu.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Device detail view
class DeviceDetailView extends StatefulWidget {
  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  const DeviceDetailView(IDevice device, {bool isDemo = false, super.key}) : _device = device, _isDemo = isDemo;

  // endregion

  // region [PRIVATE VARIABLES]

  final bool _isDemo;

  final IDevice _device;

  @override
  State<DeviceDetailView> createState() => _DeviceDetailViewState();
}

class _DeviceDetailViewState extends State<DeviceDetailView> {
  bool get showBadge => updateAvailable;
  bool updateAvailable = false;

  @override
  void initState() {
    super.initState();
    if (widget._device.connection.isWifiConnection) {
      _checkForAvailableUpdates();
      // unawaited(_checkEnOceanState());
    }
  } // endregion

  // /// Check EnOcean state
  // Future<void> _checkEnOceanState() async {
  //   // Get sensor service
  //   info('Checking EnOcean...', category: LogCategory.connection);
  //   final service = widget._device.services.firstOfTypeOrNull<ISensorService>();
  //   if (service == null) {
  //     warning('No sensor service available', category: LogCategory.connection);
  //     return;
  //   }
  //
  //   // Check if EnOcean state is available
  //   if (service.supportsEnOcean) {
  //     info('EnOcean supported', category: LogCategory.connection);
  //     return;
  //   }
  //
  //   // Check if EnOcean state is available
  //   final result = await service.getAvailableConnections();
  //   final data = result.data;
  //   if (data == null) {
  //     warning('Could not get available connections', category: LogCategory.connection);
  //     return;
  //   }
  //
  //   // Extract EnOcean state
  //   final enOceanState = data.firstWhereOrNull((c) => c.type == SensorConnectionType.enOcean)?.state;
  //   if (enOceanState == null) {
  //     warning('Could not get EnOcean state', category: LogCategory.connection);
  //     return;
  //   }
  //
  //   // Check if EnOcean is ready
  //   if (enOceanState == SensorConnectionState.unknown) {
  //     // State not ready yet
  //     warning('EnOcean state not ready yet, retrying in 1 second...', category: LogCategory.connection);
  //     await Future.delayed(const Duration(seconds: 1), _checkEnOceanState);
  //     return;
  //   }
  // }

  void _checkForAvailableUpdates() {
    const String? customURL = kDebugMode ? Br62ipFirmwareUpdateService.appTestingUpdateURL : null;
    Future.delayed(Duration.zero, () async {
      final result = await widget._device.services
          .firstOfTypeOrNull<IFirmwareUpdateService>()
          ?.checkForAvailableUpdates(customUpdateServerURL: customURL);
      if (result != null && result.success) {
        if (result.data == null) return;
        debug(
          result.data!.updatesAvailable
              ? 'Update ${result.data!.availableVersions.join(',')} available'
              : 'No updates available',
        );
        setState(() => updateAvailable = result.data!.updatesAvailable);
      }
    });
  }

  bool showBadgeForService(IService<IServiceData> service) {
    if (service is IFirmwareUpdateService) {
      return updateAvailable;
    }
    return false;
  }

  void resetBadgeForService(IService<IServiceData> service) {
    if (service is IFirmwareUpdateService) {
      setState(() => updateAvailable = false);
    }
  }

  List<Widget> _buildServiceList(BuildContext context) {
    final List<Widget> widgets = <Widget>[];
    for (final category in widget._device.serviceCategories) {
      if (category.position == ServicePosition.main) {
        if (category.services.isNotEmpty) {
          widgets.add(
            Padding(
              padding: const EdgeInsets.symmetric(vertical: EltakoPadding.xs),
              child: Text(category.localizedName.toUpperCase(), style: Theme.of(context).textTheme.titleMedium),
            ),
          );
        }
        for (int index = 0; index < category.services.length; index++) {
          widgets.add(
            Padding(
              padding: const EdgeInsets.only(bottom: EltakoPadding.xxxs),
              child: ServiceRow(
                position: RowPositionHelper.fromIndex(index, category.services.length),
                service: category.services[index],
                onTap: () async {
                  final currentRoute = GoRouter.of(context).routeInformationProvider.value.uri;
                  final destination =
                      // ignore: avoid_string_literals_inside_widget
                      '${currentRoute.path}/${EltakoRouter.serviceRouteName}/${category.services[index].key.key}';
                  await context.push(destination);
                },
              ),
            ),
          );
        }
      }
    }
    return widgets;
  }

  List<PopupMenuEntry> _buildPopupMenuItem(BuildContext context) {
    final List<PopupMenuEntry<dynamic>> entries = <PopupMenuEntry<dynamic>>[];
    for (final category in widget._device.serviceCategories) {
      if (category.position == ServicePosition.menuBar) {
        for (final service in category.services) {
          entries.add(
            PopupMenuItem(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    service.localizedName,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: service is IResetService ? EltakoStyle.of(context).error : null,
                    ),
                  ),
                  Badge(
                    alignment: Alignment.centerRight,
                    largeSize: 8,
                    smallSize: 8,
                    isLabelVisible: showBadgeForService(service),
                    backgroundColor: EltakoStyle.of(context).primaryBlue,
                  ),
                ],
              ),
              onTap: () async {
                resetBadgeForService(service);
                await service.getData();
                if (!context.mounted) return;
                final currentRoute = GoRouter.of(context).routeInformationProvider.value.uri;

                final destination =
                    // ignore: avoid_string_literals_inside_widget
                    '${currentRoute.path}/${EltakoRouter.serviceRouteName}/${service.key.key}';
                await context.push(destination, extra: service);
              },
            ),
          );
        }
      }
    }

    // TODO(FE): Add favorite setting to popup menu
    // --- Demo implementation ---
    if (kDebugMode) {
      entries.add(
        PopupMenuItem(
          // ignore: avoid_string_literals_inside_widget
          child: const Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [Text('Toggle is favorite')]),
          onTap: () async => widget._device.deviceInfo.setFavorite(!widget._device.deviceInfo.isFavorite),
        ),
      );
    }
    // --- End of demo implementation ---

    return entries;
  }

  // endregion
  @override
  Widget build(BuildContext context) => NavigationView(
    header: DeviceNavigationHeader(
      device: widget._device,
      leading: widget._isDemo ? NavigationItem(icon: Assets.icons.arrowLeft.outline.light, onTap: () => {}) : null,
      trailing: Badge(
        alignment: Alignment.topRight,
        largeSize: 16,
        smallSize: 16,
        isLabelVisible: showBadge,
        backgroundColor: EltakoStyle.of(context).primaryBlue,
        child: EltakoPopupMenu.device(
          deviceUrl: widget._device.deviceInfo.deviceType.url,
          items: _buildPopupMenuItem(context),
        ),
      ),
    ),
    child: ListenableBuilder(
      listenable: widget._device,
      builder:
          (context, child) => SliverList(
            delegate: SliverChildListDelegate.fixed(_buildServiceList(context), addAutomaticKeepAlives: false),
          ),
    ),
  );
}
