//
//  sub_device_configuration_add_on.dart
//  EltakoConnect
//
//  Created by <PERSON> on 07.03.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:convert';

import 'package:eltako_connect/enumeration/sub_device_configuration_add_on_type.dart';
import 'package:eltako_connect/extension/map_extension.dart';
import 'package:eltako_connect/mixin/extended_output.dart';
import 'package:eltako_connect/mixin/request_output.dart';
import 'package:eltako_connect/model/shutter_tilt_automatic.dart';

/// Class for storing sub device configuration add ons
class SubDeviceConfigurationAddOn with ExtendedOutput, RequestOutput {
  // region [PROPERTIES]

  /// Identifier
  final String identifier;

  /// Time
  final int? time;

  /// Data
  final int? data;

  // endregion

  // region [COMPUTED PROPERTIES]

  /// Sub device configuration add on type
  SubDeviceConfigurationAddOnType get type => SubDeviceConfigurationAddOnType.fromEltakoKey(identifier);

  /// Is add on for automatic tilt
  bool get isTiltAutomatic =>
      [SubDeviceConfigurationAddOnType.automaticSlat, SubDeviceConfigurationAddOnType.automaticReversal].contains(type);

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  SubDeviceConfigurationAddOn({required this.identifier, this.time, this.data});

  /// Create add on for target value
  factory SubDeviceConfigurationAddOn.targetValue(int value) =>
      SubDeviceConfigurationAddOn(identifier: SubDeviceConfigurationAddOnType.targetValue.eltakoKey, data: value);

  /// Create add on for off timer
  factory SubDeviceConfigurationAddOn.offTimer(Duration duration) => SubDeviceConfigurationAddOn(
    identifier: SubDeviceConfigurationAddOnType.offTimer.eltakoKey,
    time: duration.inMilliseconds,
  );

  /// Create add on for long press off timer
  factory SubDeviceConfigurationAddOn.longPressOffTimer(Duration duration) => SubDeviceConfigurationAddOn(
    identifier: SubDeviceConfigurationAddOnType.longPressOffTimer.eltakoKey,
    time: duration.inMilliseconds,
  );

  /// Create add on for pre warning before off
  factory SubDeviceConfigurationAddOn.preWarning() =>
      SubDeviceConfigurationAddOn(identifier: SubDeviceConfigurationAddOnType.preWarning.eltakoKey);

  /// Create add on for short press time
  factory SubDeviceConfigurationAddOn.shortPressNormallyOpened(Duration duration) => SubDeviceConfigurationAddOn(
    identifier: SubDeviceConfigurationAddOnType.shortPressNormallyOpened.eltakoKey,
    time: duration.inMilliseconds,
  );

  /// Create add on for extra long press short press time
  factory SubDeviceConfigurationAddOn.extraLongPressShortPressTime(Duration duration) => SubDeviceConfigurationAddOn(
    identifier: SubDeviceConfigurationAddOnType.extraLongPressNormallyOpened.eltakoKey,
    time: duration.inMilliseconds,
  );

  /// Create add on for tilt automatic
  factory SubDeviceConfigurationAddOn.tiltAutomatic(ShutterTiltAutomatic automatic) =>
      SubDeviceConfigurationAddOn(identifier: automatic.type.addOnType.eltakoKey, data: automatic.value);

  /// Create add on for target angle
  factory SubDeviceConfigurationAddOn.targetAngle(int angle) =>
      SubDeviceConfigurationAddOn(identifier: SubDeviceConfigurationAddOnType.targetAngle.eltakoKey, data: angle);

  /// Create add on for scene value override
  factory SubDeviceConfigurationAddOn.sceneValueOverride() =>
      SubDeviceConfigurationAddOn(identifier: SubDeviceConfigurationAddOnType.sceneValueOverride.eltakoKey);

  /// Create add on for scene reference movement
  factory SubDeviceConfigurationAddOn.sceneReferenceMovement() =>
      SubDeviceConfigurationAddOn(identifier: SubDeviceConfigurationAddOnType.sceneReferenceMovement.eltakoKey);

  /// Create time sub device configuration add on from [time] and [identifier]
  factory SubDeviceConfigurationAddOn.forTime(int? time, String identifier) =>
      SubDeviceConfigurationAddOn(identifier: identifier, time: time);

  /// Create data sub device configuration add on from [data] and [identifier]
  factory SubDeviceConfigurationAddOn.forData(int? data, String identifier) =>
      SubDeviceConfigurationAddOn(identifier: identifier, data: data);

  /// Create sub device configuration add on from [json] response
  static SubDeviceConfigurationAddOn? fromResponseJson(String json) {
    try {
      // Decode json
      final root = jsonDecode(json) as Map<String, dynamic>;

      // Extract values
      final identifier = root.getOrNull<String>('type');
      final time = root.getOrNull<int>('time');
      final data = root.getOrNull<int>('data');
      if (identifier == null) return null;

      // Create and return sub device configuration add on
      return SubDeviceConfigurationAddOn(identifier: identifier, time: time, data: data);
    } catch (e) {
      return null;
    }
  }

  /// Create sub device configuration add on from [map]
  static SubDeviceConfigurationAddOn? fromMap(Map<String, dynamic> map) {
    // Extract values
    final identifier = map.getOrNull<String>('identifier');
    final time = map.getOrNull<int>('time');
    final data = map.getOrNull<int>('data');
    if (identifier == null) return null;

    // Create sub device configuration add on
    return SubDeviceConfigurationAddOn(identifier: identifier, time: time, data: data);
  }

  @override
  Map<String, dynamic> toMap() => {
    'identifier': identifier,
    if (time != null) 'time': time,
    if (data != null) 'data': data,
  };

  @override
  Map<String, dynamic> toRequestMap() => {'type': identifier, 'time': time, 'data': data};

  // endregion
}
