//
//  button_shutter_scene_add_on_configuration.dart
//  EltakoConnect
//
//  Created by <PERSON> on 18.12.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/enumeration/button_channel_configuration_function.dart';
import 'package:eltako_connect/enumeration/button_channel_configuration_logic.dart';
import 'package:eltako_connect/enumeration/sub_device_configuration_add_on_type.dart';
import 'package:eltako_connect/enumeration/sub_device_configuration_logic.dart';
import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/extension/map_extension.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/shutter/i_button_shutter_add_on_configuration.dart';
import 'package:eltako_connect/model/sub_device/sub_device_configuration_add_on.dart';

/// Class for add on configurations for scene buttons (for shutters)
class ButtonShutterSceneAddOnConfiguration extends IButtonShutterAddOnConfiguration {
  // region [CONSTANTS]

  /// Configuration type key
  static const String key = 'button_shutter_scene_add_on';

  @override
  final ButtonChannelConfigurationLogic buttonLogic = ButtonChannelConfigurationLogic.scene;

  /// Supported functions
  static final List<ButtonChannelConfigurationFunction> supportedFunctions =
      IButtonShutterAddOnConfiguration.supportedFunctions;

  /// Supported API logics
  static final List<SubDeviceConfigurationLogic> supportedAPILogics =
      IButtonShutterAddOnConfiguration.supportedAPILogics;

  // endregion

  // region [PROPERTIES]

  /// Target value
  int get targetValue => _targetValue;

  /// Target value
  set targetValue(int value) {
    _targetValue = value;
    notifyListeners();
  }

  /// Target angle
  int get targetAngle => _targetAngle;

  /// Target angle
  set targetAngle(int value) {
    _targetAngle = value;
    notifyListeners();
  }

  /// Override scene value on long press
  bool get sceneValueOverride => _sceneValueOverride;

  /// Override scene value on long press
  set sceneValueOverride(bool value) {
    _sceneValueOverride = value;
    notifyListeners();
  }

  /// Make reference movement before setting position
  bool get sceneReferenceMovement => _sceneReferenceMovement;

  /// Make reference movement before setting position
  set sceneReferenceMovement(bool value) {
    _sceneReferenceMovement = value;
    notifyListeners();
  }

  // endregion

  // region [VARIABLES]

  /// Target value
  int _targetValue;

  /// Target angle
  int _targetAngle;

  /// Override scene value on long press
  bool _sceneValueOverride;

  /// Make reference movement before setting position
  bool _sceneReferenceMovement;

  // endregion

  // region [COMPUTED PROPERTIES]

  @override
  List<SubDeviceConfigurationAddOn> get addOns => [
    ...super.addOns,
    SubDeviceConfigurationAddOn.targetValue(targetValue),
    SubDeviceConfigurationAddOn.targetAngle(targetAngle),
    if (sceneValueOverride) SubDeviceConfigurationAddOn.sceneValueOverride(),
    if (sceneReferenceMovement) SubDeviceConfigurationAddOn.sceneReferenceMovement(),
  ];

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  ButtonShutterSceneAddOnConfiguration({
    super.function,
    int targetValue = 100,
    int targetAngle = 0,
    bool sceneValueOverride = true,
    bool sceneReferenceMovement = true,
  }) : _sceneReferenceMovement = sceneReferenceMovement,
       _sceneValueOverride = sceneValueOverride,
       _targetAngle = targetAngle,
       _targetValue = targetValue,
       super(key: key);

  /// Create add on configuration from logic [logic] and [addOns]
  static ButtonShutterSceneAddOnConfiguration? fromLogic(
    SubDeviceConfigurationLogic logic, {
    required List<SubDeviceConfigurationAddOn> addOns,
  }) {
    // Get add ons
    final targetValue = addOns.firstWhereOrNull((a) => a.type == SubDeviceConfigurationAddOnType.targetValue)?.data;
    if (targetValue == null) {
      return null;
    }
    final targetAngle = addOns.firstWhereOrNull((a) => a.type == SubDeviceConfigurationAddOnType.targetAngle)?.data;
    final sceneValueOverride = addOns.any((a) => a.type == SubDeviceConfigurationAddOnType.sceneValueOverride);
    final sceneReferenceMovement = addOns.any((a) => a.type == SubDeviceConfigurationAddOnType.sceneReferenceMovement);

    // Create and return configuration
    return IButtonShutterAddOnConfiguration.fromLogic(
      logic,
      addOns: addOns,
      create:
          (function) => ButtonShutterSceneAddOnConfiguration(
            function: function,
            targetValue: targetValue,
            targetAngle: targetAngle ?? 0,
            sceneValueOverride: sceneValueOverride,
            sceneReferenceMovement: sceneReferenceMovement,
          ),
    );
  }

  /// Create add on configuration from [map]
  static ButtonShutterSceneAddOnConfiguration? fromMap(Map<String, dynamic> map) {
    // Check type
    final type = map.getOrNull<String>('type');
    if (type != key) {
      return null;
    }

    // Extract values
    final function = ButtonChannelConfigurationFunction.fromKey(map.get('function', fallback: ''));
    final targetValue = map.getOrNull<int>('target_value');
    final targetAngle = map.getOrNull<int>('target_angle');
    final sceneValueOverride = map.getOrNull<bool>('scene_value_override');
    final sceneReferenceMovement = map.getOrNull<bool>('scene_reference_movement');
    if (function == null ||
        targetValue == null ||
        targetAngle == null ||
        sceneValueOverride == null ||
        sceneReferenceMovement == null) {
      return null;
    }

    // Create and return data
    return ButtonShutterSceneAddOnConfiguration(
      function: function,
      targetValue: targetValue,
      targetAngle: targetAngle,
      sceneValueOverride: sceneValueOverride,
      sceneReferenceMovement: sceneReferenceMovement,
    );
  }

  @override
  Map<String, dynamic> toMap() => {
    'type': key,
    'function': function.key,
    'target_value': targetValue,
    'target_angle': targetAngle,
    'scene_value_override': sceneValueOverride,
    'scene_reference_movement': sceneReferenceMovement,
  };

  // endregion
}
