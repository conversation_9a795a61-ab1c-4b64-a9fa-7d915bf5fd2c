//
//  share_result.dart
//  EltakoConnect
//
//  Created by <PERSON> on 22.08.25.
//  Copyright © 2025 Eltako GmbH. All rights reserved.
//

/// Result of a share operation
sealed class EltakoShareResult<T, E extends Object> {
  const EltakoShareResult();
}

/// Successful share operation
final class EltakoShareSuccess<T, E extends Object> extends EltakoShareResult<T, E> {
  /// Default constructor
  const EltakoShareSuccess(this.data);

  /// result data of the share operation
  final T data;
}

/// Failed share operation
final class EltakoShareFailure<T, E extends Object> extends EltakoShareResult<T, E> {
  /// Default constructor
  const EltakoShareFailure(this.error, {this.message});

  /// error of the share operation
  final E error;

  /// optional message of the share operation
  final String? message;
}

/// Dismissed share operation
final class EltakoShareDismissed<T, E extends Object> extends EltakoShareResult<T, E> {
  /// Default constructor
  const EltakoShareDismissed();
}
