//
//  sub_device_configuration_add_on_type.dart
//  EltakoConnect
//
//  Created by <PERSON> on 23.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/extension/list_extension.dart';

/// Enumeration for sub device configuration add on types
enum SubDeviceConfigurationAddOnType {
  // region [CASES]

  /// Target value
  targetValue,

  /// Off timer
  offTimer,

  /// Long press off timer
  longPressOffTimer,

  /// Pre warning
  preWarning,

  /// Short press normally opened
  shortPressNormallyOpened,

  /// Extra long press normally opened
  extraLongPressNormallyOpened,

  /// Automatic reversal tilt
  automaticReversal,

  /// Automatic slat tilt
  automaticSlat,

  /// Target angle
  targetAngle,

  /// Override scene value on long press
  sceneValueOverride,

  /// Make reference movement before setting position
  sceneReferenceMovement,

  /// Unknown
  unknown;

  // endregion

  // region [COMPUTED PROPERTIES]

  /// Name
  String get name {
    switch (this) {
      case SubDeviceConfigurationAddOnType.targetValue:
        return 'Target value';
      case SubDeviceConfigurationAddOnType.offTimer:
        return 'Off timer';
      case SubDeviceConfigurationAddOnType.longPressOffTimer:
        return 'Long press off timer';
      case SubDeviceConfigurationAddOnType.preWarning:
        return 'Pre warning';
      case SubDeviceConfigurationAddOnType.shortPressNormallyOpened:
        return 'Short press normally opened';
      case SubDeviceConfigurationAddOnType.extraLongPressNormallyOpened:
        return 'Extra long press normally opened';
      case SubDeviceConfigurationAddOnType.automaticReversal:
        return 'Automatic reversal';
      case SubDeviceConfigurationAddOnType.automaticSlat:
        return 'Automatic slat';
      case SubDeviceConfigurationAddOnType.targetAngle:
        return 'Target angle';
      case SubDeviceConfigurationAddOnType.sceneValueOverride:
        return 'Override scene value on long press';
      case SubDeviceConfigurationAddOnType.sceneReferenceMovement:
        return 'Make reference movement before setting position';
      case SubDeviceConfigurationAddOnType.unknown:
        return 'Unknown';
    }
  }

  /// Eltako key
  String get eltakoKey {
    switch (this) {
      case SubDeviceConfigurationAddOnType.targetValue:
        return 'targetValue';
      case SubDeviceConfigurationAddOnType.offTimer:
        return 'offTimer';
      case SubDeviceConfigurationAddOnType.longPressOffTimer:
        return 'longPressOffTimer';
      case SubDeviceConfigurationAddOnType.extraLongPressNormallyOpened:
        return 'extraLongPressNormallyOpened';
      case SubDeviceConfigurationAddOnType.preWarning:
        return 'prewarning';
      case SubDeviceConfigurationAddOnType.shortPressNormallyOpened:
        return 'shortPressNormallyOpened';
      case SubDeviceConfigurationAddOnType.automaticReversal:
        return 'automaticReversal';
      case SubDeviceConfigurationAddOnType.automaticSlat:
        return 'automaticSlat';
      case SubDeviceConfigurationAddOnType.targetAngle:
        return 'targetAngle';
      case SubDeviceConfigurationAddOnType.sceneValueOverride:
        return 'sceneValueOverride';
      case SubDeviceConfigurationAddOnType.sceneReferenceMovement:
        return 'sceneWithReferenceMovement';
      case SubDeviceConfigurationAddOnType.unknown:
        return '';
    }
  }

  /// Key
  String get key {
    switch (this) {
      case SubDeviceConfigurationAddOnType.targetValue:
        return 'target_value';
      case SubDeviceConfigurationAddOnType.offTimer:
        return 'off_timer';
      case SubDeviceConfigurationAddOnType.longPressOffTimer:
        return 'long_press_off_timer';
      case SubDeviceConfigurationAddOnType.preWarning:
        return 'pre_warning';
      case SubDeviceConfigurationAddOnType.shortPressNormallyOpened:
        return 'short_press_normally_opened';
      case SubDeviceConfigurationAddOnType.extraLongPressNormallyOpened:
        return 'extra_long_press_normally_opened';
      case SubDeviceConfigurationAddOnType.automaticReversal:
        return 'automatic_reversal';
      case SubDeviceConfigurationAddOnType.automaticSlat:
        return 'automatic_slat';
      case SubDeviceConfigurationAddOnType.targetAngle:
        return 'target_angle';
      case SubDeviceConfigurationAddOnType.sceneValueOverride:
        return 'scene_value_override';
      case SubDeviceConfigurationAddOnType.sceneReferenceMovement:
        return 'scene_reference_movement';
      case SubDeviceConfigurationAddOnType.unknown:
        return '';
    }
  }

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Get sub device configuration add on type from Eltako [key]
  static SubDeviceConfigurationAddOnType fromEltakoKey(String key) =>
      SubDeviceConfigurationAddOnType.values.firstWhereOrNull((e) => e.eltakoKey == key) ??
      SubDeviceConfigurationAddOnType.unknown;

  // endregion
}
