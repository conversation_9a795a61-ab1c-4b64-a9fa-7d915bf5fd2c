//
//  localized_error.dart
//  EltakoConnect
//
//  Created by <PERSON> on 11.12.23.
//  Copyright © 2023 Eltako GmbH. All rights reserved.
//

/// Enumeration with possible data result errors
enum LocalizedError {
  // region [CASES]

  /// No error
  none,

  /// Bluetooth (controller) error
  bluetoothError,

  /// Bluetooth inactive
  bluetoothInactive,

  /// Operation canceled
  canceled,

  /// Colliding time program
  collidingProgram,

  /// Duplicated time program
  duplicatedProgram,

  /// Connection error
  connectionFailed,

  /// Database error
  databaseError,

  /// Device error
  deviceError,

  /// Disconnect failed
  disconnectFailed,

  /// Discovery failed
  discoveryFailed,

  /// Invalid parameter
  invalidParameter,

  /// Location not found
  locationNotFound,

  /// Location service inactive
  locationInactive,

  /// Login failed
  loginFailed,

  /// Login needed
  loginNeeded,

  /// Not found
  notFound,

  /// Not readable
  notReadable,

  /// Not supported
  notSupported,

  /// Not writable
  notWritable,

  /// OS error
  osError,

  /// Pairing error
  pairingError,

  /// Permission denied
  permissionDenied,

  /// Permission permanently denied
  permissionPermanentlyDenied,

  /// Parsing failed
  parsingFailed,

  /// Provisioning failed
  provisioningFailed,

  /// Provisioning and disconnect failed
  provisioningAndDisconnectFailed,

  /// Request failed
  requestFailed,

  /// Timeout
  timeout,

  /// Unexpected null value
  unexpectedNullValue,

  /// Unknown error
  unknown;

  // endregion

  // region [COMPUTED PROPERTIES]

  /// Localization key
  String get key {
    switch (this) {
      case LocalizedError.none:
        return 'error.none';
      case LocalizedError.bluetoothError:
        return 'error.bluetooth';
      case LocalizedError.bluetoothInactive:
        return 'error.bluetooth.inactive';
      case LocalizedError.canceled:
        return 'error.canceled';
      case LocalizedError.collidingProgram:
        return 'error.time_program.colliding';
      case LocalizedError.duplicatedProgram:
        return 'error.time_program.duplicate';
      case LocalizedError.connectionFailed:
        return 'error.connection.failed';
      case LocalizedError.databaseError:
        return 'error.database';
      case LocalizedError.disconnectFailed:
        return 'error.connection.disconnect.failed';
      case LocalizedError.discoveryFailed:
        return 'error.connection.discovery.failed';
      case LocalizedError.deviceError:
        return 'error.device';
      case LocalizedError.locationNotFound:
        return 'error.location.not_found';
      case LocalizedError.locationInactive:
        return 'error.location.inactive';
      case LocalizedError.invalidParameter:
        return 'error.parameter.invalid';
      case LocalizedError.loginFailed:
        return 'error.login.failed';
      case LocalizedError.loginNeeded:
        return 'error.login.needed';
      case LocalizedError.notFound:
        return 'error.not_found';
      case LocalizedError.notReadable:
        return 'error.not_readable';
      case LocalizedError.notSupported:
        return 'error.not_supported';
      case LocalizedError.notWritable:
        return 'error.not_writable';
      case LocalizedError.osError:
        return 'error.os';
      case LocalizedError.pairingError:
        return 'error.pairing';
      case LocalizedError.parsingFailed:
        return 'error.parsing_failed';
      case LocalizedError.permissionDenied:
        return 'error.permission.denied';
      case LocalizedError.permissionPermanentlyDenied:
        return 'error.permission.permanently_denied';
      case LocalizedError.provisioningFailed:
        return 'error.connection.provisioning_failed';
      case LocalizedError.provisioningAndDisconnectFailed:
        return 'error.connection.provisioning_and_disconnect_failed';
      case LocalizedError.requestFailed:
        return 'error.request_failed';
      case LocalizedError.timeout:
        return 'error.connection.timeout';
      case LocalizedError.unexpectedNullValue:
        return 'error.unexpected_null_value';
      case LocalizedError.unknown:
        return 'error.unknown';
    }
  }

  /// Error message
  String get message {
    switch (this) {
      case LocalizedError.none:
        return 'Operation successful';
      case LocalizedError.bluetoothError:
        return 'Bluetooth error';
      case LocalizedError.canceled:
        return 'Operation canceled';
      case LocalizedError.collidingProgram:
        return 'Time program collides with another program';
      case LocalizedError.duplicatedProgram:
        return 'Time program is duplicate of another program';
      case LocalizedError.connectionFailed:
        return 'Connection failed';
      case LocalizedError.databaseError:
        return 'Database error';
      case LocalizedError.deviceError:
        return 'Device error';
      case LocalizedError.disconnectFailed:
        return 'Disconnect failed';
      case LocalizedError.discoveryFailed:
        return 'Discovery failed';
      case LocalizedError.invalidParameter:
        return 'Invalid parameter';
      case LocalizedError.locationNotFound:
        return 'Location not found';
      case LocalizedError.locationInactive:
        return 'Location services inactive';
      case LocalizedError.loginFailed:
        return 'Login failed';
      case LocalizedError.loginNeeded:
        return 'Login needed';
      case LocalizedError.notFound:
        return 'Not found';
      case LocalizedError.notReadable:
        return 'Not readable';
      case LocalizedError.notSupported:
        return 'Not supported';
      case LocalizedError.notWritable:
        return 'Not writable';
      case LocalizedError.osError:
        return 'OS error';
      case LocalizedError.pairingError:
        return 'Pairing error';
      case LocalizedError.parsingFailed:
        return 'Parsing failed';
      case LocalizedError.permissionDenied:
        return 'Permission denied';
      case LocalizedError.permissionPermanentlyDenied:
        return 'Permission permanently denied';
      case LocalizedError.provisioningFailed:
        return 'Provisioning failed';
      case LocalizedError.provisioningAndDisconnectFailed:
        return 'Provisioning and disconnect failed';
      case LocalizedError.requestFailed:
        return 'Request failed';
      case LocalizedError.timeout:
        return 'Timeout';
      case LocalizedError.unexpectedNullValue:
        return 'Unexpected null value';
      case LocalizedError.unknown:
        return 'Unknown error';
      case LocalizedError.bluetoothInactive:
        return 'Bluetooth inactive';
    }
  }

  // endregion
}
