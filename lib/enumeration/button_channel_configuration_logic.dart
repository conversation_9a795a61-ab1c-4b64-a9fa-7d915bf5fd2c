//
//  button_channel_configuration_logic.dart
//  EltakoConnect
//
//  Created by <PERSON> on 22.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/device/i_device.dart';
import 'package:eltako_connect/device/wifi/esb62ip.dart';
import 'package:eltako_connect/device/wifi/esb64ip.dart';
import 'package:eltako_connect/device/wifi/esr62ip.dart';
import 'package:eltako_connect/device/wifi/esr64ip.dart';
import 'package:eltako_connect/device/wifi/eud62ip.dart';
import 'package:eltako_connect/device/wifi/eud64ip.dart';
import 'package:eltako_connect/enumeration/button_channel_configuration_function.dart';
import 'package:eltako_connect/enumeration/device_type_category.dart';
import 'package:eltako_connect/enumeration/product_characteristic_identifier.dart';
import 'package:eltako_connect/enumeration/sub_device_configuration_add_on_type.dart';
import 'package:eltako_connect/enumeration/sub_device_configuration_logic.dart';
import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/extension/object_extension.dart';
import 'package:eltako_connect/extension/string_extension.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations_extension.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_central_down_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_central_up_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_directional_down_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_directional_up_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_scene_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_universal_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/global/button_matter_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/global/button_unset_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/global/button_wired_changeover_switch_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/i_button_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/i_button_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/relay/button_relay_off_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/relay/button_relay_on_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/relay/button_relay_universal_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/shutter/button_shutter_directional_down_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/shutter/button_shutter_directional_up_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/shutter/button_shutter_scene_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/shutter/button_shutter_universal_add_on_configuration.dart';
import 'package:eltako_connect/model/sub_device/sub_device_automation.dart';
import 'package:eltako_connect/model/sub_device/sub_device_configuration_add_on.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:flutter/widgets.dart';

/// Enumeration for button channel configuration logics
enum ButtonChannelConfigurationLogic {
  // region [CASES]

  /// Changeover switch (wired only)
  changeoverSwitch,

  /// Directional up
  directionalUp,

  /// Directional down
  directionalDown,

  /// Universal
  universal,

  /// Central up
  centralUp,

  /// Central down
  centralDown,

  /// On
  on,

  /// Off
  off,

  /// Scene
  scene,

  /// Matter controlled
  matterControlled,

  /// Unset
  unset,

  /// Unknown
  unknown;

  // endregion

  // region [CONSTANTS]

  /// Default automation name
  static final String _defaultAutomationName = 'Default impulse switch automation'.trimUp;

  /// ESB default automation search string
  static const String _esbDefaultAutomationSearchString = 'Automation for ';

  // endregion

  // region [COMPUTED PROPERTIES]

  /// Name
  String get name {
    switch (this) {
      case ButtonChannelConfigurationLogic.changeoverSwitch:
        return 'Changeover switch';
      case ButtonChannelConfigurationLogic.directionalUp:
        return 'Directional up';
      case ButtonChannelConfigurationLogic.directionalDown:
        return 'Directional down';
      case ButtonChannelConfigurationLogic.universal:
        return 'Universal';
      case ButtonChannelConfigurationLogic.centralUp:
        return 'Central up';
      case ButtonChannelConfigurationLogic.centralDown:
        return 'Central down';
      case ButtonChannelConfigurationLogic.on:
        return 'On';
      case ButtonChannelConfigurationLogic.off:
        return 'Off';
      case ButtonChannelConfigurationLogic.scene:
        return 'Scene';
      case ButtonChannelConfigurationLogic.matterControlled:
        return 'Matter controlled';
      case ButtonChannelConfigurationLogic.unset:
        return 'Unset';
      case ButtonChannelConfigurationLogic.unknown:
        return 'Unknown';
    }
  }

  /// Name
  String get localizedName {
    switch (this) {
      case ButtonChannelConfigurationLogic.changeoverSwitch:
        return l10n.detailsConfigurationSwitchesSwitch;
      case ButtonChannelConfigurationLogic.universal:
        return l10n.generalTextUniversalbutton;
      case ButtonChannelConfigurationLogic.centralUp:
        return l10n.centralOn;
      case ButtonChannelConfigurationLogic.centralDown:
        return l10n.centralOff;
      case ButtonChannelConfigurationLogic.directionalUp:
      case ButtonChannelConfigurationLogic.on:
        return l10n.generalToggleOn;
      case ButtonChannelConfigurationLogic.directionalDown:
      case ButtonChannelConfigurationLogic.off:
        return l10n.generalToggleOff;
      case ButtonChannelConfigurationLogic.scene:
        return l10n.generalTextSceneButton;
      case ButtonChannelConfigurationLogic.matterControlled:
        return l10n.generalMatterText;
      case ButtonChannelConfigurationLogic.unset:
        return l10n.buttonUnset;
      case ButtonChannelConfigurationLogic.unknown:
        return l10n.timerListitemUnknown;
    }
  }

  /// Localized Description
  String get localizedDescription {
    switch (this) {
      case ButtonChannelConfigurationLogic.changeoverSwitch:
        return l10n.detailsConfigurationSwitchesSwitchDescription;
      case ButtonChannelConfigurationLogic.universal:
        return l10n.generalDescriptionUniversalbutton;
      case ButtonChannelConfigurationLogic.centralUp:
      case ButtonChannelConfigurationLogic.centralDown:
        return l10n.centralButton;
      case ButtonChannelConfigurationLogic.directionalUp:
      case ButtonChannelConfigurationLogic.directionalDown:
      case ButtonChannelConfigurationLogic.on:
      case ButtonChannelConfigurationLogic.off:
        return l10n.generalTextDirectionalbutton;
      case ButtonChannelConfigurationLogic.scene:
        return l10n.buttonSceneDescription;
      case ButtonChannelConfigurationLogic.matterControlled:
        return l10n.matterForwardingDescription;
      case ButtonChannelConfigurationLogic.unset:
        return l10n.buttonUnsetDescription;
      case ButtonChannelConfigurationLogic.unknown:
        return StringExtension.empty;
    }
  }

  /// Image color
  Color? imageColor(BuildContext context) {
    switch (this) {
      case ButtonChannelConfigurationLogic.matterControlled:
        return EltakoStyle.of(context).primaryText;
      default:
        return null;
    }
  }

  /// Key
  String get key {
    switch (this) {
      case ButtonChannelConfigurationLogic.changeoverSwitch:
        return 'changeover_switch';
      case ButtonChannelConfigurationLogic.directionalUp:
        return 'directional_up';
      case ButtonChannelConfigurationLogic.directionalDown:
        return 'directional_down';
      case ButtonChannelConfigurationLogic.universal:
        return 'universal';
      case ButtonChannelConfigurationLogic.centralUp:
        return 'central_up';
      case ButtonChannelConfigurationLogic.centralDown:
        return 'central_down';
      case ButtonChannelConfigurationLogic.on:
        return 'on';
      case ButtonChannelConfigurationLogic.off:
        return 'off';
      case ButtonChannelConfigurationLogic.scene:
        return 'scene';
      case ButtonChannelConfigurationLogic.matterControlled:
        return 'matter_controlled';
      case ButtonChannelConfigurationLogic.unset:
        return 'unset';
      case ButtonChannelConfigurationLogic.unknown:
        return 'unknown';
    }
  }

  /// Configuration value
  int? get value {
    switch (this) {
      case ButtonChannelConfigurationLogic.on:
        return 100;
      case ButtonChannelConfigurationLogic.off:
        return 0;
      default:
        return null;
    }
  }

  /// Has selectable functions
  bool get hasSelectableFunctions {
    switch (this) {
      case ButtonChannelConfigurationLogic.off:
      case ButtonChannelConfigurationLogic.changeoverSwitch:
      case ButtonChannelConfigurationLogic.matterControlled:
      case ButtonChannelConfigurationLogic.unset:
      case ButtonChannelConfigurationLogic.unknown:
      case ButtonChannelConfigurationLogic.directionalUp:
      case ButtonChannelConfigurationLogic.directionalDown:
      case ButtonChannelConfigurationLogic.centralUp:
      case ButtonChannelConfigurationLogic.centralDown:
      case ButtonChannelConfigurationLogic.scene:
        return false;
      case ButtonChannelConfigurationLogic.universal:
      case ButtonChannelConfigurationLogic.on:
        return true;
    }
  }

  /// Logic has extensions
  bool hasExtensions(IDevice device) {
    final isEUD = device is Eud62ip || device is Eud64ip;
    final isESB = device is Esb62ip || device is Esb64ip;
    if (!isEUD && !isESB) return false;

    switch (this) {
      case ButtonChannelConfigurationLogic.off:
      case ButtonChannelConfigurationLogic.on:
      case ButtonChannelConfigurationLogic.changeoverSwitch:
      case ButtonChannelConfigurationLogic.matterControlled:
      case ButtonChannelConfigurationLogic.unset:
      case ButtonChannelConfigurationLogic.unknown:
        return false;
      case ButtonChannelConfigurationLogic.universal:
      case ButtonChannelConfigurationLogic.directionalUp:
      case ButtonChannelConfigurationLogic.directionalDown:
      case ButtonChannelConfigurationLogic.centralUp:
      case ButtonChannelConfigurationLogic.centralDown:
      case ButtonChannelConfigurationLogic.scene:
        return true;
    }
  }

  /// Automation name
  String get automationName => key;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Get supported configuration logics for [device]
  static List<IButtonConfiguration> getSupportedLogics(IDevice device, {required bool isWired}) {
    // Check device
    final isEsr = device is Esr62ip || device is Esr64ip;
    final isEud = device is Eud62ip || device is Eud64ip;
    final isEsb = device is Esb62ip || device is Esb64ip;
    if (!isEsr && !isEud && !isEsb) return [];

    // Create supported logics
    return [
      if (isEud) ...[
        ButtonDimmerUniversalLogicConfiguration(),
        ButtonDimmerDirectionalUpLogicConfiguration(),
        ButtonDimmerDirectionalDownLogicConfiguration(),
        ButtonDimmerCentralUpLogicConfiguration(),
        ButtonDimmerCentralDownLogicConfiguration(),
        ButtonDimmerSceneLogicConfiguration(),
      ],
      if (isEsr) ...[
        if (isWired) ButtonWiredChangeoverSwitchAddOnConfiguration(),
        ButtonRelayUniversalAddOnConfiguration(function: ButtonChannelConfigurationFunction.es),
        ButtonRelayOnAddOnConfiguration(function: ButtonChannelConfigurationFunction.es),
        ButtonRelayOffAddOnConfiguration(),
      ],
      if (isEsb) ...[
        ButtonShutterUniversalAddOnConfiguration(),
        ButtonShutterDirectionalUpAddOnConfiguration(),
        ButtonShutterDirectionalDownAddOnConfiguration(),
        ButtonShutterSceneAddOnConfiguration(),
      ],
      ButtonMatterConfiguration(),
      ButtonUnsetConfiguration(),
    ];
  }

  /// Create button channel configuration logic from [automation]
  static ButtonChannelConfigurationLogic fromAutomation(
    SubDeviceAutomation automation, {
    required DeviceTypeCategory deviceTypeCategory,
  }) {
    // Try getting logic from automation name
    final logic = fromKey(automation.name);
    if (deviceTypeCategory == DeviceTypeCategory.dimmer && logic == ButtonChannelConfigurationLogic.changeoverSwitch) {
      return ButtonChannelConfigurationLogic.universal;
    }
    if (logic != null) return logic;

    // Get logic from configuration type
    switch (automation.configuration.type) {
      case SubDeviceConfigurationLogic.buttonCloser:
      case SubDeviceConfigurationLogic.buttonOpener:
      case SubDeviceConfigurationLogic.buttonImpulse:
      case SubDeviceConfigurationLogic.buttonSwitch:
        return ButtonChannelConfigurationLogic.universal;
      case SubDeviceConfigurationLogic.buttonGroupRelay:
      case SubDeviceConfigurationLogic.buttonGroupSwitch:
      case SubDeviceConfigurationLogic.buttonGroupSwitchTipTurning:
        final targetValue =
            automation.configuration.addOns
                .firstWhereOrNull((a) => a.identifier == SubDeviceConfigurationAddOnType.targetValue.eltakoKey)
                ?.data;
        if (targetValue == null) return ButtonChannelConfigurationLogic.universal;
        return targetValue == 0
            ? ButtonChannelConfigurationLogic.directionalDown
            : ButtonChannelConfigurationLogic.directionalUp;
      case SubDeviceConfigurationLogic.buttonTargetValue:
        return automation.configuration.addOns
                    .firstWhereOrNull((a) => a.identifier == SubDeviceConfigurationAddOnType.targetValue.eltakoKey)
                    ?.data ==
                100
            ? ButtonChannelConfigurationLogic.on
            : ButtonChannelConfigurationLogic.off;
      case SubDeviceConfigurationLogic.unset:
        return ButtonChannelConfigurationLogic.unset;
      case SubDeviceConfigurationLogic.unknown:
        return ButtonChannelConfigurationLogic.unknown;
      case SubDeviceConfigurationLogic.buttonDimmerCentralDown:
        return ButtonChannelConfigurationLogic.centralDown;
      case SubDeviceConfigurationLogic.buttonDimmerCentralUp:
        return ButtonChannelConfigurationLogic.centralUp;
      case SubDeviceConfigurationLogic.buttonDimmerDirectionalDown:
        return ButtonChannelConfigurationLogic.directionalDown;
      case SubDeviceConfigurationLogic.buttonDimmerDirectionalUp:
        return ButtonChannelConfigurationLogic.directionalUp;
      case SubDeviceConfigurationLogic.buttonDimmerScene:
        return ButtonChannelConfigurationLogic.scene;
      case SubDeviceConfigurationLogic.buttonDimmerUniversal:
        return ButtonChannelConfigurationLogic.universal;
    }
  }

  /// Create button channel configuration logic from [key]
  static ButtonChannelConfigurationLogic? fromKey(String key) {
    // Check default ESB automation name
    final regex = RegExp('$_esbDefaultAutomationSearchString(\\w+)');
    final match = regex.firstMatch(key);
    if (match != null) {
      final channelName = match.group(1);
      if (channelName != null) {
        final identifier = ProductCharacteristicIdentifier.fromEltakoKey(channelName);
        switch (identifier) {
          case ProductCharacteristicIdentifier.inputChannel1:
            return ButtonChannelConfigurationLogic.directionalUp;
          case ProductCharacteristicIdentifier.inputChannel2:
            return ButtonChannelConfigurationLogic.directionalDown;
          case ProductCharacteristicIdentifier.inputBridged:
            return ButtonChannelConfigurationLogic.universal;
          default:
            break;
        }
      }
    }

    // Check for default automation name
    return key.trimUp == _defaultAutomationName
        ? ButtonChannelConfigurationLogic.changeoverSwitch
        : (key.isNotEmpty ? ButtonChannelConfigurationLogic.values.firstWhereOrNull((l) => l.key == key) : null);
  }

  /// Create sub device configuration logic from button channel configuration [function]
  SubDeviceConfigurationLogic createLogic({
    required ButtonChannelConfigurationFunction function,
    required DeviceTypeCategory deviceTypeCategory,
  }) {
    // Check device
    final isEud = deviceTypeCategory == DeviceTypeCategory.dimmer;

    // Create logic
    switch (this) {
      case ButtonChannelConfigurationLogic.changeoverSwitch:
        return SubDeviceConfigurationLogic.buttonSwitch;
      case ButtonChannelConfigurationLogic.directionalUp:
        return isEud
            ? SubDeviceConfigurationLogic.buttonDimmerDirectionalUp
            : function == ButtonChannelConfigurationFunction.gr
            ? SubDeviceConfigurationLogic.buttonCloser
            : SubDeviceConfigurationLogic.buttonImpulse;
      case ButtonChannelConfigurationLogic.directionalDown:
        return isEud
            ? SubDeviceConfigurationLogic.buttonDimmerDirectionalDown
            : function == ButtonChannelConfigurationFunction.gr
            ? SubDeviceConfigurationLogic.buttonCloser
            : SubDeviceConfigurationLogic.buttonImpulse;
      case ButtonChannelConfigurationLogic.universal:
        if (isEud) return SubDeviceConfigurationLogic.buttonDimmerUniversal;
        switch (function) {
          case ButtonChannelConfigurationFunction.es:
          case ButtonChannelConfigurationFunction.esv:
          case ButtonChannelConfigurationFunction.srv:
            return SubDeviceConfigurationLogic.buttonImpulse;
          case ButtonChannelConfigurationFunction.er:
            return SubDeviceConfigurationLogic.buttonCloser;
          case ButtonChannelConfigurationFunction.erInverse:
            return SubDeviceConfigurationLogic.buttonOpener;
          case ButtonChannelConfigurationFunction.gr:
            return SubDeviceConfigurationLogic.buttonGroupRelay;
          case ButtonChannelConfigurationFunction.gs:
            return SubDeviceConfigurationLogic.buttonGroupSwitch;
          case ButtonChannelConfigurationFunction.gs4:
            return SubDeviceConfigurationLogic.buttonGroupSwitchTipTurning;
          case ButtonChannelConfigurationFunction.none:
            return SubDeviceConfigurationLogic.unknown;
        }
      case ButtonChannelConfigurationLogic.centralUp:
        return isEud ? SubDeviceConfigurationLogic.buttonDimmerCentralUp : SubDeviceConfigurationLogic.unknown;
      case ButtonChannelConfigurationLogic.centralDown:
        return isEud ? SubDeviceConfigurationLogic.buttonDimmerCentralDown : SubDeviceConfigurationLogic.unknown;
      case ButtonChannelConfigurationLogic.on:
        switch (function) {
          case ButtonChannelConfigurationFunction.es:
          case ButtonChannelConfigurationFunction.esv:
          case ButtonChannelConfigurationFunction.srv:
            return SubDeviceConfigurationLogic.buttonTargetValue;
          case ButtonChannelConfigurationFunction.er:
          case ButtonChannelConfigurationFunction.erInverse:
          case ButtonChannelConfigurationFunction.gr:
          case ButtonChannelConfigurationFunction.gs:
          case ButtonChannelConfigurationFunction.gs4:
          case ButtonChannelConfigurationFunction.none:
            return SubDeviceConfigurationLogic.unknown;
        }
      case ButtonChannelConfigurationLogic.off:
        return SubDeviceConfigurationLogic.buttonTargetValue;
      case ButtonChannelConfigurationLogic.scene:
        return isEud ? SubDeviceConfigurationLogic.buttonDimmerScene : SubDeviceConfigurationLogic.buttonImpulse;
      case ButtonChannelConfigurationLogic.matterControlled:
      case ButtonChannelConfigurationLogic.unset:
        return SubDeviceConfigurationLogic.unset;
      case ButtonChannelConfigurationLogic.unknown:
        return SubDeviceConfigurationLogic.unknown;
    }
  }

  /// Create needed AddOns
  List<SubDeviceConfigurationAddOn> createNeededAddOns({required IButtonConfiguration? config}) =>
      config?.tryCast<IButtonAddOnConfiguration>()?.addOns ?? [];

  /// Image
  String? getImage({IDevice? device}) {
    switch (this) {
      case ButtonChannelConfigurationLogic.changeoverSwitch:
        return Assets.images.switchBehavior.singleMixClickBottom.path;
      case ButtonChannelConfigurationLogic.universal:
        return Assets.images.switchBehavior.singleClickBottom.path;
      case ButtonChannelConfigurationLogic.matterControlled:
        return Assets.images.logos.matter.matterLogo.path;
      case ButtonChannelConfigurationLogic.scene:
        // TODO(DK): Refactor like it was before Balkon did their thing
        return device is Esb64ip ? Assets.images.switchBehavior.singleClickBottom.path : null;
      case ButtonChannelConfigurationLogic.directionalUp:
      case ButtonChannelConfigurationLogic.directionalDown:
      case ButtonChannelConfigurationLogic.centralUp:
      case ButtonChannelConfigurationLogic.centralDown:
      case ButtonChannelConfigurationLogic.on:
      case ButtonChannelConfigurationLogic.off:
      case ButtonChannelConfigurationLogic.unset:
      case ButtonChannelConfigurationLogic.unknown:
        return null;
    }
  }

  // endregion
}
