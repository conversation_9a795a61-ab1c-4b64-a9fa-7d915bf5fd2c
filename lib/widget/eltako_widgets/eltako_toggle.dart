import 'dart:async';

import 'package:eltako_connect/theme/eltako_radius.dart';
import 'package:eltako_connect/theme/styles/eltako_style.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Eltako Toggle
class EltakoToggle extends StatefulWidget {
  // region [PUBLIC PROPERTIES]

  /// The value of the toggle.
  final bool value;

  /// Label
  final String? label;

  /// Label
  final String? description;

  /// The function that is called before toggle value switches.
  final Future<bool> Function(bool)? beforeChange;

  /// The function that is called when the toggle is switched.
  final Function(bool) onChanged;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// A toggle widget that can be used to switch a boolean value.
  const EltakoToggle({
    required this.onChanged,
    required this.value,
    super.key,
    this.beforeChange,
    this.label,
    this.description,
  });

  // endregion

  // region [OVERRIDES]

  @override
  State<EltakoToggle> createState() => _EltakoToggleState();

  // endregion
}

class _EltakoToggleState extends State<EltakoToggle> {
  // region [PUBLIC PROPERTIES]
  bool value = false;

  // endregion

  // region [OVERRIDES]

  @override
  void initState() {
    value = widget.value;
    super.initState();
  }

  @override
  Widget build(BuildContext context) => Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      InkWell(
        splashColor: Colors.transparent,
        borderRadius: BorderRadius.circular(EltakoRadius.s),
        onTap: () async {
          unawaited(HapticFeedback.selectionClick());
          if (widget.beforeChange != null) {
            final canChange = await widget.beforeChange!(!value);
            if (canChange) {
              widget.onChanged(!value);
              setState(() => value = !value);
            }
          } else {
            widget.onChanged(!value);
            setState(() => value = !value);
          }
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: widget.label != null ? MainAxisSize.max : MainAxisSize.min,
          children: [
            if (widget.label != null) Text(widget.label!, style: Theme.of(context).textTheme.titleMedium),
            CupertinoSwitch(
              activeTrackColor: WidgetStateColor.resolveWith((states) => EltakoStyle.of(context).primaryBlue),
              thumbColor: WidgetStateColor.resolveWith((states) => EltakoStyle.of(context).white),
              value: value,
              onChanged: (newValue) async {
                if (widget.beforeChange != null) {
                  await handleBeforeChange(newValue);
                } else {
                  handleOnChange(newValue);
                }
              },
            ),
          ],
        ),
      ),
      if (widget.description != null)
        Row(children: [Expanded(child: Text(widget.description!, softWrap: true)), const SizedBox(width: 51.0)]),
    ],
  );

  Future<void> handleBeforeChange(bool newValue) async {
    unawaited(HapticFeedback.selectionClick());
    final canChange = await widget.beforeChange!(newValue);
    if (canChange) {
      widget.onChanged(newValue);
      setState(() => value = newValue);
    }
  }

  void handleOnChange(bool newValue) {
    unawaited(HapticFeedback.selectionClick());
    widget.onChanged(newValue);
    setState(() => value = newValue);
  }

  // endregion
}
