name: eltako_connect
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 3.4.0+3

environment:
  sdk: '>=3.7.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  collection: ^1.19.1
  crypto: ^3.0.6
  cupertino_icons: ^1.0.8
  eltako_log:
    git:
      url: **************:Eltako/app-eltako_log.git
      ref: 0.0.2
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_nsd:
    git:
      url: **************:Eltako/app-flutter_nsd_fork.git
      ref: master
  flutter_reactive_ble: ^5.4.0
  flutter_secure_storage: ^9.2.4
  hive: ^2.2.3
  intl: any
  mobile_scanner: ^4.0.1
  native_device_orientation: ^2.0.3
  get_it: ^8.0.3
  location: ^6.0.2
  http: any
  mutex: ^3.1.0
  path: ^1.9.1
  path_provider: ^2.1.5
  sqflite: ^2.4.2
  url_launcher: ^6.3.1
  yaml: ^3.1.3
  yaml_writer: ^2.1.0
  signal_strength_indicator: ^0.4.1
  permission_handler: ^12.0.0
  go_router: ^14.8.1
  fl_chart: ^0.70.2
  wifi_iot: ^0.3.19
  esp_provisioning_softap:
    git:
      url: **************:Eltako/app-esp_provisioning_softap_fork.git
      ref: master
  shared_preferences: ^2.2.3
  flutter_native_splash: ^2.4.6
  device_info_plus: ^11.5.0
  flutter_blue_plus: ^1.34.5
  share_plus: ^11.1.0
  share_plus_platform_interface: ^6.1.0

dev_dependencies:
  golden_toolkit: ^0.15.0
  flutter_driver:
    sdk: flutter
  integration_test:
    sdk: flutter
  flutter_launcher_icons: ^0.14.3
  flutter_lints: ^5.0.0
  custom_lint: any
  flutter_test:
    sdk: flutter
  flutter_gen: ^5.10.0
  flutter_gen_runner: ^5.10.0
  build_runner: ^2.4.13

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
flutter_gen:
  output: lib/gen/ # Optional (default: lib/gen/)
  line_length: 120 # Optional (default: 80)

  # Optional
  integrations:
    flutter_svg: true
    rive: true
    lottie: true

  colors:
    inputs:
      - assets/color/colors.xml
# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # This had to be added since flutter 3.32 to generate the app localization files while pub get
  generate: true


  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/app_icon/
    - assets/images/
    - assets/images/devices/assubt-230v/
    - assets/images/devices/eoa64/
    - assets/images/devices/esb62np-ip110-240v/
    - assets/images/devices/esb64np-ipm/
    - assets/images/devices/esr62np-ip110-240v/
    - assets/images/devices/esr62pf-ip110-240v/
    - assets/images/devices/esr64np-ipm/
    - assets/images/devices/esr64pf-ipm/
    - assets/images/devices/eud12npn-bt300w-230v/
    - assets/images/devices/eud12npn-bt600w-230v/
    - assets/images/devices/eud62npn-ip110-240v/
    - assets/images/devices/eud62npn-Ipm110-240v/
    - assets/images/devices/eud64npn-ipm/
    - assets/images/devices/mfz12dbt-uc/
    - assets/images/devices/s2u12dbt-uc/
    - assets/images/devices/su12dbt11-uc/
    - assets/images/devices/su62pfbt-uc/
    - assets/images/devices/zgw16wl-ip/

    - assets/images/sensors/
    - assets/images/sensors/no_sensors_found/
    - assets/images/sensors/no_switch_found/
    - assets/images/logos/
    - assets/images/logos/eltako/
    - assets/images/logos/enocean/
    - assets/images/logos/matter/

    - assets/images/unknownError/

    - assets/images/data_matrix/
    - assets/images/permissions/
    - assets/images/permissions/noBluetooth/
    - assets/images/permissions/noLocation/
    - assets/images/permissions/noDevice/
    - assets/images/permissions/noWifi/
    - assets/images/notAvailable/
    - assets/images/relay_diagrams/
    - assets/images/switch_behavior/
    - assets/images/searchDevice/
    - assets/images/unknownError/
    - assets/images/updateDevice/

    - assets/icons/power/outline/
    - assets/icons/power/circle/
    - assets/icons/wifi/outline/
    - assets/icons/wifi/circle/
    - assets/icons/counter/circle/
    - assets/icons/mfz-bt/circle/
    - assets/icons/configuration/circle/
    - assets/icons/configuration/outline/
    - assets/icons/configuration-slash/outline/
    - assets/icons/blinds/outline/
    - assets/icons/blinds/circle/
    - assets/icons/dimmer-value/circle/
    - assets/icons/warning/filled/
    - assets/icons/warning/transparent/
    - assets/icons/error/filled/
    - assets/icons/error/transparent/
    - assets/icons/electricity-wifi/circle/
    - assets/icons/password/outline/
    - assets/icons/password/circle/
    - assets/icons/pin/outline/
    - assets/icons/pin/circle/
    - assets/icons/dimmer-behaviour/circle/
    - assets/icons/calendar/outline/
    - assets/icons/calendar/circle/
    - assets/icons/cancel/outline/
    - assets/icons/flags/
    - assets/icons/fingerprint/circle/
    - assets/icons/fingerprint/outline/
    - assets/icons/arrow-down/outline/
    - assets/icons/minus/outline/
    - assets/icons/bin/outline/
    - assets/icons/bin/circle/
    - assets/icons/speed/outline/
    - assets/icons/speed/circle/
    - assets/icons/sparkle/circle/
    - assets/icons/blinds-wifi/circle/
    - assets/icons/timer/outline/
    - assets/icons/timer/circle/
    - assets/icons/mfz-wifi/circle/
    - assets/icons/modbus/circle/
    - assets/icons/network/circle/
    - assets/icons/network/outline/
    - assets/icons/location/circle/
    - assets/icons/location/outline/
    - assets/icons/confirm/outline/
    - assets/icons/relay-bt/circle/
    - assets/icons/gear/outline/
    - assets/icons/gear/circle/
    - assets/icons/sunrise/outline/
    - assets/icons/sunrise/circle/
    - assets/icons/dimmer-bt/circle/
    - assets/icons/language/outline/
    - assets/icons/language/circle/
    - assets/icons/mqtt/circle/
    - assets/icons/mqtt/outline/
    - assets/icons/bug/outline/
    - assets/icons/bug/circle/
    - assets/icons/mfz/circle/
    - assets/icons/mfz/outline/
    - assets/icons/qr-code/circle/
    - assets/icons/qr-code/outline/
    - assets/icons/edit/outline/
    - assets/icons/edit/circle/
    - assets/icons/arrow-left/outline/
    - assets/icons/sunset/outline/
    - assets/icons/sunset/circle/
    - assets/icons/success/filled/
    - assets/icons/success/transparent/
    - assets/icons/logo/eltako/
    - assets/icons/arrow-right/outline/
    - assets/icons/dimmer-wifi/circle/
    - assets/icons/relay-wifi/circle/
    - assets/icons/bluetooth/outline/
    - assets/icons/bluetooth/circle/
    - assets/icons/arrow-up/outline/
    - assets/icons/search/outline/
    - assets/icons/search/circle/
    - assets/icons/lan/circle/
    - assets/icons/light-switch/outline/
    - assets/icons/light-switch/circle/
    - assets/icons/lightning/circle/
    - assets/icons/singleswitch/circle/
    - assets/icons/doubleswitch/circle/
    - assets/icons/multiswitch/circle/
    - assets/icons/snooze/outline/
    - assets/icons/snooze/circle/
    - assets/icons/timerswitch-bt/circle/
    - assets/icons/lock-closed/outline/
    - assets/icons/lock-closed/circle/
    - assets/icons/timerswitch/circle/
    - assets/icons/blinds-bt/circle/
    - assets/icons/dimmer/outline/
    - assets/icons/dimmer/circle/
    - assets/icons/lightbulb/outline/
    - assets/icons/lightbulb/circle/
    - assets/icons/sensor/circle/
    - assets/icons/snoozetimer/outline/
    - assets/icons/snoozetimer/circle/
    - assets/icons/dots/outline/
    - assets/icons/motionsensor/outline/
    - assets/icons/motionsensor/circle/
    - assets/icons/relay/circle/
    - assets/icons/repeat/circle/
    - assets/icons/rocket/circle/
    - assets/icons/check/outline/
    - assets/icons/impulse/outline/
    - assets/icons/impulse/circle/
    - assets/icons/timerswitch-wifi/circle/
    - assets/icons/clock/outline/
    - assets/icons/clock/circle/
    - assets/icons/devices/outline/
    - assets/icons/devices/circle/
    - assets/icons/device-config/circle/
    - assets/icons/plus/outline/
    - assets/icons/share/circle/
    - assets/icons/share/outline/
    - assets/icons/grid/outline/
    - assets/icons/grid/circle/
    - assets/icons/enocean/circle/
    - assets/icons/enocean/outline/
    - assets/icons/alexa/circle/
    - assets/icons/alexa/outline/
    - assets/icons/appleHome/circle/
    - assets/icons/appleHome/outline/
    - assets/icons/googleHome/circle/
    - assets/icons/googleHome/outline/
    - assets/icons/samsungSmartThing/circle/
    - assets/icons/samsungSmartThing/outline/
    - assets/icons/stopwatch/circle/
    - assets/icons/stopwatch/outline/
    - assets/icons/import/circle/
    - assets/icons/import/outline/
    - assets/icons/floppydisk/circle/
    - assets/icons/floppydisk/outline/
    - assets/icons/zgw-wifi/circle/

    - assets/icons/reset/bin/circle/
    - assets/icons/reset/bluetooth/circle/
    - assets/icons/reset/dimmer/circle/
    - assets/icons/reset/impulse/circle/
    - assets/icons/reset/password/circle/
    - assets/icons/reset/reconnect/circle/
    - assets/icons/reset/timer/circle/
    - assets/icons/reset/settings/circle/

    - assets/json/product_database.json

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Barlow
      fonts:
        - asset: assets/fonts/Barlow/Barlow-Regular.ttf
        - asset: assets/fonts/Barlow/Barlow-Medium.ttf
          weight: 500
        - asset: assets/fonts/Barlow/Barlow-Bold.ttf
          weight: 700
        - asset: assets/fonts/Barlow/Barlow-Italic.ttf
          style: italic
        - asset: assets/fonts/Barlow/Barlow-BoldItalic.ttf
          weight: 700
          style: italic
#     fonts:
#       - asset: fonts/Schyler-Regular.ttf
#       - asset: fonts/Schyler-Italic.ttf
#         style: italic
#   - family: Trajan Pro
#     fonts:
#       - asset: fonts/TrajanPro.ttf
#       - asset: fonts/TrajanPro_Bold.ttf
#         weight: 700
#
# For details regarding fonts from package dependencies,
# see https://flutter.dev/custom-fonts/#from-packages
